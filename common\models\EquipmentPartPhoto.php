<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\web\UploadedFile;
use yii\helpers\FileHelper;

/**
 * This is the model class for table "equipment_part_photos".
 *
 * @property int $id
 * @property int $equipment_part_id
 * @property string $photo_path
 * @property string|null $original_name
 * @property bool $is_main
 * @property int $sort_order
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property EquipmentPart $equipmentPart
 * @property string $fullPhotoPath
 * @property string $photoUrl
 */
class EquipmentPartPhoto extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'equipment_part_photos';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['equipment_part_id', 'photo_path'], 'required'],
            [['equipment_part_id', 'sort_order'], 'integer'],
            [['is_main'], 'boolean'],
            [['created_at', 'deleted_at'], 'safe'],
            [['photo_path', 'original_name'], 'string', 'max' => 255],
            [['equipment_part_id'], 'exist', 'skipOnError' => true, 'targetClass' => EquipmentPart::class, 'targetAttribute' => ['equipment_part_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'equipment_part_id' => 'Equipment Part ID',
            'photo_path' => 'Photo Path',
            'original_name' => 'Original Name',
            'is_main' => 'Is Main',
            'sort_order' => 'Sort Order',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[EquipmentPart]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getEquipmentPart()
    {
        return $this->hasOne(EquipmentPart::class, ['id' => 'equipment_part_id']);
    }

    /**
     * Возвращает полный путь к фотографии на сервере
     *
     * @return string
     */
    public function getFullPhotoPath()
    {
        return Yii::getAlias('@webroot/uploads/equipment_parts/' . $this->photo_path);
    }

    /**
     * Возвращает URL фотографии для отображения в браузере
     *
     * @return string
     */
    public function getPhotoUrl()
    {
        return Yii::getAlias('@web/uploads/equipment_parts/' . $this->photo_path);
    }

    /**
     * Сохраняет загруженный файл и создает запись в базе данных
     *
     * @param UploadedFile $file
     * @param int $equipmentPartId
     * @param bool $isMain
     * @return bool
     */
    public static function saveUploadedFile($file, $equipmentPartId, $isMain = false)
    {
        $fileName = 'part_' . $equipmentPartId . '_' . time() . '_' . uniqid() . '.' . $file->extension;
        $directory = Yii::getAlias('@webroot/uploads/equipment_parts/');
        
        if (!is_dir($directory)) {
            FileHelper::createDirectory($directory, 0777, true);
        }

        if ($file->saveAs($directory . $fileName)) {
            $photo = new self();
            $photo->equipment_part_id = $equipmentPartId;
            $photo->photo_path = $fileName;
            $photo->original_name = $file->baseName . '.' . $file->extension;
            $photo->is_main = $isMain;
            $photo->sort_order = self::getNextSortOrder($equipmentPartId);
            $photo->created_at = date('Y-m-d H:i:s');
            
            return $photo->save();
        }
        
        return false;
    }

    /**
     * Получает следующий порядковый номер для сортировки
     *
     * @param int $equipmentPartId
     * @return int
     */
    private static function getNextSortOrder($equipmentPartId)
    {
        $maxOrder = self::find()
            ->where(['equipment_part_id' => $equipmentPartId])
            ->andWhere(['deleted_at' => null])
            ->max('sort_order');
            
        return $maxOrder ? $maxOrder + 1 : 1;
    }

    /**
     * Устанавливает фотографию как главную
     *
     * @return bool
     */
    public function setAsMain()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Сначала убираем флаг is_main у всех фотографий этой запчасти
            self::updateAll(
                ['is_main' => false],
                ['equipment_part_id' => $this->equipment_part_id]
            );
            
            // Затем устанавливаем флаг для текущей фотографии
            $this->is_main = true;
            $result = $this->save();
            
            if ($result) {
                $transaction->commit();
            } else {
                $transaction->rollBack();
            }
            
            return $result;
        } catch (\Exception $e) {
            $transaction->rollBack();
            return false;
        }
    }

    /**
     * Мягкое удаление фотографии
     *
     * @return bool
     */
    public function softDelete()
    {
        $this->deleted_at = date('Y-m-d H:i:s');
        return $this->save();
    }

    /**
     * Физическое удаление файла с диска
     *
     * @return bool
     */
    public function deleteFile()
    {
        $filePath = $this->getFullPhotoPath();
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function beforeDelete()
    {
        // При удалении записи также удаляем файл
        $this->deleteFile();
        return parent::beforeDelete();
    }
}
