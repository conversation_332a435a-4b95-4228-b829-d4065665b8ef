<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "material_storage".
 *
 * @property int $id
 * @property int $material_id
 * @property float $quantity
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 *
 * @property Material $material
 * @property MaterialStorageHistory[] $materialStorageHistories
 */
class MaterialStorage extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'material_storage';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['material_id', 'quantity'], 'required'],
            [['material_id', 'quantity'], 'default', 'value' => null],
            [['material_id'], 'integer'],
            [['quantity'], 'number'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['material_id'], 'exist', 'skipOnError' => true, 'targetClass' => Material::class, 'targetAttribute' => ['material_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'material_id' => 'Material ID',
            'quantity' => 'Quantity',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[Material]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterial()
    {
        return $this->hasOne(Material::class, ['id' => 'material_id']);
    }

    /**
     * Gets query for [[MaterialStorageHistories]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterialStorageHistories()
    {
        return $this->hasMany(MaterialStorageHistory::class, ['material_storage_id' => 'id']);
    }

    /**
     * Finds storage record by material_id and date
     * 
     * @param int $materialId
     * @param string $date
     * @return MaterialStorage|null
     */
    public static function findByMaterialAndDate($materialId, $date)
    {
        return self::find()
            ->where([
                'material_id' => $materialId,
                'DATE(created_at)' => $date
            ])
            ->one();
    }
}
