<?php use yii\bootstrap5\Html;
$this->title = Yii::t("app", "currency_history");
$currency_name = $currency->name;
?>
    <div class="card-body">
        <div class="row align-items-center mb-3">
            <div class="col-md-6">
                <h4 class="my-0">
                    <?= Html::encode($currency_name) . ' - ' . Html::encode($this->title) ?>
                </h4>
            </div>

            <div class="col-md-6 text-right">
                <?= Html::a('<i class="fa fa-arrow-left"></i> ' . Yii::t('app', 'Back to Currency'), ['currency/view'], ['class' => 'btn btn-primary']) ?>
            </div>
        </div>


        <table class="table table-bordered">
            <thead>
            <tr>
                <th><?= Yii::t("app", "course") ?></th>
                <th><?= Yii::t("app", "start_date") ?></th>
                <th><?= Yii::t("app", "end_date") ?></th>
            </tr>
            </thead>
            <tbody>
            <?php if (!empty($courses)): ?>
                <?php foreach ($courses as $course): ?>
                    <tr>
                        <td><?= Html::encode($course->course) ?></td>
                        <td><?= Html::encode($course->start_date) ?></td>
                        <td><?= Html::encode($course->end_date) ?></td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="4" class="text-center">
                        <?= Yii::t("app", "No courses available for this currency.") ?>
                    </td>
                </tr>
            <?php endif; ?>
            </tbody>
        </table>
    </div>
