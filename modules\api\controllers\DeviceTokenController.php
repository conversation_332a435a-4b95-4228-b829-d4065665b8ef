<?php

namespace app\modules\api\controllers;

use app\common\models\ApiResponse;
use app\common\models\UserDeviceToken;
use Yii;
use yii\web\ForbiddenHttpException;
use Swagger\Annotations as SWG;

/**
 * Контроллер для работы с токенами устройств
 *
 * @SWG\Tag(
 *     name="Device Tokens",
 *     description="Управление токенами устройств для push-уведомлений"
 * )
 */
class DeviceTokenController extends BaseController
{
    /**
     * {@inheritdoc}
     */
    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        try {
            // Разрешаем доступ пользователям с ролями user, product_keeper и sales
            if (!Yii::$app->user->can('user') && !Yii::$app->user->can('product_keeper') && !Yii::$app->user->can('sales')) {
                throw new ForbiddenHttpException(Yii::t('app', "You don\'t have permission to access this resource"));
            }
        } catch (ForbiddenHttpException $e) {
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            Yii::$app->response->data = ApiResponse::response(
                $e->getMessage(),
                null,
                403
            );
            Yii::$app->response->statusCode = 403;
            return false;
        }

        return true;
    }

    /**
     * Регистрация токена устройства
     *
     * @SWG\Post(
     *     path="/api/device-token/register",
     *     summary="Регистрация токена устройства",
     *     description="Регистрирует токен устройства для получения push-уведомлений",
     *     tags={"Device Tokens"},
     *     security={{"Bearer":{}}},
     *     @SWG\Parameter(
     *         name="body",
     *         in="body",
     *         required=true,
     *         description="Данные токена устройства",
     *         @SWG\Schema(
     *             required={"device_token", "device_type"},
     *             @SWG\Property(property="device_token", type="string", example="fMEGMCS6:APA91bHZ3k2QFbG..."),
     *             @SWG\Property(
     *                 property="device_type",
     *                 type="string",
     *                 enum={"android", "ios", "web"},
     *                 example="android"
     *             ),
     *             @SWG\Property(property="device_name", type="string", example="Samsung Galaxy S21")
     *         )
     *     ),
     *     @SWG\Response(
     *         response=200,
     *         description="Токен успешно зарегистрирован",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="Токен устройства успешно зарегистрирован"),
     *             @SWG\Property(
     *                 property="data",
     *                 type="object",
     *                 @SWG\Property(property="id", type="integer", example=1),
     *                 @SWG\Property(property="device_token", type="string", example="fMEGMCS6:APA91bHZ3k2QFbG..."),
     *                 @SWG\Property(property="device_type", type="string", example="android"),
     *                 @SWG\Property(property="device_name", type="string", example="Samsung Galaxy S21"),
     *                 @SWG\Property(property="created_at", type="string", format="date-time", example="2023-05-15 10:00:00"),
     *                 @SWG\Property(property="roles", type="array", @SWG\Items(type="string", example="sales")),
     *                 @SWG\Property(property="is_product_keeper", type="boolean", example=false),
     *                 @SWG\Property(property="is_sales", type="boolean", example=true)
     *             ),
     *             @SWG\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @SWG\Response(
     *         response=400,
     *         description="Ошибка валидации",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="Не указаны обязательные параметры"),
     *             @SWG\Property(property="data", type="object"),
     *             @SWG\Property(property="code", type="integer", example=400)
     *         )
     *     ),
     *     @SWG\Response(
     *         response=401,
     *         description="Не авторизован",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="Your request was made with invalid credentials"),
     *             @SWG\Property(property="data", type="object"),
     *             @SWG\Property(property="code", type="integer", example=401)
     *         )
     *     ),
     *     @SWG\Response(
     *         response=403,
     *         description="Доступ запрещен",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="You don't have permission to access this resource"),
     *             @SWG\Property(property="data", type="object"),
     *             @SWG\Property(property="code", type="integer", example=403)
     *         )
     *     ),
     *     @SWG\Response(
     *         response=500,
     *         description="Внутренняя ошибка сервера",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="Ошибка при регистрации токена устройства"),
     *             @SWG\Property(property="data", type="object"),
     *             @SWG\Property(property="code", type="integer", example=500)
     *         )
     *     )
     * )
     *
     * @return array
     */
    public function actionRegister()
    {
        $user_id = Yii::$app->user->id;
        $device_token = Yii::$app->request->post('device_token');
        $device_type = Yii::$app->request->post('device_type');
        $device_name = Yii::$app->request->post('device_name');

        if (empty($device_token) || empty($device_type)) {
            return ApiResponse::response('Не указаны обязательные параметры', null, 400);
        }

        if (!in_array($device_type, [UserDeviceToken::TYPE_ANDROID, UserDeviceToken::TYPE_IOS, UserDeviceToken::TYPE_WEB])) {
            return ApiResponse::response('Неверный тип устройства', null, 400);
        }

        $token = UserDeviceToken::register($user_id, $device_token, $device_type, $device_name);

        if ($token) {
            // Получаем роли пользователя
            $auth = Yii::$app->authManager;
            $roles = $auth->getRolesByUser($user_id);
            $roleNames = [];

            foreach ($roles as $role) {
                $roleNames[] = $role->name;
            }

            // Проверяем, есть ли у пользователя роли product_keeper или sales
            $hasProductKeeperRole = in_array('product_keeper', $roleNames);
            $hasSalesRole = in_array('sales', $roleNames);

            return ApiResponse::response('Токен устройства успешно зарегистрирован', [
                'id' => $token->id,
                'device_token' => $token->device_token,
                'device_type' => $token->device_type,
                'device_name' => $token->device_name,
                'created_at' => $token->created_at,
                'roles' => $roleNames,
                'is_product_keeper' => $hasProductKeeperRole,
                'is_sales' => $hasSalesRole
            ]);
        }

        return ApiResponse::response('Ошибка при регистрации токена устройства', null, 500);
    }

    /**
     * Деактивация токена устройства
     *
     * @SWG\Post(
     *     path="/api/device-token/deactivate",
     *     summary="Деактивация токена устройства",
     *     description="Деактивирует токен устройства для прекращения получения push-уведомлений",
     *     tags={"Device Tokens"},
     *     security={{"Bearer":{}}},
     *     @SWG\Parameter(
     *         name="body",
     *         in="body",
     *         required=true,
     *         description="Данные токена устройства",
     *         @SWG\Schema(
     *             required={"device_token"},
     *             @SWG\Property(property="device_token", type="string", example="fMEGMCS6:APA91bHZ3k2QFbG...")
     *         )
     *     ),
     *     @SWG\Response(
     *         response=200,
     *         description="Токен успешно деактивирован",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="Токен устройства успешно деактивирован"),
     *             @SWG\Property(property="data", type="object"),
     *             @SWG\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @SWG\Response(
     *         response=400,
     *         description="Ошибка валидации",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="Не указан токен устройства"),
     *             @SWG\Property(property="data", type="object"),
     *             @SWG\Property(property="code", type="integer", example=400)
     *         )
     *     ),
     *     @SWG\Response(
     *         response=401,
     *         description="Не авторизован",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="Your request was made with invalid credentials"),
     *             @SWG\Property(property="data", type="object"),
     *             @SWG\Property(property="code", type="integer", example=401)
     *         )
     *     ),
     *     @SWG\Response(
     *         response=403,
     *         description="Доступ запрещен",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="You don't have permission to access this resource"),
     *             @SWG\Property(property="data", type="object"),
     *             @SWG\Property(property="code", type="integer", example=403)
     *         )
     *     ),
     *     @SWG\Response(
     *         response=500,
     *         description="Внутренняя ошибка сервера",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="Ошибка при деактивации токена устройства"),
     *             @SWG\Property(property="data", type="object"),
     *             @SWG\Property(property="code", type="integer", example=500)
     *         )
     *     )
     * )
     *
     * @return array
     */
    public function actionDeactivate()
    {
        $user_id = Yii::$app->user->id;
        $device_token = Yii::$app->request->post('device_token');

        if (empty($device_token)) {
            return ApiResponse::response('Не указан токен устройства', null, 400);
        }

        if (UserDeviceToken::deactivate($user_id, $device_token)) {
            return ApiResponse::response('Токен устройства успешно деактивирован');
        }

        return ApiResponse::response('Ошибка при деактивации токена устройства', null, 500);
    }
}
