<?php

use yii\bootstrap5\Html;
use app\common\models\Tracking;

if($materials): ?>
    <div class="table-responsive">
        <table id="supplier-materials-grid-view" class="table table-bordered table-striped compact">
            <thead>
            <tr>
                <th><?= Yii::t("app", "supplier_name") ?></th>
                <th><?= Yii::t("app", "created_at") ?></th>
                <th><?= Yii::t("app", "car_number") ?></th>
                <th><?= Yii::t("app", "accepted_by") ?></th>
                <th><?= Yii::t("app", "material_name") ?></th>
                <th><?= Yii::t("app", "quantity") ?></th>
                <th><?= Yii::t("app", "price") ?></th>
                <th><?= Yii::t("app", "total_amount") ?></th>
                <th><?= Yii::t("app", "actions") ?></th>
            </tr>
            </thead>
            <tbody>
                <?php foreach ($materials as $material): ?>
                    <tr>
                        <td><?= Html::encode($material['supplier_name']) ?></td>
                        <td><?= Html::encode(date('d.m.Y H:i', strtotime($material['created_at']))) ?></td>
                        <td><?= Html::encode($material['car_number']) ?></td>
                        <td><?= Html::encode($material['accepted_user']) ?></td>
                        <td><?= Html::encode($material['material_name']) ?></td>
                        <td><?= Html::encode($material['quantity']) ?></td>
                        <td><?= Html::encode($material['price']) ?></td>
                        <td><?= Html::encode($material['total_amount']) ?></td>

                        <td>


                        <?php 
                                $tracking = Tracking::find()
                                    ->where(['process_id' => $material['id']])
                                    ->andWhere(['progress_type' => Tracking::TYPE_MATERIAL_INCOME])
                                    ->andWhere(['is', 'accepted_at', null])
                                    ->andWhere(['is', 'deleted_at', null])
                                    ->one();
                                
                                $showButton = $tracking !== null;
                            ?>

                            <?php if ($showButton): ?>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    

                                        <a href="#" class="dropdown-item income-material-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($material['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>

                                </div>
                            </div>
                            <?php endif; ?>
                        </td>

                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else: ?>
    <div>
        <p class="mb-0"><?= Yii::t('app', 'no_data_available') ?></p>
    </div>
<?php endif; ?>
