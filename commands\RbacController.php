<?php

namespace app\commands;

use Yii;
use yii\console\Controller;
use yii\helpers\FileHelper;
use yii\helpers\Inflector;

class RbacController extends Controller
{
    public function actionInit()
    {
        $auth = Yii::$app->authManager;
        
        $admin = $auth->getRole('admin');
        if ($admin === null) {
            $admin = $auth->createRole('admin');
            $admin->description = 'Administrator';
            $auth->add($admin);
        }

        $controllerPath = Yii::getAlias('@app/modules/backend/controllers');
        $controllers = FileHelper::findFiles($controllerPath, ['only' => ['*Controller.php']]);

        foreach ($controllers as $controller) {
            $content = file_get_contents($controller);
            
            preg_match('/namespace\s+(.*?);/s', $content, $matches);
            $namespace = $matches[1];
            
            preg_match('/class\s+(\w+)/s', $content, $matches);
            $className = $matches[1];
            
            if ($className === 'BaseController') {
                continue;
            }

            $fullClassName = $namespace . '\\' . $className;
            
            $methods = get_class_methods($fullClassName);
            
            foreach ($methods as $method) {
                if (strpos($method, 'action') === 0 && $method !== 'actions') {
                    $actionName = Inflector::camel2id(substr($method, 6));
                    $controllerName = Inflector::camel2id(str_replace('Controller', '', $className));
                    
                    $permissionName = $controllerName . '/' . $actionName;
                    
                    $permission = $auth->getPermission($permissionName);
                    if ($permission === null) {
                        $permission = $auth->createPermission($permissionName);
                        $permission->description = "Доступ к действию {$actionName} в контроллере {$controllerName}";
                        $auth->add($permission);
                        
                        $auth->addChild($admin, $permission);
                        
                        echo "Создано разрешение: {$permissionName}\n";
                    }
                }
            }
        }

        echo "\nРазрешения успешно созданы и назначены роли admin.\n";
    }
}
