<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "material_categories");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");
?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin')): ?>
                <a href="#" class="btn btn-primary material-category-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "add_category") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'material-category-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="material-category-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "category_name") ?></th>
                        <th><?= Yii::t("app", "description") ?></th>
                        <th><?= Yii::t("app", "status") ?></th>
                        <th><?= Yii::t("app", "created_at") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                        <td><?= Html::encode($model['name']) ?></td>
                        <td><?= Html::encode($model['description']) ?></td>
                        <td data-order="<?= !empty($model['deleted_at']) ? strtotime($model['deleted_at']) : '' ?>">
                            <span class="<?= $model['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?php
                                $statusText = $model['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                echo $statusText;
                                ?>
                            </span>
                        </td>
                        <td data-order="<?= !empty($model['created_at']) ? strtotime($model['created_at']) : '' ?>">
                            <?= !empty($model['created_at']) ? Html::encode(date('d.m.Y H:i', strtotime($model['created_at']))) : 'N/A' ?>
                        </td>

                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <a href="#" class="dropdown-item material-category-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                        <?= Yii::t("app", "edit") ?>
                                    </a>
                                    <a href="#" class="dropdown-item material-category-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model['id']) ?>">
                                        <span class="red-text"><?= Yii::t("app", "delete") ?></span>
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "add_category") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "edit_category") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "category_delete") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";
    var all = "{$all}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#material-category-grid-view')) {
            $('#material-category-grid-view').DataTable().destroy();
        }

        $('#material-category-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[0, 'desc']],
            "columnDefs": [
                {
                    "targets": [1, 2],
                    "orderable": false
                }
            ]
        });
    }

    $(document).off('pjax:end').on('pjax:end', function() {
        initializeDataTable();
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }

    function initializeMaterialCategoryCreate() {
        $(document).off('click.material-category-create').on('click.material-category-create', '.material-category-create', function() {
            $.ajax({
                url: '/backend/material-category/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("material-category-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.material-category-create-button').on('click.material-category-create-button', '.material-category-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#create-material-category-form').serialize();
                $.ajax({
                    url: '/backend/material-category/create',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#material-category-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            $.each(response.errors, function(field, errors) {
                                if (Array.isArray(errors)) {
                                    $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                                } else if (typeof errors === 'string') {
                                    $('#' + field + '-error').css('color', 'red').text(errors);
                                } else {
                                    $('#' + field + '-error').css('color', 'red').text(JSON.stringify(errors));
                                }
                            });
                        }
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeMaterialCategoryUpdate() {
        $(document).off('click.material-category-update').on('click.material-category-update', '.material-category-update', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/material-category/update',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    if (response.status) {
                        $('#ideal-mini-modal .modal-title').html(two);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').addClass("material-category-update-button");
                        initializeSelect2();
                    }
                }
            });
        });

        $(document).off('click.material-category-update-button').on('click.material-category-update-button', '.material-category-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#update-material-category-form').serialize();
                $.ajax({
                    url: '/backend/material-category/update',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#material-category-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            $.each(response.errors, function(field, errors) {
                                if (Array.isArray(errors)) {
                                    $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                                } else if (typeof errors === 'string') {
                                    $('#' + field + '-error').css('color', 'red').text(errors);
                                } else {
                                    $('#' + field + '-error').css('color', 'red').text(JSON.stringify(errors));
                                }
                            });
                        }
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeMaterialCategoryDelete() {
        $(document).off('click.material-category-delete').on('click.material-category-delete', '.material-category-delete', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/material-category/delete',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    if (response.status) {
                        $('#ideal-mini-modal-delete .modal-title').html(three);
                        $('#ideal-mini-modal-delete .modal-body').html(response.content);
                        $('#ideal-mini-modal-delete .mini-button').addClass("material-category-delete-button");
                    }
                }
            });
        });

        $(document).off('click.material-category-delete-button').on('click.material-category-delete-button', '.material-category-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#delete-material-category-form').serialize();
                $.ajax({
                    url: '/backend/material-category/delete',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#material-category-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    initializeDataTable();
    initializeDropdown();
    initializeMaterialCategoryCreate();
    initializeMaterialCategoryUpdate();
    initializeMaterialCategoryDelete();

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
