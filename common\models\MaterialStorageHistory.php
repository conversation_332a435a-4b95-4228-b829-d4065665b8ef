<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "material_storage_history".
 *
 * @property int $id
 * @property int $material_storage_id
 * @property int $material_id
 * @property int $type
 * @property int $add_user_id
 * @property int $quantity
 * @property string|null $created_at
 * @property string|null $deleted_at
 * @property int|null $invoice_detail_id
 *
 * @property Material $material
 * @property MaterialStorage $materialStorage
 */
class MaterialStorageHistory extends \yii\db\ActiveRecord
{
    const TYPE_INCOME = 1;
    const TYPE_OUTCOME = 2;

    const TYPE_INCOME_TO_PRDUCTION = 3;
    const TYPE_OUTCOME_FROM_PRDUCTION = 4;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'material_storage_history';
    }

    

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['material_storage_id', 'material_id', 'quantity', 'add_user_id'], 'required'],
            [['material_storage_id', 'material_id', 'quantity', 'type'], 'default', 'value' => null],
            [['material_storage_id', 'material_id', 'type', 'add_user_id', 'invoice_detail_id'], 'integer'],
            [['quantity'], 'number'],
            [['created_at', 'deleted_at'], 'safe'],
            [['material_id'], 'exist', 'skipOnError' => true, 'targetClass' => Material::class, 'targetAttribute' => ['material_id' => 'id']],
            [['material_storage_id'], 'exist', 'skipOnError' => true, 'targetClass' => MaterialStorage::class, 'targetAttribute' => ['material_storage_id' => 'id']],
            [['invoice_detail_id'], 'exist', 'skipOnError' => true, 'targetClass' => InvoiceDetail::class, 'targetAttribute' => ['invoice_detail_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'material_storage_id' => 'Material Storage ID',
            'material_id' => 'Material ID',
            'quantity' => 'Quantity',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[Material]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterial()
    {
        return $this->hasOne(Material::class, ['id' => 'material_id']);
    }

    /**
     * Gets query for [[AddUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAddUser()
    {
        return $this->hasOne(\app\modules\backend\models\Users::class, ['id' => 'add_user_id']);
    }

    /**
     * Gets query for [[MaterialStorage]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterialStorage()
    {
        return $this->hasOne(MaterialStorage::class, ['id' => 'material_storage_id']);
    }

    /**
     * Gets query for [[InvoiceDetail]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getInvoiceDetail()
    {
        return $this->hasOne(InvoiceDetail::class, ['id' => 'invoice_detail_id']);
    }
}
