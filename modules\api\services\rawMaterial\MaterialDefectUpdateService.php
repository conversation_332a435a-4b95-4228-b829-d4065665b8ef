<?php

namespace app\modules\api\services\rawMaterial;

use Yii;
use app\common\models\MaterialDefect;
use app\common\models\MaterialStorage;
use app\common\models\MaterialStorageHistory;
use app\common\models\Material;
use app\common\models\Tracking;
use app\helpers\ActionLogger;
use yii\db\Exception;

/**
 * Сервис для обновления дефектных материалов
 */
class MaterialDefectUpdateService
{
    /**
     * Получить данные дефектного материала для редактирования
     * 
     * @param int $materialDefectId ID дефектного материала
     * @return array|null
     * @throws Exception
     */
    public function getDefectMaterialForUpdate($materialDefectId)
    {
        // Проверяем трекинг
        $tracking = Tracking::findOne([
            'process_id' => $materialDefectId,
            'progress_type' => Tracking::TYPE_MATERIAL_DEFECT,
            'deleted_at' => null,
            'accepted_at' => null
        ]);

        if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
            throw new Exception('Материал уже подтвержден или не найден');
        }

        // Получаем данные материала
        $query = MaterialDefect::find()
            ->select([
                'material_defect.id',
                'material_defect.created_at',
                'material_defect.material_id',
                'material_defect.quantity',
                'material_defect.description',
                'm.name as material_name',
                'm.unit_type',
                'u.username as user_name'
            ])
            ->leftJoin('material m', 'm.id = material_defect.material_id')
            ->leftJoin('users u', 'u.id = material_defect.add_user_id')
            ->where([
                'material_defect.id' => $materialDefectId,
                'material_defect.deleted_at' => null
            ])
            ->one();

        if (!$query) {
            throw new Exception('Дефектный материал не найден или уже подтвержден');
        }

        $result = $query->toArray();
        $result['unit_type_name'] = Material::getUnitTypeName($result['unit_type']);

        return $result;
    }

    /**
     * Обновить дефектный материал
     * 
     * @param int $materialDefectId ID дефектного материала
     * @param array $materials Новые материалы
     * @param string $description Описание
     * @param int $userId ID пользователя
     * @return array Результат операции
     * @throws Exception
     */
    public function updateDefectMaterial($materialDefectId, $materials, $description, $userId)
    {
        // Проверяем трекинг
        $tracking = Tracking::findOne([
            'process_id' => $materialDefectId,
            'progress_type' => Tracking::TYPE_MATERIAL_DEFECT,
            'deleted_at' => null,
            'accepted_at' => null
        ]);

        if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
            throw new Exception('Материал уже подтвержден или не найден');
        }

        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            // Находим дефектный материал
            $materialDefect = MaterialDefect::findOne([
                'id' => $materialDefectId,
                'deleted_at' => null
            ]);
            
            if (!$materialDefect) {
                throw new Exception('Дефектный материал не найден или уже подтвержден');
            }

            // Возвращаем старый материал на склад
            $this->returnMaterialToStorage($materialDefect, $userId);

            // Списываем новый материал
            $material = reset($materials);
            if (!$material) {
                throw new Exception('Не указан новый материал');
            }

            $this->deductNewMaterialFromStorage($material, $userId);

            // Обновляем запись дефектного материала
            $this->updateMaterialDefectRecord($materialDefect, $material, $description, $userId);

            // Логируем действие
            \app\common\models\ActionLogger::actionLog(
                'update_material_defect',
                'material_defect',
                $materialDefect->id,
                [
                    'old_material_id' => $materialDefect->material_id,
                    'new_material_id' => $material['material_id'],
                    'old_quantity' => $materialDefect->quantity,
                    'new_quantity' => $material['quantity'],
                    'description' => $description
                ]
            );

            $transaction->commit();
            
            return [
                'success' => true,
                'message' => 'Дефектные материалы успешно обновлены'
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * Вернуть материал на склад
     * 
     * @param MaterialDefect $materialDefect
     * @param int $userId
     * @throws Exception
     */
    private function returnMaterialToStorage($materialDefect, $userId)
    {
        $materialStorage = MaterialStorage::find()
            ->where([
                'material_id' => $materialDefect->material_id,
                'DATE(created_at)' => date('Y-m-d')
            ])
            ->andWhere(['>=', 'quantity', 0])
            ->one();

        if (!$materialStorage) {
            $materialStorage = new MaterialStorage();
            $materialStorage->material_id = $materialDefect->material_id;
            $materialStorage->quantity = 0;
            $materialStorage->created_at = date('Y-m-d H:i:s');

            if (!$materialStorage->save()) {
                throw new Exception('Ошибка создания записи на складе');
            }
        }

        $materialStorage->quantity += $materialDefect->quantity;
        if (!$materialStorage->save()) {
            throw new Exception('Ошибка возврата на склад');
        }

        // Создаем запись в истории возврата
        $this->createStorageHistory(
            $materialStorage, 
            $materialDefect->material_id, 
            $materialDefect->quantity, 
            $userId, 
            MaterialStorageHistory::TYPE_INCOME
        );
    }

    /**
     * Списать новый материал со склада
     * 
     * @param array $material
     * @param int $userId
     * @throws Exception
     */
    private function deductNewMaterialFromStorage($material, $userId)
    {
        $materialStorages = MaterialStorage::find()
            ->where(['material_id' => $material['material_id']])
            ->andWhere(['>=', 'quantity', 0])
            ->orderBy(['created_at' => SORT_ASC])
            ->all();

        $totalAvailable = array_sum(array_column($materialStorages, 'quantity'));
        if ($totalAvailable < $material['quantity']) {
            throw new Exception("Недостаточно материала #{$material['material_id']} на складе");
        }

        $remainingQuantity = $material['quantity'];
        foreach ($materialStorages as $storage) {
            if ($remainingQuantity <= 0) break;

            $quantityToDeduct = min($storage->quantity, $remainingQuantity);
            $storage->quantity -= $quantityToDeduct;
            $remainingQuantity -= $quantityToDeduct;

            if (!$storage->save()) {
                throw new Exception('Ошибка обновления склада');
            }

            // Создаем запись в истории списания
            $this->createStorageHistory(
                $storage, 
                $material['material_id'], 
                -$quantityToDeduct, 
                $userId, 
                MaterialStorageHistory::TYPE_OUTCOME
            );
        }
    }

    /**
     * Обновить запись дефектного материала
     * 
     * @param MaterialDefect $materialDefect
     * @param array $material
     * @param string $description
     * @param int $userId
     * @throws Exception
     */
    private function updateMaterialDefectRecord($materialDefect, $material, $description, $userId)
    {
        $materialDefect->material_id = $material['material_id'];
        $materialDefect->quantity = $material['quantity'];
        $materialDefect->description = $description;
        $materialDefect->created_at = date('Y-m-d H:i:s');
        $materialDefect->accepted_at = date('Y-m-d H:i:s');
        $materialDefect->accepted_user_id = $userId;

        if (!$materialDefect->save()) {
            throw new Exception('Ошибка сохранения дефектного материала');
        }
    }

    /**
     * Создать запись в истории склада
     * 
     * @param MaterialStorage $storage
     * @param int $materialId
     * @param float $quantity
     * @param int $userId
     * @param string $type
     * @throws Exception
     */
    private function createStorageHistory($storage, $materialId, $quantity, $userId, $type)
    {
        $storageHistory = new MaterialStorageHistory();
        $storageHistory->material_storage_id = $storage->id;
        $storageHistory->material_id = $materialId;
        $storageHistory->quantity = $quantity;
        $storageHistory->created_at = date('Y-m-d H:i:s');
        $storageHistory->add_user_id = $userId;
        $storageHistory->type = $type;

        if (!$storageHistory->save()) {
            throw new Exception('Ошибка сохранения истории склада');
        }
    }
}
