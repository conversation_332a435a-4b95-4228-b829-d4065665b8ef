<?php
use yii\helpers\Html;
?>

<div class="position-form">
    <form id="product-price-change-form">
        <?= Html::hiddenInput(Yii::$app->request->csrfParam, Yii::$app->request->csrfToken) ?>
        <?= Html::hiddenInput('ProductPrice[product_id]', $model->id) ?>

        <div class="form-group">
            <label><?= Yii::t('app', 'product_name') ?></label>
            <input type="text" class="form-control" value="<?= Html::encode($model->name) ?>" disabled>
        </div>

        <div class="form-group">
            <label><?= Yii::t('app', 'current_price') ?></label>
            <input type="text" class="form-control" value="<?= Html::encode($currentPrice) ?>" disabled>
        </div>

        <div class="form-group">
            <label for="price"><?= Yii::t('app', 'new_price') ?> <span class="text-danger">*</span></label>
            <input type="text" id="price" name="ProductPrice[price]" class="form-control formatted-numeric-input" required>
            <div class="error-container" id="price-error"></div>
        </div>

        <div class="form-group">
            <label for="start_date"><?= Yii::t('app', 'start_date') ?> <span class="text-danger">*</span></label>
            <input type="date" id="start_date" name="ProductPrice[start_date]" class="form-control" min="<?= date('Y-m-d') ?>" required>
            <div class="error-container" id="start_date-error"></div>
        </div>
    </form>
</div>

<script>
    $(document).ready(function() {
        var today = new Date();
        var dd = String(today.getDate()).padStart(2, '0');
        var mm = String(today.getMonth() + 1).padStart(2, '0');
        var yyyy = today.getFullYear();
        today = yyyy + '-' + mm + '-' + dd;
        
        $('#start_date').attr('min', today);
    });
</script>
