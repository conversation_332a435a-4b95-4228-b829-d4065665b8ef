<?php
?>


<?php
use yii\helpers\Html;
?>



<div class="modal-body">
    <div class="alert alert-success return-success" style="display: none;"></div>
    <div class="alert alert-danger return-error" style="display: none;"></div>

    <form id="product-return-form">
        <div class="form-group">
            <label for="client_id"><?= Yii::t('app', 'client') ?></label>
            <select id="client_id" name="ProductReturnForm[client_id]" class="form-control select2">
                <option value=""><?= Yii::t('app', 'select_client') ?></option>
                <?php foreach ($clients as $id => $name): ?>
                    <option value="<?= $id ?>"><?= Html::encode($name) ?></option>
                <?php endforeach; ?>
            </select>
            <div class="invalid-feedback client-error"></div>
        </div>

        <div class="form-group">
            <label for="invoice_id"><?= Yii::t('app', 'sales') ?></label>
            <select id="invoice_id" name="ProductReturnForm[sale_id]" class="form-control select2" disabled>
                <option value=""><?= Yii::t('app', 'select_sales') ?></option>
            </select>
            <div class="invalid-feedback invoice-error"></div>
        </div>

        <div id="products-container" class="form-group" style="display: none;">
            <label><?= Yii::t('app', 'products') ?></label>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th><?= Yii::t('app', 'product') ?></th>
                            <th><?= Yii::t('app', 'quantity_block') ?></th>
                            <th><?= Yii::t('app', 'quantity') ?></th>
                        </tr>
                    </thead>
                    <tbody id="products-list">
                        <!-- Здесь будут отображаться продукты -->
                    </tbody>
                </table>
            </div>
            <div class="invalid-feedback product-error"></div>
        </div>
    </form>
</div>

<script>
    $(document).ready(function() {
        // Делаем кнопку неактивной при загрузке
        $('.product-return-button').prop('disabled', true);

        // Функция проверки заполненности формы
        function checkFormValidity() {
            var clientId = $('#client_id').val();
            var invoiceId = $('#invoice_id').val();
            var hasValidProduct = false;

            // Проверяем, есть ли хотя бы один продукт с указанным количеством
            $('.quantity-input').each(function() {
                var quantity = $(this).val();
                if (quantity && parseInt(quantity) > 0) {
                    hasValidProduct = true;
                    return false; // прерываем цикл each
                }
            });

            if (clientId && invoiceId && hasValidProduct) {
                $('.product-return-button').prop('disabled', false);
            } else {
                $('.product-return-button').prop('disabled', true);
            }
        }

        // Добавляем проверку при изменении любого поля
        $('#client_id, #invoice_id').on('change input', function() {
            checkFormValidity();
        });

        // Делегирование событий для динамически созданных полей
        $(document).on('change input', '.quantity-input, .quantity-block-input', function() {
            checkFormValidity();
        });

        // Обработчик изменения клиента
        $('#client_id').change(function() {
            var clientId = $(this).val();
            var invoiceId = $('#invoice_id');
            var productSelect = $('#product_id');
            var quantityInput = $('#quantity');

            // Сбрасываем зависимые поля
            invoiceId.html('<option value=""><?= Yii::t('app', 'loading...') ?></option>').prop('disabled', true);
            productSelect.html('<option value=""><?= Yii::t('app', 'select_sales') ?></option>').prop('disabled', true);
            quantityInput.val('').prop('disabled', true);
            $('.product-return-button').prop('disabled', true);

            if (clientId) {
                // Запрос списка счетов клиента
                $.ajax({
                    url: '/backend/sales-invoice/get-client-invoices',
                    type: 'POST',
                    data: {client_id: clientId},
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            var optionsHtml = '<option value=""><?= Yii::t('app', 'select_sales') ?></option>';
                            $.each(response.options, function(id, value) {
                                optionsHtml += '<option value="' + id + '">' + value + '</option>';
                            });
                            invoiceId.html(optionsHtml).prop('disabled', false);
                        } else {
                            invoiceId.html('<option value=""><?= Yii::t('app', 'Ошибка загрузки счетов') ?></option>');
                            $('.return-error').text(response.message).show();
                        }
                    },
                    error: function() {
                        invoiceId.html('<option value=""><?= Yii::t('app', 'Ошибка загрузки счетов') ?></option>');
                        $('.return-error').text('<?= Yii::t('app', 'Ошибка при загрузке счетов клиента') ?>').show();
                    }
                });
            } else {
                invoiceId.html('<option value=""><?= Yii::t('app', 'Сначала выберите клиента...') ?></option>');
            }
        });

        // Добавляем индикатор загрузки
        var loadingIndicator = $('<div>', {
            class: 'loading-indicator',
            style: 'display: none; text-align: center; margin: 20px 0;'
        }).html('<div class="spinner-border text-primary" role="status"><span class="sr-only"><?= Yii::t('app', 'Загрузка...') ?></span></div><p><?= Yii::t('app', 'Загрузка данных...') ?></p>');
        $('#products-container').after(loadingIndicator);

        // Обработчик изменения счета
        $('#invoice_id').change(function() {
            var saleId = $(this).val();
            var productsContainer = $('#products-container');
            var productsList = $('#products-list');

            // Сбрасываем зависимые поля
            productsList.empty();
            productsContainer.hide();
            $('.product-return-button').prop('disabled', true);
            $('.return-error').hide();

            if (saleId) {
                // Показываем индикатор загрузки
                loadingIndicator.show();

                // Запрос списка товаров в счете и их доступного количества
                $.ajax({
                    url: '/backend/sales-invoice/get-invoice-products-with-quantities',
                    type: 'POST',
                    data: {sale_id: saleId},
                    dataType: 'json',
                    success: function(response) {
                        // Скрываем индикатор загрузки
                        loadingIndicator.hide();

                        if (response.status === 'success') {
                            if (response.products && response.products.length > 0) {
                                // Очищаем список продуктов
                                productsList.empty();

                                // Подготавливаем фрагмент DOM для более эффективного рендеринга
                                var fragment = document.createDocumentFragment();

                                // Добавляем каждый продукт в таблицу
                                $.each(response.products, function(index, product) {
                                    var row = document.createElement('tr');

                                    // Ячейка с названием продукта
                                    var productCell = document.createElement('td');
                                    productCell.textContent = product.label;

                                    var hiddenInput = document.createElement('input');
                                    hiddenInput.type = 'hidden';
                                    hiddenInput.name = 'ProductReturnForm[products][' + index + '][product_id]';
                                    hiddenInput.value = product.value;
                                    productCell.appendChild(hiddenInput);
                                    row.appendChild(productCell);

                                    // Ячейка с количеством блоков
                                    var blockQuantityCell = document.createElement('td');
                                    var blockQuantityInput = document.createElement('input');
                                    blockQuantityInput.type = 'number';
                                    blockQuantityInput.name = 'ProductReturnForm[products][' + index + '][quantity_block]';
                                    blockQuantityInput.className = 'form-control quantity-block-input';
                                    blockQuantityInput.min = '0';
                                    blockQuantityInput.dataset.productId = product.value;
                                    blockQuantityInput.dataset.index = index;

                                    // Устанавливаем максимальное значение блоков
                                    if (product.size > 0) {
                                        var maxBlocks = Math.floor(product.quantity / product.size);
                                        blockQuantityInput.max = maxBlocks;
                                        blockQuantityInput.value = maxBlocks;
                                    }

                                    // Добавляем обработчики событий
                                    $(blockQuantityInput).on('input', function() {
                                        calculateQuantity(this);
                                    }).on('keydown', function(e) {
                                        if (e.key === 'Enter') {
                                            e.preventDefault();
                                            // Находим следующий инпут в следующей строке
                                            var nextRow = $(this).closest('tr').next('tr');
                                            if (nextRow.length > 0) {
                                                nextRow.find('.quantity-block-input').focus();
                                            }
                                        }
                                    });

                                    blockQuantityCell.appendChild(blockQuantityInput);
                                    row.appendChild(blockQuantityCell);

                                    // Ячейка с количеством
                                    var quantityCell = document.createElement('td');
                                    var quantityInput = document.createElement('input');
                                    quantityInput.type = 'number';
                                    quantityInput.name = 'ProductReturnForm[products][' + index + '][quantity]';
                                    quantityInput.className = 'form-control quantity-input';
                                    quantityInput.min = '0';
                                    quantityInput.max = product.quantity;
                                    quantityInput.value = product.quantity;
                                    quantityInput.readOnly = true;
                                    quantityInput.dataset.productId = product.value;
                                    quantityInput.dataset.index = index;

                                    $(quantityInput).on('input', function() {
                                        checkFormValidity();
                                    });

                                    quantityCell.appendChild(quantityInput);
                                    row.appendChild(quantityCell);

                                    // Добавляем строку во фрагмент
                                    fragment.appendChild(row);
                                });

                                // Добавляем весь фрагмент в DOM одной операцией
                                productsList.append(fragment);

                                // Показываем контейнер с продуктами
                                productsContainer.show();

                                // Проверяем валидность формы
                                checkFormValidity();
                            } else {
                                $('.return-error').text('<?= Yii::t('app', 'Нет доступных товаров') ?>').show();
                            }
                        } else {
                            $('.return-error').text(response.message).show();
                        }
                    },
                    error: function() {
                        // Скрываем индикатор загрузки
                        loadingIndicator.hide();
                        $('.return-error').text('<?= Yii::t('app', 'Ошибка при загрузке товаров из счета') ?>').show();
                    }
                });
            }
        });



        // Функция для расчета количества на основе блоков
        function calculateQuantity(input) {
            const productId = $(input).data('product-id');
            const index = $(input).data('index');
            const quantityInput = $('input[name="ProductReturnForm[products][' + index + '][quantity]"]');
            const maxBlocks = parseInt($(input).attr('max'));

            if (!input.value) {
                quantityInput.val('');
                return;
            }

            // Проверяем, не превышает ли введенное количество блоков максимальное
            if (maxBlocks && parseInt(input.value) > maxBlocks) {
                input.value = maxBlocks;
            }

            if (productId && input.value) {
                // Используем кэширование для ускорения
                const blockSize = window.productSizes && window.productSizes[productId] ? window.productSizes[productId] : null;

                if (blockSize) {
                    // Если размер блока уже известен, рассчитываем локально
                    const calculatedQuantity = parseInt(input.value) * blockSize;
                    const maxQuantity = parseInt(quantityInput.attr('max'));

                    if (maxQuantity && calculatedQuantity > maxQuantity) {
                        quantityInput.val(maxQuantity);
                        input.value = Math.floor(maxQuantity / blockSize);
                    } else {
                        quantityInput.val(calculatedQuantity);
                    }

                    checkFormValidity();
                } else {
                    // Если размер блока неизвестен, запрашиваем с сервера
                    $.ajax({
                        url: '/backend/sales-invoice/get-block-quantity',
                        method: 'GET',
                        data: {
                            id: productId,
                            blocks: input.value
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.error) {
                                quantityInput.val('');
                            } else {
                                // Сохраняем размер блока в кэше
                                if (!window.productSizes) window.productSizes = {};
                                window.productSizes[productId] = response.quantity / input.value;

                                const maxQuantity = parseInt(quantityInput.attr('max'));
                                const calculatedQuantity = response.quantity || 0;

                                // Проверяем, не превышает ли рассчитанное количество максимально доступное
                                if (maxQuantity && calculatedQuantity > maxQuantity) {
                                    quantityInput.val(maxQuantity);
                                    input.value = Math.floor(maxQuantity / (response.quantity / input.value));
                                } else {
                                    quantityInput.val(calculatedQuantity);
                                }
                            }
                            checkFormValidity();
                        },
                        error: function() {
                            quantityInput.val('');
                            checkFormValidity();
                        }
                    });
                }
            }
        }

        // Обработчик изменения количества (делегирование событий)
        $(document).on('input', '.quantity-input', function() {
            var max = parseInt($(this).attr('max'));
            var value = parseInt($(this).val());

            if (value > max) {
                $(this).val(max);
            } else if (value <= 0) {
                $(this).val('');
            }
            checkFormValidity();
        });
    });
</script>