<?php

namespace app\modules\backend\services\report;

use app\common\models\Client;
use app\common\models\ClientBalance;
use app\modules\backend\models\Region;
use yii\db\Query;

class ClientDebtReportService
{
    /**
     * Получить отчет по задолженностям клиентов
     * 
     * @param string $type 'debtors' (клиенты должны нам), 'creditors' (мы должны клиентам), 'all' (все)
     * @param int|null $regionId ID региона для фильтрации
     * @return array
     */
    public function getClientDebtReport($type = 'all', $regionId = null)
    {
        $query = (new Query())
            ->select([
                'c.id',
                'c.full_name',
                'c.phone_number',
                'c.phone_number_2',
                'c.address',
                'r.name as region_name',
                'COALESCE(cb.amount, 0) as balance',
                'cb.updated_at as last_update'
            ])
            ->from(['c' => Client::tableName()])
            ->leftJoin(['r' => Region::tableName()], 'c.region_id = r.id')
            ->leftJoin(['cb' => ClientBalance::tableName()], 'c.id = cb.client_id')
            ->where(['IS', 'c.deleted_at', null]);

        // Фильтр по типу задолженности
        if ($type === 'debtors') {
            // Клиенты должны нам (отрицательный баланс)
            $query->andWhere(['<', 'COALESCE(cb.amount, 0)', 0]);
        } elseif ($type === 'creditors') {
            // Мы должны клиентам (положительный баланс)
            $query->andWhere(['>', 'COALESCE(cb.amount, 0)', 0]);
        } else {
            // Все клиенты с ненулевым балансом
            $query->andWhere(['!=', 'COALESCE(cb.amount, 0)', 0]);
        }

        // Фильтр по региону
        if ($regionId) {
            $query->andWhere(['c.region_id' => $regionId]);
        }

        // Сортировка по убыванию абсолютного значения задолженности
        $query->orderBy(['ABS(COALESCE(cb.amount, 0))' => SORT_DESC]);

        $items = $query->all();

        // Подсчет итогов
        $totalDebtors = 0;      // Общая сумма долгов клиентов (нам должны)
        $totalCreditors = 0;    // Общая сумма долгов нам (мы должны)
        $debtorsCount = 0;      // Количество должников
        $creditorsCount = 0;    // Количество кредиторов

        foreach ($items as $item) {
            if ($item['balance'] < 0) {
                $totalDebtors += abs($item['balance']);
                $debtorsCount++;
            } elseif ($item['balance'] > 0) {
                $totalCreditors += $item['balance'];
                $creditorsCount++;
            }
        }

        return [
            'items' => $items,
            'totalDebtors' => $totalDebtors,
            'totalCreditors' => $totalCreditors,
            'debtorsCount' => $debtorsCount,
            'creditorsCount' => $creditorsCount,
            'netBalance' => $totalCreditors - $totalDebtors // Если положительный - мы должны больше, если отрицательный - нам должны больше
        ];
    }

    /**
     * Получить топ должников
     * 
     * @param int $limit Количество записей
     * @return array
     */
    public function getTopDebtors($limit = 10)
    {
        $query = (new Query())
            ->select([
                'c.id',
                'c.full_name',
                'c.phone_number',
                'r.name as region_name',
                'ABS(cb.amount) as debt_amount'
            ])
            ->from(['c' => Client::tableName()])
            ->leftJoin(['r' => Region::tableName()], 'c.region_id = r.id')
            ->innerJoin(['cb' => ClientBalance::tableName()], 'c.id = cb.client_id')
            ->where(['IS', 'c.deleted_at', null])
            ->andWhere(['<', 'cb.amount', 0])
            ->orderBy(['ABS(cb.amount)' => SORT_DESC])
            ->limit($limit);

        return $query->all();
    }
} 