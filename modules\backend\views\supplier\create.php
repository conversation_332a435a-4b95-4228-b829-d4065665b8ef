<?php

use app\common\models\Currency;
use yii\bootstrap5\Html;
use yii\helpers\ArrayHelper;

$currency = ArrayHelper::map(
    Currency::find()
        ->where(['deleted_at' => null])
        ->select(['id', 'name'])
        ->asArray()
        ->all(),
    'id',
    'name'
);
?>

<div class="supplier-form">
    <form id="supplier-create-form">
        <div class="form-group">
            <label for="full_name"><?= Yii::t('app', 'suplier_name') ?></label>
            <input type="text" id="full_name" name="Supplier[full_name]" maxlength="255" class="form-control" required>
            <div class="error-container" id="full_name-error"></div>
        </div>

        <div class="form-group">
            <label for="currency_id"><?= Yii::t('app', 'currency') ?></label>
            <select id="currency_id" name="Supplier[currency_id]" class="form-control select2">
                <option value=""><?= Yii::t('app', 'select_currency') ?></option>
                <?php foreach ($currency as $id => $name): ?>
                    <option value="<?= $id ?>"><?= Html::encode($name) ?></option>
                <?php endforeach; ?>
            </select>
            <div class="error-container" id="currency_id-error"></div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="phone_number"><?= Yii::t('app', 'phone_number') ?></label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">+998</span>
                        </div>
                        <input type="text" id="phone_number" name="Supplier[phone_number]" class="form-control" required>
                    </div>
                    <div class="error-container text-danger" id="phone_number-error"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="phone_number_2"><?= Yii::t('app', 'phone_number_2') ?></label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">+998</span>
                        </div>
                        <input type="text" id="phone_number_2" name="Supplier[phone_number_2]" class="form-control">
                    </div>
                    <div class="error-container text-danger" id="phone_number_2-error"></div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="account_number"><?= Yii::t('app', 'account_number') ?></label>
            <input type="text" id="account_number" name="Supplier[account_number]" class="form-control">
            <div class="error-container" id="account_number-error"></div>
        </div>

        <div class="form-group">
            <label for="address"><?= Yii::t('app', 'address') ?></label>
            <textarea id="address" name="Supplier[address]" class="form-control" rows="3"></textarea>
            <div class="error-container" id="address-error"></div>
        </div>
    </form>
</div>

<script>
$(document).ready(function() {
    function initPhoneInput(inputId) {
        const input = $(`#${inputId}`);

        input.on('input', function() {
            let value = this.value.replace(/\D/g, '');
            
            if (value.length > 9) {
                value = value.slice(0, 9);
            }

            let formattedValue = '';
            if (value.length > 0) {
                formattedValue = value.substring(0, 2);
            }
            if (value.length > 2) {
                formattedValue += ' ' + value.substring(2, 5);
            }
            if (value.length > 5) {
                formattedValue += ' ' + value.substring(5, 7);
            }
            if (value.length > 7) {
                formattedValue += ' ' + value.substring(7, 9);
            }

            this.value = formattedValue;
        });

        input.on('keypress', function(e) {
            if (!/^\d$/.test(e.key)) {
                e.preventDefault();
            }
        });

        input.on('paste', function(e) {
            e.preventDefault();
            let pastedText = (e.originalEvent || window.clipboardData).getData('text');
            let numericValue = pastedText.replace(/\D/g, '').slice(0, 9);
            this.value = numericValue;
            $(this).trigger('input');
        });
    }

    initPhoneInput('phone_number');
    initPhoneInput('phone_number_2');
});
</script>
