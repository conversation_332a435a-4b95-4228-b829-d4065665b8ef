<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\assets\Select2Asset;
use app\modules\backend\models\Expenses;
use app\common\models\Tracking;

DataTablesAsset::register($this);
Select2Asset::register($this);

$this->title = Yii::t("app", "supplier_finance");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$activeText = Yii::t("app", "active");
$inactiveText = Yii::t("app", "inactive");
$all = Yii::t("app", "all");
?>


<style>
    /* Стили для конкретного select2 */
    #suplier_filter.select2 {
        min-width: 150px !important;
        width: 100% !important;
    }

    /* Стили для контейнера Select2 */
    #suplier_filter + .select2-container {
        width: 150px !important;
    }

    /* Стили для полей ввода даты */
    input[type="date"].form-control {
        width: 150px;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }

    /* Стили для загрузчика */
    .table-container {
        position: relative;
        min-height: 200px;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    .table-container.loaded {
        opacity: 1;
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        opacity: 0;
        visibility: hidden;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        border-radius: 4px;
        transition: opacity 0.3s, visibility 0.3s;
    }

    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .loading-spinner-container {
        text-align: center;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 0.8s linear infinite;
        margin-bottom: 10px;
    }

    .loading-text {
        color: #666;
        font-size: 14px;
    }

    /* Скрываем таблицу во время загрузки */
    .table-container:not(.loaded) #supplier-payment-grid-view {
        visibility: hidden;
    }

    /* Добавляем тень для оверлея */
    .loading-overlay::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        border-radius: 4px;
        pointer-events: none;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6">
            <?php if (Yii::$app->user->can('admin')): ?>
                <div class="d-flex justify-content-end align-items-center gap-2">
                    <!-- Date inputs -->
                    <div class="d-flex gap-2" style="min-width: 200px;">
                        <input type="date" id="date_from" class="form-control" placeholder="<?= Yii::t('app', 'From Date') ?>">
                        <span style="width: 10px; display: inline-block"></span>
                        <input type="date" id="date_to" class="form-control" placeholder="<?= Yii::t('app', 'To Date') ?>">
                    </div>

                     <!-- Worker filter dropdown -->
                     <select id="suplier_filter" class="form-control select2">
                        <option value=""><?= Yii::t('app', 'supliers') ?></option>
                        <?php foreach($supliers as $suplier): ?>
                            <option value="<?= $suplier->id ?>"><?= $suplier->full_name ?></option>
                        <?php endforeach; ?>
                    </select>

                    <!-- Filter button -->
                    <button type="button" class="btn btn-primary mr-2" id="search-button">
                        <?= Yii::t('app', 'search') ?>
                    </button>
                </div>
            <?php endif ?>
        </div>
    </div>

   
        <!-- Payments Tab -->
            <?php Pjax::begin(['id' => 'supplier-payment-grid-pjax']); ?>
            <?php if($payments): ?>
                <div class="table-responsive table-container">
                    <!-- Добавляем оверлей с загрузчиком внутрь контейнера таблицы -->
                    <div class="loading-overlay">
                        <div class="loading-spinner-container">
                            <div class="loading-spinner"></div>
                            <div class="loading-text"><?= Yii::t("app", "loading") ?>...</div>
                        </div>
                    </div>
                    <table id="supplier-payment-grid-view" class="table table-bordered table-striped compact">
                        <thead>
                            <tr>
                                <th><?= Yii::t("app", "supplier_name") ?></th>
                                <th><?= Yii::t("app", "payment_created_at") ?></th>
                                <th><?= Yii::t("app", "who_entered") ?></th>
                                <th><?= Yii::t("app", "payment_type") ?></th>
                                <th><?= Yii::t("app", "amount") ?></th>
                                <th><?= Yii::t("app", "status") ?></th>
                                <th><?= Yii::t("app", "actions") ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payments as $payment): ?>
                                <tr>
                                    <td><?= Html::encode($payment['supplier_name']) ?></td>
                                    <td><?= Html::encode(date('d.m.Y H:i', strtotime($payment['created_at']))) // Html::encode($material['created_at']) ?></td>
                                    <td><?= Html::encode($payment['user_name']) ?></td>
                                    <td><?= Html::encode(Expenses::getTypePayment($payment['type'])) ?></td>
                                    <td><?= Html::encode($payment['amount']) ?></td>

                                    <td>
                                        <span class="<?= $payment['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                            <?php
                                            $statusText = $payment['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                            echo $statusText;
                                            ?>
                                        </span>
                                    </td>

                                    <td>
                                        <?php 
                                            $tracking = Tracking::find()
                                                ->where(['process_id' => $payment['expense_id']])
                                                ->andWhere(['progress_type' => Tracking::PAY_FOR_SUPPLIER])
                                                ->andWhere(['is', 'accepted_at', null])
                                                ->andWhere(['is', 'deleted_at', null])
                                                ->one();
                                            
                                        ?>
                                        <?php if (!$tracking): ?>
                                            <?php if ($payment['deleted_at'] == NULL): ?>
                                                <div class="dropdown d-inline">
                                                    <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                                        <?php echo Yii::t("app", "detail"); ?>
                                                    </a>
                                                    <div class="dropdown-menu">
                                                    

                                                            <a href="#" class="dropdown-item supplier-payment-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($payment['id']) ?>">
                                                                <span class="red-text"><?= Yii::t("app", "delete") ?></span>
                                                            </a>
                                                    
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif ?>
            <?php Pjax::end(); ?>

    

<div id="two" data-text="<?= Yii::t("app", "payment_delete") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $("#one").data("text");
    var two = $("#two").data("text");
    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";
    var activeText = "{$activeText}";
    var inactiveText = "{$inactiveText}";
    var all = "{$all}";

    // Функции для управления состоянием загрузки
    function showLoading() {
        $('.table-container').removeClass('loaded');
        $('.loading-overlay').addClass('show');
    }

    function hideLoading() {
        // Задержка для гарантии завершения инициализации DataTable
        setTimeout(() => {
            $('.table-container').addClass('loaded');
            $('.loading-overlay').removeClass('show');
        }, 300);
    }

    // Common DataTable options
    var tableOptions = {
        language: {
            search: searchLabel,
            lengthMenu: lengthMenuLabel,
            zeroRecords: zeroRecordsLabel,
            info: infoLabel,
            infoEmpty: infoEmptyLabel,
            infoFiltered: infoFilteredLabel
        },
        pageLength: 50,
        order: [[1, 'desc']],
        columnDefs: [{ 
            targets: [5, 6],
            orderable: false 
        }],
        deferRender: true,
        drawCallback: function() {
            hideLoading();
        }
    };

    function initializeDataTable() {
        const table = $('#supplier-payment-grid-view');
        
        if (!table.length) return;
        
        showLoading();
        
        if ($.fn.DataTable.isDataTable(table)) {
            table.DataTable().destroy();
        }
        
        table.show().DataTable(tableOptions);
    }

    // Обработчики PJAX
    $(document)
        .on('pjax:beforeSend', function() {
            showLoading();
        })
        .on('pjax:complete', function() {
            initializeAll();
        });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }

    function initializePaymentDelete() {
        $(document).off('click.supplier-payment-delete').on('click.supplier-payment-delete', '.supplier-payment-delete', function() {
            var id = $(this).attr("data-id");
            $('#ideal-mini-modal-delete .mini-button').removeClass("btn-primary");
            $.ajax({
                url: '/backend/supplier-finance/delete',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal-delete .modal-title').html(two);
                        $('#ideal-mini-modal-delete .modal-body').html(response.content);
                        $('#ideal-mini-modal-delete .mini-button').addClass("btn-danger delete-supplier-payment-button");
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.delete-supplier-payment-button').on('click.delete-supplier-payment-button', '.delete-supplier-payment-button', function() {
            if (!$(this).prop('disabled')) {
                $('.delete-supplier-payment-button').prop('disabled', true);
                var formData = $('#supplier-payment-delete-form').serialize();
                $.ajax({
                    url: '/backend/supplier-finance/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.status === 'success') {
                            $.pjax.reload({ container: '#supplier-payment-grid-pjax' });
                            $('.delete-supplier-payment-button').prop('disabled', false);
                            $('.close').trigger('click');
                        } else if (response.status === 'error') {
                            $('#ideal-mini-modal-delete .modal-body').html(response.content);
                            iziToast.error({
                                timeout: 5000,
                                icon: 'fas fa-exclamation-triangle',
                                message: response.message,
                                position: 'topRight',
                                onOpening: function(instance, toast) {
                                    toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                                }
                            });
                            $('.delete-supplier-payment-button').prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        $('.delete-supplier-payment-button').prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializeSearch() {
        $('#search-button').off('click').on('click', function() {
            showLoading();
            
            const dateFrom = $('#date_from').val();
            const dateTo = $('#date_to').val();
            const supplierId = $('#suplier_filter').val();

            if ($.fn.DataTable.isDataTable('#supplier-payment-grid-view')) {
                $('#supplier-payment-grid-view').DataTable().destroy();
            }

            $.ajax({
                url: '/backend/supplier-finance/search',
                type: 'POST',
                data: { 
                    date_from: dateFrom, 
                    date_to: dateTo, 
                    supplier_id: supplierId, 
                    type: 'payments' 
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('.table-responsive').html(response.content);
                        initializeDataTable();
                    } else {
                        hideLoading();
                        iziToast.error({
                            title: 'Ошибка',
                            message: response.message,
                            position: 'topRight'
                        });
                    }
                },
                error: function() {
                    hideLoading();
                    iziToast.error({
                        title: 'Ошибка',
                        message: '<?= Yii::t("app", "Error occurred while searching") ?>',
                        position: 'topRight'
                    });
                }
            });
        });
    }

    // Инициализация всех компонентов
    function initializeAll() {
        initializeDataTable();
        initializeDropdown();
        initializeSelect2();
        initializePaymentDelete();
        initializeSearch();
    }

    // Запуск инициализации
    initializeAll();
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
