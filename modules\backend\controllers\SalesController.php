<?php

namespace app\modules\backend\controllers;

use Yii;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use app\common\models\Sales;
use app\common\models\Client;

class SalesController extends BaseController
{
    public function actionIndex()
    {
        $query = Sales::find()
            ->select([
                'sales.id',
                'sales.created_at',
                'sales.status',
                'sales.deleted_at',
                'sales.total_special_prices_sum',
                'SUM(sd.factory_price * sd.quantity) as total_sum',
                'client.full_name as client_name',
                'sell_user.full_name as sell_user_name',
                'confirm_user.full_name as confirm_user_name',
                '(SELECT SUM(quantity) FROM sales_detail WHERE sale_id = sales.id) as total_quantity',
            ])
            ->leftJoin('client', 'sales.client_id = client.id')
            ->leftJoin('users as sell_user', 'sales.sell_user_id = sell_user.id')
            ->leftJoin('users as confirm_user', 'sales.confirm_user_id = confirm_user.id')
            ->leftJoin('sales_detail as sd', 'sales.id = sd.sale_id')
            ->groupBy('sales.id, client.full_name, sell_user.full_name, confirm_user.full_name, sales.total_special_prices_sum')
            ->orderBy(['sales.id' => SORT_DESC]);

        $result = $query->asArray()->all();
        $clients = Client::find()->where(['deleted_at' => null])->all();

        return $this->render('index', [
            'result' => $result,
            'clients' => $clients
        ]);
    }

    public function actionSearch()
    {
        $startDate = Yii::$app->request->post('start_date');
        $endDate = Yii::$app->request->post('end_date');
        $clientId = Yii::$app->request->post('client_id');
    
        $query = Sales::find()
            ->select([
                'sales.*',
                'client.full_name as client_name',
                'sell_user.full_name as sell_user_name',
                'confirm_user.full_name as confirm_user_name',
                '(SELECT SUM(quantity) FROM sales_detail WHERE sale_id = sales.id) as total_quantity',
            ])
            ->leftJoin('client', 'sales.client_id = client.id')
            ->leftJoin('users as sell_user', 'sales.sell_user_id = sell_user.id')
            ->leftJoin('users as confirm_user', 'sales.confirm_user_id = confirm_user.id')
            ->orderBy(['sales.id' => SORT_DESC]);
    
        if ($startDate && $endDate) {
            $startDate = date('Y-m-d', strtotime($startDate));
            $endDate = date('Y-m-d', strtotime($endDate));
            $query->andFilterWhere(['between', 'sales.created_at', $startDate, $endDate]);
        }
    
        if ($clientId) {
            $query->andFilterWhere(['sales.client_id' => $clientId]);
        }
    
        // $sql = $query->createCommand()->getRawSql();
        $result = $query->asArray()->all();
        $clients = Client::find()->where(['deleted_at' => null])->all();
    
        return $this->renderPartial('_index', [
            'result' => $result,
            'clients' => $clients
        ]);
    }

    
    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        if (Yii::$app->request->isPost) {
            $id = Yii::$app->request->post('Sales')['id'];
            $model = Sales::findOne($id);
            
            if ($model->load(Yii::$app->request->post()) && $model->validate()) {
                if(!$model->save(false)) {
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors()
                    ];
                }
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'record_successfully_updated')
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Sales::findOne($id);
            
            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model
                ])
            ];
        }
    }

    public function actionDetail()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $id = Yii::$app->request->get('id');
        $model = Sales::findOne($id);
        if (!$model) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }

        $salesDetails = $model->salesDetails;

        return [
            'status' => 'success',
            'content' => $this->renderPartial('detail', [
                'model' => $model,
                'salesDetails' => $salesDetails,
            ])
        ];
    }
}
