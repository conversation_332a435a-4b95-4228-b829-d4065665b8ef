<?php

use yii\db\Migration;

/**
 * Creates table for storing multiple photos for equipment parts
 */
class m250613_000001_create_equipment_part_photos_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Создаем таблицу для хранения фотографий запчастей
        $this->createTable('equipment_part_photos', [
            'id' => $this->bigPrimaryKey(),
            'equipment_part_id' => $this->bigInteger()->notNull(),
            'photo_path' => $this->string(255)->notNull(),
            'original_name' => $this->string(255)->null(),
            'is_main' => $this->boolean()->defaultValue(false),
            'sort_order' => $this->integer()->defaultValue(0),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Создаем внешний ключ на таблицу equipment_parts
        $this->addForeignKey(
            'fk-equipment_part_photos-equipment_part_id',
            'equipment_part_photos',
            'equipment_part_id',
            'equipment_parts',
            'id',
            'CASCADE'
        );

        // Создаем индекс для быстрого поиска фотографий запчасти
        $this->createIndex(
            'idx-equipment_part_photos-equipment_part_id',
            'equipment_part_photos',
            'equipment_part_id'
        );

        // Создаем индекс для главного фото
        $this->createIndex(
            'idx-equipment_part_photos-is_main',
            'equipment_part_photos',
            ['equipment_part_id', 'is_main']
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем внешний ключ
        $this->dropForeignKey('fk-equipment_part_photos-equipment_part_id', 'equipment_part_photos');
        
        // Удаляем индексы
        $this->dropIndex('idx-equipment_part_photos-equipment_part_id', 'equipment_part_photos');
        $this->dropIndex('idx-equipment_part_photos-is_main', 'equipment_part_photos');
        
        // Удаляем таблицу
        $this->dropTable('equipment_part_photos');
    }
}
