<?php

use yii\db\Migration;

class m251029_065611_add_vacation_pay_expense_type extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Проверяем, есть ли уже такой тип расходов
        $existingType = $this->db->createCommand("SELECT id FROM expenses_type WHERE name = 'Vacation pay'")->queryScalar();
        
        if (!$existingType) {
            $this->insert('expenses_type', [
                'name' => 'Vacation pay',
                'created_at' => date('Y-m-d H:i:s'),
            ]);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->delete('expenses_type', ['name' => 'Vacation pay']);
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250709_065611_add_vacation_pay_expense_type cannot be reverted.\n";

        return false;
    }
    */
}
