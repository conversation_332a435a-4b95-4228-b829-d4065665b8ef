<?php

namespace app\common\services;

use app\common\models\Client;
use app\common\models\ClientBalance;
use app\common\models\ClientBalanceHistory;
use app\common\models\ClientReturn;
use app\common\models\ProductStorage;
use app\common\models\Sales;
use app\common\models\SalesDetail;
use app\common\models\SalesReturn;
use Yii;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

class ProductReturnService
{
    /**
     * Получение списка клиентов для выпадающего списка.
     * @return array
     */
    public function getClientList(): array
    {
        return ArrayHelper::map(
            Client::find()
                ->select('client.*') 
                ->innerJoin('client_return', 'client_return.client_id = client.id')
                ->where(['client.deleted_at' => null])
                ->groupBy('client.id') 
                ->orderBy('client.full_name')
                ->all(),
            'id',
            'full_name'
        );
    }

    /**
     * Получение списка счетов клиента за последние 24 часа.
     * @param int $clientId
     * @return array
     */
    public function getClientRecentInvoices(int $clientId): array
    {
        $twentyFourHoursAgo = new Expression("NOW() - INTERVAL '1 day'");
        $sales = Sales::find()
            ->where(['client_id' => $clientId])
            ->andWhere(['status' => Sales::STATUS_CONFIRMED])
            ->andWhere(['>=', 'created_at', $twentyFourHoursAgo])
            ->orderBy(['created_at' => SORT_DESC])
            ->all();

        $list = [];
        foreach ($sales as $sale) {
            // Проверяем, есть ли возвраты для этого заказа
            $returnedProducts = SalesReturn::find()
                ->select('product_id')
                ->where(['sale_id' => $sale->id])
                ->andWhere(['status' => SalesReturn::STATUS_APPROVED])
                ->column();

            // Получаем все продукты в заказе
            $saleDetails = SalesDetail::find()
                ->select('product_id')
                ->where(['sale_id' => $sale->id])
                ->column();

            // Если есть продукты, которые ещё не возвращены полностью
            $canReturn = false;
            foreach ($saleDetails as $productId) {
                if (!in_array($productId, $returnedProducts)) {
                    $canReturn = true; // Хотя бы один продукт ещё можно вернуть
                    break;
                } else {
                    // Проверяем количество возвращённых против проданных
                    $soldQuantity = SalesDetail::find()
                        ->select('quantity')
                        ->where(['sale_id' => $sale->id, 'product_id' => $productId])
                        ->scalar();
                    $returnedQuantity = SalesReturn::find()
                        ->select('SUM(quantity)')
                        ->where(['sale_id' => $sale->id, 'product_id' => $productId, 'status' => SalesReturn::STATUS_APPROVED])
                        ->scalar() ?? 0;
                    if ($returnedQuantity < $soldQuantity) {
                        $canReturn = true; // Количество возвращённого меньше проданного
                        break;
                    }
                }
            }

            // Добавляем заказ в список, только если его можно вернуть
            if ($canReturn) {
                $list[$sale->id] = Yii::t('app', 'order') . ' #' . $sale->id . ' ' .
                    Yii::$app->formatter->asDatetime($sale->created_at, 'short') . ', ' .
                    Yii::t('app', 'car_number') . ': ' . $sale->car_number . ', ' .
                    Yii::t('app', 'driver') . ': ' . $sale->driver;
            }
        }
        return $list;
    }

    /**
     * Получение списка товаров из конкретного счета.
     * @param int $saleId
     * @return array
     */
    public function getInvoiceProducts(int $saleId): array
    {
        $details = SalesDetail::find()
            ->with('product')
            ->where(['sale_id' => $saleId])
            ->all();

        $returns = SalesReturn::find()
            ->select('product_id')
            ->where(['sale_id' => $saleId])
            ->andWhere(['status' => SalesReturn::STATUS_APPROVED])
            ->column();

        $list = [];
        foreach ($details as $detail) {
            if ($detail->product && !in_array($detail->product_id, $returns)) {
                $list[] = [
                    'value' => $detail->product_id,
                    'label' => $detail->product->name,
                ];
            }
        }
        return $list;
    }

    /**
     * Получение информации о товаре в счете для валидации количества.
     * @param int $saleId
     * @param int $productId
     * @return SalesDetail|null
     */
     public function getInvoiceProductDetail(int $saleId, int $productId): ?SalesDetail
     {
         return SalesDetail::find()
             ->where(['sale_id' => $saleId, 'product_id' => $productId])
             ->one();
     }

    /**
     * Обработка возврата товаров
     * @param int $clientId ID клиента
     * @param int $saleId ID счета
     * @param array $products Массив продуктов для возврата [продукт_id => количество]
     * @return bool Результат операции
     */
    public function processReturnMultiple(int $clientId, int $saleId, array $products): bool
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $success = false;

            foreach ($products as $product) {
                if (!isset($product['product_id']) || !isset($product['quantity'])) {
                    continue;
                }

                $productId = $product['product_id'];
                $quantity = $product['quantity'];

                if (empty($quantity) || $quantity <= 0) {
                    continue;
                }

                $salesDetail = SalesDetail::find()
                    ->where(['sale_id' => $saleId, 'product_id' => $productId])
                    ->one();

                if (!$salesDetail || $quantity > $salesDetail->quantity) {
                    continue;
                }

                $salesReturn = new SalesReturn();
                $salesReturn->sale_id = $saleId;
                $salesReturn->product_id = $productId;
                $salesReturn->quantity = $quantity;
                $salesReturn->unit_price = $salesDetail->sell_price;
                $salesReturn->total_price = $salesDetail->sell_price * $quantity;
                $salesReturn->status = SalesReturn::STATUS_APPROVED;
                $salesReturn->created_at = new Expression('NOW()');

                if (!$salesReturn->save()) {
                    continue;
                }

                // Обновляем склад
                $today = new Expression('CURRENT_DATE');
                $productStorage = ProductStorage::find()
                    ->where(['product_id' => $productId])
                    ->andWhere(['DATE(enter_date)' => $today])
                    ->andWhere(['deleted_at' => null])
                    ->one();

                if ($productStorage === null) {
                    $productStorage = new ProductStorage();
                    $productStorage->product_id = $productId;
                    $productStorage->quantity = $quantity;
                    $productStorage->enter_date = new Expression('NOW()');
                    $productStorage->add_user_id = Yii::$app->user->getId();
                } else {
                    $productStorage->quantity += $quantity;
                }

                if (!$productStorage->save()) {
                    continue;
                }

                // Обновляем баланс клиента
                $clientBalance = ClientBalance::findOne(['client_id' => $clientId]);
                if (!$clientBalance) {
                    $clientBalance = new ClientBalance();
                    $clientBalance->client_id = $clientId;
                    $clientBalance->amount = 0;
                }
                $clientBalance->amount += $salesDetail->special_price * $quantity;
                if (!$clientBalance->save()) {
                    continue;
                }

                $clientBalanceHistory = new ClientBalanceHistory();
                $clientBalanceHistory->client_id = $clientId;
                $clientBalanceHistory->amount = $salesDetail->special_price * $quantity;
                $clientBalanceHistory->old_amount = $clientBalance->amount - ($salesDetail->special_price * $quantity);
                $clientBalanceHistory->type = ClientBalanceHistory::TYPE_DECREASE;
                $clientBalanceHistory->created_at = date('Y-m-d H:i:s');
                if (!$clientBalanceHistory->save()) {
                    continue;
                }

                $success = true;
            }

            if (!$success) {
                $transaction->rollBack();
                return false;
            }

            $transaction->commit();
            return true;
        } catch (\Throwable $e) {
            $transaction->rollBack();
            return false;
        }
    }

    /**
     * Обработка возврата товара (для обратной совместимости)
     * @param int $clientId ID клиента
     * @param int $saleId ID счета
     * @param int $productId ID товара
     * @param int $quantity Количество для возврата
     * @return bool Результат операции
     */
    public function processReturn(int $clientId, int $saleId, int $productId, int $quantity): bool
    {
        return $this->processReturnMultiple($clientId, $saleId, [
            ['product_id' => $productId, 'quantity' => $quantity]
        ]);
    }
}
