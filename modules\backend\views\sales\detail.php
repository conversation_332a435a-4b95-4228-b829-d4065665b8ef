<?php 

use app\common\models\Sales;

?>
<div class="sales-detail">

<h6 style="margin-bottom: -0.5rem"><?= Yii::t('app', 'detail_info') ?></h6>
    <table class="table table-bordered">
        <tbody>
            <tr>
                <th>ID</th>
                <td><?= $model->id ?></td>
            </tr>
            <tr>
                <th><?= Yii::t('app', 'client_name') ?></th>
                <td><?= empty($model->client->full_name) ? '' : $model->client->full_name ?></td>
            </tr>
            <tr>
            <th><?= Yii::t('app', 'Seller') ?></th>
                <td><?= empty($model->sellUser->full_name) ? '' : $model->sellUser->full_name ?></td>
            </tr>
            <tr>
            <th><?= Yii::t('app', 'accepted_by') ?></th>
                <td><?= empty($model->confirmUser->full_name) ? '' : $model->confirmUser->full_name ?></td>
            </tr>
            <tr>
            <th><?= Yii::t('app', 'status') ?></th>
                <td><?= empty(Sales::getStatusList($model->status)) ? '' : Sales::getStatusList($model->status) ?></td>
            </tr>
            <tr>
            <th><?= Yii::t('app', 'amount') ?></th>
                <td><?= empty($model->total_sum) ? '' : $model->total_sum ?></td>
            </tr>
            <tr>
            <th><?= Yii::t('app', 'sales_created_at') ?></th>
                <td><?= empty($model->created_at) ? '' : date("d.m.Y h:i",strtotime($model->created_at)) ?></td>
            </tr>
            <tr>
            <th><?= Yii::t('app', 'sales_start_date') ?></th>
            <td><?= empty($model->started_at) ? '' : date("d.m.Y h:i",strtotime($model->started_at)) ?></td>
            </tr>
            <tr>
            <th><?= Yii::t('app', 'sales_end_date') ?></th>
            <td><?= empty($model->completed_at) ? '' : date("d.m.Y h:i",strtotime($model->completed_at)) ?></td>
            </tr>
            <tr>
            <th><?= Yii::t('app', 'driver') ?></th>
                <td><?= empty($model->driver) ? '' : $model->driver ?></td>
            </tr>
            <tr>
            <th><?= Yii::t('app', 'car_number') ?></th>
                <td><?= empty($model->car_number) ? '' : $model->car_number ?></td>
            </tr>
           
           
        </tbody>
    </table>


    <h6 style="margin-bottom: -0.5rem"><?= Yii::t('app', 'sales_products') ?></h6>

    <table class="table table-bordered">
        <thead>
            <tr>
                <th><?= Yii::t('app', 'product') ?></th>
                <th><?= Yii::t('app', 'quantity') ?></th>
                <th><?= Yii::t('app', 'unit_price') ?></th>
                <th><?= Yii::t('app', 'total_price') ?></th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($salesDetails as $detail): ?>
                <tr>
                    <td><?= $detail->product->name ?></td>
                    <td><?= $detail->quantity ?></td>
                    <td><?= $detail->factory_price ?></td>
                    <td><?= $detail->total_price ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

</div>
