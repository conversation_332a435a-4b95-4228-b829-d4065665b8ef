<?php

namespace app\common\models;

use app\modules\backend\models\Users;
use app\modules\backend\models\WorkerFinances;
use Yii;

/**
 * This is the model class for table "cashbox_detail".
 *
 * @property int $id
 * @property int $cashbox_id
 * @property int|null $add_user_id
 * @property int|null $worker_finance_id
 * @property float|null $amount
 * @property string|null $created_at
 * @property string|null $deleted_at
 * @property int|null $type
 *
 * @property Users $addUser
 * @property Cashbox $cashbox
 * @property WorkerFinances $workerFinance
 */
class CashboxDetail extends \yii\db\ActiveRecord
{

    const TYPE_IN = 1;
    const TYPE_OUT = 2;
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'cashbox_detail';
    }

    public static function getPaymentTypeDescription($type)
    {
        switch ($type) {
            case self::TYPE_IN:
                return Yii::t("app", "type_in");
            case self::TYPE_OUT:
                return Yii::t("app", "type_out");
            default:
                return Yii::t("app", "unknown_type"); 
        }
    }
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['cashbox_id'], 'required'],
            [['cashbox_id', 'add_user_id', 'worker_finance_id', 'type'], 'default', 'value' => null],
            [['cashbox_id', 'add_user_id', 'worker_finance_id', 'type'], 'integer'],
            [['amount'], 'number'],
            [['created_at', 'deleted_at'], 'safe'],
            [['cashbox_id'], 'exist', 'skipOnError' => true, 'targetClass' => Cashbox::class, 'targetAttribute' => ['cashbox_id' => 'id']],
            [['add_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['add_user_id' => 'id']],
            [['worker_finance_id'], 'exist', 'skipOnError' => true, 'targetClass' => WorkerFinances::class, 'targetAttribute' => ['worker_finance_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'cashbox_id' => 'Cashbox ID',
            'add_user_id' => 'Add User ID',
            'worker_finance_id' => 'Worker Finance ID',
            'amount' => 'Amount',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[AddUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAddUser()
    {
        return $this->hasOne(Users::class, ['id' => 'add_user_id']);
    }

    /**
     * Gets query for [[Cashbox]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCashbox()
    {
        return $this->hasOne(Cashbox::class, ['id' => 'cashbox_id']);
    }

    /**
     * Gets query for [[WorkerFinance]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWorkerFinance()
    {
        return $this->hasOne(WorkerFinances::class, ['id' => 'worker_finance_id']);
    }
}
