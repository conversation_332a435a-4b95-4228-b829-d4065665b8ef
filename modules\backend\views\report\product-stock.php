<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $reportData array */
/* @var $productId int */
/* @var $products array */

$this->title = Yii::t('app', 'product_stock_report');

?>

<style>
    .stock-good {
        color: #28a745;
        font-weight: bold;
    }
    .stock-low {
        color: #ffc107;
        font-weight: bold;
    }
    .stock-critical {
        color: #dc3545;
        font-weight: bold;
    }
    .text-right {
        text-align: right;
    }
</style>

<div class="row align-items-center mb-3">
    <div class="col-md-6">
        <h3 class="mb-0"><?= Yii::t('app', 'product_stock') ?></h3>
    </div>
    <div class="col-md-6 d-flex justify-content-end">
        <?php $form = ActiveForm::begin([
            'method' => 'get',
            'action' => ['/backend/report/product-stock'],
            'options' => ['class' => 'd-inline-flex align-items-center']
        ]); ?>
            <?= Html::dropDownList('product_id', 
                $productId, 
                $products, 
                [
                    'class' => 'form-control mr-2',
                    'style' => 'width: 200px;',
                    'prompt' => Yii::t('app', 'all_products')
                ]
            ) ?>
            <?= Html::submitButton(Yii::t('app', 'search'), ['class' => 'btn btn-primary']) ?>
        <?php ActiveForm::end(); ?>
    </div>
</div>    <?php if (!empty($reportData['items'])): ?>
        <div class="card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th><?= Yii::t('app', 'product') ?></th>
                                <th class="text-center"><?= Yii::t('app', 'blocks') ?></th>
                                <th class="text-center"><?= Yii::t('app', 'quantity') ?></th>
                                <th class="text-center"><?= Yii::t('app', 'defect_quantity') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reportData['items'] as $item): ?>
                                <?php
                                $stockStatus = '';
                                $stockClass = 'stock-good';
                                
                                if ($item['quantity'] == 0) {
                                    $stockStatus = Yii::t('app', 'out_of_stock');
                                    $stockClass = 'stock-critical';
                                } elseif ($item['quantity'] < 100) {
                                    $stockStatus = Yii::t('app', 'low_stock');
                                    $stockClass = 'stock-low';
                                } else {
                                    $stockStatus = Yii::t('app', 'in_stock');
                                    $stockClass = 'stock-good';
                                }
                                ?>
                                <tr>
                                    <td><?= Html::encode($item['product_name']) ?></td>
                                    <td class="text-right"><?= number_format($item['blocks'], 0, '.', ' ') ?></td>
                                    <td class="text-right"><?= number_format($item['quantity'], 0, '.', ' ') ?></td>
                                    <td class="text-right"><?= number_format($item['defect_quantity'], 0, '.', ' ') ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot class="bg-light">
                            <tr class="font-weight-bold">
                                <th><?= Yii::t('app', 'total') ?>:</th>
                                <th class="text-right"><?= number_format($reportData['summary']['totalBlocks'], 0, '.', ' ') ?></th>
                                <th class="text-right"><?= number_format($reportData['summary']['totalQuantity'], 0, '.', ' ') ?></th>
                                <th class="text-right"><?= number_format($reportData['summary']['totalDefect'], 0, '.', ' ') ?></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="alert alert-info text-center">
            <i class="fas fa-info-circle mr-2"></i>
            <?= Yii::t('app', 'no_data_found') ?>
        </div>    <?php endif; ?>