<?php

namespace app\modules\backend\models;

use Yii;

/**
 * This is the model class for table "expenses".
 *
 * @property int $id
 * @property int|null $expense_type_id
 * @property int $add_user_id
 * @property bool|null $status
 * @property string|null $description
 * @property float|null $summa
 * @property int|null $worker_finance_id
 * @property int|null $payment_type
 * @property int|null $cashbox_id
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Users $addUser
 * @property ExpensesType $expenseType
 * @property WorkerFinances $workerFinance
 * @property \app\common\models\Cashbox $cashbox
 */
class Expenses extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'expenses';
    }

    const SCENARIO_CREATE = 'create';


    const CASH = 1;
    const CARD = 2;
    const TRANSFER = 3;
    const CARD_PAYMENT = 4;


    const TYPE_ACCEPTED = true;
    const TYPE_NOT_ACCEPTED = false;

    public static function getTypePayment($type)
    {
        if ($type === null) {
            return null;
        }

        switch ((int)$type) {
            case self::CASH:
                return Yii::t('app', 'cash');
            case self::CARD:
                return Yii::t('app', 'card');
            case self::TRANSFER:
                return Yii::t('app', 'transfer');
            case self::CARD_PAYMENT:
                return Yii::t('app', 'payment_card');
            default:
                return null;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['expense_type_id', 'summa', 'payment_type', 'cashbox_id'], 'required', 'on'=> self::SCENARIO_CREATE],
            [['expense_type_id', 'add_user_id', 'worker_finance_id', 'cashbox_id'], 'default', 'value' => null],
            [['expense_type_id', 'add_user_id', 'worker_finance_id', 'cashbox_id'], 'integer'],
            [['add_user_id'], 'required'],
            [['status'], 'boolean'],
            [['description'], 'string'],
            [['summa'], 'number'],
            [['payment_type'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            [['expense_type_id'], 'exist', 'skipOnError' => true, 'targetClass' => ExpensesType::class, 'targetAttribute' => ['expense_type_id' => 'id']],
            [['cashbox_id'], 'exist', 'skipOnError' => true, 'targetClass' => \app\common\models\Cashbox::class, 'targetAttribute' => ['cashbox_id' => 'id']],
            [['payment_type'], 'validatePaymentTypeCompatibility', 'on' => self::SCENARIO_CREATE],
            [['expense_type_id'], 'validateNotWorkerExpense', 'on' => self::SCENARIO_CREATE],
            [['add_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['add_user_id' => 'id']],
            [['worker_finance_id'], 'exist', 'skipOnError' => true, 'targetClass' => WorkerFinances::class, 'targetAttribute' => ['worker_finance_id' => 'id']],
            [['cashbox_id'], 'exist', 'skipOnError' => true, 'targetClass' => \app\common\models\Cashbox::class, 'targetAttribute' => ['cashbox_id' => 'id']],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_CREATE] = [ 'expense_type_id', 'description', 'summa', 'payment_type', 'cashbox_id'];
        return $scenarios;
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'expense_type_id' => Yii::t('app', 'expense_type'),
            'add_user_id' => Yii::t('app', 'add_user'),
            'status' => 'Status',
            'description' => 'Description',
            'summa' => Yii::t('app', 'amount'),
            'payment_type' => Yii::t('app', 'payment_type'),
            'cashbox_id' => Yii::t('app', 'cashbox'),
            'worker_finance_id' => 'Worker Finance ID',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[AddUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAddUser()
    {
        return $this->hasOne(Users::class, ['id' => 'add_user_id']);
    }

    /**
     * Gets query for [[ExpenseType]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getExpenseType()
    {
        return $this->hasOne(ExpensesType::class, ['id' => 'expense_type_id']);
    }

    /**
     * Gets query for [[WorkerFinance]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWorkerFinance()
    {
        return $this->hasOne(WorkerFinances::class, ['id' => 'worker_finance_id']);
    }

    /**
     * Gets query for [[Cashbox]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCashbox()
    {
        return $this->hasOne(\app\common\models\Cashbox::class, ['id' => 'cashbox_id']);
    }


    public function beforeValidate()
    {
        $attributes = ['summa'];

        foreach ($attributes as $attribute) {
            if (is_string($this->$attribute) && !empty($this->$attribute)) {
                $originalValue = $this->$attribute;

                $cleanValue = preg_replace('/[\s\x{A0}]+/u', '', $originalValue);
                $cleanValue = str_replace(',', '.', $cleanValue);

                if (is_numeric($cleanValue)) {
                    $this->$attribute = floatval($cleanValue);
                }
            }
        }

        return parent::beforeValidate();
    }

    /**
     * Проверяет, может ли расход быть отредактирован или удален
     *
     * @param int $id ID расхода
     * @return bool true если расход можно редактировать/удалять, false в противном случае
     */
    public static function canBeEdited($id)
    {
        // Отключаем кэширование для отладки
        // static $cache = [];
        //
        // if (isset($cache[$id])) {
        //     return $cache[$id];
        // }

        // Проверяем, что расход не удален
        $model = self::findOne($id);
        if (!$model || !empty($model->deleted_at)) {
            // $cache[$id] = false;
            return false;
        }

        // ВАЖНО: Расходы, связанные с платежами работникам, нельзя редактировать или удалять
        // Они управляются только через модуль worker-payment
        if (!empty($model->worker_finance_id)) {
            return false;
        }

        // Особый случай: для расходов с status = TYPE_NOT_ACCEPTED запись tracking не создается,
        // но такие расходы должны быть доступны для редактирования
        if ($model->status === self::TYPE_NOT_ACCEPTED) {
            return true;
        }

        // Проверяем, что tracking не подтвержден
        // Учитываем все возможные типы прогресса для расходов
        $tracking = \app\common\models\Tracking::find()
            ->where(['process_id' => $id])
            ->andWhere(['in', 'progress_type', [
                \app\common\models\Tracking::TYPE_EXPENSES,
                \app\common\models\Tracking::PAY_FOR_SUPPLIER,
                \app\common\models\Tracking::PAY_FOR_CLIENT,
                \app\common\models\Tracking::PAY_FOR_WORKER,
                \app\common\models\Tracking::TYPE_SALES,
                \app\common\models\Tracking::MATERIAL_INCOME,
                \app\common\models\Tracking::TYPE_CLIENT_PAY,
            ]])
            ->andWhere(['is', 'accepted_at', null])
            ->andWhere(['is', 'deleted_at', null])
            ->one(); // Используем one() вместо exists() для получения объекта

        $result = $tracking !== null;


        return $result;
    }

    /**
     * Валидация совместимости кассы и типа платежа
     */
    public function validatePaymentTypeCompatibility($attribute, $params)
    {
        if (!$this->cashbox_id || !$this->payment_type) {
            return;
        }

        // Проверяем, поддерживает ли выбранная касса данный тип платежа
        $cashboxPaymentType = \app\common\models\CashboxPaymentType::find()
            ->joinWith('paymentType pt')
            ->where([
                'cashbox_payment_type.cashbox_id' => $this->cashbox_id,
                'pt.type' => $this->payment_type,
                'cashbox_payment_type.deleted_at' => null,
                'pt.deleted_at' => null
            ])
            ->one();

        if (!$cashboxPaymentType) {
            $this->addError($attribute, 'Выбранная касса не поддерживает данный тип платежа.');
        }
    }

    /**
     * Валидация против создания расходов для работников
     */
    public function validateNotWorkerExpense($attribute, $params)
    {
        if (!$this->expense_type_id) {
            return;
        }

        $expenseType = ExpensesType::findOne($this->expense_type_id);
        if (!$expenseType) {
            return;
        }

        // Список типов расходов, которые предназначены для работников
        $workerExpenseTypes = [
            'Taminotchi uchun',
            'Avans',
            'Oylik',
            'Bonus',
            'Debt',
            'One time payment'
        ];

        if (in_array($expenseType->name, $workerExpenseTypes)) {
            $this->addError($attribute, 'Расходы для работников не могут быть созданы через этот интерфейс.');
        }
    }
}
