<?php

use yii\db\Migration;

class m251024_071447_add_cashbox_id_to_expensese_table extends Migration
{
    public function safeUp()
    {
        $table = 'expenses';
        $column = 'cashbox_id';
        $fkName = 'fk-expenses-cashbox_id';

        $schema = $this->db->getTableSchema($table, true);

        // Добавить колонку, если не существует
        if (!$schema->getColumn($column)) {
            $this->addColumn($table, $column, $this->integer()->null());
        }

        // Проверка наличия внешнего ключа
        $foreignKeys = $schema->foreignKeys;
        $exists = false;
        foreach ($foreignKeys as $fk) {
            if (isset($fk[$column])) {
                $exists = true;
                break;
            }
        }

        if (!$exists) {
            $this->addForeignKey(
                $fkName,
                $table,
                $column,
                'cashbox',
                'id',
                'SET NULL',
                'CASCADE'
            );
        }
    }

    public function safeDown()
    {
        $table = 'expenses';
        $column = 'cashbox_id';
        $fkName = 'fk-expenses-cashbox_id';

        // Проверка и удаление внешнего ключа, если существует
        $schema = $this->db->getTableSchema($table, true);
        $foreignKeys = $schema->foreignKeys;
        foreach ($foreignKeys as $fkNameCheck => $fk) {
            if ($fkNameCheck === $fkName || isset($fk[$column])) {
                $this->dropForeignKey($fkNameCheck, $table);
            }
        }

        // Удалить колонку, если существует
        if ($schema->getColumn($column)) {
            $this->dropColumn($table, $column);
        }
    }



    
}
