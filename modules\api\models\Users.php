<?php

namespace app\modules\api\models;

use Yii;
use yii\db\ActiveRecord;
use yii\web\IdentityInterface;

class Users extends ActiveRecord implements IdentityInterface
{

    public static function tableName()
    {
        return 'users';
    }

    public function rules()
    {
        return [
            [['username', 'password'], 'required'],
            [['username', 'full_name'], 'string', 'max' => 255],
        ];
    }
    public function attributeLabels()
    {
        return [
            'username' => Yii::t('app', 'username'),
            'password' => Yii::t('app', 'password'),
        ];
    }

    public static function findIdentity($id)
    {
        return static::findOne(['id' => $id]);
    }

    public static function findIdentityByAccessToken($token, $type = null)
    {
        return static::findOne(['access_token' => $token]);
    }

    public static function findByUsername($username)
    {
        return static::findOne(['username' => $username]);
    }

    public function getId()
    {
        return $this->getPrimaryKey();
    }

    public function getAuthKey()
    {
        return $this->auth_key;
    }

    public function validateAuthKey($authKey)
    {
        return $this->getAuthKey() === $authKey;
    }

    public function validatePassword($password)
    {
        return Yii::$app->security->validatePassword($password, $this->password);
    }

    public function setPassword($password)
    {
        $this->password = Yii::$app->security->generatePasswordHash($password);
    }

    public function generateAccessToken()
    {
        $this->access_token = Yii::$app->security->generateRandomString(40);
    }

  
    public static function getRolesList()
    {
        $auth = Yii::$app->authManager;
        $roles = $auth->getRoles();

        $result = [];
        foreach ($roles as $role) {
            $result[$role->name] = $role->description ?: ucfirst($role->name);
        }

        return $result;
    }

   
    public function getRoleName()
    {
        $roles = self::getRolesList();

        $authManager = Yii::$app->authManager;
        $userRoles = $authManager->getRolesByUser($this->id);

        $roleName = !empty($userRoles) ? reset($userRoles)->name : null;

        return $roleName && isset($roles[$roleName]) ? $roles[$roleName] : Yii::t('app', 'unknown_role');
    }
}
