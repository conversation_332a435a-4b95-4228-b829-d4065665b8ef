<?php

namespace app\modules\backend\services\report;

use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * Сервис для формирования отчетов по остаткам продукции
 */
class ProductStockReportService
{
    /**
     * Получить отчет по остаткам продукции
     * 
     * @param int|null $productId ID продукта для фильтрации
     * @return array Массив с данными отчета
     */
    public function getProductStockReport($productId = null)
    {
        $stockData = $this->getProductStockData($productId);
        $defectData = $this->getProductDefectData($productId);
        
        // Объединяем данные по продуктам
        $combinedData = [];
        foreach ($stockData as $stock) {
            $combinedData[$stock['product_id']] = [
                'product_id' => $stock['product_id'],
                'product_name' => $stock['product_name'],
                'blocks' => $stock['blocks'],
                'quantity' => $stock['quantity'],
                'defect_quantity' => 0
            ];
        }
        
        // Добавляем данные по браку
        foreach ($defectData as $defect) {
            if (isset($combinedData[$defect['product_id']])) {
                $combinedData[$defect['product_id']]['defect_quantity'] = $defect['defect_quantity'];
            } else {
                $combinedData[$defect['product_id']] = [
                    'product_id' => $defect['product_id'],
                    'product_name' => $defect['product_name'],
                    'blocks' => 0,
                    'quantity' => 0,
                    'defect_quantity' => $defect['defect_quantity']
                ];
            }
        }
        
        return [
            'items' => array_values($combinedData),
            'summary' => [
                'totalBlocks' => array_sum(ArrayHelper::getColumn($combinedData, 'blocks')),
                'totalQuantity' => array_sum(ArrayHelper::getColumn($combinedData, 'quantity')),
                'totalDefect' => array_sum(ArrayHelper::getColumn($combinedData, 'defect_quantity'))
            ]
        ];
    }
    
    /**
     * Получить данные по остаткам продукции
     * 
     * @param int|null $productId
     * @return array
     */
    protected function getProductStockData($productId = null)
    {
        $query = new Query();
        $query->select([
                'ps.product_id',
                'p.name as product_name',
                'CASE WHEN p.size > 0 THEN FLOOR(SUM(ps.quantity) / p.size) ELSE 0 END as blocks',
                'SUM(ps.quantity) as quantity'
            ])
            ->from(['ps' => 'product_storage'])
            ->leftJoin(['p' => 'product'], 'ps.product_id = p.id')
            ->where(['IS', 'ps.deleted_at', null])
            ->andWhere(['IS NOT', 'ps.accepted_at', null])
            ->andWhere(['IS NOT', 'ps.accepted_user_id', null])
            ->andWhere(['IS', 'p.deleted_at', null])
            ->andWhere(['>', 'p.size', 0]);
            
        if ($productId) {
            $query->andWhere(['ps.product_id' => $productId]);
        }
        
        $query->groupBy(['ps.product_id', 'p.name', 'p.size'])
              ->having(['>', 'SUM(ps.quantity)', 0])
              ->orderBy(['p.name' => SORT_ASC]);
        
        return $query->all();
    }
    
    /**
     * Получить данные по браку продукции
     * 
     * @param int|null $productId
     * @return array
     */
    protected function getProductDefectData($productId = null)
    {
        $query = new Query();
        $query->select([
                'p.id as product_id',
                'p.name as product_name',
                'SUM(pd.quantity) as defect_quantity'
            ])
            ->from(['pd' => 'product_defect'])
            ->leftJoin(['p' => 'product'], 'pd.product_id = p.id')
            ->where(['IS', 'pd.deleted_at', null])
            ->andWhere(['>', 'pd.quantity', 0]);
            
        if ($productId) {
            $query->andWhere(['pd.product_id' => $productId]);
        }
        
        $query->groupBy(['p.id', 'p.name'])
              ->orderBy(['p.name' => SORT_ASC]);
        
        return $query->all();
    }
    
    /**
     * Получить историю движения продукции
     * 
     * @param string|null $startDate
     * @param string|null $endDate
     * @param int|null $productId
     * @return array
     */
    public function getProductMovementHistory($startDate = null, $endDate = null, $productId = null)
    {
        $query = new Query();
        $query->select([
                'psh.id',
                'p.name as product_name',
                'psh.quantity_before',
                'psh.quantity_after',
                'psh.operation_type',
                'psh.created_at',
                'u.username as created_by'
            ])
            ->from(['psh' => 'product_storage_history'])
            ->leftJoin(['p' => 'product'], 'psh.product_id = p.id')
            ->leftJoin(['u' => 'users'], 'psh.created_by = u.id')
            ->where(['IS', 'psh.deleted_at', null]);
            
        if ($startDate) {
            $query->andWhere(['>=', 'psh.created_at', $startDate . ' 00:00:00']);
        }
        
        if ($endDate) {
            $query->andWhere(['<=', 'psh.created_at', $endDate . ' 23:59:59']);
        }
        
        if ($productId) {
            $query->andWhere(['psh.product_id' => $productId]);
        }
        
        $query->orderBy(['psh.created_at' => SORT_DESC]);
        
        return $query->all();
    }
}