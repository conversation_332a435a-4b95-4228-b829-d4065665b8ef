<?php

namespace app\modules\backend\models;

use Yii;

/**
 * This is the model class for table "worker".
 *
 * @property int $id
 * @property string $full_name
 * @property string|null $address
 * @property string|null $phone_number
 * @property string|null $phone_number_2
 * @property int $position_id
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Position $position
 * @property WorkerFinances[] $workerFinances
 */
class Worker extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'worker';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['full_name', 'position_id'], 'required'],
            [['position_id'], 'default', 'value' => null],
            [['position_id'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            [['full_name', 'address'], 'string', 'max' => 255],
            [['phone_number', 'phone_number_2'], 'string', 'max' => 20],
            [['position_id'], 'exist', 'skipOnError' => true, 'targetClass' => Position::class, 'targetAttribute' => ['position_id' => 'id']],
            [['phone_number'], 'validatePhoneNumber'],
            [['phone_number_2'], 'validatePhoneNumber'],

        ];
    }



   


  
    public function validatePhoneNumber($attribute, $params, $validator)
    {
        $value = $this->$attribute;
        
        // Если поле пустое, не валидируем
        if (empty($value)) {
            return;
        }
        
        $numericValue = preg_replace('/\D/', '', $value);

        if (strlen($numericValue) !== 9) {
            $this->addError($attribute, Yii::t('app', 'Номер телефона должен содержать 12 цифр.'));
            return;
        }

        $companyCodes = ['90', '91', '93', '94', '88', '55', '87', '95', '97', '98', '99', '20', '33', '77', '50'];

        $operatorCode = substr($numericValue, 0, 2);

        if (!in_array($operatorCode, $companyCodes)) {
            $this->addError($attribute, Yii::t('app', "Рақам коди нотўғри"));
        }
    }


    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'full_name' => Yii::t('app', 'full_name'),
            'address' => Yii::t('app', 'address'),
            'phone_number' => Yii::t('app', 'phone_number'),
            'phone_number_2' => Yii::t('app', 'phone_number_2'),
            'position_id' => Yii::t('app', 'position'),
            'created_at' => Yii::t('app', 'created_at'),
            'deleted_at' => Yii::t('app', 'deleted_at'),
        ];
    }

    /**
     * Gets query for [[Position]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPosition()
    {
        return $this->hasOne(Position::class, ['id' => 'position_id']);
    }

    /**
     * Gets query for [[WorkerFinances]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWorkerFinances()
    {
        return $this->hasMany(WorkerFinances::class, ['worker_id' => 'id']);
    }
}
