<?php

namespace app\modules\backend\models;

use Yii;
use yii\base\Model;

class IncomeMaterialUpdateForm extends Model
{
    public $id;
    public $invoice_id;
    public $material_id;
    public $quantity;
    public $price;
    public $description;

    public function rules()
    {
        return [
            [['material_id', 'quantity', 'price'], 'required'],
            [['id', 'invoice_id', 'material_id'], 'integer'],
            [['quantity', 'price'], 'number', 'min' => 0],
            ['description', 'string'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'material_id' => Yii::t('app', 'Material'),
            'quantity' => Yii::t('app', 'Quantity'),
            'price' => Yii::t('app', 'Price'),
            'description' => Yii::t('app', 'Description'),
        ];
    }
}
