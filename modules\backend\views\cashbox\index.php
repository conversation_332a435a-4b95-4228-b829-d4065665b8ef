<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\grid\GridView;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\common\models\Cashbox;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "cashbox_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
?>

<style>
    .modal-dialog {
        max-width: 695px !important;
    }
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin')): ?>
               
                <a href="#" class="btn btn-primary cashbox-transfer" data-toggle="modal" data-target="#ideal-mini-modal">
                    <i class="fas fa-exchange-alt"></i> <?= Yii::t("app", "add_transfer") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'cashbox-grid-pjax']); ?>
    <?php if($dataProvider->models): ?>
        <div>
            <table id="cashbox-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th>ID</th>
                    <th><?= Yii::t("app", "cashbox_name") ?></th>
                    <th><?= Yii::t("app", "payments_type") ?></th>
                    <th><?= Yii::t("app", "balance") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($dataProvider->models as $model): ?>
                    <tr>
                        <td><?= Html::encode($model->id) ?></td>
                        <td>
                            <strong><?= Html::encode($model->title) ?></strong>
                            <br>
                            <small class="text-muted">
                                Создана: <?= Yii::$app->formatter->asDatetime($model->created_at) ?>
                            </small>
                        </td>
                        
                        <td>
                            <small class="text-info">
                                <?= $model->getPaymentTypesString() ?>
                            </small>
                        </td>
                        <td>
                            <span class="badge <?= $model->balance >= 0 ? 'badge-success' : 'badge-danger' ?>">
                                <?= number_format($model->balance, 2) ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="<?= Yii::$app->urlManager->createUrl(['/backend/cashbox/detail', 'id' => $model->id]) ?>" 
                                   class="btn btn-info btn-sm" title="Просмотр операций">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="alert alert-info text-center">
            <i class="fas fa-info-circle"></i>
            <?= Yii::t('app', 'no_data_available') ?>
        </div>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>



<div id="one" data-text="<?= Yii::t("app", "balance_transfer") ?>"></div>


<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
   

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($('#cashbox-grid-view').length && $('#cashbox-grid-view tbody tr').length > 0) {
            if ($.fn.DataTable.isDataTable('#cashbox-grid-view')) {
                $('#cashbox-grid-view').DataTable().destroy();
            }

            $('#cashbox-grid-view').DataTable({
                "language": {
                    "search": searchLabel,
                    "lengthMenu": lengthMenuLabel,
                    "zeroRecords": zeroRecordsLabel,
                    "info": infoLabel,
                    "infoEmpty": infoEmptyLabel,
                    "infoFiltered": infoFilteredLabel
                },
                "pageLength": 50,
                "order": [[0, 'desc']],
                "columnDefs": [
                    {
                        "targets": [4],
                        "orderable": false
                    }
                ]
            });
        }
    }

    function initializeAll() {
        initializeDataTable();
        initializeSelect2();
        initializeCashboxTransfer();
    }

    // Initialize everything on first load
    initializeAll();

    // Re-initialize after PJAX reloads
    $(document).on('pjax:success', function() {
        initializeAll();
    });

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }

    

    function initializeCashboxTransfer() {
        $(document).off('click.cashbox-transfer').on('click.cashbox-transfer', '.cashbox-transfer', function() {
            $.ajax({
                url: '/backend/cashbox/transfer',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("cashbox-transfer-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.cashbox-transfer-button').on('click.cashbox-transfer-button', '.cashbox-transfer-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#transfer-form').serialize();
                $.ajax({
                    url: '/backend/cashbox/transfer',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({container: '#cashbox-grid-pjax'});
                            initializeDataTable();
                            toastr.success(response.message);
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            $('.help-block').html('');
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').html(errors.join('<br>'));
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });

        $(document).off('keypress.cashbox-transfer-button').on('keypress.cashbox-transfer-button', '#transfer-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('.cashbox-transfer-button').trigger('click');
            }
        });
    }

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
