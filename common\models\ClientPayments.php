<?php

namespace app\common\models;

use Yii;
use app\modules\backend\models\Users;

/**
 * This is the model class for table "client_payments".
 *
 * @property int $id
 * @property int $client_id
 * @property int|null $cashbox_id
 * @property int $add_user_id
 * @property int $type
 * @property float $summa
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Users $addUser
 * @property Client $client
 * @property Cashbox $cashbox
 */
class ClientPayments extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'client_payments';
    }

    const TYPE_CASH = 1;
    const TYPE_CARD = 2;
    const TYPE_TRANSFER = 3;

    public static function getTypeLabel($type)
    {
        switch ($type) {
            case self::TYPE_CASH:
                return Yii::t('app', 'cash');
            case self::TYPE_CARD:
                return Yii::t('app', 'card');
            case self::TYPE_TRANSFER:
                return Yii::t('app', 'transfer');
            default:
                return '';
        }
    }

    

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['client_id', 'type', 'summa'], 'required'],
            [['client_id', 'add_user_id', 'cashbox_id'], 'default', 'value' => null],
            [['client_id', 'add_user_id', 'cashbox_id'], 'integer'],
            [['summa'], 'number', 'min' => 0.01],
            [['created_at', 'deleted_at'], 'safe'],
            [['client_id'], 'exist', 'skipOnError' => true, 'targetClass' => Client::class, 'targetAttribute' => ['client_id' => 'id']],
            [['add_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['add_user_id' => 'id']],
            [['cashbox_id'], 'exist', 'skipOnError' => true, 'targetClass' => Cashbox::class, 'targetAttribute' => ['cashbox_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'client_id' => 'Client ID',
            'cashbox_id' => Yii::t('app', 'cashbox'),
            'add_user_id' => 'Add User ID',
            'type' => Yii::t('app', 'payment_type'),
            'summa' => Yii::t('app', 'amount'),
            'created_at' => 'Created At',
        ];
    }

    /**
     * Gets query for [[AddUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAddUser()
    {
        return $this->hasOne(Users::class, ['id' => 'add_user_id']);
    }

    /**
     * Gets query for [[Client]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClient()
    {
        return $this->hasOne(Client::class, ['id' => 'client_id']);
    }

    /**
     * Gets query for [[Cashbox]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCashbox()
    {
        return $this->hasOne(Cashbox::class, ['id' => 'cashbox_id']);
    }

    /**
     * Перед валидацией очищаем форматированные числовые поля от пробелов
     * @return bool
     */
    public function beforeValidate()
    {
        if (is_string($this->summa) && !empty($this->summa)) {
            $originalValue = $this->summa;
            
            $cleanValue = preg_replace('/[\s\xA0]+/u', '', $originalValue);
            $cleanValue = str_replace(',', '.', $cleanValue);
            
            if (is_numeric($cleanValue)) {
                $this->summa = floatval($cleanValue);
            }
        }
        
        return parent::beforeValidate();
    }
}
