<?php

namespace app\modules\api\controllers;

use app\common\models\ApiResponse;
use app\common\models\Client;
use app\common\models\ClientBalanceHistory;
use app\common\models\ClientDriver;
use app\common\models\ClientSpecialPrices;
use app\common\models\FreeProductDetail;
use app\common\models\FreeProducts;
use app\common\models\ProductPrice;
use app\common\models\ProductStorage;
use app\common\models\ProductStorageHistory;
use app\common\models\Sales;
use app\common\models\ActionLogger;
use app\common\models\ClientBalance;
use app\common\models\Product;
use app\common\models\SalesBonus;
use app\common\models\Tracking;
use app\common\models\SalesReturn;
use app\common\models\SalesDetail;
use app\modules\api\models\InvoiceCreateForm;
use app\modules\api\models\InvoiceUpdateForm;
use app\modules\api\models\ReturnProductForm;
use app\modules\api\models\SendToFreeProductsForm;
use app\services\ClientDriverService;
use app\services\InvoiceService;
use Exception;
use Yii;
use yii\web\ForbiddenHttpException;

/**
 * Контроллер для управления продажами
 *
 * @OA\Tag(
 *     name="Sales",
 *     description="Управление продажами и инвойсами"
 * )
 */
class SalesController extends BaseController
{

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        try {
            if (!Yii::$app->user->can('sales')) {
                throw new ForbiddenHttpException(Yii::t('app', "You don\'t have permission to access this resource"));
            }
        } catch (ForbiddenHttpException $e) {
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            Yii::$app->response->data = ApiResponse::response(
                $e->getMessage(),
                null,
                403
            );
            Yii::$app->response->statusCode = 403;
            return false;
        }

        return true;
    }


    /**
     * Получение списка продаж
     *
     * @OA\Get(
     *     path="/api/sales",
     *     summary="Получение списка продаж",
     *     description="Возвращает список продаж с возможностью фильтрации по дате",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="date",
     *         in="query",
     *         description="Дата для фильтрации (формат Y-m-d)",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2023-05-15")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Список продаж",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Sales list"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="sales",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="client_name", type="string", example="ООО Компания"),
     *                         @OA\Property(property="car_number", type="string", example="A123BC"),
     *                         @OA\Property(property="created_at", type="string", format="date-time", example="2023-05-15 10:00:00"),
     *                         @OA\Property(property="status", type="integer", example=2)
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Не авторизован",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Your request was made with invalid credentials"),
     *             @OA\Property(property="data", type="object"),
     *             @OA\Property(property="code", type="integer", example=401)
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Доступ запрещен",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="You don't have permission to access this resource"),
     *             @OA\Property(property="data", type="object"),
     *             @OA\Property(property="code", type="integer", example=403)
     *         )
     *     )
     * )
     */
    public function actionIndex()
    {
        $params = Yii::$app->request->get();

        $query = (new \yii\db\Query())
            ->select([
                's.id',
                'c.full_name as client_name',
                's.car_number',
                's.created_at',
                's.status',
            ])
            ->from(['s' => 'sales'])
            ->leftJoin(['c' => 'client'], 'c.id = s.client_id')
            ->where(['s.deleted_at' => null])
            ->orderBy(['s.created_at' => SORT_DESC]);

        if (!empty($params['date'])) {
            $date = date('Y-m-d', strtotime($params['date']));
            $query->andWhere(['>=', 's.created_at', $date . ' 00:00:00'])
                  ->andWhere(['<=', 's.created_at', $date . ' 23:59:59']);
        }

        $sales = $query->all();

        return ApiResponse::response("Sales list", [
            'sales' => $sales
        ]);
    }

    public function actionSalesStart()
    {
        if(Yii::$app->request->isGet) {
            $sale_id = Yii::$app->request->get('sales_id');

            if (!$sale_id) {
                return ApiResponse::response("Sale ID is required", null, 400);
            }

            $sales = Sales::find()
                ->where(['id' => $sale_id])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['status' => Sales::STATUS_NEW])
                ->one();

            if (!$sales) {
                return ApiResponse::response("Sales not found or invalid status", null, 404);
            }

            $bonusProducts = (new \yii\db\Query())
                ->select([
                    'p.name as product_name',
                    'p.size',
                    'sb.quantity'
                ])
                ->from(['sb' => 'sales_bonus'])
                ->leftJoin(['s' => 'sales'], 's.id = sb.sale_id')
                ->leftJoin(['p' => 'product'], 'p.id = sb.product_id')
                ->where([
                    'sb.sale_id' => $sale_id,
                    'sb.deleted_at' => null
                ])
                ->all();

            // Рассчитываем количество блоков для бонусных продуктов
            foreach ($bonusProducts as &$bonus) {
                $bonus['box_count'] = $bonus['quantity'] / $bonus['size'];
            }

            $totals = (new \yii\db\Query())
                ->select([
                    'SUM(quantity) as total_quantity',
                    'SUM(total_price) as total_sum'
                ])
                ->from('sales_detail')
                ->where([
                    'sale_id' => $sale_id,
                    'deleted_at' => null
                ])
                ->one();

            $query = (new \yii\db\Query())
                ->select([
                    'p.name as product_name',
                    'p.size',
                    'sd.quantity',
                    'sd.total_price'
                ])
                ->from(['sd' => 'sales_detail'])
                ->leftJoin(['p' => 'product'], 'p.id = sd.product_id')
                ->where([
                    'sd.sale_id' => $sale_id,
                    'sd.deleted_at' => null
                ]);

            $details = $query->all();

            if (empty($details)) {
                return ApiResponse::response("Sales details not found", null, 404);
            }

            // Рассчитываем количество блоков для каждого продукта
            foreach ($details as &$detail) {
                $detail['total_price'] = number_format((float)$detail['total_price'], 0, ',', '.');
                $detail['box_count'] = $detail['quantity'] / $detail['size'];
            }
            unset($detail);

            $formattedTotalSum = number_format((float)$totals['total_sum'], 0, ',', '.');

            return ApiResponse::response("Sales details", [
                'details' => $details,
                'bonus_products' => $bonusProducts,
                'total_quantity' => (int)$totals['total_quantity'],
                'total_sum' => $formattedTotalSum
            ]);
        } else if(Yii::$app->request->isPost) {
            $sales_id = Yii::$app->request->post('sales_id');

            $sales = Sales::find()
                ->where(['id' => $sales_id])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['status' => Sales::STATUS_NEW])->one();
            if (!$sales) {
                return ApiResponse::response("Sales not found", null, 404);
            }

            $sales->status = Sales::STATUS_IN_PROGRESS;
            $sales->started_at = date('Y-m-d H:i:s');

            if(!$sales->save()) {
                return ApiResponse::response("Error", null, 500);
            }

            ActionLogger::actionLog(
                'start_sales',
                'sales',
                null,
                [
                    'sales_id' => $sales_id,
                    'old_status' => Sales::STATUS_NEW,
                    'new_status' => Sales::STATUS_IN_PROGRESS,
                    'details' => array_map(function($detail) {
                        return [
                            'product_id' => $detail->product_id,
                            'quantity' => $detail->quantity,
                            'total_price' => $detail->total_price
                        ];
                    }, $sales->salesDetails)
                ]
            );

            // Отправляем уведомление через Firebase
            try {
                $clientName = $sales->client ? $sales->client->full_name : 'Клиент';
                $title = 'Инвойс в обработке';
                $body = "Инвойс #{$sales->id} для {$clientName} начал обрабатываться";
                $data = [
                    'sales_id' => $sales->id,
                    'type' => 'sales_started',
                    'client_id' => $sales->client_id,
                    'total_sum' => $sales->total_sum
                ];

                // Создаем уведомление для пользователей с ролью product_keeper
                Yii::$app->firebase->notifyRole('product_keeper', $title, $body, $data);
            } catch (\Exception $e) {
                // Логируем ошибку, но не прерываем выполнение
                Yii::error('Ошибка создания уведомления: ' . $e->getMessage(), 'firebase');
            }

            return ApiResponse::response("Sales started", [
                'sales_id' => $sales_id
            ]);
        }
    }


    public function actionSalesFinish()
    {
        if(Yii::$app->request->isGet) {
            $sales_id = Yii::$app->request->get('sales_id');

            if (!$sales_id) {
                return ApiResponse::response("Sale ID is required", null, 400);
            }

            $sales = Sales::find()
                ->where(['id' => $sales_id])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['status' => Sales::STATUS_IN_PROGRESS])
                ->one();

            if (!$sales) {
                return ApiResponse::response("Sales not found or invalid status", null, 404);
            }

            $query = (new \yii\db\Query())
                ->select([
                    'p.name as product_name',
                    'p.size',
                    'sd.quantity',
                    'sd.total_price'
                ])
                ->from(['sd' => 'sales_detail'])
                ->leftJoin(['p' => 'product'], 'p.id = sd.product_id')
                ->where([
                    'sd.sale_id' => $sales_id,
                    'sd.deleted_at' => null
                ]);

            $details = $query->all();

            if (empty($details)) {
                return ApiResponse::response("Sales details not found", null, 404);
            }

            // Рассчитываем количество блоков для каждого продукта
            foreach ($details as &$detail) {
                $detail['total_price'] = number_format((float)$detail['total_price'], 0, ',', '.');
                $detail['box_count'] = $detail['quantity'] / $detail['size'];
            }
            unset($detail);

            $totals = (new \yii\db\Query())
                ->select([
                    'SUM(quantity) as total_quantity',
                    'SUM(total_price) as total_sum'
                ])
                ->from('sales_detail')
                ->where([
                    'sale_id' => $sales_id,
                    'deleted_at' => null
                ])
                ->one();

                $bonusProducts = (new \yii\db\Query())
                ->select([
                    'p.name as product_name',
                    'p.size',
                    'sb.quantity'
                ])
                ->from(['sb' => 'sales_bonus'])
                ->leftJoin(['s' => 'sales'], 's.id = sb.sale_id')
                ->leftJoin(['p' => 'product'], 'p.id = sb.product_id')
                ->where([
                    'sb.sale_id' => $sales_id,
                    'sb.deleted_at' => null
                ])
                ->all();

            // Рассчитываем количество блоков для бонусных продуктов
            foreach ($bonusProducts as &$bonus) {
                $bonus['box_count'] = $bonus['quantity'] / $bonus['size'];
            }

            $formattedTotalSum = number_format((float)$totals['total_sum'], 0, ',', '.');

            return ApiResponse::response("Sales details", [
                'details' => $details,
                'bonus_products' => $bonusProducts,
                'total_quantity' => (int)$totals['total_quantity'],
                'total_sum' => $formattedTotalSum
            ]);
        } else if (Yii::$app->request->isPost) {
            $sales_id = Yii::$app->request->post('sales_id');

            $sales = Sales::find()
                ->where(['id' => $sales_id])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['status' => Sales::STATUS_IN_PROGRESS])
                ->one();
            if (!$sales) {
                return ApiResponse::response("Sales not found", null, 404);
            }

            // Начинаем транзакцию
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $sales->confirm_user_id = Yii::$app->user->id;
                $sales->status = Sales::STATUS_CONFIRMED;
                $sales->completed_at = date('Y-m-d H:i:s');
                if (!$sales->save()) {
                    throw new \Exception("Ошибка при сохранении продажи");
                }

                // Обрабатываем каждый товар в продаже
                foreach ($sales->salesDetails as $detail) {
                    $remainingQuantity = $detail->quantity;

                    // Получаем все записи склада для данного продукта
                    $productStorages = ProductStorage::find()
                        ->where(['product_id' => $detail->product_id, 'deleted_at' => null])
                        ->orderBy(['enter_date' => SORT_ASC]) // Берем сначала старые записи
                        ->all();

                    $totalAvailable = array_sum(array_column($productStorages, 'quantity'));
                    if ($totalAvailable < $remainingQuantity) {
                        throw new \Exception("Недостаточно товара {$detail->product->name} на складе");
                    }

                    // Уменьшаем количество последовательно из каждой записи склада
                    foreach ($productStorages as $storage) {
                        if ($remainingQuantity <= 0) {
                            break;
                        }

                        $deductFromThisStorage = min($remainingQuantity, $storage->quantity);
                        $storage->quantity -= $deductFromThisStorage;
                        $remainingQuantity -= $deductFromThisStorage;

                        if (!$storage->save()) {
                            throw new \Exception("Ошибка при обновлении количества на складе");
                        }

                        // Записываем в историю склада
                        $history = new ProductStorageHistory();
                        $history->product_storage_id = $storage->id;
                        $history->product_id = $detail->product_id;
                        $history->quantity = $deductFromThisStorage;
                        $history->type = ProductStorageHistory::TYPE_OUTCOME;
                        $history->created_at = date('Y-m-d H:i:s');
                        if (!$history->save()) {
                            throw new \Exception("Ошибка при сохранении истории склада");
                        }
                    }
                }

                // Обрабатываем бонусные товары
                $salesBonuses = SalesBonus::findAll(['sale_id' => $sales->id, 'deleted_at' => null]);
                foreach ($salesBonuses as $bonus) {
                    $remainingQuantity = $bonus->quantity;

                    // Получаем все записи склада для бонусного продукта
                    $productStorages = ProductStorage::find()
                        ->where(['product_id' => $bonus->product_id, 'deleted_at' => null])
                        ->orderBy(['enter_date' => SORT_ASC])
                        ->all();

                    $totalAvailable = array_sum(array_column($productStorages, 'quantity'));
                    if ($totalAvailable < $remainingQuantity) {
                        throw new \Exception("Недостаточно бонусного товара {$bonus->product->name} на складе");
                    }

                    // Уменьшаем количество последовательно из каждой записи склада
                    foreach ($productStorages as $storage) {
                        if ($remainingQuantity <= 0) {
                            break;
                        }

                        $deductFromThisStorage = min($remainingQuantity, $storage->quantity);
                        $storage->quantity -= $deductFromThisStorage;
                        $remainingQuantity -= $deductFromThisStorage;

                        if (!$storage->save()) {
                            throw new \Exception("Ошибка при обновлении количества бонусного товара на складе");
                        }

                        // Записываем в историю склада
                        $history = new ProductStorageHistory();
                        $history->product_storage_id = $storage->id;
                        $history->product_id = $bonus->product_id;
                        $history->quantity = $deductFromThisStorage;
                        $history->type = ProductStorageHistory::TYPE_OUTCOME;
                        $history->created_at = date('Y-m-d H:i:s');
                        if (!$history->save()) {
                            throw new \Exception("Ошибка при сохранении истории склада для бонуса");
                        }
                    }
                }

                $clientBalance = ClientBalance::findOne(['client_id' => $sales->client_id]);
                if (!$clientBalance) {
                    $clientBalance = new ClientBalance();
                    $clientBalance->client_id = $sales->client_id;
                    $clientBalance->amount = 0;
                }
                $clientBalance->amount -= $sales->total_special_prices_sum;
                if (!$clientBalance->save()) {
                    throw new \Exception("Ошибка при сохранении баланса клиента");
                }

                $clientBalanceHistory = new ClientBalanceHistory();
                $clientBalanceHistory->client_id = $sales->client_id;
                $clientBalanceHistory->amount = $sales->total_special_prices_sum;
                $clientBalanceHistory->old_amount = $clientBalance->amount + $sales->total_special_prices_sum;
                $clientBalanceHistory->type = ClientBalanceHistory::TYPE_DECREASE;
                $clientBalanceHistory->created_at = date('Y-m-d H:i:s');
                if (!$clientBalanceHistory->save()) {
                    throw new \Exception("Ошибка при сохранении истории баланса");
                }

                ActionLogger::actionLog(
                    'finish_sales',
                    'sales',
                    null,
                    [
                        'sales_id' => $sales_id,
                        'old_status' => Sales::STATUS_IN_PROGRESS,
                        'new_status' => Sales::STATUS_CONFIRMED,
                        'confirm_user_id' => Yii::$app->user->id,
                        'details' => array_map(function ($detail) {
                            return [
                                'product_id' => $detail->product_id,
                                'quantity' => $detail->quantity,
                                'total_price' => $detail->total_price
                            ];
                        }, $sales->salesDetails)
                    ]
                );

                $transaction->commit();

                return ApiResponse::response("Sales finished", [
                    'sales_id' => $sales_id
                ]);
            } catch (\Exception $e) {
                $transaction->rollBack();
                return ApiResponse::response("Error: " . $e->getMessage(), null, 500);
            }
        }
    }

    public function actionSalesDetail()
    {
        if (Yii::$app->request->isGet) {
            $sales_id = Yii::$app->request->get('sales_id');

            if (!$sales_id) {
                return ApiResponse::response("Sales ID is required", null, 400);
            }

            $sales = Sales::find()
                ->where(['id' => $sales_id])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['status' => Sales::STATUS_CONFIRMED])
                ->one();

            if (!$sales) {
                return ApiResponse::response("Sales not found or invalid status", null, 404);
            }

            $bonusProducts = (new \yii\db\Query())
                ->select([
                    'p.name as product_name',
                    'p.size',
                    'sb.quantity'
                ])
                ->from(['sb' => 'sales_bonus'])
                ->leftJoin(['s' => 'sales'], 's.id = sb.sale_id')
                ->leftJoin(['p' => 'product'], 'p.id = sb.product_id')
                ->where([
                    'sb.sale_id' => $sales_id,
                    'sb.deleted_at' => null
                ])
                ->all();

            foreach ($bonusProducts as &$bonus) {
                $bonus['box_count'] = $bonus['quantity'] / $bonus['size'];
            }
            unset($bonus);

            $totals = (new \yii\db\Query())
                ->select([
                    'SUM(quantity) as total_quantity',
                    'SUM(total_price) as total_sum'
                ])
                ->from('sales_detail')
                ->where([
                    'sale_id' => $sales_id,
                    'deleted_at' => null
                ])
                ->one();

            $query = (new \yii\db\Query())
                ->select([
                    'sd.id',
                    'sd.quantity',
                    'sd.total_price',
                    'p.name as product_name',
                    'p.size'
                ])
                ->from(['sd' => 'sales_detail'])
                ->leftJoin(['p' => 'product'], 'p.id = sd.product_id')
                ->where([
                    'sd.sale_id' => $sales_id,
                    'sd.deleted_at' => null
                ]);

            $details = $query->all();

            if (empty($details)) {
                return ApiResponse::response("Sales details not found", null, 404);
            }

            foreach ($details as &$detail) {
                $detail['total_price'] = number_format((float)$detail['total_price'], 0, ',', '.');
                $detail['box_count'] = $detail['quantity'] / $detail['size'];
            }
            unset($detail);

            $formattedTotalSum = number_format((float)$totals['total_sum'], 0, ',', '.');

            return ApiResponse::response("Sales details", [
                'details' => $details,
                'bonus_products' => $bonusProducts,
                'total_quantity' => (int)$totals['total_quantity'],
                'total_sum' => $formattedTotalSum
            ]);
        }
    }







    public function actionGetFreeProducts()
    {
        $request = Yii::$app->request;
        $dateFilter = $request->get('date', date('Y-m-d'));

        $freeProductsQuery = FreeProducts::find()
            ->where(['deleted_at' => null]);

            if ($dateFilter) {
                $startDate = date('Y-m-d 00:00:00', strtotime($dateFilter));
                $endDate = date('Y-m-d 23:59:59', strtotime($dateFilter));

                $freeProductsQuery->andWhere(['between', 'created_at', $startDate, $endDate]);
            }

        $freeProducts = $freeProductsQuery->all();

        $responseData = [];
        foreach ($freeProducts as $freeProduct) {
            $freeProductData = $freeProduct->attributes;
            $freeProductData['details'] = [];

            // Получаем детали бесплатных продуктов
            foreach ($freeProduct->freeProductDetails as $detail) {
                $product = $detail->product;

                // Рассчитываем количество блоков
                $quantityBlock = null;
                if ($product && $product->size > 0) {
                    $quantityBlock = $detail->quantity / $product->size;
                }

                $freeProductData['details'][] = [
                    'id' => $detail->id,
                    'product_id' => $detail->product_id,
                    'product_name' => $product ? $product->name : null,
                    'quantity' => $detail->quantity,
                    'quantity_block' => $quantityBlock,
                    'product_size' => $product ? $product->size : null
                ];
            }

            $responseData[] = $freeProductData;
        }

        $availableProducts = Product::find()
            ->where(['deleted_at' => null])
            ->all();

        // Добавляем размер блока к каждому продукту
        $availableProductsData = [];
        foreach ($availableProducts as $product) {
            $availableProductsData[] = array_merge(
                $product->attributes,
                [
                    'size' => $product->size
                ]
            );
        }

        $data['free_products'] = $responseData;
        $data['available_products'] = $availableProductsData;

        return ApiResponse::response("Free products retrieved", $data);
    }

    public function actionSetFreeProducts()
    {
        $model = new SendToFreeProductsForm();
        $model->load(Yii::$app->request->post(), '');

        if (!$model->validate()) {
            return ApiResponse::response("Error", $model->errors, 500);
        }

        // Проверяем наличие продуктов на складе
        foreach ($model->products as $productData) {
            $productId = $productData['product_id'];

            // Получаем продукт для определения размера блока
            $product = Product::findOne($productId);
            if (!$product) {
                return ApiResponse::response("Product not found: " . $productId, null, 404);
            }

            // Конвертируем количество блоков в штуки
            $quantity = $productData['quantity_block'] * $product->size;

            // Выбираем только подтвержденные записи (accepted_at и accepted_user_id не NULL)
            $productStorages = ProductStorage::find()
                ->where([
                    'product_id' => $productId,
                    'deleted_at' => null
                ])
                ->andWhere(['IS NOT', 'accepted_at', null])
                ->andWhere(['IS NOT', 'accepted_user_id', null])
                ->andWhere(['>=', 'quantity', 0])
                ->orderBy(['enter_date' => SORT_ASC]) // Сначала используем старые записи
                ->all();

            $totalQuantity = array_sum(array_column($productStorages, 'quantity'));

            if ($totalQuantity < $quantity) {
                $productName = $product->name;
                return ApiResponse::response(
                    Yii::t('app', 'Not enough product {name} on stock. Available: {available}, required: {required}',
                        ['name' => $productName, 'available' => $totalQuantity, 'required' => $quantity]
                    ),
                    null,
                    400
                );
            }
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Создаем запись в основной таблице
            $freeProducts = new FreeProducts();
            $freeProducts->client = $model->client;
            $freeProducts->car_number = $model->car_number;
            $freeProducts->status = FreeProducts::STATUS_PENDING;
            $freeProducts->created_at = date('Y-m-d H:i:s');

            if (!$freeProducts->save()) {
                throw new \Exception("Error saving free products: " . json_encode($freeProducts->errors));
            }

            // Создаем записи в детальной таблице
            $details = [];
            foreach ($model->products as $productData) {
                $productId = $productData['product_id'];

                // Получаем продукт для определения размера блока
                $product = Product::findOne($productId);
                if (!$product) {
                    throw new \Exception("Product not found: " . $productId);
                }

                // Конвертируем количество блоков в штуки
                $quantity = $productData['quantity_block'] * $product->size;

                $detail = new FreeProductDetail();
                $detail->free_product_id = $freeProducts->id;
                $detail->product_id = $productId;
                $detail->quantity = $quantity;
                $detail->created_at = date('Y-m-d H:i:s');

                if (!$detail->save()) {
                    throw new \Exception("Error saving free product detail: " . json_encode($detail->errors));
                }

                $details[] = $detail;
            }

            // Создаем запись в таблице отслеживания
            $tracking = new Tracking();
            $tracking->progress_type = Tracking::TYPE_FREE_PRODUCTS;
            $tracking->process_id = $freeProducts->id;
            $tracking->created_at = date('Y-m-d H:i:s');
            $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
            $tracking->accepted_at = null;

            if (!$tracking->save()) {
                throw new \Exception("Error saving tracking: " . json_encode($tracking->errors));
            }

            // Формируем ответ
            $responseData = $freeProducts->attributes;
            $responseData['details'] = [];

            foreach ($details as $detail) {
                $product = $detail->product;
                $responseData['details'][] = [
                    'id' => $detail->id,
                    'product_id' => $detail->product_id,
                    'product_name' => $product ? $product->name : null,
                    'quantity' => $detail->quantity,
                    'quantity_block' => $product && $product->size > 0 ? ($detail->quantity / $product->size) : null,
                    'product_size' => $product ? $product->size : null
                ];
            }

            $transaction->commit();
            return ApiResponse::response("Free products set", $responseData);

        } catch (\Exception $e) {
            $transaction->rollBack();
            return ApiResponse::response("Error: " . $e->getMessage(), null, 500);
        }
    }


    public function actionUpdateFreeProducts()
    {
        $data = Yii::$app->request->post();

        if (!isset($data['id'])) {
            return ApiResponse::response("ID is required", null, 400);
        }

        $freeProducts = FreeProducts::findOne($data['id']);
        if (!$freeProducts || $freeProducts->status == FreeProducts::STATUS_APPROVED) {
            return ApiResponse::response(Yii::t('app', 'Record not found'), null, 404);
        }

        $tracking = Tracking::findOne([
            'process_id' => $freeProducts->id,
            'progress_type' => Tracking::TYPE_FREE_PRODUCTS,
            'deleted_at' => null,
            'accepted_at' => null
        ]);

        if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
            return ApiResponse::response(
                Yii::t('app', 'Record already accepted or not found'),
                null,
                ApiResponse::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        $model = new SendToFreeProductsForm();
        $model->load($data, '');

        if (!$model->validate()) {
            return ApiResponse::response("Error", $model->errors, 500);
        }

        // Проверяем наличие продуктов на складе
        foreach ($model->products as $productData) {
            $productId = $productData['product_id'];

            // Получаем продукт для определения размера блока
            $product = Product::findOne($productId);
            if (!$product) {
                return ApiResponse::response("Product not found: " . $productId, null, 404);
            }

            // Конвертируем количество блоков в штуки
            $quantity = $productData['quantity_block'] * $product->size;

            // Выбираем только подтвержденные записи (accepted_at и accepted_user_id не NULL)
            $productStorages = ProductStorage::find()
                ->where([
                    'product_id' => $productId,
                    'deleted_at' => null
                ])
                ->andWhere(['IS NOT', 'accepted_at', null])
                ->andWhere(['IS NOT', 'accepted_user_id', null])
                ->andWhere(['>=', 'quantity', 0])
                ->orderBy(['enter_date' => SORT_ASC]) // Сначала используем старые записи
                ->all();

            $totalQuantity = array_sum(array_column($productStorages, 'quantity'));

            if ($totalQuantity < $quantity) {
                $productName = $product->name;
                return ApiResponse::response(
                    Yii::t('app', 'Not enough product {name} on stock. Available: {available}, required: {required}',
                        ['name' => $productName, 'available' => $totalQuantity, 'required' => $quantity]
                    ),
                    null,
                    400
                );
            }
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Обновляем основную запись
            $freeProducts->client = $model->client;
            $freeProducts->car_number = $model->car_number;

            if (!$freeProducts->save()) {
                throw new \Exception("Error saving free products: " . json_encode($freeProducts->errors));
            }

            // Удаляем старые детали, они будут пересозданы заново ниже
            FreeProductDetail::deleteAll(
                ['free_product_id' => $freeProducts->id]
            );

            // Создаем новые записи в детальной таблице
            $details = [];
            foreach ($model->products as $productData) {
                $productId = $productData['product_id'];

                // Получаем продукт для определения размера блока
                $product = Product::findOne($productId);
                if (!$product) {
                    throw new \Exception("Product not found: " . $productId);
                }

                // Конвертируем количество блоков в штуки
                $quantity = $productData['quantity_block'] * $product->size;

                $detail = new FreeProductDetail();
                $detail->free_product_id = $freeProducts->id;
                $detail->product_id = $productId;
                $detail->quantity = $quantity;
                $detail->created_at = date('Y-m-d H:i:s');

                if (!$detail->save()) {
                    throw new \Exception("Error saving free product detail: " . json_encode($detail->errors));
                }

                $details[] = $detail;
            }

            // Формируем ответ
            $responseData = $freeProducts->attributes;
            $responseData['details'] = [];

            foreach ($details as $detail) {
                $product = $detail->product;
                $responseData['details'][] = [
                    'id' => $detail->id,
                    'product_id' => $detail->product_id,
                    'product_name' => $product ? $product->name : null,
                    'quantity' => $detail->quantity,
                    'quantity_block' => $product && $product->size > 0 ? ($detail->quantity / $product->size) : null,
                    'product_size' => $product ? $product->size : null
                ];
            }

            $transaction->commit();
            return ApiResponse::response("Free products updated", $responseData);

        } catch (\Exception $e) {
            $transaction->rollBack();
            return ApiResponse::response("Error: " . $e->getMessage(), null, 500);
        }
    }

    public function actionDeleteFreeProducts()
    {
        $data = Yii::$app->request->post();

        if (!isset($data['id'])) {
            return ApiResponse::response("ID is required", null, 400);
        }

        $products = FreeProducts::findOne($data['id']);
        if (!$products || $products->status == FreeProducts::STATUS_APPROVED) {
            return ApiResponse::response(Yii::t('app', 'Record not found'), null, 404);
        }

        $tracking = Tracking::findOne([
            'process_id' => $products->id,
            'progress_type' => Tracking::TYPE_FREE_PRODUCTS,
            'deleted_at' => null,
            'accepted_at' => null
        ]);

        if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
            return ApiResponse::response(
                Yii::t('app', 'Record already accepted or not found'),
                null,
                ApiResponse::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $tracking->deleted_at = date('Y-m-d H:i:s');
            if (!$tracking->save()) {
                throw new \Exception("Error updating tracking");
            }

            if (!$products->delete()) {
                throw new \Exception("Error deleting free products");
            }

            $transaction->commit();
            return ApiResponse::response("Free products deleted", null);

        } catch (\Exception $e) {
            $transaction->rollBack();
            return ApiResponse::response("Error: " . $e->getMessage(), null, 500);
        }
    }

    public function actionDetailFreeProducts()
    {
        $data = Yii::$app->request->get();

        if (!isset($data['id'])) {
            return ApiResponse::response("ID is required", null, 400);
        }

        $freeProducts = FreeProducts::findOne($data['id']);
        if (!$freeProducts) {
            return ApiResponse::response(Yii::t('app', 'Record not found'), null, 200);
        }

        $freeProductData = $freeProducts->attributes;
        $freeProductData['details'] = [];

        // Получаем детали бесплатных продуктов
        foreach ($freeProducts->freeProductDetails as $detail) {
            if ($detail->deleted_at !== null) {
                continue; // Пропускаем удаленные записи
            }

            $product = $detail->product;

            // Рассчитываем количество блоков
            $quantityBlock = null;
            if ($product && $product->size > 0) {
                $quantityBlock = $detail->quantity / $product->size;
            }

            $freeProductData['details'][] = [
                'id' => $detail->id,
                'product_id' => $detail->product_id,
                'product_name' => $product ? $product->name : null,
                'quantity' => $detail->quantity,
                'quantity_block' => $quantityBlock,
                'product_size' => $product ? $product->size : null
            ];
        }

        $responseData = [
            'free_products' => $freeProductData
        ];

        return ApiResponse::response("Free products details", $responseData);
    }







    public function actionReturnProduct()
    {
        if (Yii::$app->request->isGet) {
            $returns = SalesReturn::find()->all();
            return ApiResponse::response("Список возвращаемых продуктов", $returns);
        } elseif (Yii::$app->request->isPost) {
            $model = new ReturnProductForm();
            $model->load(Yii::$app->request->post(), '');

            if (!$model->validate()) {
                return ApiResponse::response("Ошибка валидации", $model->errors, 400);
            }

            $sale = Sales::findOne($model->sale_id);
            $transaction = Yii::$app->db->beginTransaction();

            try {
                $totalReturnAmount = 0;
                $returns = [];

                foreach ($model->products as $productData) {
                    $saleDetail = SalesDetail::find()
                        ->where([
                            'sale_id' => $model->sale_id,
                            'product_id' => $productData['product_id'],
                            'deleted_at' => null
                        ])
                        ->one();

                        $today = date('Y-m-d');
                        $productStorage = ProductStorage::find()
                            ->where(['deleted_at' => null])
                            ->andWhere(['product_id' => $productData['product_id']])
                            ->andWhere(['IS NOT', 'accepted_at', null])
                            ->andWhere(['IS NOT', 'accepted_user_id', null])
                            ->andWhere(['DATE(enter_date)' => $today])
                            ->orderBy(['enter_date' => SORT_DESC])
                            ->one();

                        if(!$productStorage){
                            $productStorage = new ProductStorage();
                            $productStorage->product_id = $productData['product_id'];
                            $productStorage->quantity = 0;
                            $productStorage->enter_date = date('Y-m-d H:i:s');
                            $productStorage->add_user_id = Yii::$app->user->getId();
                            $productStorage->accepted_at = date('Y-m-d H:i:s');
                            $productStorage->accepted_user_id = Yii::$app->user->getId();
                        }
                        $productStorage->quantity += $productData['quantity'];

                    if (!$productStorage->save()) {
                        throw new \Exception('Ошибка при обновлении количества на складе');
                    }

                    $history = new ProductStorageHistory();
                    $history->product_storage_id = $productStorage->id;
                    $history->product_id = $productData['product_id'];
                    $history->quantity = $productData['quantity'];
                    $history->type = ProductStorageHistory::TYPE_RETURN;
                    $history->created_at = date('Y-m-d H:i:s');
                    if (!$history->save()) {
                        throw new \Exception('Ошибка при сохранении истории склада');
                    }

                    $returnAmount = $productData['quantity'] * $saleDetail->unit_price;
                    $totalReturnAmount += $returnAmount;

                    $salesReturn = new SalesReturn();
                    $salesReturn->sale_id = $sale->id;
                    $salesReturn->product_id = $productData['product_id'];
                    $salesReturn->quantity = $productData['quantity'];
                    $salesReturn->unit_price = $saleDetail->unit_price;
                    $salesReturn->total_price = $returnAmount;
                    $salesReturn->status = SalesReturn::STATUS_PENDING;
                    $salesReturn->comment = isset($productData['comment']) ? $productData['comment'] : null;
                    if (!$salesReturn->save()) {
                        throw new \Exception('Ошибка при создании записи о возврате');
                    }



                    $returns[] = [
                        'return_id' => $salesReturn->id,
                        'product_id' => $productData['product_id'],
                        'quantity' => $productData['quantity'],
                        'amount' => $returnAmount
                    ];
                }

                $tracking = new Tracking();
                    $tracking->progress_type = Tracking::TYPE_RETURN_PRODUCT;
                    $tracking->process_id = $sale->id;
                    $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                    $tracking->created_at = date('Y-m-d H:i:s');
                    if (!$tracking->save()) {
                        throw new \Exception('Ошибка при создании записи отслеживания');
                    }

                $transaction->commit();
                return ApiResponse::response("Возврат успешно создан", [
                    'sale_id' => $sale->id,
                    'total_return_amount' => $totalReturnAmount,
                    'returns' => $returns
                ]);

            } catch (\Exception $e) {
                $transaction->rollBack();
                return ApiResponse::response($e->getMessage(), null, 500);
            }
        }

        return ApiResponse::response("Метод не поддерживается", null, 405);
    }













    public function actionGetClients()
    {
        $clientGenerator = function () {
            $query = (new \yii\db\Query())
                ->select([
                    'c.id',
                    'c.full_name',
                    'c.phone_number',
                    'r.name as region_name'
                ])
                ->from(['c' => 'client'])
                ->leftJoin(['r' => 'region'], 'r.id = c.region_id')
                ->where(['c.deleted_at' => null])
                ->orderBy(['c.full_name' => SORT_ASC]);

            foreach ($query->each() as $client) {
                yield $client;
            }
        };

        $clients = $clientGenerator();
        $firstClient = $clients->current();
        if (!$firstClient) {
            return ApiResponse::response("Клиенты не найдены", [], 404);
        }

        $productGenerator = function () {
            $query = (new \yii\db\Query())
                ->select(['p.id', 'p.name'])
                ->from(['p' => 'product'])
                ->where(['p.deleted_at' => null])
                ->orderBy(['p.id' => SORT_ASC]);

            foreach ($query->each() as $product) {
                yield $product;
            }
        };

        $clientList = iterator_to_array($clientGenerator());
        $productList = iterator_to_array($productGenerator());

        return ApiResponse::response("Список клиентов", [
            'clients' => $clientList,
            'products' => $productList
        ]);
    }

    /**
     * Получение списка водителей клиента
     *
     * @OA\Get(
     *     path="/api/sales/get-drivers",
     *     summary="Получение списка водителей клиента",
     *     description="Возвращает список водителей для указанного клиента",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="client_id",
     *         in="query",
     *         description="ID клиента",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Список водителей клиента",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Список водителей клиента"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="drivers",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="Иванов Иван"),
     *                         @OA\Property(property="car_number", type="string", example="A123BC")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="ID клиента обязателен"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     )
     * )
     */
    public function actionGetDrivers()
    {
        $client_id = Yii::$app->request->get('client_id');

        if (empty($client_id)) {
            return ApiResponse::response("ID клиента обязателен", null, 400);
        }

        $service = new ClientDriverService();
        $drivers = $service->getDriversByClientId($client_id);

        return ApiResponse::response("Список водителей клиента", [
            'drivers' => array_values($drivers)
        ]);
    }

    /**
     * Получение деталей водителя
     *
     * @OA\Get(
     *     path="/api/sales/get-driver-details",
     *     summary="Получение деталей водителя",
     *     description="Возвращает информацию о водителе по его ID",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="driver_id",
     *         in="query",
     *         description="ID водителя",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Информация о водителе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Информация о водителе"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="car_number", type="string", example="A123BC")
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     )
     * )
     */
    public function actionGetDriverDetails()
    {
        $driver_id = Yii::$app->request->get('driver_id');

        $service = new InvoiceService();
        $details = $service->getDriverDetails($driver_id);

        return ApiResponse::response("Информация о водителе", $details);
    }

    /**
     * Создание водителя
     *
     * @OA\Post(
     *     path="/api/sales/create-driver",
     *     summary="Создание нового водителя",
     *     description="Создает нового водителя для клиента",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"client_id", "driver_name"},
     *             @OA\Property(property="client_id", type="integer", example=1),
     *             @OA\Property(property="driver_name", type="string", example="Иванов Иван"),
     *             @OA\Property(property="car_number", type="string", example="A123BC")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Водитель успешно создан",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Водитель успешно добавлен"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="success", type="boolean", example=true)
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Не указаны обязательные параметры"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     )
     * )
     */
    public function actionCreateDriver()
    {
        $client_id = Yii::$app->request->post('client_id');
        $driver_name = Yii::$app->request->post('driver_name');
        $car_number = Yii::$app->request->post('car_number');

        $service = new ClientDriverService();
        $result = $service->createDriver($client_id, $driver_name, $car_number);

        if ($result['success']) {
            return ApiResponse::response($result['message'], [
                'id' => $result['id'],
                'name' => $driver_name,
                'car_number' => $car_number
            ]);
        } else {
            return ApiResponse::response($result['message'], null, 400);
        }
    }

    /**
     * Обновление водителя
     *
     * @OA\Put(
     *     path="/api/sales/update-driver",
     *     summary="Обновление данных водителя",
     *     description="Обновляет данные существующего водителя",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"id", "driver_name"},
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="driver_name", type="string", example="Иванов Иван"),
     *             @OA\Property(property="car_number", type="string", example="A123BC")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Водитель успешно обновлен",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Водитель успешно обновлен"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="success", type="boolean", example=true)
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Не указаны обязательные параметры"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Водитель не найден",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Водитель не найден"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=404)
     *         )
     *     )
     * )
     */
    public function actionUpdateDriver()
    {
        $id = Yii::$app->request->post('id');
        $driver_name = Yii::$app->request->post('driver_name');
        $car_number = Yii::$app->request->post('car_number');

        $service = new ClientDriverService();
        $result = $service->updateDriver($id, $driver_name, $car_number);

        if ($result['success']) {
            return ApiResponse::response($result['message'], [
                'id' => $result['id'],
                'name' => $driver_name,
                'car_number' => $car_number
            ]);
        } else {
            $code = strpos($result['message'], 'не найден') !== false ? 404 : 400;
            return ApiResponse::response($result['message'], null, $code);
        }
    }

    /**
     * Удаление водителя
     *
     * @OA\Delete(
     *     path="/api/sales/delete-driver",
     *     summary="Удаление водителя",
     *     description="Удаляет водителя (мягкое удаление)",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"id"},
     *             @OA\Property(property="id", type="integer", example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Водитель успешно удален",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Водитель успешно удален"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="success", type="boolean", example=true)
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Неверный ID водителя"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Водитель не найден",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Водитель не найден"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=404)
     *         )
     *     )
     * )
     */
    public function actionDeleteDriver()
    {
        $id = Yii::$app->request->post('id');

        $service = new ClientDriverService();
        $result = $service->deleteDriver($id);

        if ($result['success']) {
            return ApiResponse::response($result['message'], [
                'success' => true
            ]);
        } else {
            $code = strpos($result['message'], 'не найден') !== false ? 404 : 400;
            return ApiResponse::response($result['message'], null, $code);
        }
    }

    /**
     * Создание инвойса
     *
     * @OA\Post(
     *     path="/api/sales/create-invoice",
     *     summary="Создание нового инвойса",
     *     description="Создает новый инвойс (продажу)",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"client_id", "products"},
     *             @OA\Property(property="client_id", type="integer", example=1),
     *             @OA\Property(property="driver_id", type="integer", example=1),
     *             @OA\Property(property="car_number", type="string", example="A123BC"),
     *             @OA\Property(property="total_price", type="number", format="float", example=1000, description="Необязательное поле, будет рассчитано автоматически на сервере"),
     *             @OA\Property(
     *                 property="products",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="product_id", type="integer", example=1),
     *                     @OA\Property(property="quantity", type="integer", example=10, description="Количество в штуках (можно указать либо quantity, либо quantity_block)"),
     *                     @OA\Property(property="quantity_block", type="number", format="float", example=0.83, description="Количество в блоках (будет автоматически конвертировано в штуки с учетом размера блока)"),
     *                     @OA\Property(property="special_price", type="number", format="float", example=100, description="Необязательное поле, будет установлено автоматически на сервере"),
     *                     @OA\Property(property="sell_price", type="number", format="float", example=120, description="Необязательное поле, будет установлено автоматически на сервере")
     *                 )
     *             ),
     *             @OA\Property(property="has_bonus", type="boolean", example=false, description="Если true, то требуется указать бонусные продукты в массиве bonus. Если false, то бонусные продукты будут проигнорированы, даже если они указаны."),
     *             @OA\Property(
     *                 property="bonus",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="product_id", type="integer", example=2),
     *                     @OA\Property(property="quantity", type="integer", example=5, description="Количество в штуках (можно указать либо quantity, либо quantity_block)"),
     *                     @OA\Property(property="quantity_block", type="number", format="float", example=0.42, description="Количество в блоках (будет автоматически конвертировано в штуки с учетом размера блока)")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Инвойс успешно создан",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Запись успешно создана"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="status", type="string", example="success"),
     *                 @OA\Property(
     *                     property="data",
     *                     type="object",
     *                     @OA\Property(property="sales_id", type="integer", example=1)
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Ошибка валидации"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="status",
     *                     type="string",
     *                     example="error"
     *                 ),
     *                 @OA\Property(
     *                     property="message",
     *                     type="object",
     *                     @OA\Property(
     *                         property="driver",
     *                         type="array",
     *                         @OA\Items(type="string", example="Водитель обязателен")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     )
     * )
     */
    public function actionCreateInvoice()
    {
        $model = new InvoiceCreateForm();
        $model->load(Yii::$app->request->post(), '');

        if (!$model->validate()) {
            return ApiResponse::response("Ошибка валидации", [
                'status' => 'error',
                'message' => $model->getErrors()
            ], 200);
        }

        $service = new InvoiceService();
        $result = $service->createInvoice([
            'client_id' => $model->client_id,
            'driver_id' => $model->driver_id,
            'car_number' => $model->car_number,
            'products' => $model->products,
            'has_bonus' => $model->has_bonus,
            'bonus' => $model->bonus
        ]);

        if ($result['status'] === 'success') {
            return ApiResponse::response($result['message'], [
                'status' => 'success',
                'data' => $result['data']
            ]);
        } else {
            return ApiResponse::response("Ошибка при создании инвойса", [
                'status' => 'error',
                'message' => $result['message']
            ], 200);
        }
    }

    /**
     * Получение списка всех продуктов с информацией о количестве в блоках
     *
     * @OA\Get(
     *     path="/api/sales/get-products",
     *     summary="Получение списка всех продуктов",
     *     description="Возвращает список всех продуктов с информацией о количестве в блоках",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Список продуктов",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Список продуктов"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="products",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="Продукт 1"),
     *                         @OA\Property(property="size", type="integer", example=12),
     *                         @OA\Property(property="price", type="number", format="float", example=100.50),
     *                         @OA\Property(property="quantity", type="integer", example=120),
     *                         @OA\Property(property="quantity_block", type="number", format="float", example=10),
     *                         @OA\Property(property="type", type="integer", example=0)
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     )
     * )
     */
    public function actionGetProducts()
    {
        // Создаем SQL-запрос для получения продуктов с информацией о количестве на складе и ценах
        $sql = "
            SELECT
                p.id,
                p.name,
                p.size,
                p.type,
                COALESCE(pp.price, 0) as price,
                COALESCE(SUM(ps.quantity), 0) as quantity
            FROM
                product p
            LEFT JOIN
                product_price pp ON p.id = pp.product_id AND pp.deleted_at IS NULL
                AND pp.end_date = '9999-12-31'
            LEFT JOIN
                product_storage ps ON p.id = ps.product_id AND ps.deleted_at IS NULL
                AND ps.accepted_at IS NOT NULL
            WHERE
                p.deleted_at IS NULL
            GROUP BY
                p.id, p.name, p.size, p.type, pp.price
            ORDER BY
                p.priority ASC, p.name ASC
        ";

        $products = Yii::$app->db->createCommand($sql)->queryAll();

        // Добавляем количество в блоках для каждого продукта
        foreach ($products as &$product) {
            // Если size равен 0, устанавливаем его в 1, чтобы избежать деления на ноль
            $size = $product['size'] > 0 ? $product['size'] : 1;

            // Вычисляем количество в блоках
            $product['quantity_block'] = round($product['quantity'] / $size, 2);
        }

        return ApiResponse::response("Список продуктов", [
            'products' => $products
        ]);
    }

    /**
     * Получение списка инвойсов
     *
     * @OA\Get(
     *     path="/api/sales/get-invoices",
     *     summary="Получение списка инвойсов",
     *     description="Возвращает список инвойсов с возможностью фильтрации по дате и клиенту",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="client_id",
     *         in="query",
     *         description="ID клиента (необязательно)",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="date_from",
     *         in="query",
     *         description="Дата начала периода в формате YYYY-MM-DD (необязательно)",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2023-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="date_to",
     *         in="query",
     *         description="Дата окончания периода в формате YYYY-MM-DD (необязательно)",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2023-12-31")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Статус инвойса (необязательно): 1 - новый, 2 - в процессе, 3 - подтвержден",
     *         required=false,
     *         @OA\Schema(type="integer", enum={1, 2, 3}, example=3)
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Номер страницы (по умолчанию 1)",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Количество записей на странице (по умолчанию 20, максимум 100)",
     *         required=false,
     *         @OA\Schema(type="integer", example=20)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Список инвойсов",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Список инвойсов"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="invoices",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01 12:00:00"),
     *                         @OA\Property(property="client_name", type="string", example="ООО Компания"),
     *                         @OA\Property(property="driver", type="string", example="Иванов И.И."),
     *                         @OA\Property(property="car_number", type="string", example="A123BC"),
     *                         @OA\Property(property="total_sum", type="string", example="10 000"),
     *                         @OA\Property(property="status", type="integer", example=3),
     *                         @OA\Property(property="status_name", type="string", example="Подтвержден")
     *                     )
     *                 ),
     *                 @OA\Property(property="total_count", type="integer", example=100),
     *                 @OA\Property(property="page", type="integer", example=1),
     *                 @OA\Property(property="per_page", type="integer", example=20),
     *                 @OA\Property(property="page_count", type="integer", example=5)
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     )
     * )
     */
    public function actionGetInvoices()
    {
        // Получаем параметры запроса
        $params = [
            'client_id' => Yii::$app->request->get('client_id'),
            'date_from' => Yii::$app->request->get('date_from'),
            'date_to' => Yii::$app->request->get('date_to'),
            'status' => Yii::$app->request->get('status'),
            'page' => (int)Yii::$app->request->get('page', 1),
            'per_page' => (int)Yii::$app->request->get('per_page', 20)
        ];

        // Используем сервис для получения списка инвойсов
        $service = new InvoiceService();
        $result = $service->getInvoices($params);

        return ApiResponse::response("Список инвойсов", $result);
    }

    /**
     * Получение деталей инвойса
     *
     * @OA\Get(
     *     path="/api/sales/get-invoice-details",
     *     summary="Получение деталей инвойса",
     *     description="Возвращает детальную информацию об инвойсе по его ID",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="invoice_id",
     *         in="query",
     *         description="ID инвойса",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Детали инвойса",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Детали инвойса"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="invoice",
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01 12:00:00"),
     *                     @OA\Property(property="client_name", type="string", example="ООО Компания"),
     *                     @OA\Property(property="driver", type="string", example="Иванов И.И."),
     *                     @OA\Property(property="car_number", type="string", example="A123BC"),
     *                     @OA\Property(property="total_sum", type="string", example="10 000"),
     *                     @OA\Property(property="status", type="integer", example=3),
     *                     @OA\Property(property="status_name", type="string", example="Подтвержден")
     *                 ),
     *                 @OA\Property(
     *                     property="products",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="product_id", type="integer", example=1),
     *                         @OA\Property(property="product_name", type="string", example="Продукт 1"),
     *                         @OA\Property(property="quantity", type="integer", example=10),
     *                         @OA\Property(property="quantity_block", type="number", format="float", example=0.83),
     *                         @OA\Property(property="special_price", type="number", format="float", example=100),
     *                         @OA\Property(property="sell_price", type="number", format="float", example=120),
     *                         @OA\Property(property="total_price", type="string", example="1 200")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="bonus_products",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="product_id", type="integer", example=2),
     *                         @OA\Property(property="product_name", type="string", example="Бонусный продукт"),
     *                         @OA\Property(property="quantity", type="integer", example=5),
     *                         @OA\Property(property="quantity_block", type="number", format="float", example=0.42)
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="ID инвойса обязателен"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Инвойс не найден",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Инвойс не найден"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=404)
     *         )
     *     )
     * )
     */
    public function actionGetInvoiceDetails()
    {
        // Получаем ID инвойса из запроса
        $invoice_id = Yii::$app->request->get('invoice_id');

        // Проверяем, что ID инвойса указан
        if (empty($invoice_id)) {
            return ApiResponse::response("ID инвойса обязателен", null, 400);
        }

        // Используем сервис для получения деталей инвойса
        $service = new InvoiceService();
        $result = $service->getInvoiceDetails($invoice_id);

        // Проверяем, что инвойс найден
        if (!$result) {
            return ApiResponse::response("Инвойс не найден", null, 404);
        }

        return ApiResponse::response("Детали инвойса", $result);
    }

    /**
     * Обновление инвойса
     *
     * @OA\Post(
     *     path="/api/sales/update-invoice",
     *     summary="Обновление инвойса",
     *     description="Обновляет существующий инвойс",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"invoice_id", "client_id", "products"},
     *             @OA\Property(property="invoice_id", type="integer", example=1, description="ID инвойса для обновления"),
     *             @OA\Property(property="client_id", type="integer", example=1),
     *             @OA\Property(property="driver_id", type="integer", example=1),
     *             @OA\Property(property="car_number", type="string", example="A123BC"),
     *             @OA\Property(
     *                 property="products",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="product_id", type="integer", example=1),
     *                     @OA\Property(property="quantity", type="integer", example=10, description="Количество в штуках (можно указать либо quantity, либо quantity_block)"),
     *                     @OA\Property(property="quantity_block", type="number", format="float", example=0.83, description="Количество в блоках (будет автоматически конвертировано в штуки с учетом размера блока)"),
     *                     @OA\Property(property="special_price", type="number", format="float", example=100, description="Необязательное поле, будет установлено автоматически на сервере"),
     *                     @OA\Property(property="sell_price", type="number", format="float", example=120, description="Необязательное поле, будет установлено автоматически на сервере")
     *                 )
     *             ),
     *             @OA\Property(property="has_bonus", type="boolean", example=false, description="Если true, то требуется указать бонусные продукты в массиве bonus. Если false, то бонусные продукты будут проигнорированы, даже если они указаны."),
     *             @OA\Property(
     *                 property="bonus",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="product_id", type="integer", example=2),
     *                     @OA\Property(property="quantity", type="integer", example=5, description="Количество в штуках (можно указать либо quantity, либо quantity_block)"),
     *                     @OA\Property(property="quantity_block", type="number", format="float", example=0.42, description="Количество в блоках (будет автоматически конвертировано в штуки с учетом размера блока)")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Результат обновления инвойса",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Инвойс успешно обновлен"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="status",
     *                     type="string",
     *                     example="success"
     *                 ),
     *                 @OA\Property(
     *                     property="message",
     *                     type="string",
     *                     example="Запись успешно обновлена"
     *                 ),
     *                 @OA\Property(
     *                     property="data",
     *                     type="object",
     *                     @OA\Property(property="sales_id", type="integer", example=1)
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка валидации",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Ошибка валидации"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="status",
     *                     type="string",
     *                     example="error"
     *                 ),
     *                 @OA\Property(
     *                     property="message",
     *                     type="object",
     *                     @OA\Property(
     *                         property="invoice_id",
     *                         type="array",
     *                         @OA\Items(type="string", example="ID инвойса обязателен")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     )
     * )
     */
    public function actionUpdateInvoice()
    {
        $model = new InvoiceUpdateForm();
        $model->load(Yii::$app->request->post(), '');

        if (!$model->validate()) {
            return ApiResponse::response("Ошибка валидации", [
                'status' => 'error',
                'message' => $model->getErrors()
            ], 400);
        }

        $service = new InvoiceService();
        $result = $service->updateInvoice([
            'invoice_id' => $model->invoice_id,
            'client_id' => $model->client_id,
            'driver_id' => $model->driver_id,
            'car_number' => $model->car_number,
            'products' => $model->products,
            'has_bonus' => $model->has_bonus,
            'bonus' => $model->bonus
        ]);

        if ($result['status'] === 'error') {
            return ApiResponse::response("Ошибка обновления инвойса", $result, 400);
        }

        return ApiResponse::response("Инвойс успешно обновлен", $result);
    }

    public function actionGetClientSales()
    {
        if (!Yii::$app->request->isGet) {
            return ApiResponse::response("Метод не поддерживается", null, 405);
        }

        $client_id = Yii::$app->request->get('client_id');
        if (!$client_id) {
            return ApiResponse::response("ID клиента обязателен", null, 400);
        }

        $sales = (new \yii\db\Query())
            ->select([
                's.id',
                's.created_at',
                's.total_sum',
                's.status',
                'sd.product_id',
                'p.name as product_name',
                'sd.quantity',
                'sd.unit_price',
                'sd.total_price'
            ])
            ->from(['s' => 'sales'])
            ->innerJoin(['sd' => 'sales_detail'], 'sd.sale_id = s.id')
            ->innerJoin(['p' => 'product'], 'p.id = sd.product_id')
            ->where([
                's.client_id' => $client_id,
                's.status' => Sales::STATUS_CONFIRMED,
                's.deleted_at' => null,
                'sd.deleted_at' => null
            ])
            ->orderBy(['s.created_at' => SORT_DESC])
            ->all();

        if (empty($sales)) {
            return ApiResponse::response("Продажи не найдены", null, 200);
        }

        // Группируем продажи по ID
        $groupedSales = [];
        foreach ($sales as $sale) {
            $saleId = $sale['id'];
            if (!isset($groupedSales[$saleId])) {
                $groupedSales[$saleId] = [
                    'id' => $saleId,
                    'created_at' => $sale['created_at'],
                    'total_sum' => $sale['total_sum'],
                    'status' => $sale['status'],
                    'products' => []
                ];
            }
            $groupedSales[$saleId]['products'][] = [
                'product_id' => $sale['product_id'],
                'product_name' => $sale['product_name'],
                'quantity' => $sale['quantity'],
                'unit_price' => $sale['unit_price'],
                'total_price' => $sale['total_price']
            ];
        }

        return ApiResponse::response("Продажи клиента", [
            'sales' => array_values($groupedSales)
        ]);
    }
}