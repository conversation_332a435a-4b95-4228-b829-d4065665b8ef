<?php

use yii\db\Migration;

class m250405_053217_update_column_client_balance_history extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn('{{%client_balance_history}}', 'amount', $this->decimal(15, 2)->null()->defaultValue(0));
        $this->alterColumn('{{%client_balance_history}}', 'old_amount', $this->decimal(15, 2)->null()->defaultValue(0));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->alterColumn('{{%client_balance_history}}', 'amount', $this->decimal(10, 2)->null()->defaultValue(0));
        $this->alterColumn('{{%client_balance_history}}', 'old_amount', $this->decimal(10, 2)->null()->defaultValue(0));
    }

   
}
