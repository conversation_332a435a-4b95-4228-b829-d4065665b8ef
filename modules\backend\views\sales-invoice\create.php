<?php
use yii\widgets\ActiveForm;
use app\common\models\Client;
use app\common\models\ClientDriver;
use app\common\models\Product;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\helpers\Html;

$clients = ArrayHelper::map(Client::find()->where(['deleted_at' => null])->all(), 'id', 'full_name');
$clientDrivers = ArrayHelper::map(ClientDriver::find()->where(['deleted_at' => null])->all(), 'id', 'driver');
$products = ArrayHelper::map(Product::find()->where(['deleted_at' => null])->all(), 'id', 'name');
$productsJson = Json::encode($products);
?>

<?php $form = ActiveForm::begin(['id' => 'invoice-create-form']); ?>

<style>
/* Стили для индикатора загрузки */
.loading-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #007bff;
    font-size: 1.2rem;
    display: none;
    z-index: 10;
}

/* Стили для индикатора загрузки цен */
.price-loading {
    position: relative;
    display: block;
    margin: 10px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    text-align: center;
    color: #007bff;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Анимация вращения для индикатора */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spin {
    animation: spin 1s linear infinite;
}

/* Стили для полей с ошибками */
.is-invalid {
    border-color: #dc3545 !important;
    background-color: #fff8f8 !important;
}

.is-invalid:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

/* product uchun */
.fizzy-input {
    background-color: rgba(204, 0, 0, 0.1) !important; /* Более мягкий красный */
    border: 1px solid #cc0000 !important; /* Темно-красная рамка для разделения */
}

/* Стили для placeholder */
.fizzy-input::placeholder {
    /* color: #f0f0f0 !important; */
    opacity: 1 !important;
}

/* Стили при фокусе */
.fizzy-input:focus {
    border-color: #990000 !important; /* Еще более темная рамка */
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.5); /* Легкая тень для акцента */
}

/* Убираем стрелки для Chrome, Safari, Edge */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Убираем стрелки для Firefox */
input[type="number"] {
    -moz-appearance: textfield;
    appearance: textfield;
}

/* Уменьшаем высоту строк */
.compact-row {
    margin-bottom: 0.25rem !important; /* Уменьшаем внешний отступ снизу (было mb-1 = 0.25rem, но уточняем) */
    padding: -0.2rem 0; /* Уменьшаем внутренние отступы строки */
}

/* Уменьшаем высоту полей ввода */
.compact-input {
    height: 24px; /* Уменьшаем высоту (стандарт Bootstrap ~38px) */
    padding: 0.25rem 0.5rem; /* Уменьшаем внутренние отступы */
    font-size: 0.600rem; /* Уменьшаем размер шрифта (14px вместо 16px) */
    line-height: 1; /* Уменьшаем высоту строки текста */
}

/* Уменьшаем высоту и отступы для текста */
.compact-row strong {
    font-size: 0.800rem; /* Уменьшаем шрифт названия продукта */
    line-height: 1;
}

/* Уменьшаем высоту ошибок */
.compact-error {
    font-size: 0.75rem; /* Уменьшаем шрифт сообщений об ошибках (12px) */
    margin-top: 0.1rem; /* Уменьшаем отступ сверху */
    line-height: 1.1;
}

/* Убедимся, что элементы выравниваются компактно */
.align-items-center {
    align-items: flex-start; /* Выравнивание по верхнему краю для экономии пространства */
}

.select2-container .select2-dropdown {
    top: 100% !important; /* Убеждаемся, что dropdown открывается вниз */
    margin-top: 2px; /* Небольшой отступ от поля */
}

/* Стили для поля поиска в select2 */
.select2-search__field {
    width: 100% !important;
    padding: 6px 8px !important;
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
    box-shadow: none !important;
    font-size: 14px !important;
}

.select2-search__field:focus {
    border-color: #80bdff !important;
    outline: 0 !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* Стили для выпадающего списка select2 */
.select2-dropdown {
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) !important;
}

/* Стили для результатов поиска */
.select2-results__option {
    padding: 6px 12px !important;
    font-size: 14px !important;
}

.select2-results__option--highlighted {
    background-color: #007bff !important;
    color: white !important;
}



/* Показываем поле поиска */
.show-search .select2-search {
    display: block !important;
}



.dropdown-toggle-btn:hover {
    background-color: #f8f9fa;
}

.dropdown-toggle-btn:focus {
    outline: none;
}

.dropdown-toggle-btn i {
    font-size: 12px;
    color: #6c757d;
}


/* Стили для кнопки добавления водителя */
.add-driver-btn {
    position: absolute;
    right: -30px;
    top: 0;
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    border-radius: 3px;
    font-size: 12px;
}

.add-driver-btn i {
    font-size: 12px;
}

/* Стили для модального окна */
.modal {
    overflow-y: auto !important;
}

.modal-open {
    overflow: auto !important;
    padding-right: 0 !important;
}

.modal-dialog {
    margin: 30px auto;
}



.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
}

.modal-content {
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,.5);
}

/* Стили для невалидных полей */
.is-invalid {
    border-color: #dc3545 !important;
    background-color: #fff8f8 !important;
}

.is-invalid:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

/* Стили для блока ошибок */
.quantity-error {
    display: none;
    margin: 10px 0;
    padding: 10px 15px;
    border-radius: 4px;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    font-size: 14px;
    animation: errorShake 0.5s ease-in-out;
}

/* Стили для ошибок продуктов */
.product-error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    padding: 0.25rem 0.5rem;
    background-color: rgba(220, 53, 69, 0.1);
    border-radius: 0.25rem;
    margin-left: 15px;
    margin-right: 15px;
}

/* Анимация для ошибок */
@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.product-error {
    animation: errorShake 0.5s ease-in-out;
}

</style>





<div class="row mb-3">
    <div class="col-md-4">
        <label for="date"><?= Yii::t('app', 'client') ?></label>
        <?= $form->field($model, 'client_id')->dropDownList($clients, [
            'class' => 'form-control select2',
            'prompt' => Yii::t('app', 'select_client')
        ])->label(false) ?>
        <div class="error-message" id="client_id-error"></div>
    </div>

            <div class="col-md-4">

                    <div style="display: flex; align-items: center; margin-bottom: 9px;">
                        <label style="margin-bottom: 0; margin-right: 5px;"><?= Yii::t('app', 'driver') ?></label>
                        <button type="button" class="btn btn-sm btn-success" id="add-driver-btn" title="<?= Yii::t('app', 'add_new_driver') ?>" style="width: 20px; height: 20px; padding: 0; display: flex; align-items: center; justify-content: center; font-size: 10px;">
                            <i class="fas fa-plus" style="font-size: 10px;"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-primary ml-1" id="edit-driver-btn" title="<?= Yii::t('app', 'edit_driver') ?>" style="width: 20px; height: 20px; padding: 0; display: flex; align-items: center; justify-content: center; font-size: 10px; display: none;">
                            <i class="fas fa-edit" style="font-size: 10px;"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger ml-1" id="delete-driver-btn" title="<?= Yii::t('app', 'delete_driver') ?>" style="width: 20px; height: 20px; padding: 0; display: flex; align-items: center; justify-content: center; font-size: 10px; display: none;">
                            <i class="fas fa-trash" style="font-size: 10px;"></i>
                        </button>
                    </div>


                    <div class="driver-input-container">
                        <?= $form->field($model, 'driver')->dropDownList([], [
                            'class' => 'form-control select2',
                            'id' => 'driver-select',
                            'prompt' => Yii::t('app', 'select_driver')
                        ])->label(false) ?>
                    </div>
                    <div class="error-message" id="driver-error"></div>
            </div>

            <div class="col-md-4">
                <label><?= Yii::t('app', 'car_number') ?></label>
                <?= $form->field($model, 'car_number')->textInput([
                    'class' => 'form-control',
                    'id' => 'car-number-input'
                ])->label(false) ?>
                <div class="error-message" id="car_number-error"></div>
            </div>
</div>



<div id="product-rows">

<?php
    $productData = Product::find()
        ->where(['deleted_at' => null])
        ->orderBy([
            'priority' => SORT_ASC,
        ])
        ->all();
    ?>


    <?php foreach ($productData as $product): ?>

        <div class="row mb-1 align-items-center compact-row" data-product-id="<?= $product->id ?>">
            <div class="col-md-2">

                <strong <?= $product->type == Product::TYPE_FIZZY ? 'style="color: red;"' : '' ?>>
                    <?= Html::encode($product->name) ?>
                </strong>

                <input type="hidden"
                    name="InvoiceCreateForm[products][<?= $product->id ?>][product_id]"
                    value="<?= $product->id ?>">
            </div>

            <div class="col-md-5">
                <input type="number"
                    class="form-control quantity-input compact-input <?= $product->type == Product::TYPE_FIZZY ? 'fizzy-input' : '' ?>"
                    name="InvoiceCreateForm[products][<?= $product->id ?>][quantity_block]"
                    placeholder="<?= Yii::t('app', 'quantity_block') ?>"
                    min="0"
                    onkeydown="handleKeyDown(event, this)"
                    oninput="validateAmount(this); calculateQuantity(this);">
            </div>

            <div class="col-md-5">
                <input type="number"
                    class="form-control quantity-input compact-input <?= $product->type == Product::TYPE_FIZZY ? 'fizzy-input' : '' ?>"
                    name="InvoiceCreateForm[products][<?= $product->id ?>][quantity]"
                    placeholder="<?= Yii::t('app', 'quantity') ?>"
                    min="0"
                    oninput="validateAmount(this); updateTotals();">
            </div>

            <div class="col-md-3" style="display:none">
                <input type="number"
                    class="form-control price-input compact-input"
                    name="InvoiceCreateForm[products][<?= $product->id ?>][special_price]"
                    placeholder="<?= Yii::t('app', 'price') ?>"
                    onkeydown="handleKeyDown(event, this)"
                    oninput="validateAmount(this)">
                <input type="hidden"
                    class="total-input"
                    name="InvoiceCreateForm[products][<?= $product->id ?>][total_price]">
            </div>

            <div class="col-md-3" style="display:none">
                <input type="number"
                    class="form-control sell-price-input compact-input"
                    name="InvoiceCreateForm[products][<?= $product->id ?>][sell_price]"
                    placeholder="<?= Yii::t('app', 'sell_price') ?>"
                    onkeydown="handleKeyDown(event, this)"
                    oninput="validateAmount(this); updateTotals()">
            </div>
        </div>
    <?php endforeach; ?>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="alert alert-danger quantity-error" style="display: none;"></div>
        <div class="alert alert-danger bonus-error" style="display: none;"></div>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-12">
        <div class="form-check mb-2">
            <input type="checkbox"
                   class="form-check-input"
                   id="has-bonus"
                   name="InvoiceCreateForm[has_bonus]"
                   value="1"
                   <?= $model->has_bonus ? 'checked' : '' ?>>
            <label class="form-check-label" for="has-bonus"><?= Yii::t('app', 'has_bonus') ?></label>
        </div>
        <div id="bonus-products" style="display: none;">
            <div id="bonus-product-rows">
                <div class="row mb-2 align-items-center" data-bonus-row="0">
                    <div class="col-md-4">
                        <select class="form-control select2 bonus-product-select"
                                name="InvoiceCreateForm[bonus][0][product_id]">
                            <option value=""><?= Yii::t('app', 'select_product') ?></option>
                            <?php foreach ($products as $id => $name): ?>
                                <option value="<?= $id ?>"><?= $name ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="number"
                               class="form-control bonus-quantity-block-input"
                               name="InvoiceCreateForm[bonus][0][quantity_block]"
                               placeholder="<?= Yii::t('app', 'quantity_block') ?>"
                               min="1"
                               onkeydown="restrictInput(event)"
                               oninput="validateAmount(this); calculateBonusQuantity(this);">
                    </div>
                    <div class="col-md-3">
                        <input type="number"
                               class="form-control bonus-quantity-input"
                               name="InvoiceCreateForm[bonus][0][quantity]"
                               placeholder="<?= Yii::t('app', 'quantity') ?>"
                               min="1"
                               onkeydown="restrictInput(event)"
                               oninput="validateAmount(this)">
                    </div>
                    <div class="col-md-2">
                        <div class="btn-group">
                            <button type="button" class="btn btn-success add-bonus-row">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button type="button" class="btn btn-danger remove-bonus-row" style="display:none">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 text-right" style="display:none">
        <h6><?= Yii::t('app', 'total_sum') ?>: <span id="grand-total">0</span></h6>
        <?= Html::hiddenInput('InvoiceCreateForm[total_price]', 0, ['id' => 'total-price-input']) ?>
    </div>
</div>

<div id="one1" data-text="<?= Yii::t("app", "select_product") ?>"></div>
<div id="one2" data-text="<?= Yii::t("app", "quantity") ?>"></div>
<div id="one3" data-text="<?= Yii::t("app", "price") ?>"></div>
<div id="one4" data-text="<?= Yii::t("app", "total") ?>"></div>
<div id="one5" data-text="<?= Yii::t("app", "quantity_block") ?>"></div>



<?php ActiveForm::end(); ?>




<script>
    // Инициализация глобальных переменных
    if (typeof window.productsList === 'undefined') {
        window.productsList = <?= $productsJson ?>;
    }

    // Инициализация кэшей
    window.productSizeCache = window.productSizeCache || {};
    window.elementCache = window.elementCache || {};
    window.priceCache = window.priceCache || {};
    window.ajaxTimeouts = window.ajaxTimeouts || {};
    window.preloadedQuantities = window.preloadedQuantities || {};
    window.cachedElements = window.cachedElements || {};

    window.translations = {
        select_driver: "<?= Yii::t('app', 'select_driver') ?>",
        no_results: "<?= Yii::t('app', 'no_results') ?>",
        add_new_driver: "<?= Yii::t('app', 'add_new_driver') ?>",
        driver_name: "<?= Yii::t('app', 'driver_name') ?>",
        enter_driver_name: "<?= Yii::t('app', 'enter_driver_name') ?>",
        car_number: "<?= Yii::t('app', 'car_number') ?>",
        enter_car_number: "<?= Yii::t('app', 'enter_car_number') ?>",
        cancel: "<?= Yii::t('app', 'cancel') ?>",
        save: "<?= Yii::t('app', 'save') ?>",
        please_enter_driver_name: "<?= Yii::t('app', 'please_enter_driver_name') ?>",
        please_select_client_first: "<?= Yii::t('app', 'please_select_client_first') ?>",
        error_saving_driver: "<?= Yii::t('app', 'error_saving_driver') ?>",
        confirm_delete_driver: "<?= Yii::t('app', 'confirm_delete_driver') ?>",
        error_deleting_driver: "<?= Yii::t('app', 'error_deleting_driver') ?>",
        error_updating_driver: "<?= Yii::t('app', 'error_updating_driver') ?>",
        please_fill_quantity_for_all_products_with_blocks: "<?= Yii::t('app', 'please_fill_quantity_for_all_products_with_blocks') ?>",
        please_fill_at_least_one_product: "<?= Yii::t('app', 'please_fill_at_least_one_product') ?>"
    };

    var one = $('#one1').data('text');
    var two = $('#one2').data('text');
    var three = $('#one3').data('text');
    var four = $('#one4').data('text');
    var five = $('#one5').data('text');

    // Функция для получения DOM-элементов с кэшированием
    function getElement(selector, context = document) {
        const cacheKey = `${context === document ? 'document' : context.id || 'unknown'}_${selector}`;
        if (!window.elementCache[cacheKey]) {
            window.elementCache[cacheKey] = $(selector, context);
        }
        return window.elementCache[cacheKey];
    }

    // Функция для очистки кэша DOM-элементов
    function clearElementCache() {
        Object.keys(window.elementCache).forEach(key => {
            delete window.elementCache[key];
        });
    }

    // Предварительная загрузка данных для часто используемых значений

    // Функция для предварительной загрузки данных
    function preloadQuantities() {
        try {
            // Получаем все уникальные ID продуктов
            const productIds = [];
            $('#product-rows .row').each(function() {
                const productId = $(this).data('product-id');
                if (productId && !productIds.includes(productId)) {
                    productIds.push(productId);
                }
            });

            // Предварительно загружаем данные для часто используемых значений (1-10 блоков)
            productIds.forEach(productId => {
                if (!window.preloadedQuantities[productId]) {
                    window.preloadedQuantities[productId] = {};

                    // Создаем массив запросов для пакетной загрузки
                    const batchRequests = [];
                    for (let i = 1; i <= 10; i++) {
                        batchRequests.push({
                            id: productId,
                            blocks: i
                        });
                    }

                    // Отправляем пакетный запрос
                    $.ajax({
                        url: '/backend/sales-invoice/get-batch-quantities',
                        method: 'POST',
                        data: { requests: JSON.stringify(batchRequests) },
                        dataType: 'json',
                        success: function(response) {
                            if (response && response.results) {
                                response.results.forEach(result => {
                                    if (result.id && result.blocks) {
                                        const cacheKey = `${result.id}_${result.blocks}`;
                                        window.productSizeCache[cacheKey] = result.quantity || 0;
                                        window.preloadedQuantities[result.id][result.blocks] = result.quantity || 0;
                                    }
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Error preloading quantities:', error);
                        }
                    });
                }
            });
        } catch (error) {
            console.error('Error in preloadQuantities:', error);
        }
    }

    function calculateQuantity(input) {
        try {
            const row = $(input).closest('.row');
            const productId = row.data('product-id');
            const quantityInput = row.find('input[name$="[quantity]"]');

            // Добавляем индикатор загрузки
            let loadingIndicator = row.find('.loading-indicator');
            if (loadingIndicator.length === 0) {
                loadingIndicator = $('<div class="loading-indicator"><i class="fas fa-spinner fa-spin"></i></div>');
                row.find('.col-md-3').append(loadingIndicator);
            }
            loadingIndicator.show();

            if (!input.value) {
                quantityInput.val('');
                updateTotals();
                loadingIndicator.hide();
                return;
            }

            // Проверяем кэш
            const cacheKey = `${productId}_${input.value}`;
            if (window.productSizeCache[cacheKey]) {
                quantityInput.val(window.productSizeCache[cacheKey]).removeClass('is-invalid');
                updateTotals();
                loadingIndicator.hide();
                return;
            }

            // Проверяем предварительно загруженные данные
            if (window.preloadedQuantities[productId] && window.preloadedQuantities[productId][input.value]) {
                const quantity = window.preloadedQuantities[productId][input.value];
                quantityInput.val(quantity).removeClass('is-invalid');
                window.productSizeCache[cacheKey] = quantity; // Сохраняем в основной кэш
                updateTotals();
                loadingIndicator.hide();
                return;
            }

            if (productId && input.value) {
                // Отменяем предыдущий запрос, если он еще не выполнен
                if (window.ajaxTimeouts[cacheKey]) {
                    clearTimeout(window.ajaxTimeouts[cacheKey]);
                }

                // Устанавливаем таймаут перед отправкой AJAX-запроса
                window.ajaxTimeouts[cacheKey] = setTimeout(() => {
                    $.ajax({
                        url: '/backend/sales-invoice/get-block-quantity',
                        method: 'GET',
                        data: {
                            id: productId,
                            blocks: input.value
                        },
                        dataType: 'json',
                        success: function (response) {
                            const quantity = response.quantity || 0;
                            quantityInput.val(quantity).removeClass('is-invalid');
                            // Сохраняем в кэш
                            window.productSizeCache[cacheKey] = quantity;
                            // Сохраняем в предварительно загруженные данные
                            if (!window.preloadedQuantities[productId]) {
                                window.preloadedQuantities[productId] = {};
                            }
                            window.preloadedQuantities[productId][input.value] = quantity;
                            updateTotals();
                            loadingIndicator.hide();

                            // Предварительно загружаем следующие значения
                            const nextValue = parseInt(input.value) + 1;
                            const nextCacheKey = `${productId}_${nextValue}`;
                            if (!window.productSizeCache[nextCacheKey]) {
                                $.ajax({
                                    url: '/backend/sales-invoice/get-block-quantity',
                                    method: 'GET',
                                    data: {
                                        id: productId,
                                        blocks: nextValue
                                    },
                                    dataType: 'json',
                                    success: function(nextResponse) {
                                        window.productSizeCache[nextCacheKey] = nextResponse.quantity || 0;
                                        if (!window.preloadedQuantities[productId]) {
                                            window.preloadedQuantities[productId] = {};
                                        }
                                        window.preloadedQuantities[productId][nextValue] = nextResponse.quantity || 0;
                                    }
                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('AJAX error:', error);
                            loadingIndicator.hide();
                        }
                    });
                }, 50); // Уменьшаем задержку до 50 мс для более быстрого отклика
            }
        } catch (error) {
            console.error('Error in calculateQuantity:', error);
            // Запасной вариант в случае ошибки
            try {
                const row = $(input).closest('.row');
                row.find('input[name$="[quantity]"]').val('0');
                row.find('.loading-indicator').hide();
                updateTotals();
            } catch (fallbackError) {
                console.error('Fallback error handling failed:', fallbackError);
            }
        }
    }

    function updateTotals() {
        let grandTotal = 0;

        $('#product-rows .row').each(function () {
            const row = $(this);
            const quantity = parseFloat(row.find('input[name$="[quantity]"]').val()) || 0;
            const specialPrice = parseFloat(row.find('.price-input').val()) || 0;
            const total = quantity * specialPrice;

            row.find('.total-input').val(total);
            grandTotal += total;
        });

        $('#grand-total').text(grandTotal.toLocaleString('ru-RU'));
        $('#total-price-input').val(grandTotal);
    }

    window.bonusRowCount = 1;

    function updateBonusButtons() {
        const rows = $('#bonus-product-rows .row');
        rows.each(function (index) {
            const isFirst = index === 0;
            const isLast = index === rows.length - 1;

            $(this).find('.add-bonus-row').toggle(isLast);
            $(this).find('.remove-bonus-row').toggle(!isFirst);
        });
    }

    function calculateBonusQuantity(input) {
        try {
            const row = $(input).closest('.row');
            const productId = row.find('.bonus-product-select').val();
            const quantityBlockInput = row.find('input[name$="[quantity_block]"]');
            const quantityInput = row.find('input[name$="[quantity]"]');
            const blocks = quantityBlockInput.val();

            // Добавляем индикатор загрузки
            let loadingIndicator = row.find('.loading-indicator');
            if (loadingIndicator.length === 0) {
                loadingIndicator = $('<div class="loading-indicator"><i class="fas fa-spinner fa-spin"></i></div>');
                row.find('.col-md-3').append(loadingIndicator);
            }
            loadingIndicator.show();

            if (!blocks || !productId) {
                quantityInput.val('');
                loadingIndicator.hide();
                return;
            }

            // Проверяем кэш
            const cacheKey = `${productId}_${blocks}`;
            if (window.productSizeCache[cacheKey]) {
                quantityInput.val(window.productSizeCache[cacheKey]).removeClass('is-invalid');
                loadingIndicator.hide();
                return;
            }

            // Проверяем предварительно загруженные данные
            if (window.preloadedQuantities[productId] && window.preloadedQuantities[productId][blocks]) {
                const quantity = window.preloadedQuantities[productId][blocks];
                quantityInput.val(quantity).removeClass('is-invalid');
                window.productSizeCache[cacheKey] = quantity; // Сохраняем в основной кэш
                loadingIndicator.hide();
                return;
            }

            // Отменяем предыдущий запрос, если он еще не выполнен
            if (window.ajaxTimeouts[cacheKey]) {
                clearTimeout(window.ajaxTimeouts[cacheKey]);
            }

            // Устанавливаем таймаут перед отправкой AJAX-запроса
            window.ajaxTimeouts[cacheKey] = setTimeout(() => {
                $.ajax({
                    url: '/backend/sales-invoice/get-block-quantity',
                    method: 'GET',
                    data: {
                        id: productId,
                        blocks: blocks
                    },
                    dataType: 'json',
                    success: function (response) {
                        const quantity = response.quantity || 0;
                        quantityInput.val(quantity).removeClass('is-invalid');
                        // Сохраняем в кэш
                        window.productSizeCache[cacheKey] = quantity;
                        // Сохраняем в предварительно загруженные данные
                        if (!window.preloadedQuantities[productId]) {
                            window.preloadedQuantities[productId] = {};
                        }
                        window.preloadedQuantities[productId][blocks] = quantity;
                        loadingIndicator.hide();

                        // Предварительно загружаем следующие значения
                        const nextValue = parseInt(blocks) + 1;
                        const nextCacheKey = `${productId}_${nextValue}`;
                        if (!window.productSizeCache[nextCacheKey]) {
                            $.ajax({
                                url: '/backend/sales-invoice/get-block-quantity',
                                method: 'GET',
                                data: {
                                    id: productId,
                                    blocks: nextValue
                                },
                                dataType: 'json',
                                success: function(nextResponse) {
                                    window.productSizeCache[nextCacheKey] = nextResponse.quantity || 0;
                                    if (!window.preloadedQuantities[productId]) {
                                        window.preloadedQuantities[productId] = {};
                                    }
                                    window.preloadedQuantities[productId][nextValue] = nextResponse.quantity || 0;
                                }
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('AJAX error:', error);
                        quantityInput.val('0');
                        loadingIndicator.hide();
                    }
                });
            }, 50); // Уменьшаем задержку до 50 мс для более быстрого отклика
        } catch (error) {
            console.error('Error in calculateBonusQuantity:', error);
            // Запасной вариант в случае ошибки
            try {
                const row = $(input).closest('.row');
                row.find('input[name$="[quantity]"]').val('0');
                row.find('.loading-indicator').hide();
            } catch (fallbackError) {
                console.error('Fallback error handling failed:', fallbackError);
            }
        }
    }

    function addBonusProductRow() {
        const bonusRows = document.getElementById('bonus-product-rows');
        const newRow = document.createElement('div');
        newRow.className = 'row mb-2 align-items-center';
        newRow.dataset.bonusRow = window.bonusRowCount;

        const options = Object.entries(window.productsList)
            .map(([id, name]) => `<option value="${id}">${name}</option>`)
            .join('');

        newRow.innerHTML = `
            <div class="col-md-4">
                <select class="form-control select2 bonus-product-select"
                        name="InvoiceCreateForm[bonus][${window.bonusRowCount}][product_id]">
                    <option value="">${one}</option>
                    ${options}
                </select>
                <div class="error-message bonus-product-error"></div>
            </div>
            <div class="col-md-3">
                <input type="number"
                       class="form-control bonus-quantity-block-input"
                       name="InvoiceCreateForm[bonus][${window.bonusRowCount}][quantity_block]"
                       placeholder="${two}"
                       min="1"
                       onkeydown="restrictInput(event)"
                       oninput="validateAmount(this); calculateBonusQuantity(this);">
                <div class="error-message bonus-quantity-block-error"></div>
            </div>
            <div class="col-md-3">
                <input type="number"
                       class="form-control bonus-quantity-input"
                       name="InvoiceCreateForm[bonus][${window.bonusRowCount}][quantity]"
                       placeholder="${two}"
                       min="1"
                       onkeydown="restrictInput(event)"
                       oninput="validateAmount(this)">
                <div class="error-message bonus-quantity-error"></div>
            </div>
            <div class="col-md-2">
                <div class="btn-group">
                    <button type="button" class="btn btn-success add-bonus-row">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button type="button" class="btn btn-danger remove-bonus-row">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
        `;

        bonusRows.appendChild(newRow);

        const select = $(newRow).find('.select2');
        select.select2({
            width: '100%',
            dropdownParent: $('#bonus-products'),
            language: {
                noResults: function () {
                    return translations.no_results;
                }
            }
        });

        initializeBonusRowHandlers(newRow);
        updateBonusButtons();
        window.bonusRowCount++;
    }

    function initializeBonusRowHandlers(container) {
        const row = $(container);

        row.find('.add-bonus-row').on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            addBonusProductRow();
        });

        row.find('.remove-bonus-row').on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            if ($('#bonus-product-rows .row').length > 1) {
                row.remove();
                updateBonusButtons();
            }
        });
    }

    function restrictInput(event) {
        if (event.key === 'e' || event.key === 'E' || event.key === '-' || event.key === '+') {
            event.preventDefault();
        }
    }

    function handleKeyDown(event, input) {
        // Сначала проверяем ограничения на ввод
        restrictInput(event);

        // Если нажат Enter, переходим к следующему полю ввода
        if (event.key === 'Enter') {
            event.preventDefault();

            try {
                // Находим текущую строку и следующую
                const currentRow = $(input).closest('.row');
                const nextRow = currentRow.next('.row');

                // Если есть следующая строка, фокусируемся на поле ввода блоков в ней
                if (nextRow.length > 0) {
                    nextRow.find('input[name*="[quantity_block]"]').focus();
                }
            } catch (error) {
                console.error('Error in handleKeyDown:', error);
                // Запасной вариант, если произошла ошибка
                try {
                    // Находим все поля ввода quantity_block
                    const quantityBlockInputs = document.querySelectorAll('input[name$="[quantity_block]"]');
                    const inputArray = Array.from(quantityBlockInputs);

                    // Находим текущее поле ввода
                    const currentIndex = inputArray.indexOf(event.target);

                    // Если найдено текущее поле и есть следующее поле, перемещаем фокус на него
                    if (currentIndex !== -1 && currentIndex < inputArray.length - 1) {
                        inputArray[currentIndex + 1].focus();
                    }
                } catch (fallbackError) {
                    console.error('Fallback navigation failed:', fallbackError);
                }
            }
        }
    }

    function validateAmount(input) {
        input.value = input.value.replace(/[^0-9.]/g, '');
        if (input.value.startsWith('0')) {
            input.value = '';
        }
    }

    $(document).ready(function () {
        // Предварительная загрузка данных для часто используемых значений
        setTimeout(preloadQuantities, 500); // Запускаем с небольшой задержкой после загрузки страницы
        function restoreFormFields() {
            $('#product-rows .row input, #bonus-product-rows .row select, #bonus-product-rows .row input').prop('disabled', false);
        }

        function validateQuantityFields() {
            $('#product-rows .row').each(function () {
                const row = $(this);
                const quantityInput = row.find('input[name$="[quantity]"]');
                const quantityBlockInput = row.find('input[name$="[quantity_block]"]');

                const quantity = quantityInput.val().trim();
                const quantityBlock = quantityBlockInput.val().trim();

                if (quantityBlock && quantityBlock !== '0' && (!quantity || quantity == '0')) {
                    quantityInput.addClass('is-invalid');
                } else {
                    quantityInput.removeClass('is-invalid');
                }
            });

            if ($('#has-bonus').is(':checked')) {
                $('#bonus-product-rows .row').each(function () {
                    const row = $(this);
                    const productSelect = row.find('select[name$="[product_id]"]');
                    const quantityInput = row.find('input[name$="[quantity]"]');
                    const quantityBlockInput = row.find('input[name$="[quantity_block]"]');

                    const productId = productSelect.val();
                    const quantity = quantityInput.val().trim();
                    const quantityBlock = quantityBlockInput.val().trim();

                    if (productId && quantityBlock && quantityBlock !== '0' && (!quantity || quantity == '0')) {
                        quantityInput.addClass('is-invalid');
                    } else {
                        quantityInput.removeClass('is-invalid');
                    }
                });
            }
        }

        validateQuantityFields();

        $('#product-rows').on('input', 'input[name$="[quantity]"], input[name$="[quantity_block]"]', function () {
            validateQuantityFields();
        });

        $('#bonus-product-rows').on('input', 'input[name$="[quantity]"], input[name$="[quantity_block]"]', function () {
            validateQuantityFields();
        });

        $('#bonus-product-rows').on('change', 'select[name$="[product_id]"]', function () {
            validateQuantityFields();
        });

        $('#bonus-product-rows').on('change', '.bonus-product-select', function () {
            const row = $(this).closest('.row');
            const quantityBlockInput = row.find('input[name$="[quantity_block]"]');
            if (quantityBlockInput.val()) {
                calculateBonusQuantity(quantityBlockInput[0]);
            }
        });

        $('#invoice-create-form').on('submit', function (e) {
            e.preventDefault();
            
            // Предотвращаем множественные отправки
            if ($(this).data('submitting')) {
                return false;
            }
            $(this).data('submitting', true);
            
            let hasFilledProducts = false;
            let hasIncompleteProducts = false;

            $('.is-invalid').removeClass('is-invalid');
            $('.product-error').remove();
            $('#form-errors').hide();

            const submitButton = $(this).find('button[type="submit"]');
            submitButton.prop('disabled', true);

            $('#product-rows .row').each(function () {
                const row = $(this);
                const quantityInput = row.find('input[name$="[quantity]"]');
                const quantityBlockInput = row.find('input[name$="[quantity_block]"]');
                const priceInput = row.find('input[name$="[price]"]');
                const sellPriceInput = row.find('input[name$="[sell_price]"]');
                const totalPriceInput = row.find('input[name$="[total_price]"]');
                const productIdInput = row.find('input[name$="[product_id]"]');

                const quantity = quantityInput.val().trim();
                const quantityBlock = quantityBlockInput.val().trim();

                if (quantityBlock && quantityBlock !== '0' && (!quantity || quantity === '0')) {
                    hasIncompleteProducts = true;
                    quantityInput.addClass('is-invalid');
                } else {
                    quantityInput.removeClass('is-invalid');
                }

                if ((!quantity || quantity === '0') && (!quantityBlock || quantityBlock === '0')) {
                    quantityInput.prop('disabled', true);
                    quantityBlockInput.prop('disabled', true);
                    priceInput.prop('disabled', true);
                    sellPriceInput.prop('disabled', true);
                    totalPriceInput.prop('disabled', true);
                    productIdInput.prop('disabled', true);
                } else {
                    hasFilledProducts = true;
                }
            });

            if (hasIncompleteProducts) {
                displayErrors(translations.please_fill_quantity_for_all_products_with_blocks);
                restoreFormFields();
                $(this).data('submitting', false); // Снимаем блокировку
                return false;
            }

            if (!hasFilledProducts) {
                displayErrors(translations.please_fill_at_least_one_product);
                restoreFormFields();
                $(this).data('submitting', false); // Снимаем блокировку
                return false;
            }

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: new FormData(this),
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.status === 'success') {
                        // Показываем сообщение об успехе
                        iziToast.success({
                            title: 'Успешно',
                            message: 'Накладная успешно создана',
                            position: 'topRight'
                        });
                        
                        // Быстрое перенаправление
                        window.location.href = '/backend/sales-invoice/index';
                    } else {
                        displayErrors(response.message);
                        restoreFormFields();

                        const firstError = $('.product-error').first();
                        if (firstError.length) {
                            $('html, body').animate({
                                scrollTop: firstError.offset().top - 100
                            }, 500);
                        }
                    }
                },
                error: function (xhr, status, error) {
                    displayErrors('Произошла ошибка при отправке формы');
                    restoreFormFields();
                },
                complete: function () {
                    submitButton.prop('disabled', false);
                    $('#invoice-create-form').data('submitting', false); // Снимаем блокировку
                }
            });

            return false;
        });

        const driverSelect = $('#driver-select');
        driverSelect.select2({
            width: '100%',
            allowClear: true,
            placeholder: translations.select_driver,
            minimumInputLength: 0,
            dropdownParent: $('.driver-input-container'),
            language: {
                noResults: function () {
                    return translations.no_results;
                },
                inputTooShort: function() {
                    return "";
                }
            },
            // Используем matcher для обеспечения поиска
            matcher: function(params, data) {
                // Если нет поискового запроса, возвращаем все данные
                if ($.trim(params.term) === '') {
                    return data;
                }

                // Если данные пустые, возвращаем null
                if (typeof data.text === 'undefined') {
                    return null;
                }

                // Поиск без учета регистра
                if (data.text.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                    return data;
                }

                // Если не найдено, возвращаем null
                return null;
            }
        });

        driverSelect.off('select2:opening');
        driverSelect.off('focus');

        // Принудительно активируем поиск при открытии выпадающего списка
        driverSelect.on('select2:open', function() {
            setTimeout(function() {
                $('.select2-search__field').prop('disabled', false);
                $('.select2-search__field').focus();
                $('.select2-dropdown').addClass('show-search');
            }, 50);
        });

        $('#add-driver-btn').on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();

            const modalHtml = `
                <div class="modal fade" id="add-driver-modal" tabindex="-1" role="dialog" aria-labelledby="add-driver-modal-label" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="add-driver-modal-label">${translations.add_new_driver}</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="form-group">
                                    <label for="new-driver-name">${translations.driver_name}</label>
                                    <input type="text" class="form-control" id="new-driver-name" placeholder="${translations.enter_driver_name}">
                                </div>
                                <div class="form-group">
                                    <label for="new-driver-car-number">${translations.car_number}</label>
                                    <input type="text" class="form-control" id="new-driver-car-number" placeholder="${translations.enter_car_number}">
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">${translations.cancel}</button>
                                <button type="button" class="btn btn-primary" id="save-new-driver">${translations.save}</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            if (!$('#add-driver-modal').length) {
                $('body').append(modalHtml);
            }

            $('#add-driver-modal').modal({
                backdrop: true,
                keyboard: true,
                focus: true,
                show: true
            });

            // Очищаем поля ввода при открытии модального окна
            $('#new-driver-name').val('');
            $('#new-driver-car-number').val('');

            $('#add-driver-modal').css('overflow-y', 'auto');

            $('body').css('padding-right', '0');

            $('#add-driver-modal').on('hidden.bs.modal', function () {
                $('body').css('padding-right', '0').removeClass('modal-open');
                $('.modal-backdrop').remove();
            });




            $('#save-new-driver').off('click').on('click', function () {
                const driverName = $('#new-driver-name').val().trim();
                const carNumber = $('#new-driver-car-number').val().trim();

                if (!driverName) {
                    alert(translations.please_enter_driver_name);
                    return;
                }

                const clientId = $('#invoicecreateform-client_id').val();
                if (!clientId) {
                    alert(translations.please_select_client_first);
                    $('#add-driver-modal').modal('hide');
                    return;
                }

                $.ajax({
                    url: '/backend/client-driver/create',
                    method: 'POST',
                    data: {
                        'ClientDriver[client_id]': clientId,
                        'ClientDriver[driver]': driverName,
                        'ClientDriver[car_number]': carNumber
                    },
                    dataType: 'json',
                    success: function (response) {
                        if (response.success) {
                            const newOption = new Option(driverName, response.id, true, true);
                            $(newOption).data('car-number', carNumber);
                            driverSelect.append(newOption).trigger('change');

                            $('#car-number-input').val(carNumber).prop('readonly', true);

                            $('#edit-driver-btn, #delete-driver-btn').show();

                            $('#add-driver-modal').modal('hide');
                        } else {
                            alert(response.message || translations.error_saving_driver);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Error saving driver:', error);
                        alert(translations.error_saving_driver);
                    }
                });
            });
        });

        $('#invoicecreateform-client_id').on('change', function () {
            const clientId = $(this).val();
            if (!clientId) {
                driverSelect.val(null).trigger('change');
                $('#car-number-input').val('').prop('readonly', false);
                $('#edit-driver-btn, #delete-driver-btn').hide();
                return;
            }

            $.ajax({
                url: '/backend/sales-invoice/get-drivers',
                method: 'GET',
                data: { client_id: clientId },
                dataType: 'json',
                success: function (response) {
                    // Destroy existing select2 instance before rebuilding
                    if (driverSelect.hasClass("select2-hidden-accessible")) {
                        driverSelect.select2('destroy');
                    }

                    // Clear and rebuild options
                    driverSelect.empty();
                    driverSelect.append($('<option>', {
                        value: '',
                        text: translations.select_driver
                    }));

                    if (response && response.drivers && typeof response.drivers === 'object') {
                        Object.entries(response.drivers).forEach(([id, data]) => {
                            if (id && data && data.name) {
                                const option = new Option(data.name, id, false, false);
                                $(option).data('car-number', data.car_number || '');
                                driverSelect.append(option);
                            }
                        });
                    }

                    // Reinitialize select2 с тем же подходом, что и при начальной инициализации
                    driverSelect.select2({
                        width: '100%',
                        allowClear: true,
                        placeholder: translations.select_driver,
                        minimumInputLength: 0,
                        dropdownParent: $('.driver-input-container'),
                        language: {
                            noResults: function () {
                                return translations.no_results;
                            },
                            inputTooShort: function() {
                                return "";
                            }
                        },
                        // Используем matcher для обеспечения поиска
                        matcher: function(params, data) {
                            // Если нет поискового запроса, возвращаем все данные
                            if ($.trim(params.term) === '') {
                                return data;
                            }

                            // Если данные пустые, возвращаем null
                            if (typeof data.text === 'undefined') {
                                return null;
                            }

                            // Поиск без учета регистра
                            if (data.text.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                                return data;
                            }

                            // Если не найдено, возвращаем null
                            return null;
                        }
                    });

                    // Принудительно активируем поиск при открытии выпадающего списка
                    driverSelect.off('select2:opening').off('focus');
                    driverSelect.on('select2:open', function() {
                        setTimeout(function() {
                            $('.select2-search__field').prop('disabled', false);
                            $('.select2-search__field').focus();
                            $('.select2-dropdown').addClass('show-search');
                        }, 50);
                    });

                    driverSelect.trigger('change');
                },
                error: function (xhr, status, error) {
                    console.error('Error loading drivers:', error);
                }
            });

            // Добавляем индикатор загрузки цен
            const priceLoadingIndicator = $('<div class="loading-indicator price-loading"><i class="fas fa-spinner fa-spin"></i> Загрузка цен...</div>');
            $('#product-rows').prepend(priceLoadingIndicator);

            // Создаем кэш для цен
            window.priceCache = window.priceCache || {};

            // Проверяем кэш цен для данного клиента
            if (window.priceCache[clientId]) {
                applyPrices(window.priceCache[clientId]);
                priceLoadingIndicator.remove();
            } else {
                // Загружаем цены с сервера
                $.ajax({
                    url: '/backend/sales-invoice/get-prices',
                    method: 'GET',
                    data: { client_id: clientId },
                    dataType: 'json',
                    success: function (prices) {
                        // Сохраняем в кэш
                        window.priceCache[clientId] = prices;
                        // Применяем цены
                        applyPrices(prices);
                    },
                    error: function (xhr, status, error) {
                        console.error('Error loading prices:', error);
                        // Показываем уведомление об ошибке
                        const errorMessage = $('<div class="alert alert-danger">\u041e\u0448\u0438\u0431\u043a\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043a\u0438 \u0446\u0435\u043d. \u041f\u043e\u0436\u0430\u043b\u0443\u0439\u0441\u0442\u0430, \u043f\u043e\u043f\u0440\u043e\u0431\u0443\u0439\u0442\u0435 \u0435\u0449\u0435 \u0440\u0430\u0437.</div>');
                        $('#product-rows').prepend(errorMessage);
                        setTimeout(() => errorMessage.fadeOut(500, function() { $(this).remove(); }), 5000);
                    },
                    complete: function() {
                        // Удаляем индикатор загрузки
                        priceLoadingIndicator.remove();
                    }
                });
            }

            // Функция для применения цен
            function applyPrices(prices) {
                try {
                    if (!prices || typeof prices !== 'object') {
                        console.error('Invalid prices data:', prices);
                        return;
                    }

                    $('#product-rows .row').each(function () {
                        const row = $(this);
                        const productId = row.data('product-id');

                        if (productId && prices[productId]) {
                            const priceInput = row.find('.price-input');
                            const sellPriceInput = row.find('.sell-price-input');

                            // Применяем цены с проверкой
                            if (priceInput.length) {
                                priceInput.val(prices[productId].price || 0);
                            }

                            if (sellPriceInput.length) {
                                sellPriceInput.val(prices[productId].sell_price || 0);
                            }
                        }
                    });

                    // Обновляем итоги
                    updateTotals();
                } catch (error) {
                    console.error('Error applying prices:', error);
                }
            }
        });

        driverSelect.on('change', function () {
            const selectedOption = $(this).find('option:selected');
            const carNumberInput = $('#car-number-input');
            const editBtn = $('#edit-driver-btn');
            const deleteBtn = $('#delete-driver-btn');

            if (selectedOption.length && selectedOption.val()) {
                editBtn.show();
                deleteBtn.show();
                if (selectedOption.data('car-number')) {
                    carNumberInput.val(selectedOption.data('car-number')).prop('readonly', true);
                } else {
                    carNumberInput.val('').prop('readonly', false);
                }
            } else {
                editBtn.hide();
                deleteBtn.hide();
                carNumberInput.val('').prop('readonly', false);
            }
        });

        $('#edit-driver-btn').on('click', function () {
            const selectedOption = driverSelect.find('option:selected');
            const driverId = selectedOption.val();
            if (!driverId) {
                console.log('Водитель не выбран');
                return;
            }

            const modalHtml = `
                <div class="modal fade" id="edit-driver-modal" tabindex="-1" role="dialog" aria-labelledby="edit-driver-modal-label" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="edit-driver-modal-label">${translations.edit_driver}</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="form-group">
                                    <label for="edit-driver-name">${translations.driver_name}</label>
                                    <input type="text" class="form-control" id="edit-driver-name" placeholder="${translations.enter_driver_name}">
                                </div>
                                <div class="form-group">
                                    <label for="edit-driver-car-number">${translations.car_number}</label>
                                    <input type="text" class="form-control" id="edit-driver-car-number" placeholder="${translations.enter_car_number}">
                                </div>
                                <input type="hidden" id="edit-driver-id" value="${driverId}">
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">${translations.cancel}</button>
                                <button type="button" class="btn btn-primary" id="save-edit-driver">${translations.save}</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            if (!$('#edit-driver-modal').length) {
                $('body').append(modalHtml);
            }

            $('#edit-driver-modal').modal({
                backdrop: true,
                keyboard: true,
                focus: true,
                show: true
            });

            $('#edit-driver-modal').css('overflow-y', 'auto');

            $('body').css('padding-right', '0');

            $('#edit-driver-modal').on('hidden.bs.modal', function () {
                $('body').css('padding-right', '0').removeClass('modal-open');
                $('.modal-backdrop').remove();
                $(this).remove(); // Удаляем модальное окно при закрытии
            });

            $('#edit-driver-name').val(selectedOption.text());
            $('#edit-driver-car-number').val(selectedOption.data('car-number'));

            // Перемещаем обработчик события save-edit-driver сюда
            $('#save-edit-driver').on('click', function () {
                const driverId = $('#edit-driver-id').val();
                const driverName = $('#edit-driver-name').val().trim();
                const carNumber = $('#edit-driver-car-number').val().trim();
                const clientId = $('#invoicecreateform-client_id').val();

                if (!driverId) {
                    alert('ID водителя не найден');
                    return;
                }

                if (!driverName) {
                    alert(translations.please_enter_driver_name);
                    return;
                }

                $.ajax({
                    url: '/backend/client-driver/update',
                    method: 'POST',
                    data: {
                        id: driverId,
                        'ClientDriver[client_id]': clientId,
                        'ClientDriver[driver]': driverName,
                        'ClientDriver[car_number]': carNumber
                    },
                    dataType: 'json',
                    success: function (response) {
                        if (response.success) {
                            // После успешного обновления перезагружаем список водителей
                            $.ajax({
                                url: '/backend/sales-invoice/get-drivers',
                                method: 'GET',
                                data: { client_id: clientId },
                                dataType: 'json',
                                success: function (response) {
                                    // Destroy existing select2 instance before rebuilding
                                    if (driverSelect.hasClass("select2-hidden-accessible")) {
                                        driverSelect.select2('destroy');
                                    }

                                    // Clear and rebuild options
                                    driverSelect.empty();
                                    driverSelect.append($('<option>', {
                                        value: '',
                                        text: translations.select_driver
                                    }));

                                    if (response.drivers) {
                                        Object.entries(response.drivers).forEach(([id, data]) => {
                                            const option = new Option(data.name, id, id === driverId, id === driverId);
                                            $(option).data('car-number', data.car_number);
                                            driverSelect.append(option);
                                        });
                                    }

                                    // Reinitialize select2 с тем же подходом, что и при начальной инициализации
                                    driverSelect.select2({
                                        width: '100%',
                                        allowClear: true,
                                        placeholder: translations.select_driver,
                                        minimumInputLength: 0,
                                        dropdownParent: $('.driver-input-container'),
                                        language: {
                                            noResults: function () {
                                                return translations.no_results;
                                            },
                                            inputTooShort: function() {
                                                return "";
                                            }
                                        },
                                        // Используем matcher для обеспечения поиска
                                        matcher: function(params, data) {
                                            // Если нет поискового запроса, возвращаем все данные
                                            if ($.trim(params.term) === '') {
                                                return data;
                                            }

                                            // Если данные пустые, возвращаем null
                                            if (typeof data.text === 'undefined') {
                                                return null;
                                            }

                                            // Поиск без учета регистра
                                            if (data.text.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                                                return data;
                                            }

                                            // Если не найдено, возвращаем null
                                            return null;
                                        }
                                    });

                                    // Принудительно активируем поиск при открытии выпадающего списка
                                    driverSelect.off('select2:opening').off('focus');
                                    driverSelect.on('select2:open', function() {
                                        setTimeout(function() {
                                            $('.select2-search__field').prop('disabled', false);
                                            $('.select2-search__field').focus();
                                            $('.select2-dropdown').addClass('show-search');
                                        }, 50);
                                    });

                                    driverSelect.trigger('change');
                                    $('#car-number-input').val(carNumber).prop('readonly', true);
                                    $('#edit-driver-modal').modal('hide');
                                }
                            });
                        } else {
                            alert(response.message || translations.error_updating_driver);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Error updating driver:', error);
                        alert(translations.error_updating_driver);
                    }
                });
            });
        });

        $('#delete-driver-btn').on('click', function () {
            const selectedOption = driverSelect.find('option:selected');
            const driverId = selectedOption.val();

            if (!driverId) return;

            if (!confirm(translations.confirm_delete_driver)) {
                return;
            }

            $.ajax({
                url: '/backend/client-driver/delete',
                method: 'POST',
                data: { id: driverId },
                dataType: 'json',
                success: function (response) {
                    if (response.success) {
                        selectedOption.remove();
                        driverSelect.trigger('change');

                        $('#car-number-input').val('').prop('readonly', false);
                    } else {
                        alert(response.message || translations.error_deleting_driver);
                    }
                },
error: function (xhr, status, error) {
                    console.error('Error deleting driver:', error);
                    alert(translations.error_deleting_driver);
                }
            });
        });

        $('#has-bonus').on('change', function () {
            $('#bonus-products').toggle(this.checked);
            if (!this.checked) {
                $('#bonus-product-rows').find('select').val(null).trigger('change');
                $('#bonus-product-rows').find('input[type="number"]').val('');
            }
        });

        const firstBonusRow = $('#bonus-product-rows .row').first();
        firstBonusRow.find('.select2').select2({
            width: '100%',
            dropdownParent: $('#bonus-products'),
            language: {
                noResults: function () {
                    return translations.no_results;
                }
            }
        });

        initializeBonusRowHandlers(firstBonusRow);
        updateBonusButtons();

        const clientId = $('#invoicecreateform-client_id').val();
        if (clientId) {
            $.ajax({
                url: '/backend/sales-invoice/get-prices',
                method: 'GET',
                data: { client_id: clientId },
                dataType: 'json',
                success: function (prices) {
                    $('#product-rows .row').each(function () {
                        const row = $(this);
                        const productId = row.data('product-id');
                        if (productId && prices[productId]) {
                            row.find('.price-input').val(prices[productId].price);
                            row.find('.sell-price-input').val(prices[productId].sell_price);
                        }
                    });
                    updateTotals();
                },
                error: function (xhr, status, error) {
                    console.error('Error loading prices:', error);
                }
            });
        }

        function displayErrors(errors) {
            const $quantityError = $('.quantity-error');
            const $productRows = $('#product-rows .row');

            $quantityError.hide().empty();
            $('.product-error').remove();

            if (typeof errors === 'object') {
                if (errors.products) {
                    let errorMessage = errors.products;
                    if (typeof errorMessage === 'string') {
                        const productIdMatch = errorMessage.match(/product #(\d+)/);
                        if (productIdMatch) {
                            const productId = productIdMatch[1];
                            const $productRow = $productRows.filter(`[data-product-id="${productId}"]`);
                            if ($productRow.length) {
                                $productRow.find('input').addClass('is-invalid');
                                $quantityError.html(errorMessage).show();
                                $('html, body').animate({
                                    scrollTop: $productRow.offset().top - 100
                                }, 500);
                            }
                        } else {
                            $quantityError.html(errorMessage).show();
                        }
                    }
                }

                Object.entries(errors).forEach(([field, messages]) => {
                    if (field !== 'products') {
                        const errorText = Array.isArray(messages) ? messages.join(', ') : messages;
                        $(`#${field}-error`).text(errorText).show();
                    }
                });
            } else if (typeof errors === 'string') {
                $quantityError.html(errors).show();
            }
        }
    });
</script>

