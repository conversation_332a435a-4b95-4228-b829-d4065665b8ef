<?php

use yii\db\Migration;

class m250401_000002_fill_unit_type_for_existing_materials extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Устанавливаем значение по умолчанию для всех существующих записей
        $this->update('material', ['unit_type' => 1], 'unit_type IS NULL');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Нет необходимости в откате, так как мы просто заполнили значения по умолчанию
        return true;
    }
}
