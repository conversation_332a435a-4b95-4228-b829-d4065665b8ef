<?php

namespace app\modules\backend\controllers;

use Yii;
use app\modules\backend\models\Worker;
use app\modules\backend\models\WorkerSalary;
use yii\web\Response;
use yii\web\NotFoundHttpException;

/**
 * ArchiveWorkerController осуществляет работу с архивированными (уволенными) работниками.
 */
class ArchiveWorkerController extends BaseController
{
    /**
     * Список архивированных работников.
     * @return string
     */
    public function actionIndex()
    {
        $sql = "SELECT 
                    w.id,
                    w.full_name,
                    w.phone_number,
                    w.address,
                    ws.amount as salary,
                    w.position_id,
                    p.name as position_name,
                    w.created_at,
                    w.deleted_at
                FROM worker w
                LEFT JOIN position p ON p.id = w.position_id
                LEFT JOIN worker_salary ws ON w.id = ws.worker_id 
                    AND ws.deleted_at IS NULL 
                    AND ws.end_date = '9999-12-31'
                WHERE w.deleted_at IS NOT NULL
                ORDER BY w.deleted_at DESC";

        $result = Yii::$app->db->createCommand($sql)->queryAll();

        return $this->render('index', [
            'result' => $result
        ]);
    }

    /**
     * Восстановление работника.
     * GET – форма назначения оклада, POST – обработка.
     * @throws NotFoundHttpException
     */
    public function actionReturn()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $workerId = $postData['worker_id'] ?? null;
            if (!$workerId || !($worker = Worker::findOne($workerId))) {
                throw new NotFoundHttpException('Worker not found');
            }

            $salaryModel = new WorkerSalary();
            $salaryModel->load($postData);
            $salaryModel->worker_id = $workerId;
            $salaryModel->end_date = '9999-12-31';
            $salaryModel->created_at = date('Y-m-d H:i:s');

            if ($salaryModel->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    // Завершаем предыдущую активную запись зарплаты, если была
                    $currentSalary = WorkerSalary::find()
                        ->where(['worker_id' => $workerId, 'end_date' => '9999-12-31'])
                        ->one();
                    if ($currentSalary) {
                        $currentSalary->end_date = date('Y-m-d', strtotime('-1 day', strtotime($salaryModel->start_date)));
                        $currentSalary->deleted_at = date('Y-m-d H:i:s');
                        $currentSalary->save(false);
                    }

                    // Сохраняем новую зарплату
                    $salaryModel->save(false);

                    // Снимаем флаг deleted_at у работника
                    $worker->deleted_at = null;
                    $worker->save(false);

                    $transaction->commit();

                    return ['status' => 'success', 'message' => Yii::t('app', 'worker_restored_successfully')];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return ['status' => 'fail', 'message' => $e->getMessage()];
                }
            }

            return ['status' => 'fail', 'errors' => $salaryModel->getErrors()];
        }

        // GET – показать форму
        $workerId = Yii::$app->request->get('id');
        if (!$workerId || !($worker = Worker::findOne($workerId))) {
            throw new NotFoundHttpException('Worker not found');
        }

        $salaryModel = new WorkerSalary();
        $salaryModel->start_date = date('Y-m-d');

        return [
            'status'  => 'success',
            'content' => $this->renderPartial('return', [
                'worker'      => $worker,
                'salaryModel' => $salaryModel,
            ])
        ];
    }
} 