<?php

namespace app\modules\api\controllers;

use Yii;
use app\common\models\ApiResponse;
use app\common\models\Sales;
use yii\web\ForbiddenHttpException;

class HeawerController extends BaseController
{

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        try {
            if (!Yii::$app->user->can('heawer')) {
                throw new ForbiddenHttpException(Yii::t('app', "You don\'t have permission to access this resource"));
            }
        } catch (ForbiddenHttpException $e) {
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            Yii::$app->response->data = ApiResponse::response(
                $e->getMessage(),
                null,
                403
            );
            Yii::$app->response->statusCode = 403;
            return false;
        }

        return true;
    }
    /**
     * Отображает список начатых заказов (только для просмотра)
     * @return array
     */
    public function actionTracking()
    {
        $params = Yii::$app->request->get();

        $query = (new \yii\db\Query())
            ->select([
                's.id',
                'c.full_name as client_name',
                's.car_number',
                's.driver as driver_full_name',
                's.created_at',
                's.started_at',
                's.status',
                'u.full_name as started_by'
            ])
            ->from(['s' => 'sales'])
            ->leftJoin(['c' => 'client'], 'c.id = s.client_id')
            ->leftJoin(['u' => 'users'], 'u.id = s.sell_user_id')
            ->where(['s.deleted_at' => null])
            ->andWhere(['s.status' => Sales::STATUS_IN_PROGRESS])
            ->orderBy(['s.started_at' => SORT_DESC]);

        if (!empty($params['date'])) {
            $date = date('Y-m-d', strtotime($params['date']));
            $query->andWhere(['>=', 's.started_at', $date . ' 00:00:00'])
                  ->andWhere(['<=', 's.started_at', $date . ' 23:59:59']);
        }

        $sales = $query->all();

        // Получаем детали продаж для каждой записи
        foreach ($sales as &$sale) {
            $detailsSql = "
                SELECT
                    p.name as product_name,
                    p.size,
                    sd.quantity,
                    sd.factory_price,
                    sd.sell_price,
                    sd.total_price
                FROM
                    sales_detail sd
                LEFT JOIN
                    product p ON sd.product_id = p.id
                WHERE
                    sd.sale_id = :sale_id
                    AND sd.deleted_at IS NULL
            ";

            $detailsCommand = Yii::$app->db->createCommand($detailsSql);
            $detailsCommand->bindValue(':sale_id', $sale['id']);

            $details = $detailsCommand->queryAll();
            $sale['products'] = $details;
        }

        return ApiResponse::response("Список начатых заказов", [
            'sales' => $sales
        ]);
    }
}