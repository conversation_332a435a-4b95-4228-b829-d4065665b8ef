<?php 

namespace app\modules\backend\controllers;

use app\common\models\Product;
use app\modules\backend\services\report\ProductSalesReportService;
use Yii;
use yii\helpers\ArrayHelper;

class SalesReportController extends BaseController
{
    public function actionProductSales()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-d')); // Начальная дата
        $endDate = $request->get('end_date', date('Y-m-d')); // Конечная дата
        $startTime = $request->get('start_time', '00:00'); // Начальное время
        $endTime = $request->get('end_time', '23:59'); // Конечное время
        $productId = $request->get('product_id');

        $reportService = new ProductSalesReportService();
        $reportData = $reportService->getProductSalesReport('day', $startDate, $endDate, $productId, $startTime, $endTime);

        $products = ArrayHelper::map(
            Product::find()->where(['IS', 'deleted_at', null])->all(),
            'id',
            'name'
        );

        return $this->render('product-sales', [
            'reportData' => $reportData,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'productId' => $productId,
            'products' => $products,
        ]);
    }
}