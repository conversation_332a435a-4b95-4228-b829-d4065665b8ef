<?php

namespace app\modules\backend\controllers;

use Yii;
use app\modules\backend\models\Users;
use yii\web\NotFoundHttpException;
use yii\data\ArrayDataProvider;
use yii\web\Response;

/**
 * UserController implements the CRUD actions for Users model.
 */
class UserController extends BaseController
{
    public function actionIndex()
    {
        $users = Users::find()->all();

        $dataProvider = new ArrayDataProvider([
            'allModels' => $users,
            'pagination' => [
                'pageSize' => 50,
            ],
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new Users(['scenario' => Users::SCENARIO_CREATE]);
            $model->load(Yii::$app->request->post());

            if ($model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $model->save(false);
                    
                    $auth = Yii::$app->authManager;
                    $role = $auth->getRole($model->role);
                    if ($role) {
                        $auth->assign($role, $model->id);
                    }
                    
                    $transaction->commit();
                
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'user_created_successfully'),
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage(),
                    ];
                }
            } else {
                return [
                    'status' => 'fail',
                    'errors' => $model->getErrors(),
                ];
            }
        } else {
           
            return [
                'status' => 'success',
                'content' => $this->renderPartial('create', [
                    'model' => new Users(['scenario' => Users::SCENARIO_CREATE])
                ])
            ];
        }
    }




   public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $id = Yii::$app->request->post('Users')['id'];
            $model = $this->findModel($id);
            
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'user_not_found')
                ];
            }

            $oldPassword = $model->password;
            $oldRole = $model->role;
            $model->scenario = Users::SCENARIO_UPDATE;
            $model->load(Yii::$app->request->post());
            
            if (empty($model->password)) {
                $model->password = $oldPassword;
            }
            
            if ($model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $model->save(false);

                    if ($oldRole !== $model->role) {
                        $auth = Yii::$app->authManager;
                        $auth->revokeAll($model->id);
                        $newRole = $auth->getRole($model->role);
                        if ($newRole) {
                            $auth->assign($newRole, $model->id);
                        }
                    }

                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'user_updated_successfully'),
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage(),
                    ];
                }
            } else {
                return [
                    'status' => 'fail',
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = $this->findModel($id);
            
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'user_not_found')
                ];
            }

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', ['model' => $model])
            ];
        }
    }






    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $model = Users::findOne($postData['Users']['id']);
            
            if (!$model) {
                return ['status' => 'error', 'message' => Yii::t('app', 'user_not_found')];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $model->deleted_at = date('Y-m-d H:i:s');
                
                if ($model->save(false)) {
                    $auth = Yii::$app->authManager;
                    $auth->revokeAll($model->id);
                    
                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'user_deleted_successfully')
                    ];
                } else {
                    $transaction->rollBack();
                    return [
                        'status' => 'fail',
                        'errors' => $model->getErrors()
                    ];
                }
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Users::findOne($id);
            
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'user_not_found')
                ];
            }

            return [
                'status' => 'success',
                'content' => $this->renderPartial('delete', ['model' => $model])
            ];
        }

        return ['status' => 'error', 'message' => Yii::t('app', 'invalid_request_method')];
    }

    

    protected function findModel($id)
    {
        if (($model = Users::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException(Yii::t('app', 'user_not_found'));
    }
}
