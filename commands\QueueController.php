<?php

namespace app\commands;

use yii\console\Controller;
use yii\console\ExitCode;

/**
 * Контроллер для работы с очередью
 */
class QueueController extends Controller
{
    /**
     * Запуск обработчика очереди
     * 
     * @return int Код завершения
     */
    public function actionRun()
    {
        $this->stdout("Запуск обработчика очереди...\n");
        
        // Запускаем обработчик очереди
        $exitCode = \Yii::$app->queue->run(false);
        
        $this->stdout("Обработчик очереди завершил работу с кодом: $exitCode\n");
        return $exitCode;
    }
    
    /**
     * Запуск обработчика очереди в режиме демона
     * 
     * @return int Код завершения
     */
    public function actionListen()
    {
        $this->stdout("Запуск обработчика очереди в режиме демона...\n");
        
        // Запускаем обработчик очереди в режиме демона
        $exitCode = \Yii::$app->queue->run(true);
        
        $this->stdout("Обработчик очереди завершил работу с кодом: $exitCode\n");
        return $exitCode;
    }
    
    /**
     * Очистка очереди
     * 
     * @return int Код завершения
     */
    public function actionClear()
    {
        $this->stdout("Очистка очереди...\n");
        
        // Очищаем очередь
        \Yii::$app->queue->clear();
        
        $this->stdout("Очередь очищена\n");
        return ExitCode::OK;
    }
}
