<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\LinkPager;

/* @var $this yii\web\View */
/* @var $reportData array */
/* @var $startDate string */
/* @var $endDate string */
/* @var $regionId int|null */
/* @var $status string|null */
/* @var $regions array */
/* @var $pagination \yii\data\Pagination */

$this->title = Yii::t('app', 'security_report');
?>

<style>
    .text-right {
        text-align: right;
    }

    .status-pending {
        color: #ffc107;
        font-weight: bold;
    }

    .status-accepted {
        color: #28a745;
        font-weight: bold;
    }

    .no-wrap {
        white-space: nowrap;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .badge-pending {
        background-color: #ffc107;
        color: #212529;
    }

    .badge-accepted {
        background-color: #28a745;
        color: #fff;
    }

    /* Стили для пагинации */
    .pagination {
        display: flex !important;
        margin-bottom: 0;
    }

    .card-footer {
        display: block !important;
        background-color: #f8f9fa;
        padding: 0.75rem 1.25rem;
    }

    /* Убедимся, что DataTables не скрывает нашу пагинацию */
    .dataTables_wrapper+.card-footer {
        display: block !important;
    }
</style>

<div class="row align-items-center mb-3">
    <div class="col-md-6">
        <h3 class="mb-0"><?= Yii::t('app', 'security_report') ?></h3>
    </div>
    <div class="col-md-6 d-flex justify-content-end">
        <?= Html::beginForm(['security'], 'get', ['class' => 'd-inline-flex align-items-center']) ?>
        <?= Html::dropDownList('region_id', $regionId, ['' => Yii::t('app', 'regions')] + $regions, [
            'class' => 'form-control mr-2',
            'style' => 'width:180px;'
        ]) ?>
        <?= Html::dropDownList('status', $status, [
            '' => Yii::t('app', 'status'),
            'pending' => Yii::t('app', 'pending'),
            'accepted' => Yii::t('app', 'accepted')
        ], [
            'class' => 'form-control mr-2',
            'style' => 'width:140px;'
        ]) ?>
        <?= Html::input('date', 'start_date', $startDate, ['class' => 'form-control mr-2', 'style' => 'width: 160px;', 'placeholder' => 'С']) ?>
        <?= Html::input('date', 'end_date', $endDate, ['class' => 'form-control mr-2', 'style' => 'width: 160px;', 'placeholder' => 'По']) ?>
        <?= Html::submitButton('<i class="fas fa-search"></i> ' . Yii::t('app', 'search'), ['class' => 'btn btn-primary']) ?>
        <?= Html::hiddenInput('page', 1) ?>
        <?= Html::endForm() ?>
    </div>
</div>



<?php $items = $reportData['items'] ?? []; ?>
<?php if (!empty($items)): ?>
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered table-striped table-hover mb-0" id="security-report-table">
                    <thead class="thead-light">
                        <tr>
                            <th>ID</th>
                            <th><?= Yii::t('app', 'car_number') ?></th>
                            <th><?= Yii::t('app', 'driver_name') ?></th>
                            <th><?= Yii::t('app', 'invoice_id') ?></th>
                            <th><?= Yii::t('app', 'description') ?></th>
                            <th><?= Yii::t('app', 'region') ?></th>
                            <th><?= Yii::t('app', 'created_by_security') ?></th>
                            <th><?= Yii::t('app', 'created_at') ?></th>
                            <th><?= Yii::t('app', 'accepted_by') ?></th>
                            <th><?= Yii::t('app', 'accepted_at_security') ?></th>
                            <th class="text-center"><?= Yii::t('app', 'status') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($items as $row): ?>
                            <?php $isAccepted = !empty($row['accepted_at']); ?>
                            <tr>
                                <td><?= Html::encode($row['id']) ?></td>
                                <td><?= Html::encode($row['car_number']) ?></td>
                                <td><?= Html::encode($row['driver_full_name']) ?></td>
                                <td>
                                    <?php if (!empty($row['invoice_id'])): ?>
                                        <a href="<?= Url::to(['/backend/invoice/view', 'id' => $row['invoice_id']]) ?>"
                                            class="text-primary font-weight-bold"
                                            title="<?= Yii::t('app', 'view_invoice') ?>">
                                            #<?= Html::encode($row['invoice_id']) ?>
                                        </a>
                                        <br>
                                        <small class="text-muted">
                                            <?= Yii::$app->formatter->asDecimal($row['invoice_total'], 0) ?> <?= Yii::t('app', 'sum') ?>
                                        </small>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= Html::encode($row['description'] ?: '-') ?></td>
                                <td><?= Html::encode($row['region_name'] ?: '-') ?></td>
                                <td><?= Html::encode($row['add_user_name'] ?: '-') ?></td>
                                <td class="no-wrap"><?= date('d.m.Y H:i', strtotime($row['created_at'])) ?></td>
                                <td><?= Html::encode($row['accepted_user_name'] ?: '-') ?></td>
                                <td class="no-wrap">
                                    <?= $row['accepted_at'] ? date('d.m.Y H:i', strtotime($row['accepted_at'])) : '-' ?>
                                </td>
                                <td class="text-center">
                                    <?php if ($isAccepted): ?>
                                        <span class="badge badge-accepted"><?= Yii::t('app', 'accepted') ?></span>
                                    <?php else: ?>
                                        <span class="badge badge-pending"><?= Yii::t('app', 'pending') ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Пагинация -->
        <div class="card-footer bootstrap-pagination-container">
            <div class="row">
                <div class="col-md-6">
                    <div class="text-muted">
                        <?= Yii::t('app', 'showing') ?> <?= count($items) ?> <?= Yii::t('app', 'of') ?> <?= $reportData['total_count'] ?> <?= Yii::t('app', 'entries') ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <nav aria-label="Page navigation" class="bootstrap-pagination">
                        <?php
                        // Добавляем отладочную информацию
                        echo "<!-- Debug: Total pages: " . ceil($pagination->totalCount / $pagination->pageSize) . " -->\n";
                        echo "<!-- Debug: Current page: " . ($pagination->page + 1) . " -->\n";
                        echo "<!-- Debug: Total count: " . $pagination->totalCount . " -->\n";
                        echo "<!-- Debug: Page size: " . $pagination->pageSize . " -->\n";

                        // Создаем пагинацию вручную
                        $totalPages = ceil($pagination->totalCount / $pagination->pageSize);
                        $currentPage = $pagination->page + 1;

                        echo '<ul class="pagination justify-content-end" id="security-pagination">';

                        // Сохраняем текущие GET параметры
                        $params = Yii::$app->request->get();

                        // Функция для создания URL с параметрами
                        $createUrl = function ($page) use ($params) {
                            $params['page'] = $page;
                            return Url::to(array_merge(['security'], $params));
                        };

                        // Кнопка "Первая страница"
                        if ($currentPage > 1) {
                            echo '<li class="page-item"><a class="page-link" href="' . $createUrl(1) . '">«</a></li>';
                        } else {
                            echo '<li class="page-item disabled"><a class="page-link" href="#">«</a></li>';
                        }

                        // Кнопка "Предыдущая страница"
                        if ($currentPage > 1) {
                            echo '<li class="page-item"><a class="page-link" href="' . $createUrl($currentPage - 1) . '">‹</a></li>';
                        } else {
                            echo '<li class="page-item disabled"><a class="page-link" href="#">‹</a></li>';
                        }

                        // Номера страниц
                        $startPage = max(1, $currentPage - 2);
                        $endPage = min($totalPages, $startPage + 4);

                        for ($i = $startPage; $i <= $endPage; $i++) {
                            if ($i == $currentPage) {
                                echo '<li class="page-item active"><a class="page-link" href="#">' . $i . '</a></li>';
                            } else {
                                echo '<li class="page-item"><a class="page-link" href="' . $createUrl($i) . '">' . $i . '</a></li>';
                            }
                        }

                        // Кнопка "Следующая страница"
                        if ($currentPage < $totalPages) {
                            echo '<li class="page-item"><a class="page-link" href="' . $createUrl($currentPage + 1) . '">›</a></li>';
                        } else {
                            echo '<li class="page-item disabled"><a class="page-link" href="#">›</a></li>';
                        }

                        // Кнопка "Последняя страница"
                        if ($currentPage < $totalPages) {
                            echo '<li class="page-item"><a class="page-link" href="' . $createUrl($totalPages) . '">»</a></li>';
                        } else {
                            echo '<li class="page-item disabled"><a class="page-link" href="#">»</a></li>';
                        }

                        echo '</ul>';
                        ?>
                    </nav>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="alert alert-info text-center">
        <i class="fas fa-info-circle mr-2"></i>
        <?= Yii::t('app', 'no_data_found') ?>
    </div>
<?php endif; ?>

<script>
    // валидация года в date input
    document.querySelectorAll('input[type="date"]').forEach(input => {
        input.addEventListener('input', function(e) {
            let val = e.target.value;
            if (val) {
                let parts = val.split('-');
                if (parts[0].length > 4) {
                    parts[0] = parts[0].slice(0, 4);
                }
                if (parseInt(parts[0]) > 9999) {
                    parts[0] = '9999';
                }
                e.target.value = parts.join('-');
            }
        });
    });
</script> <?php

            // Добавляем скрипт для инициализации после загрузки страницы
            $js = <<<JS
// Этот скрипт выполняется после загрузки страницы
(function() {
    // Функция для инициализации DataTables без пагинации
    function initDataTableWithoutPagination() {
        if (typeof jQuery !== 'undefined' && typeof jQuery.fn.DataTable !== 'undefined') {
            // Находим нашу таблицу
            var securityTable = jQuery('#security-report-table');
            
            // Если таблица уже инициализирована как DataTable, уничтожаем её
            if (jQuery.fn.DataTable.isDataTable(securityTable)) {
                securityTable.DataTable().destroy();
                console.log('Previous DataTable instance destroyed');
            }
            
            // Удаляем класс no-datatable, чтобы разрешить инициализацию
            securityTable.removeClass('no-datatable');
            
            // Инициализируем DataTable с отключенной пагинацией
            securityTable.DataTable({
                "paging": false,      // Отключаем пагинацию DataTable
                "ordering": true,     // Включаем сортировку
                "searching": true,    // Включаем поиск
                "info": false,        // Отключаем информацию о количестве записей
                "responsive": true,   // Включаем адаптивность
                "dom": '<"row mb-2"<"col-sm-12 col-md-6"><"col-sm-12 col-md-6 text-right"f>>rt<"row"<"col-sm-12 col-md-6"><"col-sm-12 col-md-6">>',
                "language": {
                    "search": "Поиск:",
                    "zeroRecords": "Ничего не найдено",
                    "infoEmpty": "Нет данных",
                    "infoFiltered": "(отфильтровано из _MAX_ записей)"
                }
            });
            
            console.log('DataTable initialized without pagination');
        }
    }
    
    // Выполняем функцию с небольшой задержкой, чтобы убедиться, что DOM полностью загружен
    setTimeout(initDataTableWithoutPagination, 100);
    
    // Убедимся, что наша пагинация Bootstrap видна
    setTimeout(function() {
        console.log('Checking pagination visibility...');
        
        // Проверяем, есть ли пагинация
        var paginationContainer = jQuery('#security-pagination');
        console.log('Pagination container found:', paginationContainer.length);
        
        if (paginationContainer.length > 0) {
            // Убедимся, что пагинация видна
            paginationContainer.css('display', 'flex');
            jQuery('.bootstrap-pagination-container').css('display', 'block');
            
            // Проверяем, есть ли страницы для отображения
            var pageItems = paginationContainer.find('li');
            console.log('Pagination items found:', pageItems.length);
            
            if (pageItems.length <= 3) {
                console.log('Not enough pages for pagination');
            } else {
                console.log('Bootstrap pagination is visible with ' + (pageItems.length - 4) + ' pages'); // -4 для первой, последней, предыдущей и следующей
            }
            
            // Выводим HTML пагинации для отладки
            console.log('Pagination HTML:', paginationContainer.parent().html());
        } else {
            console.log('Bootstrap pagination not found, checking other selectors');
            
            // Проверяем другие селекторы
            var altPagination = jQuery('.pagination');
            console.log('Alternative pagination found:', altPagination.length);
            
            if (altPagination.length > 0) {
                console.log('Alternative pagination HTML:', altPagination.parent().html());
                altPagination.css('display', 'flex');
                altPagination.parent().css('display', 'block');
            }
        }
        
        // Убедимся, что DataTables не скрывает нашу пагинацию
        jQuery('.dataTables_paginate').hide();
        
        // Убедимся, что card-footer видим
        jQuery('.card-footer').css('display', 'block !important');
        jQuery('.bootstrap-pagination').css('display', 'block !important');
        
        // Принудительно добавляем стили для отображения пагинации
        var style = document.createElement('style');
        style.type = 'text/css';
        style.innerHTML = `
            .card-footer { display: block !important; }
            .bootstrap-pagination { display: block !important; }
            .pagination { display: flex !important; }
            .dataTables_wrapper + .card-footer { display: block !important; }
        `;
        document.head.appendChild(style);
        
        // Проверяем, есть ли пагинация в родительском документе
        if (window.parent && window.parent.document) {
            try {
                var parentPagination = window.parent.document.querySelector('#security-pagination');
                if (parentPagination) {
                    console.log('Found pagination in parent document');
                    parentPagination.style.display = 'flex';
                }
            } catch (e) {
                console.log('Error accessing parent document:', e);
            }
        }
    }, 200);
})();
JS;

            $this->registerJs($js, \yii\web\View::POS_READY);
            ?>

<?php
// Добавляем скрипт для прямого доступа к странице (без AJAX)
$directAccessJs = <<<JS
// Проверяем, загружена ли страница напрямую или через AJAX
if (window.location.pathname.indexOf('/backend/report/security') !== -1) {
    console.log('Direct access to security report detected');
    
    // Добавляем обработчик для кнопок пагинации
    document.addEventListener('click', function(e) {
        var target = e.target;
        
        // Проверяем, является ли цель кнопкой пагинации
        if (target.classList.contains('page-link') || target.parentElement.classList.contains('page-link')) {
            var pageLink = target.classList.contains('page-link') ? target : target.parentElement;
            var href = pageLink.getAttribute('href');
            
            // Если есть href и он содержит параметр page
            if (href && href.indexOf('page=') !== -1) {
                e.preventDefault();
                
                // Получаем номер страницы
                var pageMatch = href.match(/page=(\d+)/);
                if (pageMatch && pageMatch[1]) {
                    var page = pageMatch[1];
                    
                    // Обновляем URL
                    var newUrl = window.location.pathname + '?page=' + page;
                    window.history.pushState({page: page}, '', newUrl);
                    
                    // Перезагружаем страницу
                    window.location.href = newUrl;
                }
            }
        }
    });
}
JS;

$this->registerJs($directAccessJs, \yii\web\View::POS_END);
?>