<?php
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use app\assets\Select2Asset;
use app\common\models\EquipmentPart;

Select2Asset::register($this);
?>

<div class="part-form">
    <?php $form = ActiveForm::begin(['id' => 'part-create-form', 'options' => ['enctype' => 'multipart/form-data']]); ?>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Name') ?></label>
                <input type="text" class="form-control" name="name" required>
                <div class="invalid-feedback" id="name-error"></div>
            </div>
        </div>        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'photos') ?> <small class="text-muted">(можно выбрать несколько)</small></label>
                <input type="file" class="form-control" name="photos[]" accept="image/*" multiple required>
                <div class="invalid-feedback" id="photos-error"></div>
                
                <!-- Превью выбранных фотографий -->
                <div id="photos-preview" class="mt-2" style="display: none;">
                    <div class="row" id="preview-container"></div>
                </div>
            </div>
        </div>
    </div>



    <div class="row mt-3">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Source Type') ?></label>
                <select class="form-control select2" name="source_type" required>
                    <?php foreach (EquipmentPart::getSourceTypeLabels() as $value => $label): ?>
                        <option value="<?= $value ?>"><?= $label ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="invalid-feedback" id="source_type-error"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Status') ?></label>
                <select class="form-control select2" name="status" required>
                    <?php foreach (EquipmentPart::getStatusLabels() as $value => $label): ?>
                        <option value="<?= $value ?>"><?= $label ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="invalid-feedback" id="status-error"></div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'equipment') ?></label>
                <select class="form-control select2" name="equipment_id">
                    <option value=""><?= Yii::t('app', 'Not attached') ?></option>
                    <?php foreach ($equipments as $equipment): ?>
                        <option value="<?= $equipment->id ?>"><?= Html::encode($equipment->name) ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="invalid-feedback" id="equipment_id-error"></div>
            </div>
        </div>
        <div class="col-md-6">
            <!-- Пустая колонка для выравнивания -->
        </div>
    </div>

    <div class="row mt-3" id="installation-date-container" style="display: none;">
        <div class="col-md-12">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Installation Date') ?></label>
                <input type="date" class="form-control" name="installation_date">
                <div class="invalid-feedback" id="installation_date-error"></div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Currency') ?></label>
                <select class="form-control select2" name="currency_id">
                    <option value="">-</option>
                    <?php foreach ($currencies as $currency): ?>
                        <option value="<?= $currency->id ?>"><?= Html::encode($currency->name) ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="invalid-feedback" id="currency_id-error"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Unit Type') ?></label>
                <select class="form-control select2" name="unit_type" required>
                    <?php foreach (EquipmentPart::unitsList() as $value => $label): ?>
                        <option value="<?= $value ?>"><?= $label ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="invalid-feedback" id="unit_type-error"></div>
            </div>
        </div>
    </div>

    <!-- Поля для прихода запчастей -->
    <div class="row mt-3">
        <div class="col-md-12">
            <h6 class="text-muted border-bottom pb-2"><?= Yii::t('app', 'Income Information') ?> </h6>
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'income_quantity') ?></label>
                <input type="number" class="form-control" name="income_quantity" min="0" step="1" placeholder="<?= Yii::t('app', 'Enter initial quantity') ?>">
                <div class="invalid-feedback" id="income_quantity-error"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'price_per_unit') ?></label>
                <input type="number" class="form-control" name="income_price_per_unit" step="0.01" min="0" placeholder="<?= Yii::t('app', 'Enter price per unit') ?>">
                <div class="invalid-feedback" id="income_price_per_unit-error"></div>
            </div>
        </div>
    </div>


    <div class="row mt-3">
        <div class="col-md-12">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'comment') ?></label>
                <textarea class="form-control" name="comment" rows="3" style="min-height: 100px; resize: vertical;"></textarea>
                <div class="invalid-feedback" id="comment-error"></div>
            </div>
        </div>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<style>
.invalid-feedback {
    display: block;
    margin-top: 5px;
}

/* Исправляем стили Select2 */
.select2-container--bootstrap4 .select2-selection--single {
    background-color: #fff !important;
    border: 1px solid #ced4da !important;
    height: calc(1.5em + 0.75rem + 2px) !important;
}

.select2-container--bootstrap4 .select2-selection__rendered {
    color: #495057 !important;
    line-height: calc(1.5em + 0.75rem) !important;
}

.select2-container--bootstrap4 .select2-search__field {
    background-color: #fff !important;
    color: #495057 !important;
}

.select2-dropdown {
    background-color: #fff !important;
    border: 1px solid #ced4da !important;
}
</style>

<script>
    function restrictInput(event) {
        if (event.key === 'e' || event.key === 'E' || event.key === '-' || event.key === '+') {
            event.preventDefault();
        }
    }

    function validateAmount(input) {
        input.value = input.value.replace(/[^0-9.]/g, '');
        if (input.value.startsWith('0')) {
            input.value = '';
        }
    }

    $(document).ready(function() {
        // Показывать/скрывать поле даты установки в зависимости от выбора оборудования
        $('select[name="equipment_id"]').on('change', function() {
            if ($(this).val()) {
                $('#installation-date-container').show();
                $('input[name="installation_date"]').prop('required', true);
            } else {
                $('#installation-date-container').hide();
                $('input[name="installation_date"]').prop('required', false);
            }
        });

        // Инициализация при загрузке
        if ($('select[name="equipment_id"]').val()) {
            $('#installation-date-container').show();
            $('input[name="installation_date"]').prop('required', true);
        }

        // Валидация полей прихода
        $('input[name="income_quantity"]').on('input', function() {
            var quantity = parseFloat($(this).val());
            var priceField = $('input[name="income_price_per_unit"]');
            
            if (quantity > 0) {
                // Если указано количество, делаем цену рекомендуемой (но не обязательной)
                priceField.closest('.form-group').find('label').html('<?= Yii::t("app", "price_per_unit") ?> <small class="text-info">(<?= Yii::t("app", "recommended") ?>)</small>');
            } else {
                // Если количество не указано, убираем пометку с цены
                priceField.closest('.form-group').find('label').html('<?= Yii::t("app", "price_per_unit") ?>');
            }
        });

        // Ограничение ввода только положительных чисел для количества прихода
        $('input[name="income_quantity"]').on('keypress', function(e) {
            // Разрешаем только цифры, backspace, delete, tab, escape, enter
            if ([46, 8, 9, 27, 13].indexOf(e.keyCode) !== -1 ||
                // Разрешаем Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                (e.keyCode === 65 && e.ctrlKey === true) ||
                (e.keyCode === 67 && e.ctrlKey === true) ||
                (e.keyCode === 86 && e.ctrlKey === true) ||
                (e.keyCode === 88 && e.ctrlKey === true)) {
                return;
            }
            // Запрещаем все остальное кроме цифр
            if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                e.preventDefault();
            }
        });

        // Инициализация Select2 с правильными настройками
        $('.select2').select2({
            width: '100%',
            placeholder: 'Выберите...',
            dropdownParent: $('#ideal-mini-modal'),
            language: {
                noResults: function() {
                    return "Ничего не найдено";
                },
                searching: function() {
                    return "Поиск...";
                }
            },
            minimumInputLength: 0,
            minimumResultsForSearch: 1
        });
    });
</script>

