<?php

namespace app\modules\api\models;

use app\common\models\Material;
use Yii;
use yii\base\Model;

class SendToMaterialDefectForm extends Model
{    public $materials;
    public $description;
    public $is_returned;
     public function rules()
    {        return [
            [['materials'], 'required'],
            ['materials', 'validateMaterials'],
            ['description', 'string'],
            ['is_returned', 'boolean'],
            ['is_returned', 'default', 'value' => false],
        ];
    }

    public function validateMaterials($attribute, $params)
    {
        if (!is_array($this->materials)) {
            $this->addError($attribute, 'Materials должен быть массивом');
            return;
        }

        foreach ($this->materials as $index => $material) {
            if (!isset($material['material_id']) || !isset($material['quantity'])) {
                $this->addError($attribute, "Материал #{$index} должен содержать material_id и quantity");
                continue;
            }

            if (!is_numeric($material['material_id']) || $material['material_id'] <= 0) {
                $this->addError($attribute, "Неверный material_id в материале #{$index}");
                continue;
            }

            // Проверяем, что количество является числом и больше 0
            if (!is_numeric($material['quantity']) || floatval($material['quantity']) <= 0) {
                $this->addError($attribute, "Количество должно быть больше 0 в материале #{$index}");
                continue;
            }

            // Получаем тип единицы измерения материала
            $materialModel = Material::findOne($material['material_id']);
            if (!$materialModel) {
                $this->addError($attribute, "Материал с ID {$material['material_id']} не найден");
                continue;
            }

            // Для штук (UNIT_TYPE_PIECE) проверяем, что количество целое
            if ($materialModel->unit_type == Material::UNIT_TYPE_PIECE && floor(floatval($material['quantity'])) != floatval($material['quantity'])) {
                $this->addError($attribute, "Для штучных материалов количество должно быть целым числом в материале #{$index}");
            }
        }
    }
}
