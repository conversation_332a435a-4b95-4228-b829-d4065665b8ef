<?php

use yii\db\Migration;

class m250306_061016_add_material_currency_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('material_currency', [
            'id' => $this->primaryKey(),
            'material_id' => $this->integer()->null(),
            'currency_id' => $this->integer()->null(),
            'price' => $this->decimal(10, 2)->null(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk_material_currency_material',
            'material_currency',
            'material_id',
            'material',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_material_currency_currency',
            'material_currency',
            'currency_id',
            'currency',
            'id',
            'CASCADE'
        );

        $this->addColumn('invoice_detail', 'currency_id', $this->integer()->null());

        $this->addForeignKey(
            'fk_invoice_detail_currency',
            'invoice_detail',
            'currency_id',
            'currency',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk_material_currency_material', 'material_currency');
        $this->dropForeignKey('fk_material_currency_currency', 'material_currency');
        $this->dropForeignKey('fk_invoice_detail_currency', 'currency');
        $this->dropColumn('invoice_detail', 'currency_id');
        $this->dropTable('material_currency');
    }

   
}
