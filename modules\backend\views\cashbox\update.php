<?php
use yii\widgets\ActiveForm;
?>

<?php $form = ActiveForm::begin([
    'id' => 'cashbox-update-form',
    'enableAjaxValidation' => false,
    'enableClientValidation' => true,
]); ?>
    <?= $form->field($model, 'id')->hiddenInput()->label(false) ?>
    
    <div class="row">
        <div class="col-md-12">
            <?= $form->field($model, 'title')->textInput([
                'maxlength' => true,
                'placeholder' => Yii::t('app', 'enter_cashbox_title'),
                'class' => 'form-control'
            ])->label(Yii::t('app', 'cashbox_title')) ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <?= $form->field($model, 'currency_id')->dropDownList(
                \yii\helpers\ArrayHelper::map($currencies, 'id', 'name'),
                [
                    'prompt' => Yii::t('app', 'select_currency'),
                    'class' => 'form-control'
                ]
            )->label(Yii::t('app', 'currency')) ?>
        </div>
    </div>


<?php ActiveForm::end(); ?> 