<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use app\modules\backend\models\Users;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "users");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('product_keeper')): ?>
                <a href="#" class="btn btn-primary user-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "add_user") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'user-grid-pjax']); ?>
    <?php if($dataProvider->getModels()): ?>
        <div class="table-responsive">
            <table id="user-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th>#</th>
                        <th><?= Yii::t('app', 'full_name') ?></th>
                        <th><?= Yii::t('app', 'username') ?></th>
                        <th><?= Yii::t('app', 'role') ?></th>
                        <th><?= Yii::t('app', 'status') ?></th>
                        <th><?= Yii::t('app', 'created_at') ?></th>
                        <th><?= Yii::t('app', 'actions') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($dataProvider->getModels() as $index => $model): ?>
                    <tr>
                        <td><?= $index + 1 ?></td>
                        <td><?= Html::encode($model->full_name) ?></td>
                        <td><?= Html::encode($model->username) ?></td>
                        <td>
                            <?php
                            $roles = Users::getRoles();
                            $authManager = Yii::$app->authManager;
                            $userRoles = $authManager->getRolesByUser($model->id);
                            $roleName = !empty($userRoles) ? reset($userRoles)->name : null;
                            $roleLabel = isset($roles[$roleName]) ? $roles[$roleName] : Yii::t('app', 'unknown_role');
                            $roleClass = $roleName === 'admin' ? 'badge badge-danger' : 'badge badge-info';
                            echo Html::tag('span', Html::encode($roleLabel), ['class' => $roleClass]);
                            ?>
                        </td>
                        <td>
                            <?php
                            $statusClass = $model['deleted_at'] == NULL ? 'badge badge-success' : 'badge badge-danger';
                            $statusText = $model['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                            echo Html::tag('span', $statusText, ['class' => $statusClass]);
                            ?>
                        </td>
                        <td data-order="<?= !empty($model->created_at) ? strtotime($model->created_at) : '' ?>">
                            <?= !empty($model->created_at) ? Html::encode(date("d.m.Y h:i", strtotime($model->created_at))) : 'N/A' ?>
                        </td>

                        <td>
                        <?php
                            $currentUserRoles = $authManager->getRolesByUser(Yii::$app->user->id);
                            $currentUserRole = !empty($currentUserRoles) ? reset($currentUserRoles)->name : null;

                            $userRoles = $authManager->getRolesByUser($model->id);
                            $userRole = !empty($userRoles) ? reset($userRoles)->name : null;

                            // Разрешить product_keeper и admin управлять всеми, кроме admin
                            $canManageUser = in_array($currentUserRole, ['admin', 'product_keeper']) && $userRole !== 'admin';

                            if ($canManageUser):

                            ?>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                    <?= Yii::t("app", "actions") ?>
                                </button>
                                <div class="dropdown-menu">
                                    <?php if ($userRole !== 'admin' || $currentUserRole === 'admin'): ?>
                                        <a href="#" class="dropdown-item user-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model->id) ?>">
                                            <i class="fas fa-edit"></i> <?= Yii::t("app", "edit") ?>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <?php if ($model->deleted_at == NULL && ($userRole !== 'admin' || $currentUserRole === 'admin')): ?>
                                        <div class="dropdown-divider"></div>
                                        <a href="#" class="dropdown-item text-danger user-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model->id) ?>">
                                            <i class="fas fa-trash"></i> <?= Yii::t("app", "delete") ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </td>
                        
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> <?= Yii::t('app', 'no_data_available') ?>
        </div>
    <?php endif; ?>
    <?php Pjax::end(); ?>

</div>


<div id="one" data-text="<?= Yii::t("app", "add_user") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "edit_user") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "delete_user") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#user-grid-view')) {
            $('#user-grid-view').DataTable().destroy();
        }
        
        $('#user-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[5, 'desc']], 
            "columnDefs": [
                {
                    "targets": [6], 
                    "orderable": false
                }
            ]
        });
    }

    function initializeAll() {
        initializeDataTable();
        initializeDropdown();
        initializeSelect2();
        initializeUserCreate();
        initializeUserUpdate();
        initializeUserDelete();
    }

    initializeAll();

    $(document).on('pjax:success', function() {
        initializeAll();
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }
    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }

    function initializeUserCreate() {
        $(document).off('click.user-create').on('click.user-create', '.user-create', function() {
            $.ajax({
                url: '/backend/user/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("user-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.user-create-button').on('click.user-create-button', '.user-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#user-form').serialize();
                $.ajax({
                    url: '/backend/user/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('#ideal-mini-modal').modal('hide');
                            $.pjax.reload({container: '#user-grid-pjax'});
                        } else if (response && response.errors) {
                            button.prop('disabled', false);
                            $('.help-block').html('');
                            $.each(response.errors, function(field, errors) {
                                $('#' + field).next('.help-block').html(errors.join('<br>'));
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeUserUpdate() {
        $(document).off('click.user-update').on('click.user-update', '.user-update', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/user/update',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(two);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("user-update-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.user-update-button').on('click.user-update-button', '.user-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#user-update-form').serialize();
                $.ajax({
                    url: '/backend/user/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('#ideal-mini-modal').modal('hide');
                            $.pjax.reload({container: '#user-grid-pjax'});
                        } else if (response && response.errors) {
                            button.prop('disabled', false);
                            $('.help-block').html('');
                            $.each(response.errors, function(field, errors) {
                                $('#' + field).next('.help-block').html(errors.join('<br>'));
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializeUserDelete() {
        $(document).off('click.user-delete').on('click.user-delete', '.user-delete', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/user/delete',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal-delete .modal-title').html(three);
                    $('#ideal-mini-modal-delete .modal-body').html(response.content);
                    $('#ideal-mini-modal-delete .mini-button').addClass("user-delete-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.user-delete-button').on('click.user-delete-button', '.user-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#user-delete-form').serialize();
                $.ajax({
                    url: '/backend/user/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('#ideal-mini-modal-delete').modal('hide');
                            $.pjax.reload({container: '#user-grid-pjax'});
                        }
                        button.prop('disabled', false);
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
