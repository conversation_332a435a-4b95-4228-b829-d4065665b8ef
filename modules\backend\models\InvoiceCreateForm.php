<?php

namespace app\modules\backend\models;

use Yii;
use yii\base\Model;

class InvoiceCreateForm extends Model
{
    public $id;
    public $client_id;
    public $driver_id;
    public $products = [];
    public $driver;
    public $total_price;
    public $car_number;
    public $bonus = [];
    public $has_bonus;

    public function rules()
    {
        return [
            [['client_id', 'products'], 'required'],
            [['client_id', 'id'], 'integer'],
            [['total_price'], 'number'],
            [['car_number'], 'string', 'max' => 20],
            [['driver'], 'string', 'max' => 255],
            ['products', 'validateProducts'],
            ['bonus', 'validateBonus', 'skipOnEmpty' => false],

            ['has_bonus', 'boolean'],
            ['bonus', 'validateBonus', 'when' => function ($model) {
                return $model->has_bonus;
            }, 'whenClient' => "function (attribute, value) {
                return $('#has-bonus').is(':checked');
            }"],
        ];
    }

    public function validateProducts($attribute, $params)
    {
        if (!is_array($this->products) || empty($this->products)) {
            $this->addError($attribute, Yii::t('app', 'fill_all_fields'));
            return;
        }

        foreach ($this->products as $product) {

            if (empty($product['product_id']) || empty($product['quantity']) || empty($product['quantity_block'])) {
                $this->addError($attribute, Yii::t('app', 'fill_all_fields'));
                return;
            }

            if ($product['quantity'] < 1) {
                $this->addError($attribute, Yii::t('app', 'quantity_error'));
                return;
            }
            if($product['special_price'] < 1) {
                $this->addError($attribute, Yii::t('app', 'price_error'));
                return;
            }
            if($product['sell_price'] < 1) {
                $this->addError($attribute, Yii::t('app', 'sell_price_error'));
                return;
            }
        }
    }

    public function validateBonus($attribute, $params)
    {
        if (!$this->has_bonus) {
            return;
        }

        if (!is_array($this->bonus) || empty($this->bonus)) {
            $this->addError($attribute, Yii::t('app', 'fill_all_fields'));
            return;
        }
        
        foreach ($this->bonus as $index => $bonus) {
            if (empty($bonus['product_id']) || empty($bonus['quantity'])) {
                $this->addError("bonus[$index][product_id]", Yii::t('app', 'fill_all_fields'));
                return;
            }

            if ($bonus['quantity'] < 1) {
                $this->addError("bonus[$index][quantity]", Yii::t('app', 'quantity_error'));
                return;
            }
        }
    }

    public function attributeLabels()
    {
        return [
            'client_id' => Yii::t('app', 'client'),
            'products' => Yii::t('app', 'products'),
            'driver' => Yii::t('app', 'driver'),
            'car_number' => Yii::t('app', 'car number')
        ];

    }
}