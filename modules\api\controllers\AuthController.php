<?php

namespace app\modules\api\controllers;

use Yii;
use app\common\models\ApiResponse;
use app\modules\api\models\Users;
use Swagger\Annotations as SWG;

/**
 * Контроллер аутентификации
 *
 * @SWG\Tag(
 *     name="Auth",
 *     description="Аутентификация пользователей"
 * )
 */
class AuthController extends BaseController
{

    protected function allowedActions()
    {
        return ['login'];
    }

    /**
     * Вход в систему
     *
     * @SWG\Post(
     *     path="/api/auth/login",
     *     summary="Вход в систему",
     *     description="Аутентификация пользователя и получение токена доступа",
     *     tags={"Auth"},
     *     @SWG\Parameter(
     *         name="body",
     *         in="body",
     *         required=true,
     *         description="Учетные данные пользователя",
     *         @SWG\Schema(
     *             required={"username", "password"},
     *             @SWG\Property(property="username", type="string", example="admin"),
     *             @SWG\Property(property="password", type="string", example="password")
     *         )
     *     ),
     *     @SWG\Response(
     *         response=200,
     *         description="Успешная аутентификация",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="Login successful"),
     *             @SWG\Property(
     *                 property="data",
     *                 type="object",
     *                 @SWG\Property(property="id", type="integer", example=1),
     *                 @SWG\Property(property="role", type="string", example="admin"),
     *                 @SWG\Property(property="access_token", type="string", example="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...")
     *             ),
     *             @SWG\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @SWG\Response(
     *         response=400,
     *         description="Ошибка валидации",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="Validation error"),
     *             @SWG\Property(
     *                 property="data",
     *                 type="object",
     *                 @SWG\Property(
     *                     property="username",
     *                     type="array",
     *                     @SWG\Items(type="string", example="Username cannot be blank")
     *                 )
     *             ),
     *             @SWG\Property(property="code", type="integer", example=400)
     *         )
     *     ),
     *     @SWG\Response(
     *         response=401,
     *         description="Неверные учетные данные",
     *         @SWG\Schema(
     *             type="object",
     *             @SWG\Property(property="message", type="string", example="Invalid username or password"),
     *             @SWG\Property(property="data", type="object"),
     *             @SWG\Property(property="code", type="integer", example=401)
     *         )
     *     )
     * )
     */
    public function actionLogin()
    {
        $model = new Users();
        $model->load(Yii::$app->request->post(), '');

        if (!$model->validate(['username', 'password'])) {
            return ApiResponse::response(
                Yii::t('app', 'Validation error'),
                $model->getErrors(),
                ApiResponse::HTTP_BAD_REQUEST
            );
        }

        $username = $model->username;
        $password = $model->password;

        $user = Users::findOne(['username' => $username]);

        if ($user && $user->validatePassword($password)) {
            if (empty($user->access_token)) {
                $user->generateAccessToken();
                $user->save(false);
            }

            return ApiResponse::response(
                Yii::t('app', 'Login successful'),
                [
                    'id' => $user->id,
                    'role' => $user->getRoleName(),
                    'access_token' => $user->access_token,
                ],
                ApiResponse::HTTP_OK
            );
        }

        return ApiResponse::response(
            Yii::t('app', 'Invalid username or password'),
            null,
            ApiResponse::HTTP_UNAUTHORIZED
        );
    }





}
