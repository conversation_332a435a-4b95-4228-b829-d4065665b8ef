<?php
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\ArrayHelper;
use app\common\models\Material;
use app\common\models\Supplier;
use app\assets\Select2Asset;

Select2Asset::register($this);

$this->registerJs("
    $(document).ready(function() {
        $('#supplier_id, #material_id').select2({
            width: '100%',
            dropdownParent: $('#material-create-form')
        });
    });
");
?>

<div class="material-form">
    <?php $form = ActiveForm::begin(['id' => 'material-create-form']); ?>
    
    <div class="row">
        <div class="col-md-12">
            <?= $form->field($model, 'supplier_id')->dropDownList(
                ArrayHelper::map(Supplier::find()->where(['deleted_at' => null])->all(), 'id', 'full_name'),
                [
                    'prompt' => Yii::t('app', 'select_supplier'),
                    'id' => 'supplier_id',
                    'class' => 'form-control select2'
                ]
            ) ?>
            <div id="supplier_id-error" class="error-message"></div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-6">
            <?= $form->field($model, 'material_id')->dropDownList(
                ArrayHelper::map(Material::find()->all(), 'id', 'name'),
                [
                    'prompt' => Yii::t('app', 'select_material'),
                    'id' => 'material_id',
                    'class' => 'form-control select2'
                ]
            ) ?>
            <div id="material_id-error" class="error-message"></div>
        </div>
        
        <div class="col-md-6">
            <?= $form->field($model, 'quantity')->textInput([
                'type' => 'number', 
                'min' => '1',
                'id' => 'quantity'
            ]) ?>
            <div id="quantity-error" class="error-message"></div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-12">
            <?= $form->field($model, 'description')->textarea([
                'rows' => 3,
                'id' => 'description'
            ]) ?>
            <div id="description-error" class="error-message"></div>
        </div>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<style>
.error-message {
    color: red;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
</style>
