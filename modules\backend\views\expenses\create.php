<?php
use app\common\models\Cashbox;
?>

<div class="expenses-form">
    <form id="expenses-form" method="post">
        <div class="form-group">
            <label for="expense_type_id"><?= Yii::t('app', 'Expense type') ?></label>
            <select id="expense_type_id" name="Expenses[expense_type_id]" class="form-control select2" required>
                <option value=""><?= Yii::t('app', 'select_expense_type') ?></option>
                <?php foreach ($expenseTypes as $expenseType): ?>
                    <option value="<?= $expenseType->id ?>"><?= $expenseType->name ?></option>
                <?php endforeach; ?>
            </select>
            <div class="help-block"></div> 
        </div>

        <div class="form-group">
            <label for="summa"><?= Yii::t('app', 'amount') ?></label>
            <input type="text" id="summa" name="Expenses[summa]" class="form-control formatted-numeric-input" step="0.01" required>
            <div class="help-block"></div> 
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="cashbox_id"><?= Yii::t('app', 'cashbox') ?></label>
                    <select id="cashbox_id" name="Expenses[cashbox_id]" class="form-control select2" required>
                        <option value=""><?= Yii::t('app', 'select') ?></option>
                        <?php
                        $cashboxes = Cashbox::find()->where(['deleted_at' => null])->all();
                        foreach ($cashboxes as $cashbox): ?>
                            <option value="<?= $cashbox->id ?>">
                                <?= $cashbox->title ?> (<?= $cashbox->getPaymentTypesString() ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="help-block"></div> 
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="payment_type"><?= Yii::t('app', 'Payment type') ?></label>
                    <select id="payment_type" name="Expenses[payment_type]" class="form-control select2" required>
                        <option value=""><?= Yii::t('app', 'select_type') ?></option>
                        <!-- Опции будут загружены динамически через JavaScript -->
                    </select>
                    <div class="help-block"></div>
                </div>
            </div>
        </div>

       

        <div class="form-group">
            <label for="description"><?= Yii::t('app', 'description') ?></label>
            <textarea id="description" name="Expenses[description]" class="form-control" rows="3"></textarea>
            <div class="help-block"></div> 
        </div>
    </form>
    <div id="form-error-message" style="display: none;"></div>
</div>

<script>

$(document).ready(function() {
    $('.select2').select2({
        width: '100%',
        placeholder: '<?= Yii::t("app", "select") ?>',
        allowClear: true,
        dropdownParent: $('#ideal-mini-modal'),
        language: {
            noResults: function() {
                return "<?= Yii::t('app', 'Nothing found') ?>";
            },
            searching: function() {
                return "<?= Yii::t('app', 'Searching...') ?>";
            }
        },
        minimumInputLength: 0,
        minimumResultsForSearch: 1
    });

    // Функция для загрузки доступных типов платежей по выбранной кассе
    function loadPaymentTypesByCashbox(cashboxId) {
        if (!cashboxId) {
            $('#payment_type').empty().append('<option value=""><?= Yii::t("app", "select_type") ?></option>');
            return;
        }

        $.ajax({
            url: '<?= \yii\helpers\Url::to(['/backend/expenses/get-payment-types-by-cashbox']) ?>',
            type: 'GET',
            data: { cashbox_id: cashboxId },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    var paymentTypeSelect = $('#payment_type');
                    paymentTypeSelect.empty();
                    paymentTypeSelect.append('<option value=""><?= Yii::t("app", "select_type") ?></option>');

                    $.each(response.payment_types, function(index, paymentType) {
                        paymentTypeSelect.append(
                            '<option value="' + paymentType.type + '">' + paymentType.label + '</option>'
                        );
                    });

                    // Обновляем select2
                    paymentTypeSelect.trigger('change');
                } else {
                    console.error('Ошибка загрузки типов платежей:', response.message);
                    $('#payment_type').empty().append('<option value=""><?= Yii::t("app", "select_type") ?></option>');
                }
            },
            error: function() {
                console.error('Ошибка AJAX запроса для загрузки типов платежей');
                $('#payment_type').empty().append('<option value=""><?= Yii::t("app", "select_type") ?></option>');
            }
        });
    }

    // Обработчик изменения кассы
    $('#cashbox_id').on('change', function() {
        var cashboxId = $(this).val();
        loadPaymentTypesByCashbox(cashboxId);
    });
});
</script>
