<?php

use yii\helpers\Html;
use app\assets\DataTablesAsset;

/* @var $this yii\web\View */
/* @var $reportData array */

DataTablesAsset::register($this);

?>

<div class="card">
    <div style="margin-bottom: 3rem;">
        <h3 class="mb-0"><?= Yii::t('app', 'supplier_balance') ?></h3>
    </div>
    <div class="card-body">
       

        <!-- Таблица баланса поставщиков -->
        <div class="table-responsive">
            <table id="supplier-balance-table" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'supplier') ?></th>
                        <th><?= Yii::t('app', 'phone') ?></th>
                        <th><?= Yii::t('app', 'balance') ?></th>
                        <th><?= Yii::t('app', 'currency') ?></th>
                        <th><?= Yii::t('app', 'last_update') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($reportData['items'] as $item): ?>
                        <tr>
                            <td>
                                <?= Html::encode($item['full_name']) ?>
                                <?php if ($item['address']): ?>
                                    <br><small class="text-muted"><?= Html::encode($item['address']) ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?= Html::encode($item['phone_number']) ?>
                                <?php if ($item['phone_number_2']): ?>
                                    <br><?= Html::encode($item['phone_number_2']) ?>
                                <?php endif; ?>
                            </td>
                            <td class="text-right <?= $item['balance'] > 0 ? 'text-danger' : 'text-success' ?>">
                                <strong><?= Yii::$app->formatter->asDecimal($item['balance'], 0) ?></strong>
                            </td>
                            <?php
                                $rawCur = mb_strtoupper($item['currency'] ?? 'UZS');
                                $displayCur = (str_contains($rawCur, 'USD') || str_contains($rawCur, 'DOLLAR')) ? 'USD' : 'UZS';
                            ?>
                            <td><?= Html::encode($displayCur) ?></td>
                            <td><?= $item['last_update'] ? date('d.m.Y H:i', strtotime($item['last_update'])) : '-' ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
               
            </table>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    if ($.fn.DataTable && !$.fn.DataTable.isDataTable('#supplier-balance-table')) {
        $('#supplier-balance-table').DataTable({
            "pageLength": 25,
            "responsive": true,
            "order": [[2, "desc"]],
            "language": {
                "search": "<?= Yii::t('app', 'search') ?>:",
                "lengthMenu": "<?= Yii::t('app', 'show') ?> _MENU_ <?= Yii::t('app', 'records') ?>",
                "zeroRecords": "<?= Yii::t('app', 'no_records_found') ?>",
                "info": "<?= Yii::t('app', 'showing') ?> _START_ <?= Yii::t('app', 'to') ?> _END_ <?= Yii::t('app', 'of') ?> _TOTAL_ <?= Yii::t('app', 'records') ?>",
                "infoEmpty": "<?= Yii::t('app', 'no_data') ?>",
                "infoFiltered": "(<?= Yii::t('app', 'filtered_from') ?> _MAX_ <?= Yii::t('app', 'total_records') ?>)",
                "paginate": {
                    "first": "<?= Yii::t('app', 'first') ?>",
                    "last": "<?= Yii::t('app', 'last') ?>",
                    "next": "<?= Yii::t('app', 'next') ?>",
                    "previous": "<?= Yii::t('app', 'previous') ?>"
                }
            }
        });
    }
});
</script> 