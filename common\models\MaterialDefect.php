<?php

namespace app\common\models;

use Yii;
use app\modules\backend\models\Users;
/**
 * This is the model class for table "material_defect".
 *
 * @property int $id
 * @property int $material_id
 * @property int $quantity
 * @property int $add_user_id
 * @property int $accepted_user_id
 * @property string|null $description
 * @property string|null $created_at
 * @property string|null $accepted_at
 * @property string|null $deleted_at
 * @property int|null $source
 * @property bool|null $is_processed
 *
 * @property Users $acceptedUser
 * @property Material $material
 */
class MaterialDefect extends \yii\db\ActiveRecord
{
    const SOURCE_KEEPER = 1;
    const SOURCE_MANUFACTURER = 2;


    public static function getSourceList($source)
    {
        switch ($source) {
            case self::SOURCE_KEEPER:
                return Yii::t('app', 'raw_keeper');
            case self::SOURCE_MANUFACTURER:
                return Yii::t('app', 'manufacturer');
            default:
                return('unknown');
        }
    }


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'material_defect';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['material_id', 'quantity', 'add_user_id'], 'required'],
            [['material_id', 'accepted_user_id', 'add_user_id', 'source'], 'integer'],
            [['description'], 'string'],
            [['quantity'], 'number'],
            [['is_processed'], 'boolean'],
            [['created_at', 'accepted_at', 'deleted_at'], 'safe'],
            [['material_id'], 'exist', 'skipOnError' => true, 'targetClass' => Material::class, 'targetAttribute' => ['material_id' => 'id']],
            [['accepted_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['accepted_user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'material_id' => 'Material ID',
            'quantity' => 'Quantity',
            'accepted_user_id' => 'Accepted User ID',
            'description' => 'Description',
            'created_at' => 'Created At',
            'is_processed' => Yii::t('app', 'is_processed'),
            'source' => Yii::t('app', 'source'),
        ];
    }

    /**
     * Gets query for [[AcceptedUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAcceptedUser()
    {
        return $this->hasOne(Users::class, ['id' => 'accepted_user_id']);
    }

    /**
     * Gets query for [[AddUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAddUser()
    {
        return $this->hasOne(Users::class, ['id' => 'add_user_id']);
    }

    /**
     * Gets query for [[Material]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterial()
    {
        return $this->hasOne(Material::class, ['id' => 'material_id']);
    }
}
