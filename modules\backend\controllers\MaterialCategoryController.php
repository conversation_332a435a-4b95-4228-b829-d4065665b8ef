<?php

namespace app\modules\backend\controllers;

use Yii;
use app\common\models\MaterialCategory;
use yii\web\Response;

class MaterialCategoryController extends BaseController
{
    public function actionIndex()
    {
        $query = MaterialCategory::find()
            ->where(['deleted_at' => null])
            ->asArray()
            ->all();

        return $this->render('index', [
            'result' => $query,
        ]);
    }

    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new MaterialCategory();
            $model->load(Yii::$app->request->post());
            if ($model->validate()) {
                $category = MaterialCategory::findOne(['name' => $model->name]);
                if ($category) {
                    return [
                        'status' => 'error',
                        'errors' => ['name' => Yii::t('app', 'Category already exists.')],
                    ];
                }

                $model->save();

                return [
                    'status'=> 'success',
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $model = new MaterialCategory();
            return [
                "status" => 'success',
                "content" => $this->renderPartial('create', ['model' => $model])
            ];
        }
    }

    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $model = MaterialCategory::findOne($postData['MaterialCategory']['id']);
            
            if (!$model) {
                return ['status' => 'error', 'message' => Yii::t('app', 'Category not found.')];
            }

            $model->load($postData);
            
            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }

            $category = MaterialCategory::findOne(['name' => $model->name]);
            if ($category && $category->id != $model->id) {
                return [
                    'status' => 'error',
                    'errors' => ['name' => Yii::t('app', 'Category already exists.')],
                ];
            }

            if ($model->save()) {
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Category updated successfully.'),
                ];
            } else {
                return [
                    'status' => 'fail',
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = MaterialCategory::findOne($id);
            
            if (!$model) {
                return ['status' => 'error', 'message' => Yii::t('app', 'Category not found.')];
            }

            return [
                "status" => true,
                "content" => $this->renderPartial('update', ['model' => $model])
            ];
        }
    }

    public function actionDelete()
    {
       Yii::$app->response->format = Response::FORMAT_JSON;

       if(Yii::$app->request->isPost) {
        $data = Yii::$app->request->post();
            $id = $data['MaterialCategory']['id'];
            $model = MaterialCategory::findOne($id);
            if ($model) {
                $model->deleted_at = date('Y-m-d H:i:s');
                if ($model->save()) {
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'Category deleted successfully.'),
                    ];
                } else {
                    return [
                        'status' => 'fail',
                        'errors' => $model->getErrors(),
                    ];
                }
            } else {
                return ['status' => 'error', 'message' => Yii::t('app', 'Category not found.')];
            }
       } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = MaterialCategory::findOne($id);
            if (!$model) {
                return ['status' => 'error', 'message' => Yii::t('app', 'Category not found.')];
            }
            return [
                "status" => true,
                "content" => $this->renderPartial('delete', ['model' => $model])
            ];
       }
    }
}
