<?php

namespace app\common\models;

use app\modules\backend\models\Users;
use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "product_defect".
 *
 * @property int $id
 * @property int $product_id
 * @property int $quantity
 * @property int|null $add_user_id
 * @property int|null $accepted_user_id
 * @property string|null $description
 * @property bool|null $is_repackaging
 * @property int|null $original_storage_id
 * @property string|null $repackaging_reason
 * @property string $created_at
 * @property string|null $accepted_at
 * @property string|null $deleted_at
 *
 * @property Users $addUser
 * @property Users $acceptedUser
 * @property Product $product
 * @property ProductStorage $originalStorage
 */
class ProductDefect extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product_defect';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['product_id', 'quantity'], 'required'],
            [['product_id', 'quantity', 'add_user_id', 'accepted_user_id', 'original_storage_id'], 'integer'],
            [['description', 'repackaging_reason'], 'string'],
            [['is_repackaging'], 'boolean'],
            [['created_at', 'accepted_at', 'deleted_at'], 'safe'],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
            [['add_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['add_user_id' => 'id']],
            [['accepted_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['accepted_user_id' => 'id']],
            [['original_storage_id'], 'exist', 'skipOnError' => true, 'targetClass' => ProductStorage::class, 'targetAttribute' => ['original_storage_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'product_id' => 'Продукт',
            'quantity' => 'Количество',
            'add_user_id' => 'Добавил',
            'accepted_user_id' => 'Подтвердил',
            'description' => 'Описание',
            'is_repackaging' => 'Переупаковка',
            'original_storage_id' => 'Исходная запись на складе',
            'repackaging_reason' => 'Причина переупаковки',
            'created_at' => 'Дата создания',
            'accepted_at' => 'Дата подтверждения',
            'deleted_at' => 'Дата удаления',
        ];
    }

    /**
     * Gets query for [[AddUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAddUser()
    {
        return $this->hasOne(Users::class, ['id' => 'add_user_id']);
    }

    /**
     * Gets query for [[AcceptedUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAcceptedUser()
    {
        return $this->hasOne(Users::class, ['id' => 'accepted_user_id']);
    }

    /**
     * Gets query for [[Product]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }

    /**
     * Gets query for [[OriginalStorage]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getOriginalStorage()
    {
        return $this->hasOne(ProductStorage::class, ['id' => 'original_storage_id']);
    }
}
