<?php

namespace app\services;

use app\common\models\ActionLogger;
use app\common\models\Client;
use app\common\models\ClientDriver;
use app\common\models\ClientSpecialPrices;
use app\common\models\Product;
use app\common\models\ProductPrice;
use app\common\models\ProductStorage;
use app\common\models\Sales;
use app\common\models\SalesBonus;
use app\common\models\SalesDetail;
use app\common\models\Tracking;
use Exception;
use Yii;

/**
 * Сервис для работы с инвойсами
 */
class InvoiceService
{
    /**
     * Получение списка водителей клиента
     *
     * @param int $clientId ID клиента
     * @return array Массив водителей
     */
    public function getDriversByClientId($clientId)
    {
        $drivers = ClientDriver::find()
            ->where([
                'client_id' => $clientId,
                'deleted_at' => null
            ])
            ->all();

        $result = [];
        foreach ($drivers as $driver) {
            $result[$driver->id] = [
                'id' => $driver->id,
                'name' => $driver->driver,
                'car_number' => $driver->car_number
            ];
        }

        return $result;
    }

    /**
     * Получение деталей водителя по ID
     *
     * @param int|null $driverId ID водителя
     * @return array Информация о водителе
     */
    public function getDriverDetails($driverId)
    {
        if (empty($driverId)) {
            return [
                'car_number' => null
            ];
        }

        $driver = ClientDriver::findOne(['id' => $driverId, 'deleted_at' => null]);
        return [
            'car_number' => $driver ? $driver->car_number : null
        ];
    }

    /**
     * Создание нового инвойса
     *
     * @param array $data Данные инвойса
     * @return array Результат операции
     */
    public function createInvoice($data)
    {
        // Проверка на наличие водителя
        if (empty($data['driver_id'])) {
            return [
                'status' => 'error',
                'message' => [
                    'driver' => [Yii::t('app', 'driver_is_required')]
                ]
            ];
        }

        // Проверка на наличие продуктов
        if (empty($data['products']) || !is_array($data['products'])) {
            return [
                'status' => 'error',
                'message' => [
                    'products' => [Yii::t('app', 'products_required')]
                ]
            ];
        }

        // Фильтрация продуктов с нулевым количеством в блоках
        $products = array_filter($data['products'], function($product) {
            return !empty($product['quantity_block']) && $product['quantity_block'] > 0;
        });

        if (empty($products)) {
            return [
                'status' => 'error',
                'message' => [
                    'products' => [Yii::t('app', 'at_least_one_product_required')]
                ]
            ];
        }

        // Конвертируем количество в блоках в количество штук, устанавливаем цены, рассчитываем общую сумму
        $totalSum = 0;
        foreach ($products as &$product) {
            // Получаем модель продукта
            $productModel = Product::findOne(['id' => $product['product_id'], 'deleted_at' => null]);
            if (!$productModel) {
                return [
                    'status' => 'error',
                    'message' => [
                        'products' => [Yii::t('app', 'product_not_found')]
                    ]
                ];
            }

            // Получаем актуальные цены для продукта
            $productPrice = ProductPrice::findOne([
                'product_id' => $product['product_id'],
                'deleted_at' => null,
                'end_date' => '9999-12-31'
            ]);

            if (!$productPrice) {
                return [
                    'status' => 'error',
                    'message' => [
                        'products' => [Yii::t('app', 'price_not_found_for_product', [
                            'product' => $productModel->name
                        ])]
                    ]
                ];
            }

            // Проверяем, есть ли специальная цена для клиента
            $clientSpecialPrice = ClientSpecialPrices::findOne([
                'client_id' => $data['client_id'],
                'product_id' => $product['product_id'],
                'deleted_at' => null
            ]);

            // Устанавливаем цены
            $product['special_price'] = $clientSpecialPrice ? $clientSpecialPrice->special_price : $productPrice->price;
            $product['sell_price'] = $clientSpecialPrice ? $clientSpecialPrice->sell_price : $productPrice->price;

            // Конвертируем количество блоков в количество штук
            $product['quantity'] = $product['quantity_block'] * $productModel->size;

            // Рассчитываем общую сумму
            $product['total_price'] = $product['sell_price'] * $product['quantity'];
            $totalSum += $product['total_price'];
        }

        // Устанавливаем общую сумму
        $data['total_price'] = $totalSum;

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $sales = new Sales();
            $sales->client_id = $data['client_id'];
            $sales->sell_user_id = Yii::$app->user->getId();
            $sales->total_sum = $data['total_price'];
            $sales->status = Sales::STATUS_NEW;
            $sales->created_at = date('Y-m-d H:i:s');
            $sales->total_special_prices_sum = 0; // Будет обновлено позже

            // Если водитель выбран, находим его данные
            if (!empty($data['driver_id'])) {
                $driver = ClientDriver::findOne(['id' => $data['driver_id'], 'deleted_at' => null]);
                $sales->driver = $driver->driver ?? null;
                $sales->car_number = $driver->car_number ?? null;
            } else {
                // Если водитель не выбран, устанавливаем пустые значения
                $sales->driver = null;
                $sales->car_number = $data['car_number'] ?? null;
            }

            if (!$sales->save()) {
                throw new Exception('Ошибка при сохранении продажи: ' . json_encode($sales->getErrors()));
            }

            $salesDetails = [];
            foreach ($products as $product) {
                $factory_price = ProductPrice::findOne(['product_id' => $product['product_id'], 'deleted_at' => null]);
                $salesDetail = new SalesDetail();
                $salesDetail->sale_id = $sales->id;
                $salesDetail->product_id = $product['product_id'];
                $salesDetail->special_price = $product['special_price'];
                $salesDetail->sell_price = $product['sell_price'];
                $salesDetail->total_price = $product['sell_price'] * $product['quantity'];
                $salesDetail->factory_price = $factory_price->price ?? 0;
                $salesDetail->quantity = $product['quantity'];

                if (!$salesDetail->save()) {
                    throw new Exception('Ошибка при сохранении деталей продажи: ' . json_encode($salesDetail->getErrors()));
                }

                $sales->total_special_prices_sum += $product['special_price'] * $product['quantity'];
                if (!$sales->save()) {
                    throw new Exception('Ошибка при сохранении продажи: ' . json_encode($sales->getErrors()));
                }

                // Обновление специальных цен для клиента
                $this->updateClientSpecialPrices($sales->client_id, $product['product_id'], $product['special_price'], $product['sell_price']);

                // Проверка наличия на складе
                $this->checkProductAvailability($product['product_id'], $product['quantity']);

                $salesDetails[] = [
                    'product_id' => $product['product_id'],
                    'quantity' => $product['quantity'],
                    'price' => $product['special_price'],
                    'sell_price' => $product['sell_price']
                ];
            }

            // Обработка бонусных продуктов
            if (!empty($data['has_bonus']) && is_array($data['bonus']) && !empty($data['bonus'])) {
                $this->processBonusProducts($sales->id, $data['bonus']);
            }

            // Создание записи в tracking
            $tracking = new Tracking();
            $tracking->progress_type = Tracking::TYPE_NEW_SALES;
            $tracking->process_id = $sales->id;
            $tracking->created_at = date('Y-m-d H:i:s');
            $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
            $tracking->accepted_at = null;

            if (!$tracking->save()) {
                throw new Exception('Ошибка при сохранении tracking: ' . json_encode($tracking->getErrors()));
            }

            // Логирование действия
            ActionLogger::actionLog(
                'create_invoice',
                'sales',
                $sales->id,
                [
                    'client_id' => $sales->client_id,
                    'driver' => $sales->driver,
                    'car_number' => $sales->car_number,
                    'total_sum' => $sales->total_sum,
                    'products' => $salesDetails,
                    'tracking_id' => $tracking->id
                ]
            );

            // Отправка уведомления через Firebase
            $this->sendInvoiceNotification($sales);

            $transaction->commit();
            return [
                'status' => 'success',
                'message' => Yii::t('app', 'record_successfully_created'),
                'data' => [
                    'sales_id' => $sales->id
                ]
            ];

        } catch (Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Обновление специальных цен для клиента
     *
     * @param int $clientId ID клиента
     * @param int $productId ID продукта
     * @param float $specialPrice Специальная цена
     * @param float $sellPrice Цена продажи
     * @throws Exception
     */
    private function updateClientSpecialPrices($clientId, $productId, $specialPrice, $sellPrice)
    {
        $existingSpecialPrice = ClientSpecialPrices::findOne([
            'client_id' => $clientId,
            'product_id' => $productId,
            'deleted_at' => null
        ]);

        $newPrice = round((float)$specialPrice, 2);
        $newSellPrice = round((float)$sellPrice, 2);
        $currentTime = date('Y-m-d H:i:s');

        // Допустимая погрешность для сравнения цен - 5 сум
        $priceThreshold = 5.0;

        if ($existingSpecialPrice) {
            $existingSpecialPriceRounded = round($existingSpecialPrice->special_price, 2);
            $existingSellPriceRounded = round($existingSpecialPrice->sell_price, 2);

            // Проверяем разницу с допустимой погрешностью
            $specialPriceDiff = abs($existingSpecialPriceRounded - $newPrice);
            $sellPriceDiff = abs($existingSellPriceRounded - $newSellPrice);
            $pricesChanged = ($specialPriceDiff > $priceThreshold) || ($sellPriceDiff > $priceThreshold);

            // Временное логирование для отладки
            Yii::info("InvoiceService - Сравнение цен для клиента {$clientId}, продукт {$productId}: " .
                "существующая_спец={$existingSpecialPriceRounded}, новая_спец={$newPrice}, разница_спец={$specialPriceDiff}, " .
                "существующая_продажа={$existingSellPriceRounded}, новая_продажа={$newSellPrice}, разница_продажа={$sellPriceDiff}, " .
                "порог={$priceThreshold}, изменились=" . ($pricesChanged ? 'ДА' : 'НЕТ'), 'special_prices');

            if ($pricesChanged) {
                Yii::info("InvoiceService - Обновление специальной цены для клиента {$clientId}, продукт {$productId}: " .
                    "помечаем старую запись как удаленную (ID={$existingSpecialPrice->id})", 'special_prices');

                $existingSpecialPrice->deleted_at = $currentTime;
                if (!$existingSpecialPrice->save()) {
                    $errorMsg = 'Ошибка при удалении старой специальной цены: ' . json_encode($existingSpecialPrice->getErrors());
                    Yii::error("InvoiceService - " . $errorMsg, 'special_prices');
                    throw new Exception($errorMsg);
                }

                $specialPriceModel = new ClientSpecialPrices();
                $specialPriceModel->client_id = $clientId;
                $specialPriceModel->product_id = $productId;
                $specialPriceModel->special_price = $newPrice;
                $specialPriceModel->sell_price = $newSellPrice;
                $specialPriceModel->created_at = $currentTime;

                if (!$specialPriceModel->save()) {
                    $errorMsg = 'Ошибка при сохранении новой специальной цены: ' . json_encode($specialPriceModel->getErrors());
                    Yii::error("InvoiceService - " . $errorMsg, 'special_prices');
                    throw new Exception($errorMsg);
                }

                Yii::info("InvoiceService - Создана новая специальная цена (ID={$specialPriceModel->id}) для клиента {$clientId}, продукт {$productId}", 'special_prices');
            } else {
                Yii::info("InvoiceService - Цены не изменились для клиента {$clientId}, продукт {$productId}: пропускаем обновление", 'special_prices');
            }
        } else {
            Yii::info("InvoiceService - Создание новой специальной цены для клиента {$clientId}, продукт {$productId}: " .
                "спец_цена={$newPrice}, цена_продажи={$newSellPrice}", 'special_prices');

            $specialPriceModel = new ClientSpecialPrices();
            $specialPriceModel->client_id = $clientId;
            $specialPriceModel->product_id = $productId;
            $specialPriceModel->special_price = $newPrice;
            $specialPriceModel->sell_price = $newSellPrice;
            $specialPriceModel->created_at = $currentTime;

            if (!$specialPriceModel->save()) {
                $errorMsg = 'Ошибка при создании новой специальной цены: ' . json_encode($specialPriceModel->getErrors());
                Yii::error("InvoiceService - " . $errorMsg, 'special_prices');
                throw new Exception($errorMsg);
            }

            Yii::info("InvoiceService - Создана новая специальная цена (ID={$specialPriceModel->id}) для клиента {$clientId}, продукт {$productId}", 'special_prices');
        }
    }

    /**
     * Проверка наличия продукта на складе
     *
     * @param int $productId ID продукта
     * @param int $quantity Требуемое количество
     * @throws Exception
     */
    private function checkProductAvailability($productId, $quantity)
    {
        $productStorage = ProductStorage::findOne(['product_id' => $productId, 'deleted_at' => null]);
        $productName = Product::findOne(['id' => $productId, 'deleted_at' => null])->name;

        if (!$productStorage) {
            throw new Exception(Yii::t('app', 'Product "{product_name}" not found on the warehouse', [
                'product_name' => $productName
            ]));
        }

        // Проверяем наличие на складе
        $productStorages = ProductStorage::find()
            ->where(['product_id' => $productId, 'deleted_at' => null])
            ->all();

        $totalAvailable = array_sum(array_column($productStorages, 'quantity'));
        if ($totalAvailable < $quantity) {
            throw new Exception(Yii::t('app', 'Not enough product "{product_name}" on the warehouse. Available: {available}, Required: {required}', [
                'product_name' => $productName,
                'available' => $totalAvailable,
                'required' => $quantity
            ]));
        }
    }

    /**
     * Обработка бонусных продуктов
     *
     * @param int $saleId ID продажи
     * @param array $bonusProducts Массив бонусных продуктов
     * @throws Exception
     */
    private function processBonusProducts($saleId, $bonusProducts)
    {
        foreach ($bonusProducts as $bonus) {
            if (empty($bonus['product_id'])) {
                continue;
            }

            // Проверяем, что указано хотя бы одно из полей: quantity или quantity_block
            if (empty($bonus['quantity']) && empty($bonus['quantity_block'])) {
                continue;
            }

            // Получаем модель продукта
            $product = Product::findOne(['id' => $bonus['product_id'], 'deleted_at' => null]);
            if (!$product) {
                throw new Exception(Yii::t('app', 'Product with ID #{product_id} not found', [
                    'product_id' => $bonus['product_id']
                ]));
            }
            $productName = $product->name;

            // Конвертируем количество блоков в количество штук, если указано
            $quantity = 0;
            if (!empty($bonus['quantity_block']) && $bonus['quantity_block'] > 0) {
                $quantity = $bonus['quantity_block'] * $product->size;
            } elseif (!empty($bonus['quantity']) && $bonus['quantity'] > 0) {
                $quantity = $bonus['quantity'];
            }

            // Если количество равно 0, пропускаем
            if ($quantity <= 0) {
                continue;
            }

            $salesBonus = new SalesBonus();
            $salesBonus->sale_id = $saleId;
            $salesBonus->product_id = $bonus['product_id'];
            $salesBonus->quantity = $quantity;
            $salesBonus->created_at = date('Y-m-d H:i:s');
            if (!$salesBonus->save()) {
                throw new Exception(Yii::t('app', 'Error saving bonus for product "{product_name}"', [
                    'product_name' => $productName
                ]));
            }

            // Проверка наличия на складе
            $this->checkProductAvailability($bonus['product_id'], $quantity);
        }
    }

    /**
     * Отправка уведомления о создании инвойса
     *
     * @param Sales $sales Объект продажи
     */
    private function sendInvoiceNotification($sales)
    {
        try {
            $clientName = $sales->client ? $sales->client->full_name : 'Клиент';
            $title = 'Новый инвойс';
            $body = "Создан новый инвойс для {$clientName} на сумму {$sales->total_sum}";
            $data = [
                'sales_id' => $sales->id,
                'type' => 'new_invoice',
                'client_id' => $sales->client_id,
                'total_sum' => $sales->total_sum
            ];

            // Создаем уведомление для пользователей с ролью product_keeper
            Yii::$app->firebase->notifyRole('product_keeper', $title, $body, $data);
            // Создаем уведомление для пользователей с ролью sales
            Yii::$app->firebase->notifyRole('sales', $title, $body, $data);
        } catch (Exception $e) {
            // Логируем ошибку, но не прерываем выполнение
            Yii::error('Ошибка создания уведомления: ' . $e->getMessage(), 'firebase');
        }
    }

    /**
     * Получение списка инвойсов с возможностью фильтрации
     *
     * @param array $params Параметры фильтрации
     * @return array Результат операции
     */
    public function getInvoices($params = [])
    {
        // Получаем параметры запроса
        $client_id = $params['client_id'] ?? null;
        $date_from = $params['date_from'] ?? null;
        $date_to = $params['date_to'] ?? null;
        $status = $params['status'] ?? null;
        $page = (int)($params['page'] ?? 1);
        $per_page = (int)($params['per_page'] ?? 20);

        // Ограничиваем максимальное количество записей на странице
        if ($per_page > 100) {
            $per_page = 100;
        }

        // Вычисляем смещение для пагинации
        $offset = ($page - 1) * $per_page;

        // Формируем базовый запрос
        $query = (new \yii\db\Query())
            ->select([
                's.id',
                's.created_at',
                'c.full_name as client_name',
                's.driver',
                's.car_number',
                's.total_sum',
                's.status'
            ])
            ->from(['s' => 'sales'])
            ->leftJoin(['c' => 'client'], 'c.id = s.client_id')
            ->where(['s.deleted_at' => null])
            ->orderBy(['s.created_at' => SORT_DESC]);

        // Добавляем фильтры, если они указаны
        if (!empty($client_id)) {
            $query->andWhere(['s.client_id' => $client_id]);
        }

        if (!empty($date_from)) {
            $query->andWhere(['>=', 's.created_at', $date_from . ' 00:00:00']);
        }

        if (!empty($date_to)) {
            $query->andWhere(['<=', 's.created_at', $date_to . ' 23:59:59']);
        }

        if ($status !== null && $status !== '') {
            $query->andWhere(['s.status' => $status]);
        }

        // Получаем общее количество записей для пагинации
        $total_count = $query->count();

        // Добавляем пагинацию
        $query->limit($per_page)->offset($offset);

        // Выполняем запрос
        $invoices = $query->all();

        // Форматируем данные
        foreach ($invoices as &$invoice) {
            // Форматируем сумму с разделителями тысяч
            $invoice['total_sum'] = number_format((float)$invoice['total_sum'], 0, ',', ' ');

            // Добавляем текстовое представление статуса
            $invoice['status_name'] = $this->getStatusName($invoice['status']);
        }

        // Вычисляем общее количество страниц
        $page_count = ceil($total_count / $per_page);

        return [
            'invoices' => $invoices,
            'total_count' => $total_count,
            'page' => $page,
            'per_page' => $per_page,
            'page_count' => $page_count
        ];
    }

    /**
     * Получение деталей инвойса по ID
     *
     * @param int $invoice_id ID инвойса
     * @return array|null Детали инвойса или null, если инвойс не найден
     */
    public function getInvoiceDetails($invoice_id)
    {
        if (empty($invoice_id)) {
            return null;
        }

        // Получаем основную информацию об инвойсе
        $invoice = (new \yii\db\Query())
            ->select([
                's.id',
                's.created_at',
                'c.full_name as client_name',
                's.driver',
                's.car_number',
                's.total_sum',
                's.status'
            ])
            ->from(['s' => 'sales'])
            ->leftJoin(['c' => 'client'], 'c.id = s.client_id')
            ->where(['s.id' => $invoice_id, 's.deleted_at' => null])
            ->one();

        // Проверяем, что инвойс найден
        if (!$invoice) {
            return null;
        }

        // Форматируем сумму с разделителями тысяч
        $invoice['total_sum'] = number_format((float)$invoice['total_sum'], 0, ',', ' ');

        // Добавляем текстовое представление статуса
        $invoice['status_name'] = $this->getStatusName($invoice['status']);

        // Получаем продукты инвойса
        $products = (new \yii\db\Query())
            ->select([
                'sd.product_id',
                'p.name as product_name',
                'p.size',
                'sd.quantity',
                'sd.special_price',
                'sd.sell_price',
                'sd.total_price'
            ])
            ->from(['sd' => 'sales_detail'])
            ->leftJoin(['p' => 'product'], 'p.id = sd.product_id')
            ->where(['sd.sale_id' => $invoice_id, 'sd.deleted_at' => null])
            ->all();

        // Форматируем данные продуктов
        foreach ($products as &$product) {
            // Вычисляем количество в блоках
            $size = $product['size'] > 0 ? $product['size'] : 1;
            $product['quantity_block'] = round($product['quantity'] / $size, 2);

            // Форматируем сумму с разделителями тысяч
            $product['total_price'] = number_format((float)$product['total_price'], 0, ',', ' ');
        }

        // Получаем бонусные продукты
        $bonusProducts = (new \yii\db\Query())
            ->select([
                'sb.product_id',
                'p.name as product_name',
                'p.size',
                'sb.quantity'
            ])
            ->from(['sb' => 'sales_bonus'])
            ->leftJoin(['p' => 'product'], 'p.id = sb.product_id')
            ->where(['sb.sale_id' => $invoice_id, 'sb.deleted_at' => null])
            ->all();

        // Форматируем данные бонусных продуктов
        foreach ($bonusProducts as &$bonus) {
            // Вычисляем количество в блоках
            $size = $bonus['size'] > 0 ? $bonus['size'] : 1;
            $bonus['quantity_block'] = round($bonus['quantity'] / $size, 2);
        }

        return [
            'invoice' => $invoice,
            'products' => $products,
            'bonus_products' => $bonusProducts
        ];
    }

    /**
     * Обновление существующего инвойса
     *
     * @param array $data Данные инвойса
     * @return array Результат операции
     */
    public function updateInvoice($data)
    {
        // Проверка на наличие ID инвойса
        if (empty($data['invoice_id'])) {
            return [
                'status' => 'error',
                'message' => [
                    'invoice_id' => [Yii::t('app', 'invoice_id_is_required')]
                ]
            ];
        }

        // Проверка на наличие водителя
        if (empty($data['driver_id'])) {
            return [
                'status' => 'error',
                'message' => [
                    'driver' => [Yii::t('app', 'driver_is_required')]
                ]
            ];
        }

        // Проверка на наличие продуктов
        if (empty($data['products']) || !is_array($data['products'])) {
            return [
                'status' => 'error',
                'message' => [
                    'products' => [Yii::t('app', 'products_required')]
                ]
            ];
        }

        // Находим инвойс по ID
        $sales = Sales::findOne([
            'id' => $data['invoice_id'],
            'deleted_at' => null
        ]);

        if (!$sales) {
            return [
                'status' => 'error',
                'message' => [
                    'invoice_id' => [Yii::t('app', 'invoice_not_found')]
                ]
            ];
        }

        // Проверяем статус инвойса - можно обновлять только новые инвойсы
        if ($sales->status != Sales::STATUS_NEW) {
            return [
                'status' => 'error',
                'message' => [
                    'invoice_id' => [Yii::t('app', 'invoice_cannot_be_updated')]
                ]
            ];
        }

        // Фильтрация продуктов с нулевым количеством в блоках
        $products = array_filter($data['products'], function($product) {
            return !empty($product['quantity_block']) && $product['quantity_block'] > 0;
        });

        if (empty($products)) {
            return [
                'status' => 'error',
                'message' => [
                    'products' => [Yii::t('app', 'products_required')]
                ]
            ];
        }

        // Конвертируем количество в блоках в количество штук, устанавливаем цены, рассчитываем общую сумму
        $totalSum = 0;
        foreach ($products as &$product) {
            // Получаем модель продукта
            $productModel = Product::findOne(['id' => $product['product_id'], 'deleted_at' => null]);
            if (!$productModel) {
                return [
                    'status' => 'error',
                    'message' => [
                        'products' => [Yii::t('app', 'product_not_found')]
                    ]
                ];
            }

            // Получаем актуальные цены для продукта
            $productPrice = ProductPrice::findOne([
                'product_id' => $product['product_id'],
                'deleted_at' => null,
                'end_date' => '9999-12-31'
            ]);

            if (!$productPrice) {
                return [
                    'status' => 'error',
                    'message' => [
                        'products' => [Yii::t('app', 'price_not_found_for_product', [
                            'product' => $productModel->name
                        ])]
                    ]
                ];
            }

            // Получаем специальную цену для клиента, если она есть
            $specialPrice = ClientSpecialPrices::findOne([
                'client_id' => $data['client_id'],
                'product_id' => $product['product_id'],
                'deleted_at' => null
            ]);

            // Конвертируем количество блоков в количество штук
            if (!empty($product['quantity_block'])) {
                $product['quantity'] = $product['quantity_block'] * $productModel->size;
            } elseif (empty($product['quantity']) || $product['quantity'] <= 0) {
                return [
                    'status' => 'error',
                    'message' => [
                        'products' => [Yii::t('app', 'quantity_error')]
                    ]
                ];
            }

            // Устанавливаем цены
            $product['special_price'] = $specialPrice ? $specialPrice->special_price : $productPrice->price;
            $product['sell_price'] = $specialPrice ? $specialPrice->sell_price : $productPrice->sell_price;

            // Рассчитываем общую сумму
            $totalSum += $product['sell_price'] * $product['quantity'];
        }

        $data['total_price'] = $totalSum;

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Обновляем основную запись инвойса
            $sales->client_id = $data['client_id'];
            $sales->total_sum = $data['total_price'];
            $sales->total_special_prices_sum = 0; // Будет обновлено позже

            // Если водитель выбран, находим его данные
            if (!empty($data['driver_id'])) {
                $driver = ClientDriver::findOne(['id' => $data['driver_id'], 'deleted_at' => null]);
                $sales->driver = $driver->driver ?? null;
                $sales->car_number = $driver->car_number ?? null;
            } else {
                // Если водитель не выбран, устанавливаем пустые значения
                $sales->driver = null;
                $sales->car_number = $data['car_number'] ?? null;
            }

            if (!$sales->save()) {
                throw new Exception('Ошибка при обновлении инвойса: ' . json_encode($sales->getErrors()));
            }

            // Удаляем старые детали инвойса
            SalesDetail::updateAll(['deleted_at' => date('Y-m-d H:i:s')], ['sale_id' => $sales->id]);

            // Добавляем новые детали инвойса
            $salesDetails = [];
            foreach ($products as $product) {
                $factory_price = ProductPrice::findOne(['product_id' => $product['product_id'], 'deleted_at' => null]);
                $salesDetail = new SalesDetail();
                $salesDetail->sale_id = $sales->id;
                $salesDetail->product_id = $product['product_id'];
                $salesDetail->special_price = $product['special_price'];
                $salesDetail->sell_price = $product['sell_price'];
                $salesDetail->total_price = $product['sell_price'] * $product['quantity'];
                $salesDetail->factory_price = $factory_price->price ?? 0;
                $salesDetail->quantity = $product['quantity'];

                if (!$salesDetail->save()) {
                    throw new Exception('Ошибка при сохранении деталей продажи: ' . json_encode($salesDetail->getErrors()));
                }

                $sales->total_special_prices_sum += $product['special_price'] * $product['quantity'];
                if (!$sales->save()) {
                    throw new Exception('Ошибка при сохранении продажи: ' . json_encode($sales->getErrors()));
                }

                // Обновление специальных цен для клиента
                $this->updateClientSpecialPrices($sales->client_id, $product['product_id'], $product['special_price'], $product['sell_price']);

                // Проверка наличия на складе
                $this->checkProductAvailability($product['product_id'], $product['quantity']);

                $salesDetails[] = [
                    'product_id' => $product['product_id'],
                    'quantity' => $product['quantity'],
                    'price' => $product['special_price'],
                    'sell_price' => $product['sell_price']
                ];
            }

            // Удаляем старые бонусные продукты
            SalesBonus::updateAll(['deleted_at' => date('Y-m-d H:i:s')], ['sale_id' => $sales->id]);

            // Обработка бонусных продуктов
            if (!empty($data['has_bonus']) && is_array($data['bonus']) && !empty($data['bonus'])) {
                $this->processBonusProducts($sales->id, $data['bonus']);
            }

            // Логирование действия
            ActionLogger::actionLog(
                'update_invoice',
                'sales',
                $sales->id,
                [
                    'client_id' => $sales->client_id,
                    'driver' => $sales->driver,
                    'car_number' => $sales->car_number,
                    'total_sum' => $sales->total_sum,
                    'products' => $salesDetails
                ]
            );

            $transaction->commit();
            return [
                'status' => 'success',
                'message' => Yii::t('app', 'record_successfully_updated'),
                'data' => [
                    'sales_id' => $sales->id
                ]
            ];

        } catch (Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Получение текстового представления статуса инвойса
     *
     * @param int $status Статус инвойса
     * @return string Текстовое представление статуса
     */
    private function getStatusName($status)
    {
        switch ($status) {
            case Sales::STATUS_NEW: // 1
                return 'Новый';
            case Sales::STATUS_IN_PROGRESS: // 2
                return 'В процессе';
            case Sales::STATUS_CONFIRMED: // 3
                return 'Подтвержден';
            case 0: // Для обратной совместимости
                return 'Новый';
            default:
                return 'Неизвестный статус';
        }
    }
}
