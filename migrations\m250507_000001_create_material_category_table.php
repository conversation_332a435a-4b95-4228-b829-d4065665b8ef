<?php

use yii\db\Migration;

/**
 * Создает таблицу категорий материалов и добавляет колонку category_id в таблицу material
 */
class m250507_000001_create_material_category_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Создаем таблицу категорий материалов
        $this->createTable('material_category', [
            'id' => $this->primaryKey(),
            'name' => $this->string(255)->notNull(),
            'description' => $this->text()->null(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Добавляем колонку category_id в таблицу material
        $this->addColumn('material', 'category_id', $this->integer()->null()->after('unit_type'));

        // Добавляем внешний ключ
        $this->addForeignKey(
            'fk-material-category_id',
            'material',
            'category_id',
            'material_category',
            'id',
            'SET NULL'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем внешний ключ
        $this->dropForeignKey('fk-material-category_id', 'material');

        // Удаляем колонку category_id из таблицы material
        $this->dropColumn('material', 'category_id');

        // Удаляем таблицу категорий материалов
        $this->dropTable('material_category');
    }
}
