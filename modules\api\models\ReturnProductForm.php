<?php

namespace app\modules\api\models;

use Yii;
use yii\base\Model;
use app\common\models\Sales;
use app\common\models\SalesDetail;
use app\common\models\SalesReturn;

class ReturnProductForm extends Model
{
    public $sale_id;
    public $products;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sale_id', 'products'], 'required', 'message' => 'Поле {attribute} обязательно'],
            ['sale_id', 'integer'],
            ['sale_id', 'validateSale'],
            ['products', 'validateProducts'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'sale_id' => 'ID продажи',
            'products' => 'Продукты',
        ];
    }

    /**
     * Validates sale existence and status
     */
    public function validateSale($attribute, $params)
    {
        $sale = Sales::find()
            ->where(['id' => $this->sale_id])
            ->andWhere(['deleted_at' => null])
            ->andWhere(['status' => Sales::STATUS_CONFIRMED])
            ->one();

        if (!$sale) {
            $this->addError($attribute, 'Продажа не найдена или не подтверждена');
        }
    }

    /**
     * Validates products array structure and quantities
     */
    public function validateProducts($attribute, $params)
    {
        if (!is_array($this->products)) {
            $this->addError($attribute, 'Продукты должны быть массивом');
            return;
        }

        foreach ($this->products as $index => $product) {
            if (!isset($product['product_id']) || !isset($product['quantity'])) {
                $this->addError($attribute, "Продукт #{$index}: отсутствуют обязательные поля");
                continue;
            }

            if (!is_numeric($product['product_id']) || !is_numeric($product['quantity'])) {
                $this->addError($attribute, "Продукт #{$index}: неверный формат данных");
                continue;
            }

            if ($product['quantity'] <= 0) {
                $this->addError($attribute, "Продукт #{$index}: количество должно быть больше 0");
                continue;
            }

            // Проверяем существование продукта в продаже и его количество
            $saleDetail = SalesDetail::find()
                ->where([
                    'sale_id' => $this->sale_id,
                    'product_id' => $product['product_id'],
                    'deleted_at' => null
                ])
                ->one();

            if (!$saleDetail) {
                $this->addError($attribute, "Продукт #{$index}: не найден в данной продаже");
                continue;
            }

            if ($product['quantity'] > $saleDetail->quantity) {
                $this->addError($attribute, "Продукт #{$index}: количество для возврата превышает проданное количество");
                continue;
            }
        }
    }
}
