<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\assets;

use yii\web\AssetBundle;
use yii\web\JqueryAsset;

/**
 * Main application asset bundle.
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class AppAsset extends AssetBundle
{
    public $basePath = '@webroot';
    public $baseUrl = '@web';
    public $css = [
        'css/site.css',
        'modules/bootstrap/css/bootstrap.min.css',
        'modules/bootstrap-daterangepicker/daterangepicker.css',
        'modules/bootstrap-datepicker.min.css',
        'modules/fontawesome/css/all.min.css',
        'modules/ionicons/css/ionicons.min.css',
        'modules/select2/dist/css/select2.min.css',
        'modules/izitoast/css/iziToast.min.css',
        'css/style.css',
        'css/components.css',
        'css/magnific-popup.css',
        'modules/bootstrap-timepicker/css/bootstrap-timepicker.min.css'
    ];
    public $js = [
        // 'modules/jquery.min.js',
        'modules/bootstrap/js/bootstrap.min.js',
        'modules/bootstrap-datepicker.min.js',
        'modules/nicescroll/jquery.nicescroll.min.js',
        'modules/moment.min.js',
        'modules/bootstrap-daterangepicker/daterangepicker.js',
        'js/stisla.js',
        'modules/izitoast/js/iziToast.min.js',
        'modules/select2/dist/js/select2.min.js',
        'modules/jquery-ui/jquery-ui.min.js',
        'js/scripts.js',
        'js/jquery.inputmask.min.js',
        'modules/chocolat/dist/js/jquery.chocolat.min.js',
        'js/jquery.magnific-popup.js',
        'js/menu.js',
        'modules/moment.min.js',
        'modules/bootstrap-timepicker/js/bootstrap-timepicker.min.js',
        '//cdnjs.cloudflare.com/ajax/libs/numeral.js/2.0.6/numeral.min.js', // Добавлено numeral.js
        'js/input-formatter.js', // Добавлен общий форматер
    ];
    
    public $depends = [
        'yii\web\YiiAsset',
        'yii\web\JqueryAsset',
    ];
}
