<?php

$routes = [
    '' => 'site/index',
    'login' => 'site/login',
    'logout' => 'site/logout',
    'site/<action>' => 'site/<action>',

    // API и Swagger маршруты
    'api/<controller:[\w-]+>/<action:[\w-]+>' => 'api/<controller>/<action>',
    'api/<controller:[\w-]+>' => 'api/<controller>/index',
    'swagger/json' => 'swagger/json',
    'swagger/ui' => 'swagger/ui',
    'swagger' => 'swagger/ui',


    // Backend маршруты
    'backend/position/<action>' => 'backend/position/<action>',
    'backend/position' => 'backend/position/index',
    'backend/equipment/<id:\d+>/parts' => 'backend/equipment/parts',
    'backend/equipment/defect-part/<equipmentId:\d+>/<partId:\d+>' => 'backend/equipment/defect-part',
    'backend/equipment/reserve-part/<equipmentId:\d+>/<partId:\d+>' => 'backend/equipment/reserve-part',
    'backend/equipment/repair-part/<equipmentId:\d+>/<partId:\d+>' => 'backend/equipment/repair-part',
    'backend/equipment/activate-part/<equipmentId:\d+>/<partId:\d+>' => 'backend/equipment/activate-part',
    'backend/<controller>/<action>/<id:\d+>' => 'backend/<controller>/<action>',
    'backend/<controller>/<action>' => 'backend/<controller>/<action>',
    'backend/<controller>' => 'backend/<controller>/index',

    // Специфичные маршруты
    'rating' => 'r/i',
    'r/<hash:[0-9a-zA-Z\_\-]+>' => 'r/i',
    '<controller:\w+>/<action:\w+>' => '<controller>/<action>',
];

// Загрузка дополнительных маршрутов из файлов
$path = dirname(__DIR__) . '/routes';
if (is_dir($path)) {
    $routeFiles = glob("{$path}/*.php");
    if (!empty($routeFiles)) {
        foreach ($routeFiles as $item) {
            $route = require($item);
            if (is_array($route)) {
                $routes = array_merge($routes, $route);
            }
        }
    }
}

return $routes;