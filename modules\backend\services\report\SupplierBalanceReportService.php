<?php

namespace app\modules\backend\services\report;

use app\common\models\Supplier;
use app\common\models\SupplierBalance;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\db\Expression;

class SupplierBalanceReportService
{
    /**
     * Формирует отчет по балансам поставщиков
     * @return array
     */
    public function getSupplierBalanceReport()
    {
        $query = (new Query())
            ->select([
                's.id',
                's.full_name',
                's.phone_number',
                's.phone_number_2',
                's.address',
                'COALESCE(sb.amount, 0) as balance',
                'sb.updated_at as last_update',
                'cur.name as currency'
            ])
            ->from(['s' => Supplier::tableName()])
            ->leftJoin(['sb' => SupplierBalance::tableName()], 's.id = sb.supplier_id')
            ->leftJoin(['cur' => 'currency'], 'cur.id = s.currency_id')
            ->where(['IS', 's.deleted_at', null])
            ->orderBy(['ABS(COALESCE(sb.amount,0))' => SORT_DESC]);

        $items = $query->all();

        $totals = [
            'UZS' => ['positive' => 0, 'negative' => 0],
            'USD' => ['positive' => 0, 'negative' => 0],
        ];
        $positiveCount = $negativeCount = 0;

        foreach ($items as $item) {
            $rawCur = mb_strtoupper($item['currency'] ?? 'UZS');
            // нормализуем к USD или UZS
            if (str_contains($rawCur, 'USD') || str_contains($rawCur, 'DOLLAR')) {
                $cur = 'USD';
            } else {
                $cur = 'UZS';
            }

            // гарантируем существование ключа
            if (!isset($totals[$cur])) {
                $totals[$cur] = ['positive' => 0, 'negative' => 0];
            }

            $balance = (float)$item['balance'];
            if ($balance > 0) {
                $totals[$cur]['positive'] += $balance;
                $positiveCount++;
            } elseif ($balance < 0) {
                $totals[$cur]['negative'] += abs($balance);
                $negativeCount++;
            }
        }

        return [
            'items' => $items,
            'totals' => $totals,
            'positiveCount' => $positiveCount,
            'negativeCount' => $negativeCount,
        ];
    }
} 