<?php
use yii\helpers\Html;
use app\assets\Select2Asset;
use app\modules\backend\models\Position;

Select2Asset::register($this);

$positions = Position::find()->where(['deleted_at' => null])->all();
$positionsList = \yii\helpers\ArrayHelper::map($positions, 'id', 'name');


?>

<div class="worker-form">
    <form id="worker-create-form">
        <div class="form-group">
            <label for="full_name"><?= Yii::t('app', 'full_name') ?></label>
            <input type="text" id="full_name" name="Worker[full_name]" maxlength="255" class="form-control">
            <div class="error-container" id="full_name-error"></div>
        </div>

        <div class="form-group">
            <label for="position_id"><?= Yii::t('app', 'position') ?></label>
            <select id="position_id" name="Worker[position_id]" class="form-control select2">
                <option value=""><?= Yii::t('app', 'select_position') ?></option>
                <?php foreach ($positionsList as $id => $name): ?>
                    <option value="<?= $id ?>"><?= Html::encode($name) ?></option>
                <?php endforeach; ?>
            </select>
            <div class="error-container" id="position_id-error"></div>
        </div>

        <div class="form-group">
            <label for="address"><?= Yii::t('app', 'address') ?></label>
            <input type="text" id="address" name="Worker[address]" maxlength="255" class="form-control">
            <div class="error-container" id="address-error"></div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="phone-number"><?= Yii::t('app', 'phone_number') ?></label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">+998</span>
                        </div>
                        <input type="text" id="phone_number" name="Worker[phone_number]" class="form-control">
                    </div>
                    <div class="error-container text-danger" id="phone_number-error"></div>
                </div>
                <br>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="phone-number-2"><?= Yii::t('app', 'phone_number_2') ?></label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">+998</span>
                        </div>
                        <input type="text" id="phone_number_2" name="Worker[phone_number_2]" class="form-control">
                    </div>
                    <div class="error-container text-danger" id="phone_number_2-error"></div>
                </div>
                <br>
            </div>
        </div>


        <div class="form-group">
            <label for="worker_salary_amount"><?= Yii::t('app', 'worker_salary_amount') ?></label>
            <input type="text" id="worker_salary_amount" name="WorkerSalary[amount]" step="0.01" class="form-control formatted-numeric-input">
            <div class="error-container" id="worker_salary_amount-error"></div>
        </div>

        <div class="form-group">
            <label for="worker_salary_start_date"><?= Yii::t('app', 'worker_salary_start_date') ?></label>
            <input type="date" id="worker_salary_start_date" name="WorkerSalary[start_date]" class="form-control" value="<?= date('Y-m-d') ?>">
            <div class="error-container" id="worker_salary_start_date-error"></div>
        </div>

    </form>
</div>




<script>
    $(document).ready(function () {
        function initPhoneInput(inputId) {
            const input = $(`#${inputId}`);

            input.on('input', function () {
                let value = this.value.replace(/\D/g, ''); 

                if (value.length > 9) {
                    value = value.slice(0, 9); 
                }

                let formattedValue = '';
                if (value.length > 0) {
                    formattedValue = value.substring(0, 2); 
                }
                if (value.length > 2) {
                    formattedValue += ' ' + value.substring(2, 5); 
                }
                if (value.length > 5) {
                    formattedValue += ' ' + value.substring(5, 7); 
                }
                if (value.length > 7) {
                    formattedValue += ' ' + value.substring(7, 9); 
                }

                this.value = formattedValue;
            });

            input.on('keypress', function (e) {
                if (!/^\d$/.test(e.key)) {
                    e.preventDefault();
                }
            });

            input.on('paste', function (e) {
                e.preventDefault();
                let pastedText = (e.originalEvent.clipboardData || window.clipboardData).getData('text');
                let numericValue = pastedText.replace(/\D/g, '').slice(0, 9); 
                $(this).val(numericValue).trigger('input');
            });
        }

        initPhoneInput('phone_number');
        initPhoneInput('phone_number_2');
    });


</script>
