<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use yii\widgets\Pjax;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app","currency_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");

?>

<div class="card-body">

    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'currency-grid-pjax']); ?>

    <?php if($dataProvider->models): ?>
        <table id="currency-grid-view" class="table table-bordered table-striped compact">
            <thead>
                <tr>
                    <th><?= Yii::t("app", "currency_name") ?></th>
                    <th><?= Yii::t("app", "course") ?></th>
                    <th><?= Yii::t("app", "created_at") ?></th>
                    <th><?= Yii::t("app", "the course is fixed time") ?></th>
                    <th><?= Yii::t("app", "status") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($dataProvider->models as $model): ?>
                    <tr>
                        <td><?= !empty($model['name']) ? Html::encode($model['name']) : '' ?></td>
                        <td><?= !empty($model['course']) ? Yii::$app->formatter->asDecimal($model['course'], 2) : 1 ?></td>
                        <td data-order="<?= !empty($model['created_at']) ? strtotime($model['created_at']) : '' ?>">
                            <?= !empty($model['created_at']) ? Html::encode(date('d.m.Y H:i', strtotime($model['created_at']))) : 'N/A' ?>
                        </td>
                        <td data-order="<?= !empty($model['start_date']) ? strtotime($model['start_date']) : '' ?>">
                            <?= !empty($model['start_date']) ? Html::encode(date('d.m.Y', strtotime($model['start_date']))) : '' ?>
                        </td>
                        <td>
                            <span class="<?= $model['deleted_at'] === NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?= $model['deleted_at'] === NULL ? Yii::t("app", "active") : Yii::t("app", "not_active") ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($model['deleted_at'] == NULL && !$model['is_main']): ?>
                                <div class="dropdown d-inline">
                                    <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                        <?= Yii::t("app", "detail") ?>
                                    </a>
                                    <div class="dropdown-menu">
                                        <a href="#" class="dropdown-item course-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit_course") ?>
                                        </a>
                                        <a href="/backend/currency/courses?id=<?= Html::encode($model['id']) ?>" class="dropdown-item">
                                            <?= Yii::t("app", "detail") ?>
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>

    <?php Pjax::end(); ?>

</div>

<div id="two" data-text="<?= Yii::t("app","edit_course") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');
    var four = $('#four').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#currency-grid-view')) {
            $('#currency-grid-view').DataTable().destroy();
        }

        $('#currency-grid-view').DataTable({
            paging: true,
            searching: true,
            ordering: true,
            pageLength: 50,
            language: {
                paginate: {
                    previous: '<span class="custom-pagination prev"><i class="fa fa-arrow-left"></i></span>',
                    next: '<span class="custom-pagination next"><i class="fa fa-arrow-right"></i></span>'
                },
                search: searchLabel,
                lengthMenu: lengthMenuLabel,
                zeroRecords: zeroRecordsLabel,
                info: infoLabel,
                infoEmpty: infoEmptyLabel,
                infoFiltered: infoFilteredLabel
            },
            columnDefs: [
                { targets: [0, 4, 5], orderable: false },
                { 
                    targets: 2, 
                    type: 'num', 
                    render: function(data, type) {
                        if (type === 'sort' || type === 'type') {
                            return parseFloat(data);
                        }
                        return data;
                    } 
                },
                { 
                    targets: 3, 
                    type: 'num', 
                    render: function(data, type) {
                        return data;
                    } 
                }
            ]
        });
    }

    function initializeDropdown() {
        $('.dropdown-toggle').each(function() {
            var button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click.dropdown', function(e) {
                    e.preventDefault();
                    var dropdownMenu = button.next('.dropdown-menu');
                    $('.dropdown-menu').not(dropdownMenu).hide();
                    dropdownMenu.toggle();
                });
                button.data('handler-attached', true);
            }
        });

        $(document).on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').hide();
            }
        });
    }

    function initializeUpdateCourse() {
        $('.course-update').each(function() {
            var button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click', function() {
                    button.prop('disabled', true);
                    var id = button.attr('data-id');
                    $.ajax({
                        url: '/backend/currency/update',
                        dataType: 'json',
                        type: 'GET',
                        data: { id: id },
                        success: function(response) {
                            if (response.status === 'success') {
                                $('#ideal-mini-modal .modal-title').html(two);
                                $('#ideal-mini-modal .modal-body').html(response.content);
                                $('#ideal-mini-modal .mini-button').addClass('btn-danger update-course-button');
                                $('#ideal-mini-modal').modal('show');
                                
                                // Инициализация форматирования числовых полей в модальном окне
                                if (typeof initializeModalNumberFormatting === 'function') {
                                    initializeModalNumberFormatting();
                                }
                            }
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            console.error('AJAX Error:', xhr.statusText, errorThrown);
                        },
                        complete: function() {
                            button.prop('disabled', false);
                        }
                    });
                });
                button.data('handler-attached', true);
            }
        });

        $(document).on('click', '.update-course-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#cource-update-form').serialize(); 
                $.ajax({
                    url: '/backend/currency/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.status === 'success') {
                            $.pjax.reload({ container: '#currency-grid-pjax' });
                            $('#ideal-mini-modal').modal('hide');
                            iziToast.success({
                                timeout: 5000,
                                icon: 'fas fa-check',
                                message: response.message,
                                position: 'topRight'
                            });
                        } else if (response.status === 'fail') {
                            iziToast.error({
                                timeout: 5000,
                                icon: 'fas fa-exclamation-triangle',
                                message: response.message,
                                position: 'topRight'
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });

        $(document).off('keypress.update-course-button').on('keypress.update-course-button', '#cource-update-form', function(e) { 
            if (e.which === 13) {
                e.preventDefault();
                $('.update-course-button').trigger('click'); 
            }
        });
    }

    function initializeViewCourses() {
        $('.currency-view').each(function() {
            var button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click', function() {
                    var id = button.data('id');
                    window.location.href = '/backend/currency/courses?id=' + id;
                });
                button.data('handler-attached', true);
            }
        });
    }

    function initializeAll() {
        initializeDataTable();
        initializeDropdown();
        initializeUpdateCourse();
        initializeViewCourses();
    }

    $(document).ready(initializeAll);
    $(document).on('pjax:complete', initializeAll);
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>