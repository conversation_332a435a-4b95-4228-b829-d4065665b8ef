<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "currency".
 *
 * @property int $id
 * @property string|null $name
 * @property bool|null $is_main
 * @property string|null $end_date
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property CurrencyCourse[] $currencyCourses
 */
class Currency extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'currency';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['is_main'], 'boolean'],
            [['end_date', 'created_at', 'deleted_at'], 'safe'],
            [['name'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'is_main' => 'Is Main',
            'end_date' => 'End Date',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[CurrencyCourses]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCurrencyCourses()
    {
        return $this->hasMany(CurrencyCourse::class, ['currency_id' => 'id']);
    }
}
