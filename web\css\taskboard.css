.board {
  bottom: 0;
  left: 20px;
  margin-bottom: 8px;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 8px;
  position: absolute;
  right: 20px;
  top: 0;
  -webkit-user-select: none;
  user-select: none;
  white-space: nowrap;
  height: calc(100vh - 75px);
  padding: 0 10px;
  transition: .3s ease-in-out;
}

.item {
  box-sizing: border-box;
  display: inline-block;
  height: 100%;
  margin: 10px 4px;
  vertical-align: top;
  white-space: nowrap;
  width: 300px;
  border-radius: 5px;
  overflow: hidden;
}

.item .title {
  margin: 0;
  font-size: 14px;
  text-align: center;
  font-weight: bold;
  background: #EBECF0;
  display: flex;
  justify-content: space-between;
  padding: 5px 15px;

}
.mini_title{
  display: block;
  text-align: center;
  background: #EBECF0;
}



.client_name{
  font-size: 13px;
  font-weight: lighter !important;
}

.cards {
  margin: 0;
  height: calc(100vh - 160px);
  list-style-type: none;
  overflow-y: auto;
  overflow-x: hidden;
}

.cards ul {
  list-style-type: none;
  margin: 0;
  padding: 5px; 
  background: #EBECF0;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.cards li {
  z-index: 1;
  margin-bottom: 10px;
  padding: 10px;
  /*  border: 1px solid rgba(0,0,0,.2);*/
  box-shadow: 0 1px 0 #091e4240;
  border-radius: 3px;
  margin: 0 0 5px;
  background: #fff;
  padding: 5px;
  position: relative;
  min-height: 52px;
  overflow: hidden;
}

.top_block{
  line-height: 12px;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.add_date{
  font-size: 12px;
  padding: 2px 3px;
  border-radius: 3px;
}

.loadMore {
  opacity: 1 !important;
  background-color: transparent;
  border: none;
  padding: 8px 6px; 
}

.hover {
  background-color: #d4e5f5; 
}

.highlight {
  min-height: 18px;
  border: 1px dashed #9E9E9E !important;
  background-color: transparent !important; 
}



@-webkit-keyframes animateLoadMore {
  0% {
    opacity: 0.5; 
  }
  33% {
    opacity: 0; 
  }
  66% {
    opacity: 0.5; 
  }
  100% {
    opacity: 1; 
  } 
}

.animateLoadMore {
  background-color: #d4e5f5;
  -webkit-animation: animateLoadMore .3s infinite;
  -moz-animation: animateLoadMore .3s infinite;
  -o-animation: animateLoadMore .3s infinite;
  animation: animateLoadMore .3s infinite; 
}




/* Works on Chrome, Edge, and Safari */
.board::-webkit-scrollbar {
    width: 5px;
    height: 8px;
    border-radius: 15px;
}

.board::-webkit-scrollbar-track {
    background: #ccc;
    border-radius: 15px;
}

.board::-webkit-scrollbar-thumb {
    background-color: grey;
    border-radius: 15px;
}

.cards::-webkit-scrollbar {
    width: 5px;
    height: 8px;
    border-radius: 15px;
}

.cards::-webkit-scrollbar-track {
    background: #ccc;
    border-radius: 15px;
}

.cards::-webkit-scrollbar-thumb {
    background-color: grey;
    border-radius: 15px;
}
