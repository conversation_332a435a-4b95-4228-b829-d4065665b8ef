<?php

$params = require __DIR__ . '/params.php';
$db = require __DIR__ . '/db.php';

$config = [
    'id' => 'basic-console',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log', 'queue'],
    'controllerNamespace' => 'app\commands',
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
        '@tests' => '@app/tests',
        '@storage' => '@app/storage',
        '@webroot' => '@app/web',
    ],
    'components' => [
        'authManager' => [
            'class' => 'yii\rbac\DbManager',
        ],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'log' => [
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'queue' => [
            'class' => \yii\queue\db\Queue::class,
            'db' => 'db',
            'tableName' => '{{%queue}}',
            'channel' => 'default',
            'as log' => 'yii\queue\LogBehavior',
            'mutex' => \yii\mutex\PgsqlMutex::class,
            'deleteReleased' => false,
            'attempts' => 1,
            // 'on afterError' => function (ExecEvent $event) {
            //     Yii::error($event->error->getMessage());
            // }
        ],
        'i18n' => [
            'translations' => [
                'app*' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@app/common/languages',
                    'fileMap' => [
                        'app' => 'app.php',
                        'app/error' => 'error.php',
                    ],
                ],
            ],
        ],
        'db' => $db,
    ],
    'params' => $params,
    // 'controllerMap' => [
    //     // 'fixture' => [ // Fixture generation command line.
    //     //     'class' => 'yii\faker\FixtureController',
    //     // ],
    //     'queue' => 'yii\queue\console\QueueController',
    //     'migrate' => [
    //         'class' => 'yii\console\controllers\MigrateController',
    //         'migrationPath' => null,
    //         'migrationNamespaces' => [
    //             // ...
    //             'yii\queue\db\migrations',
    //         ],
    //     ],
    // ],
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
    ];
    // configuration adjustments for 'dev' environment
    // requires version `2.1.21` of yii2-debug module
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        'allowedIPs' => ['*', '::1'],
    ];
}

return $config;
