<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "material_currency".
 *
 * @property int $id
 * @property int|null $material_id
 * @property int|null $currency_id
 * @property float|null $price
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Currency $currency
 * @property Material $material
 */
class MaterialCurrency extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'material_currency';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['material_id', 'currency_id'], 'default', 'value' => null],
            [['material_id', 'currency_id'], 'integer'],
            [['price'], 'number'],
            [['created_at', 'deleted_at'], 'safe'],
            [['currency_id'], 'exist', 'skipOnError' => true, 'targetClass' => Currency::class, 'targetAttribute' => ['currency_id' => 'id']],
            [['material_id'], 'exist', 'skipOnError' => true, 'targetClass' => Material::class, 'targetAttribute' => ['material_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'material_id' => 'Material ID',
            'currency_id' => 'Currency ID',
            'price' => 'Price',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[Currency]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCurrency()
    {
        return $this->hasOne(Currency::class, ['id' => 'currency_id']);
    }

    /**
     * Gets query for [[Material]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterial()
    {
        return $this->hasOne(Material::class, ['id' => 'material_id']);
    }
}
