
<?php
use app\assets\Select2Asset;
Select2Asset::register($this);
?>
<div class="material-form">
    <form id="material-create-form">
        <div class="form-group">
            <label for="name"><?= Yii::t('app', 'material_name') ?></label>
            <input type="text" id="name" name="Material[name]" maxlength="255" class="form-control" required>
            <div class="error-container text-danger" id="name-error"></div>
        </div>

        <div class="form-group">
            <label for="description"><?= Yii::t('app', 'material_description') ?></label>
            <textarea id="description" name="Material[description]" class="form-control" rows="6"></textarea>
            <div class="error-container text-danger" id="description-error"></div>
        </div>

        <div class="form-group">
            <label for="unit_type"><?= Yii::t('app', 'unit_type') ?></label>
            <select id="unit_type" name="Material[unit_type]" class="form-control select2">
                <?php foreach (\app\common\models\Material::getUnitTypeList() as $value => $label): ?>
                    <option value="<?= $value ?>"><?= $label ?></option>
                <?php endforeach; ?>
            </select>
            <div class="error-container text-danger" id="unit_type-error"></div>
        </div>

        <div class="form-group">
            <label for="category_id"><?= Yii::t('app', 'category') ?></label>
            <select id="category_id" name="Material[category_id]" class="form-control select2">
                <option value=""><?= Yii::t('app', 'select_category') ?></option>
                <?php foreach (\app\common\models\MaterialCategory::find()->where(['deleted_at' => null])->all() as $category): ?>
                    <option value="<?= $category->id ?>"><?= $category->name ?></option>
                <?php endforeach; ?>
            </select>
            <div class="error-container text-danger" id="category_id-error"></div>
        </div>

    </form>
</div>
