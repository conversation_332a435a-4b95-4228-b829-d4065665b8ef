<?php
use yii\helpers\Html;
use yii\helpers\ArrayHelper;
use app\common\models\Product;
use app\common\models\Sales;

/* @var $reportData array */
/* @var $startDate string */
/* @var $endDate string */
/* @var $productId int|null */
/* @var $status int|null */

$this->title = Yii::t('app', 'sales_report');
$this->params['breadcrumbs'][] = $this->title;

$products = ArrayHelper::map(
    Product::find()
        ->select(['id', 'name'])
        ->where(['deleted_at' => null])
        ->orderBy('name')
        ->asArray()
        ->all(),
    'id',
    'name'
);

$statuses = [
    Sales::STATUS_NEW => Yii::t('app', 'new'),
    Sales::STATUS_IN_PROGRESS => Yii::t('app', 'in_progress'), 
    Sales::STATUS_CONFIRMED => Yii::t('app', 'confirmed')
];

// Группируем продажи по sale_id
$salesGrouped = [];
foreach ($reportData['sales']['items'] as $row) {
    $salesGrouped[$row['id']][] = $row;
}

// Индекс бонусов по sale_id
$bonusMap = [];
if (!empty($reportData['bonus']['items'])) {
    foreach ($reportData['bonus']['items'] as $b) {
        $bonusMap[$b['sale_id']][] = $b; // каждый элемент содержит product_name и total_quantity
    }
}

// Функция форматирования числовых значений: если дробная часть равна 0 — без десятых, иначе с двумя знаками
$formatMoney = function($value) {
    return (fmod($value, 1.0) == 0.0)
        ? Yii::$app->formatter->asDecimal($value, 0)
        : Yii::$app->formatter->asDecimal($value, 2);
};
?>

<div class="row align-items-center mb-3">
    <div class="col-md-6">
        <h3 class="mb-0"><?= Html::encode($this->title) ?></h3>
    </div>
    <div class="col-md-6 d-flex justify-content-end">
        <?= Html::beginForm(['sales'], 'get', ['class' => 'd-inline-flex align-items-center']) ?>
            <?= Html::input('date', 'start_date', $startDate, ['class' => 'form-control mr-2', 'style' => 'width: 160px;', 'placeholder' => 'С']) ?>
            <?= Html::input('date', 'end_date', $endDate, ['class' => 'form-control mr-2', 'style' => 'width: 160px;', 'placeholder' => 'По']) ?>
            <?= Html::dropDownList('product_id', $productId, ['' => Yii::t('app', 'all_products')] + $products, ['class' => 'form-control mr-2']) ?>
            <?= Html::dropDownList('status', $status, ['' => Yii::t('app', 'status')] + $statuses, ['class' => 'form-control mr-2']) ?>
            <?= Html::submitButton('<i class="fas fa-search"></i> ' . Yii::t('app', 'search'), ['class' => 'btn btn-primary']) ?>
        <?= Html::endForm() ?>
    </div>
</div>

<?php if (empty($reportData['sales']['items'])): ?>
    <div class="alert alert-info text-center mt-3">
        <i class="fas fa-info-circle mr-2"></i> <?= Yii::t('app', 'no_data_found') ?>.
    </div>
<?php else: ?>
<div class="card">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-bordered table-striped mb-0" style="min-width:100%;table-layout:fixed;">
                <thead class="thead-light text-center">
                <tr>
                    <th>ID</th>
                    <th><?= Yii::t('app', 'client') ?></th>
                    <th><?= Yii::t('app', 'product') ?></th>
                    <th><?= Yii::t('app', 'block') ?></th>
                    <th><?= Yii::t('app', 'quantity') ?></th>
                    <th><?= Yii::t('app', 'amount') ?></th>
                    <th><?= Yii::t('app', 'total_amount') ?></th>
                    <th><?= Yii::t('app', 'bonus') ?></th>
                    <th><?= Yii::t('app', 'status') ?></th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($salesGrouped as $saleId => $rows): ?>
                    <?php
                    $rowspan = count($rows);
                    // Подготовка текста бонусов
                    $bonusDisplay = '—';
                    if (isset($bonusMap[$saleId])) {
                        $parts = [];
                        foreach ($bonusMap[$saleId] as $b) {
                            $parts[] = Html::encode($b['product_name']) . ' (' . (int)$b['total_quantity'] . ')';
                        }
                        $bonusDisplay = implode(', ', $parts);
                    }
                    $first = true;
                    foreach ($rows as $row):
                        $qty    = (int)$row['total_quantity'];
                        $blocks = (int)$row['total_blocks'];
                        $amount = (float)$row['total_amount'];
                        $unitPrice = $qty > 0 ? $amount / $qty : 0;

                        // Статус
                        $statusText = '';
                        $statusClass = '';
                        switch ((int)$row['status']) {
                            case Sales::STATUS_NEW:
                                $statusText = 'Новый';
                                $statusClass = 'badge badge-danger';
                                break;
                            case Sales::STATUS_IN_PROGRESS:
                                $statusText = 'В обработке';
                                $statusClass = 'badge badge-primary';
                                break;
                            case Sales::STATUS_CONFIRMED:
                                $statusText = 'Подтвержден';
                                $statusClass = 'badge badge-success';
                                break;
                            default:
                                $statusText = 'Неизвестно';
                                $statusClass = 'badge badge-secondary';
                        }
                    ?>
                    <tr>
                        <?php if ($first): ?>
                            <td rowspan="<?= $rowspan ?>" class="text-center align-middle"><?= $row['id'] ?></td>
                            <td rowspan="<?= $rowspan ?>" class="align-middle"><?= Html::encode($row['full_name'] ?? '—') ?></td>
                        <?php endif; ?>
                        <td><?= Html::encode($row['product_name']) ?></td>
                        <td class="text-right"><?= $blocks ?></td>
                        <td class="text-right"><?= $qty ?></td>
                        <td class="text-right"><?= $formatMoney($unitPrice) ?></td>
                        <td class="text-right"><?= $formatMoney($amount) ?></td>
                        <?php if ($first): ?>
                            <td rowspan="<?= $rowspan ?>" class="align-middle"><?= $bonusDisplay ?></td>
                            <td rowspan="<?= $rowspan ?>" class="text-center align-middle"><span class="<?= $statusClass ?>"><?= $statusText ?></span></td>
                        <?php endif; ?>
                    </tr>
                    <?php $first = false; endforeach; ?>
                <?php endforeach; ?>
                </tbody>
                <tfoot class="bg-light font-weight-bold">
                <tr>
                    <td colspan="3" class="text-right"><?= Yii::t('app', 'total') ?></td>
                    <td class="text-right"><?= $reportData['summary']['totalBlocks'] ?></td>
                    <td class="text-right"><?= $reportData['summary']['totalSales'] ?></td>
                    <td></td>
                    <td class="text-right"><?= $formatMoney($reportData['summary']['totalAmount']) ?></td>
                    <td class="text-right"><?= $reportData['summary']['totalBonus'] ?></td>
                    <td></td>
                </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>
