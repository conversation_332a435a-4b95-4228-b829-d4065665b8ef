<?php

namespace app\modules\backend\models;

use Yii;
use yii\db\ActiveRecord;
use yii\web\IdentityInterface;
use app\common\models\ActionLogs;
use app\common\models\ClientPayments;
use app\common\models\Invoice;
use app\common\models\MaterialDefect;
use app\common\models\Sales;
use app\common\models\SecurityRecords;
/**
 * This is the model class for table "users".
 *
 * @property int $id
 * @property string $username
 * @property string|null $full_name
 * @property string $password
 * @property string $access_token
 * @property string $created_at
 * @property string|null $deleted_at
 * @property int|null $role
 *
 */
class Users extends ActiveRecord implements IdentityInterface
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'users';
    }


    const SCENARIO_CREATE = 'create';
    const SCENARIO_UPDATE = 'update';

    public static function getRoles()
    {
        $auth = Yii::$app->authManager;
        $roles = $auth->getRoles();
        $result = [];
        foreach ($roles as $role) {
            $roleName = $role->name;
            $result[$roleName] = Yii::t('app', $roleName);
        }
        return $result;
    }

    public function getAuthRole()
    {
        $auth = Yii::$app->authManager;
        $roles = $auth->getRolesByUser($this->id);
        return !empty($roles) ? reset($roles)->name : null;
    }
    
    

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['username', 'full_name', 'password', 'role'], 'required', 'on' => self::SCENARIO_CREATE],
            [['username',  'role', 'full_name'], 'required', 'on' => self::SCENARIO_UPDATE],
            [['username', 'password', 'role'], 'required'],
            [['created_at', 'deleted_at', 'access_token'], 'safe'],
            [['username', 'full_name'], 'string', 'max' => 255],
            [['password'], 'string', 'max' => 255],
            [['username'], 'unique'],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_CREATE] = ['username', 'full_name',  'password', 'role'];
        $scenarios[self::SCENARIO_UPDATE] = ['username', 'full_name',  'role', 'password'];
        return $scenarios;
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'username' => 'Логин',
            'full_name' => 'ФИО',
            'password' => 'Пароль',
            'created_at' => 'Дата создания',
            'deleted_at' => 'Дата удаления',
        ];
    }



    /**
     * {@inheritdoc}
     */
    public static function findIdentity($id)
    {
        return static::findOne(['id' => $id, 'deleted_at' => null]);
    }

    /**
     * {@inheritdoc}
     */
    public static function findIdentityByAccessToken($token, $type = null)
    {
        return null; // Не используем токены в данном случае
    }

    /**
     * {@inheritdoc}
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * {@inheritdoc}
     */
    public function getAuthKey()
    {
        return null; // Не используем auth key в данном случае
    }

    /**
     * {@inheritdoc}
     */
    public function validateAuthKey($authKey)
    {
        return true; // Не используем auth key в данном случае
    }

    /**
     * Finds user by username
     *
     * @param string $username
     * @return static|null
     */
    public static function findByUsername($username)
    {
        return static::findOne(['username' => $username, 'deleted_at' => null]);
    }

    /**
     * Validates password
     *
     * @param string $password password to validate
     * @return bool if password provided is valid for current user
     */
    public function validatePassword($password)
    {
        return Yii::$app->security->validatePassword($password, $this->password);
    }

    /**
     * {@inheritdoc}
     */
    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            if ($this->isNewRecord || $this->isAttributeChanged('password')) {
                $this->password = Yii::$app->security->generatePasswordHash($this->password);
            }
            return true;
        }
        return false;
    }

    /**
     * Gets query for [[ActionLogs]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getActionLogs()
    {
        return $this->hasMany(ActionLogs::class, ['user_id' => 'id']);
    }

    /**
     * Gets query for [[ClientPayments]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClientPayments()
    {
        return $this->hasMany(ClientPayments::class, ['add_user_id' => 'id']);
    }

    /**
     * Gets query for [[Expenses]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getExpenses()
    {
        return $this->hasMany(Expenses::class, ['add_user_id' => 'id']);
    }

    /**
     * Gets query for [[Invoices]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getInvoices()
    {
        return $this->hasMany(Invoice::class, ['accept_user_id' => 'id']);
    }

    /**
     * Gets query for [[MaterialDefects]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterialDefects()
    {
        return $this->hasMany(MaterialDefect::class, ['accepted_user_id' => 'id']);
    }

    /**
     * Gets query for [[Sales]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSales()
    {
        return $this->hasMany(Sales::class, ['sell_user_id' => 'id']);
    }

    /**
     * Gets query for [[Sales0]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSales0()
    {
        return $this->hasMany(Sales::class, ['confirm_user_id' => 'id']);
    }

    /**
     * Gets query for [[SecurityRecords]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSecurityRecords()
    {
        return $this->hasMany(SecurityRecords::class, ['add_user_id' => 'id']);
    }
}
