<?php

use yii\db\Migration;

/**
 * Class m250219_000001_create_worker_debt_table
 */
class m250219_000001_create_worker_debt_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('worker_debt', [
            'id' => $this->bigPrimaryKey(),
            'worker_id' => $this->bigInteger()->notNull(),
            'amount' => $this->decimal(10, 2)->notNull(),
            'reason' => $this->text(),
            'status' => $this->boolean()->defaultValue(false),
            'due_date' => $this->date(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'paid_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Добавляем внешний ключ для связи с таблицей worker
        $this->addForeignKey(
            'fk-worker_debt-worker_id',
            'worker_debt',
            'worker_id',
            'worker',
            'id',
            'CASCADE'
        );

        $this->insert('expenses_type', [
            'name' => 'Debt',
            'created_at' => new \yii\db\Expression('NOW()'),
        ]);

        $this->insert('expenses_type', [
            'name' => 'One time payment',
            'created_at' => new \yii\db\Expression('NOW()'),
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-worker_debt-worker_id', 'worker_debt');
        $this->dropTable('worker_debt');
        
    }
}