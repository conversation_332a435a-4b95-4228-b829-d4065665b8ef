<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "client_balance_history".
 *
 * @property int $id
 * @property int $client_id
 * @property float $amount
 * @property float $old_amount
 * @property int $type
 * @property string|null $created_at
 *
 * @property Client $client
 */
class ClientBalanceHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'client_balance_history';
    }

    const TYPE_INCREASE = 1;
    const TYPE_DECREASE = 2;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['client_id', 'amount', 'old_amount', 'type'], 'required'],
            [['client_id'], 'default', 'value' => null],
            [['client_id'], 'integer'],
            [['amount', 'old_amount'], 'number'],
            [['created_at'], 'safe'],
            [['client_id'], 'exist', 'skipOnError' => true, 'targetClass' => Client::class, 'targetAttribute' => ['client_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'client_id' => 'Client ID',
            'amount' => 'Amount',
            'old_amount' => 'Old Amount',
            'type' => 'Type',
            'created_at' => 'Created At',
        ];
    }

    /**
     * Gets query for [[Client]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClient()
    {
        return $this->hasOne(Client::class, ['id' => 'client_id']);
    }
}
