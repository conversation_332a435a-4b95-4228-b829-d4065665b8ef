<?php

namespace app\modules\backend\controllers;

use app\common\models\Cashbox;
use app\common\models\CashboxDetail;
use app\modules\backend\models\Expenses;
use app\modules\backend\models\ExpensesType;
use app\modules\backend\models\WorkerDebt;
use app\modules\backend\models\WorkerFinanceValidationForm;
use app\modules\backend\models\WorkerSalary;
use Yii;
use app\modules\backend\models\Worker;
use app\modules\backend\models\WorkerFinances;
use yii\web\Response;
use app\common\models\Tracking;
use Exception;

class WorkerFinanceController extends BaseController
{
    public function actionIndex()
    {
        $worker_id = Yii::$app->request->get('worker_id');

        $sql = "WITH RunningTotal AS (
            SELECT 
                wf.id,
                wf.worker_id,
                wf.month,
                wf.amount,
                wf.type,
                wf.description,
                wf.created_at,
                w.full_name as worker_name,
                p.name as position,
                ws.amount as salary,
                SUM(
                    CASE 
                        WHEN wf.type = :type_1 OR wf.type = :type_2 THEN wf.amount
                        ELSE 0 
                    END
                ) OVER (
                    PARTITION BY wf.worker_id, wf.month 
                    ORDER BY wf.created_at
                    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                ) as total_paid
            FROM worker_finances wf
            LEFT JOIN worker w ON w.id = wf.worker_id
            LEFT JOIN position p ON p.id = w.position_id
            LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
            WHERE wf.deleted_at IS NULL
            " . ($worker_id ? " AND wf.worker_id = :worker_id" : "") . "
        )
        SELECT 
            *,
            GREATEST(0, salary - total_paid) as remaining_salary
        FROM RunningTotal
        ORDER BY id DESC";
    
        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':type_1', WorkerFinances::TYPE_SALARY);
        $command->bindValue(':type_2', WorkerFinances::TYPE_ADVANCE);

        if ($worker_id) {
            $command->bindValue(':worker_id', $worker_id);
        }
        $result = $command->queryAll();
    
        $workers = Worker::find()
            ->select(['id', 'full_name'])
            ->where(['deleted_at' => null])
            ->all();
    
        return $this->render('index', [
            'result' => $result,
            'workers' => $workers
        ]);
    }

    public function actionSearch()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $worker_id = Yii::$app->request->post('worker_id');
        $startDate = Yii::$app->request->post('start_date');
        $endDate = Yii::$app->request->post('end_date');

        // Приводим даты к полному формату с временем
        if ($startDate) {
            $startDate .= ' 00:00:00';
        }
        if ($endDate) {
            $endDate .= ' 23:59:59';
        }

        $sql = "WITH RunningTotal AS (
            SELECT 
                wf.id,
                wf.worker_id,
                wf.month,
                 wf.amount,
                wf.type,
                wf.description,
                wf.created_at,
                w.full_name as worker_name,
                p.name as position,
                ws.amount as salary,
                SUM(
                    CASE 
                        WHEN wf.type = :type_1 OR wf.type = :type_2 THEN wf.amount
                        ELSE 0 
                    END
                ) OVER (
                    PARTITION BY wf.worker_id, wf.month 
                    ORDER BY wf.created_at
                    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                ) as total_paid
            FROM worker_finances wf
            LEFT JOIN worker w ON w.id = wf.worker_id
            LEFT JOIN position p ON p.id = w.position_id
            LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
            WHERE wf.deleted_at IS NULL
            " . ($worker_id ? " AND wf.worker_id = :worker_id" : "") .
            ($startDate ? " AND wf.created_at >= :start_date" : "") .
            ($endDate ? " AND wf.created_at <= :end_date" : "") .
            "
        )
        SELECT 
            *,
            GREATEST(0, salary - total_paid) as remaining_salary
        FROM RunningTotal
        ORDER BY id DESC";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':type_1', WorkerFinances::TYPE_SALARY);
        $command->bindValue(':type_2', WorkerFinances::TYPE_ADVANCE);
        if ($worker_id) {
            $command->bindValue(':worker_id', $worker_id);
        }
        if ($startDate) {
            $command->bindValue(':start_date', $startDate);
        }
        if ($endDate) {
            $command->bindValue(':end_date', $endDate);
        }

        $result = $command->queryAll();

        // Добавляем информацию о tracking для каждой записи
        foreach ($result as &$row) {
            $tracking = Tracking::find()
                ->where(['process_id' => $row['id']])
                ->andWhere(['progress_type' => Tracking::PAY_FOR_WORKER])
                ->andWhere(['is', 'accepted_at', null])
                ->andWhere(['is', 'deleted_at', null])
                ->one();

            $row['tracking'] = $tracking ? [
                'process_id' => $tracking->process_id,
                'progress_type' => $tracking->progress_type,
                'accepted_at' => $tracking->accepted_at,
                'deleted_at' => $tracking->deleted_at
            ] : null;
        }

        return [
            'status' => 'success',
            'data' => $result
        ];
    }

    public function actionCheckSalary()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $workerId = Yii::$app->request->get('worker_id');

        if (!$workerId) {
            return [
                'status' => 'error',
                'message' => 'Ходим танланмаган!',
            ];
        }

        $currentSalary = WorkerSalary::find()
            ->where(['worker_id' => $workerId])
            ->andWhere(['end_date' => '9999-12-31'])
            ->andWhere(['deleted_at' => null])
            ->one();

        if ($currentSalary) {
            return [
                'status' => 'success',
                'has_salary' => true,
                'amount' => $currentSalary->amount
            ];
        } else {
            return [
                'status' => 'success',
                'has_salary' => false,
            ];
        }
    }

    public function actionGetLastPaymentMonth()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        try {
            $worker_id = Yii::$app->request->get('worker_id');

            if (!$worker_id) {
                throw new \Exception('Ходим танланмаган!');
            }

            $workerSalary = WorkerSalary::find()
                ->where([
                    'worker_id' => $worker_id,
                    'end_date' => '9999-12-31',
                    'deleted_at' => null
                ])
                ->one();

            if (!$workerSalary) {
                throw new \Exception('Ходимнинг актив маоши топилмади!');
            }

            $currentMonth = date('Y-m');
            $result = $this->findAvailableMonth($worker_id, $workerSalary, $currentMonth);
            
            return [
                'status' => 'success',
                'month' => $result['month'],
                'remaining' => $result['remaining'],
                'max_amount' => $result['max_amount'] ?? null
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    private function findAvailableMonth($worker_id, $workerSalary, $month)
    {
        $query = WorkerFinances::find()
            ->where([
                'worker_id' => $worker_id,
                'month' => $month,
                'deleted_at' => null,
            ])
            ->andWhere(['<>', 'type', WorkerFinances::TYPE_BONUS])
            ->andWhere(['<>', 'type', WorkerFinances::TYPE_DEBT]);

        $totalPaid = $query->sum('amount') ?: 0;
        $remaining = $workerSalary->amount - $totalPaid;

        if ($remaining <= 0) {
            return $this->findNextAvailableMonth($worker_id, $workerSalary, $month);
        }

        return [
            'month' => $month,
            'remaining' => $remaining,
            'max_amount' => $workerSalary->amount
        ];
    }

    private function findNextAvailableMonth($worker_id, $workerSalary, $currentMonth)
    {
        $date = new \DateTime($currentMonth . '-01');
        
        for ($i = 0; $i < 12; $i++) { 
            $date->modify('+1 month');
            $nextMonth = $date->format('Y-m');
            
            $query = WorkerFinances::find()
                ->where([
                    'worker_id' => $worker_id,
                    'month' => $nextMonth,
                    'deleted_at' => null,
                ])
                ->andWhere(['<>', 'type', WorkerFinances::TYPE_BONUS])
                ->andWhere(['<>', 'type', WorkerFinances::TYPE_DEBT]);

            $totalPaid = $query->sum('amount') ?: 0;
            $remaining = $workerSalary->amount - $totalPaid;

            if ($remaining > 0) {
                return [
                    'month' => $nextMonth,
                    'remaining' => $remaining,
                    'max_amount' => $workerSalary->amount
                ];
            }
        }

        // Если не найдено ни одного месяца с остатком
        return [
            'month' => $currentMonth,
            'remaining' => 0,
            'max_amount' => $workerSalary->amount
        ];
    }

    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
    
        if (Yii::$app->request->isPost) {
            $model = new WorkerFinanceValidationForm();
            $model->load(Yii::$app->request->post());
            
            if ($model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $createdRecords = [];
                    
                    if ($model->payment_type == WorkerFinances::TYPE_DEBT) {
                        $workerDebt = new WorkerDebt();
                        $workerDebt->worker_id = $model->worker_id;
                        $workerDebt->amount = $model->amount;
                        $workerDebt->reason = $model->description;
                        $workerDebt->status = false;
                        
                        if ($model->deduct_from_salary) {
                            $workerDebt->due_date = $model->due_date;
                        }
                        
                        $workerDebt->created_at = date('Y-m-d H:i:s');
                        
                        if (!$workerDebt->save()) {
                            throw new Exception('Failed to save worker debt record: ' . json_encode($workerDebt->getErrors(), JSON_UNESCAPED_UNICODE));
                        }

                        $workerFinance = new WorkerFinances();
                        $workerFinance->worker_id = $model->worker_id;
                        $workerFinance->month = date('Y-m');
                        $workerFinance->type = $model->payment_type;
                        $workerFinance->amount = $model->amount;
                        $workerFinance->description = $model->description;
                        $workerFinance->created_at = date('Y-m-d H:i:s');

                        if (!$workerFinance->save()) {
                            throw new Exception('Failed to save worker finance record: ' . json_encode($workerFinance->getErrors(), JSON_UNESCAPED_UNICODE));
                        }

                        $createdRecords[] = $workerFinance;
                    } else {
                        $createdRecords = $this->createWorkerFinanceRecords(
                            $model->worker_id,
                            $model->month,
                            $model->amount,
                            $model->payment_type,   
                            $model->description,
                            $model->deduct_from_salary,
                            $model->deduct_amount
                        );
                    }

                    foreach ($createdRecords as $record) {
                        if ($model->payment_type == WorkerFinances::TYPE_SALARY) {
                            $expense_type = ExpensesType::find()->where(['name' => 'Oylik'])->one()->id;
                        } elseif ($model->payment_type == WorkerFinances::TYPE_ADVANCE) {
                            $expense_type = ExpensesType::find()->where(['name' => 'Avans'])->one()->id;
                        } elseif ($model->payment_type == WorkerFinances::TYPE_BONUS) {
                            $expense_type = ExpensesType::find()->where(['name' => 'Bonus'])->one()->id;
                        } elseif ($model->payment_type == WorkerFinances::TYPE_DEBT) {
                            $expense_type = ExpensesType::find()->where(['name' => 'Debt'])->one()->id;
                        } elseif ($model->payment_type == WorkerFinances::TYPE_ONE_TIME_PAYMENT) {
                            $expense_type = ExpensesType::find()->where(['name' => 'One time payment'])->one()->id;
                        } elseif ($model->payment_type == WorkerFinances::TYPE_VACATION_PAY) {
                            $expenseTypeRecord = ExpensesType::find()->where(['name' => 'Vacation pay'])->one();
                            if (!$expenseTypeRecord) {
                                throw new Exception('Тип расходов "Vacation pay" не найден в базе данных');
                            }
                            $expense_type = $expenseTypeRecord->id;
                        } else {
                            $expense_type = null;
                        }
                        
    
                        $expense = new Expenses();
                        $expense->expense_type_id = $expense_type;
                        $expense->add_user_id = Yii::$app->user->id;
                        $expense->payment_type = $model->type;
                        $expense->cashbox_id = $model->cashbox_id;
                        $expense->description = $model->description;
                        $expense->summa = $record->amount;
                        $expense->worker_finance_id = $record->id;
                        // Выплаты сотрудникам всегда подтверждены
                        $expense->status = Expenses::TYPE_ACCEPTED;
                        $expense->created_at = date('Y-m-d H:i:s');
                        
                        if (!$expense->save()) {
                            throw new Exception('Харажатни сақлашда хатолик: ' . json_encode($expense->getErrors(), JSON_UNESCAPED_UNICODE));
                        }
    
                        $tracking = new Tracking();
                        $tracking->progress_type = Tracking::PAY_FOR_WORKER;
                        $tracking->process_id = $record->id;
                        $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                        $tracking->accepted_at = null;
                        $tracking->created_at = date('Y-m-d H:i:s');
                        
                        if (!$tracking->save()) {
                            throw new Exception('Кузатувни сақлашда хатолик: ' . json_encode($tracking->getErrors(), JSON_UNESCAPED_UNICODE));
                        }

                        // Получаем выбранную кассу
                        $cashbox = Cashbox::findOne($model->cashbox_id);
                        if (!$cashbox) {
                            throw new Exception(Yii::t('app', 'Cashbox not found'));
                        }

                        // Выплаты сотрудникам производятся только в сомах (валюта кассы должна быть ID=2)
                        if ($cashbox->currency_id != 2) {
                            throw new Exception('Выплаты сотрудникам можно производить только из кассы в сомах');
                        }

                        if($cashbox->balance < $record->amount) {
                            throw new Exception(Yii::t('app', 'Not enough funds on the source cashbox.'));
                        }
            
                        $cashbox->balance -= $record->amount;
                        if (!$cashbox->save(false)) {
                            throw new Exception(Yii::t('app', 'Failed to update cashbox balance'));
                        }
            
                        $cashboxDetail = new CashboxDetail();
                        $cashboxDetail->cashbox_id = $cashbox->id;
                        $cashboxDetail->amount = $record->amount;
                        $cashboxDetail->add_user_id = Yii::$app->user->id;
                        $cashboxDetail->created_at = date('Y-m-d H:i:s');
                        $cashboxDetail->type = CashboxDetail::TYPE_OUT; 
                        $cashboxDetail->worker_finance_id = $record->id;
                        if (!$cashboxDetail->save(false)) {
                            throw new Exception(Yii::t('app', 'Failed to save cashbox detail'));
                        }
    
                    }
    
                    $transaction->commit();
                    return [
                        'status' => 'success',
                    ];
                } catch (Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage(),
                    ];
                }
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $model = new WorkerFinances();
            $workers = Worker::find()
                ->select(['id', 'full_name'])
                ->where(['deleted_at' => null])
                ->orderBy(['full_name' => SORT_ASC])
                ->asArray()
                ->all();
            
            return [
                "status" => true,
                "content" => $this->renderPartial('create', [
                    'model' => $model,
                    'workers' => $workers,
                ])
            ];
        }
    }
    
    private function createWorkerFinanceRecords($workerId, $month, $amount, $type, $description, $deductFromSalary = false, $deductAmount = 0)
    {
        $monthlySalary = 0;
        $remainingAmount = $amount;
        $createdRecords = [];
        $currentMonth = $month;

        if (!in_array($type, [WorkerFinances::TYPE_BONUS, WorkerFinances::TYPE_ONE_TIME_PAYMENT, WorkerFinances::TYPE_VACATION_PAY])) {
            $workerSalary = WorkerSalary::find()
                ->where([
                    'worker_id' => $workerId,
                    'end_date' => '9999-12-31',
                    'deleted_at' => null
                ])
                ->one();

            if (!$workerSalary) {
                throw new \Exception('Ходимнинг актив маоши топилмади!');
            }
            $monthlySalary = $workerSalary->amount;
        }

        if ($type == WorkerFinances::TYPE_BONUS || $type == WorkerFinances::TYPE_ONE_TIME_PAYMENT || $type == WorkerFinances::TYPE_VACATION_PAY) {
            $model = new WorkerFinances();
            $model->worker_id = $workerId;
            $model->month = $month;
            $model->type = $type;
            $model->amount = $amount;
            $model->description = $description;
            $model->created_at = date('Y-m-d H:i:s');

            if (!$model->save()) {
                throw new \Exception('Тўловни сақлашда хатолик: ' . json_encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
            }
            $createdRecords[] = $model;
        } else {
            if ($deductFromSalary && $currentMonth == $month && $deductAmount > 0) {
                $debts = WorkerDebt::find()
                    ->where([
                        'worker_id' => $workerId,
                        'status' => false,
                    ])
                    ->andWhere(['>', 'amount', 0])
                    ->orderBy(['created_at' => SORT_ASC])
                    ->all();
    
                $remainingDeduction = $deductAmount;
                foreach ($debts as $debt) {
                    if ($remainingDeduction <= 0) {
                        break;
                    }
    
                    $deductionAmount = min($remainingDeduction, $debt->amount);
    
                    $debtPayment = new WorkerFinances();
                    $debtPayment->worker_id = $workerId;
                    $debtPayment->type = WorkerFinances::TYPE_DEBT_PAYMENT;
                    $debtPayment->amount = $deductionAmount;
                    $debtPayment->description = Yii::t('app', 'debt_payment');
                    $debtPayment->month = $currentMonth;
                    $debtPayment->created_at = date('Y-m-d H:i:s');
    
                    if (!$debtPayment->save()) {
                        throw new \Exception('Ошибка при сохранении погашения долга');
                    }
    
                    $debt->amount -= $deductionAmount;
                    if ($debt->amount <= 0) {
                        $debt->status = true;
                        $debt->paid_at = date('Y-m-d H:i:s');
                    }
    
                    if (!$debt->save()) {
                        throw new \Exception('Ошибка при обновлении долга');
                    }
    
                    $remainingDeduction -= $deductionAmount;
                    $createdRecords[] = $debtPayment;
                }
            }
    
            while ($remainingAmount > 0) {
                $query = WorkerFinances::find()
                    ->where([
                        'worker_id' => $workerId,
                        'month' => $currentMonth,
                        'deleted_at' => null,
                    ])
                    ->andWhere(['<>', 'type', WorkerFinances::TYPE_BONUS])
                    ->andWhere(['<>', 'type', WorkerFinances::TYPE_DEBT]);
    
                $totalPaid = $query->sum('amount') ?: 0;
                $remaining = $monthlySalary - $totalPaid;
    
                if ($remaining > 0) {
                    $paymentAmount = min($remainingAmount, $remaining);
    
                    if ($paymentAmount > 0) {
                        $model = new WorkerFinances();
                        $model->worker_id = $workerId;
                        $model->month = $currentMonth;
                        $model->type = $type;
                        $model->amount = $paymentAmount;
                        $model->description = $description;
                        $model->created_at = date('Y-m-d H:i:s');
    
                        if (!$model->save()) {
                            throw new \Exception('Тўловни сақлашда хатолик: ' . json_encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
                        }
    
                        $remainingAmount -= $paymentAmount;
                        $createdRecords[] = $model;
                    }
                }
    
                if ($remainingAmount > 0) {
                    $currentMonth = date('Y-m', strtotime($currentMonth . '-01 +1 month'));
                } else {
                    break;
                }
            }
    
            if ($remainingAmount > 0) {
                throw new \Exception('Не удалось распределить всю сумму. Остаток: ' . $remainingAmount);
            }
        }
    
        return $createdRecords;
    }
    

    public function actionGetActiveDebts()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $worker_id = Yii::$app->request->get('worker_id');
        
        if (!$worker_id) {
            return [
                'status' => 'error',
                'message' => 'Не указан ID работника'
            ];
        }

        $debts = WorkerDebt::find()
            ->where(['worker_id' => $worker_id])
            ->andWhere(['>', 'amount', 0])
            ->all();

        return [
            'status' => 'success',
            'debts' => array_map(function($debt) {
                return [
                    'id' => $debt->id,
                    'amount' => $debt->amount,
                    'created_at' => $debt->created_at
                ];
            }, $debts)
        ];
    }

    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $currentRecord = WorkerFinances::findOne($data['WorkerFinances']['id']);
                if (!$currentRecord) {
                    throw new \Exception('Запись не найдена');
                }

                $lastRecord = WorkerFinances::find()
                    ->where(['worker_id' => $currentRecord->worker_id])
                    ->andWhere(['deleted_at' => null])
                    ->orderBy(['id' => SORT_DESC])
                    ->one();

                if ($lastRecord->id !== $currentRecord->id) {
                    throw new \Exception('You can only delete the last entry');
                }

                $tracking = Tracking::find()
                    ->where(['process_id' => $currentRecord->id])
                    ->andWhere(['progress_type' => Tracking::PAY_FOR_WORKER])
                    ->andWhere(['is not', 'accepted_at', null])
                    ->andWhere(['is not', 'deleted_at', null])
                    ->one();

                if ($tracking) {
                    throw new \Exception(Yii::t('app', 'Cannot delete confirmed payment'));
                }

                Expenses::updateAll(['deleted_at' => date('Y-m-d H:i:s')], ['worker_finance_id' => $currentRecord->id]);
                Tracking::updateAll(['deleted_at' => date('Y-m-d H:i:s')], ['process_id' => $currentRecord->id, 'progress_type' => Tracking::PAY_FOR_WORKER]);
                
                // Получаем кассу из связанного расхода
                $expense = Expenses::find()
                    ->where(['worker_finance_id' => $currentRecord->id])
                    ->andWhere(['deleted_at' => null])
                    ->one();
                
                if (!$expense || !$expense->cashbox_id) {
                    throw new \Exception('Не найден связанный расход или касса');
                }
                
                $cashbox = Cashbox::findOne($expense->cashbox_id);
                if (!$cashbox) {
                    throw new \Exception(Yii::t('app', 'Cashbox not found'));
                }

                // Выплаты сотрудникам производятся только в сомах (валюта кассы должна быть ID=2)
                if ($cashbox->currency_id != 2) {
                    throw new \Exception('Выплаты сотрудникам можно производить только из кассы в сомах');
                }

                // При удалении выплаты возвращаем средства в кассу
                $cashbox->balance += $currentRecord->amount;
                if (!$cashbox->save(false)) {
                    throw new \Exception(Yii::t('app', 'Failed to update cashbox balance'));
                }
            
                $cashboxDetail = new CashboxDetail();
                $cashboxDetail->cashbox_id = $cashbox->id;
                $cashboxDetail->amount = $currentRecord->amount;
                $cashboxDetail->add_user_id = Yii::$app->user->id;
                $cashboxDetail->created_at = date('Y-m-d H:i:s');
                $cashboxDetail->type = CashboxDetail::TYPE_IN; // Возврат средств в кассу
                if (!$cashboxDetail->save(false)) {
                    throw new \Exception(Yii::t('app', 'Failed to save cashbox detail'));
                }
                
                $currentRecord->deleted_at = date('Y-m-d H:i:s');
                if (!$currentRecord->save()) {
                    throw new \Exception('Ошибка при мягком удалении записи: ' . json_encode($currentRecord->getErrors()));
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $currentRecord = WorkerFinances::findOne($id);
            return [
                'status' => 'success',
                'content' => $this->renderPartial('delete', ['model' => $currentRecord])
            ];
        }
    }

    public function actionExportMonthlyReport()
{
    if (Yii::$app->request->isGet) {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $workers = Worker::find()->where(['deleted_at' => null])->all();
        $month = [
            '1' => 'Январь',
            '2' => 'Февраль',
            '3' => 'Март',
            '4' => 'Апрель',
            '5' => 'Май',
            '6' => 'Июнь',
            '7' => 'Июль',
            '8' => 'Август',
            '9' => 'Сентябрь',
            '10' => 'Октябрь',
            '11' => 'Ноябрь',
            '12' => 'Декабрь',
        ];
        
        $years = [];
        $currentYear = date('Y');
        for ($i = $currentYear - 2; $i <= $currentYear + 2; $i++) {
            $years[$i] = $i;
        }
        
        return [
            'status' => 'success',
            'content' => $this->renderPartial('export-monthly-report', [
                'workers' => $workers,
                'month' => $month,
                'years' => $years
            ])
        ];
    } else if (Yii::$app->request->isPost) {
        $worker_id = Yii::$app->request->post('worker_id');
        $month = Yii::$app->request->post('month');
        $year = Yii::$app->request->post('year', date('Y'));

        // Создаем Excel-файл
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();

        // Массив месяцев для названий листов
        $monthNames = [
            '1' => 'Январь', '2' => 'Февраль', '3' => 'Март', '4' => 'Апрель',
            '5' => 'Май', '6' => 'Июнь', '7' => 'Июль', '8' => 'Август',
            '9' => 'Сентябрь', '10' => 'Октябрь', '11' => 'Ноябрь', '12' => 'Декабрь'
        ];

        // Определяем оплаченные месяцы
        $paidMonths = [];
        if ($month && !empty($month)) {
            // Если указан конкретный месяц, проверяем, оплачен ли он
            $monthStr = str_pad((string)$month, 2, '0', STR_PAD_LEFT);
            $periodStr = $year . '-' . $monthStr;
            $hasPayments = WorkerFinances::find()
                ->where(['month' => $periodStr, 'deleted_at' => null])
                ->exists();
            if ($hasPayments) {
                $paidMonths[] = $month;
            }
        } else {
            // Если указан только год, ищем все оплаченные месяцы
            for ($m = 1; $m <= 12; $m++) {
                $monthStr = str_pad((string)$m, 2, '0', STR_PAD_LEFT);
                $periodStr = $year . '-' . $monthStr;
                $hasPayments = WorkerFinances::find()
                    ->where(['month' => $periodStr, 'deleted_at' => null])
                    ->exists();
                if ($hasPayments) {
                    $paidMonths[] = $m;
                }
            }
        }

        // Если нет оплаченных месяцев, возвращаем пустой файл или сообщение
        if (empty($paidMonths)) {
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('Отчет');
            $sheet->setCellValue('A1', 'Нет данных об оплате за указанный период');
            $fileName = ($month && !empty($month)) 
                ? 'Грузчики_' . $monthNames[$month] . '_' . $year . '_пустой.xlsx'
                : 'Грузчики_' . $year . '_годовой_пустой.xlsx';
        } else {
            // Получаем данные о работниках
            $workersQuery = Worker::find()
                ->select(['w.id', 'w.full_name', 'p.name as position', 'ws.amount as salary'])
                ->from('worker w')
                ->leftJoin('position p', 'p.id = w.position_id')
                ->leftJoin('worker_salary ws', 'ws.worker_id = w.id AND ws.end_date = :end_date')
                ->where(['w.deleted_at' => null]);

            if ($worker_id) {
                $workersQuery->andWhere(['w.id' => $worker_id]);
            }

            $workers = $workersQuery->params([':end_date' => '9999-12-31'])->asArray()->all();

            // Проходим по каждому оплаченному месяцу
            foreach ($paidMonths as $index => $currentMonth) {
                $monthStr = str_pad((string)$currentMonth, 2, '0', STR_PAD_LEFT);
                $periodStr = $year . '-' . $monthStr;

                // Создаем лист
                if ($index == 0) {
                    $sheet = $spreadsheet->getActiveSheet();
                } else {
                    $sheet = $spreadsheet->createSheet();
                }
                $sheet->setTitle($monthNames[$currentMonth]);

                // Получаем данные о финансах для каждого работника
                $result = [];
                foreach ($workers as $worker) {
                    $workerId = $worker['id'];

                    $salary = $worker['salary'] ?: 0;
                    $advanceAmount = WorkerFinances::find()
                        ->where(['worker_id' => $workerId, 'month' => $periodStr, 'type' => WorkerFinances::TYPE_ADVANCE, 'deleted_at' => null])
                        ->sum('amount') ?: 0;
                    $salaryAmount = WorkerFinances::find()
                        ->where(['worker_id' => $workerId, 'month' => $periodStr, 'type' => WorkerFinances::TYPE_SALARY, 'deleted_at' => null])
                        ->sum('amount') ?: 0;
                    $bonusAmount = WorkerFinances::find()
                        ->where(['worker_id' => $workerId, 'month' => $periodStr, 'type' => WorkerFinances::TYPE_BONUS, 'deleted_at' => null])
                        ->sum('amount') ?: 0;
                    $oneTimeAmount = WorkerFinances::find()
                        ->where(['worker_id' => $workerId, 'month' => $periodStr, 'type' => WorkerFinances::TYPE_ONE_TIME_PAYMENT, 'deleted_at' => null])
                        ->sum('amount') ?: 0;
                    $debtPaymentAmount = WorkerFinances::find()
                        ->where(['worker_id' => $workerId, 'month' => $periodStr, 'type' => WorkerFinances::TYPE_DEBT_PAYMENT, 'deleted_at' => null])
                        ->sum('amount') ?: 0;
                    $totalDebt = WorkerDebt::find()
                        ->where(['worker_id' => $workerId, 'status' => false])
                        ->sum('amount') ?: 0;

                    $totalToPay = $salary + $bonusAmount + $oneTimeAmount - $advanceAmount;
                    $receivedAmount = ($debtPaymentAmount > 0) ? max(0, $totalToPay - $debtPaymentAmount) : ($salaryAmount > 0 ? $salaryAmount : $totalToPay);

                    $result[] = [
                        'id' => $workerId,
                        'full_name' => $worker['full_name'],
                        'position' => $worker['position'],
                        'salary' => $salary,
                        'bonus' => $bonusAmount,
                        'advance' => $advanceAmount,
                        'to_pay' => $totalToPay,
                        'total_debt' => $totalDebt,
                        'debt_payment' => $debtPaymentAmount,
                        'received' => $receivedAmount
                    ];
                }

                // Устанавливаем заголовок листа
                $title = 'Работники ' . $monthNames[$currentMonth] . ' ' . $year;

                // Стили
                $titleStyle = [
                    'font' => ['bold' => true, 'size' => 14],
                    'alignment' => [
                        'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                        'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                    ],
                ];
                $headerStyle = [
                    'font' => ['bold' => true],
                    'alignment' => [
                        'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                        'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                    ],
                    'borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]],
                ];
                $dataStyle = [
                    'borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]],
                    'alignment' => ['vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER],
                ];

                // Заголовок отчета
                $sheet->setCellValue('A1', $title);
                $sheet->mergeCells('A1:K1');
                $sheet->getStyle('A1:K1')->applyFromArray($titleStyle);
                $sheet->getRowDimension(1)->setRowHeight(30);

                // Заголовки таблицы
                $sheet->setCellValue('A2', '№');
                $sheet->setCellValue('B2', 'Ф.И.О.');
                $sheet->setCellValue('C2', 'должн.');
                $sheet->setCellValue('D2', 'Оклад');
                $sheet->setCellValue('E2', '(+)Надбавки');
                $sheet->setCellValue('F2', '(-)Аванс');
                $sheet->setCellValue('G2', 'К выд.');
                $sheet->setCellValue('H2', 'Долг (общий)');
                $sheet->setCellValue('I2', 'Удержан долг за мес');
                $sheet->setCellValue('J2', 'получено на руки');
                $sheet->setCellValue('K2', 'роспись');
                $sheet->getStyle('A2:K2')->applyFromArray($headerStyle);

                // Заполняем данные
                $row = 3;
                $counter = 1;
                foreach ($result as $item) {
                    $sheet->setCellValue('A' . $row, $counter);
                    $sheet->setCellValue('B' . $row, $item['full_name']);
                    $sheet->setCellValue('C' . $row, $item['position']);
                    $sheet->setCellValue('D' . $row, $item['salary']);
                    $sheet->setCellValue('E' . $row, $item['bonus']);
                    $sheet->setCellValue('F' . $row, $item['advance']);
                    $sheet->setCellValue('G' . $row, $item['to_pay']);
                    $sheet->setCellValue('H' . $row, $item['total_debt']);
                    $sheet->setCellValue('I' . $row, $item['debt_payment']);
                    $sheet->setCellValue('J' . $row, $item['received']);
                    $sheet->getStyle('A' . $row . ':K' . $row)->applyFromArray($dataStyle);
                    $row++;
                    $counter++;
                }

                // Итоговая строка
                $sheet->setCellValue('A' . $row, 'ИТОГО');
                $sheet->mergeCells('A' . $row . ':C' . $row);
                $lastDataRow = $row - 1;
                $sheet->setCellValue('D' . $row, '=SUM(D3:D' . $lastDataRow . ')');
                $sheet->setCellValue('E' . $row, '=SUM(E3:E' . $lastDataRow . ')');
                $sheet->setCellValue('F' . $row, '=SUM(F3:F' . $lastDataRow . ')');
                $sheet->setCellValue('G' . $row, '=SUM(G3:G' . $lastDataRow . ')');
                $sheet->setCellValue('H' . $row, '=SUM(H3:H' . $lastDataRow . ')');
                $sheet->setCellValue('I' . $row, '=SUM(I3:I' . $lastDataRow . ')');
                $sheet->setCellValue('J' . $row, '=SUM(J3:J' . $lastDataRow . ')');
                $sheet->getStyle('A' . $row . ':K' . $row)->applyFromArray([
                    'font' => ['bold' => true],
                    'borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]],
                ]);

                // Устанавливаем ширину столбцов
                $sheet->getColumnDimension('A')->setAutoSize(true);
                $sheet->getColumnDimension('B')->setAutoSize(true);
                $sheet->getColumnDimension('C')->setAutoSize(true);
                $sheet->getColumnDimension('D')->setAutoSize(true);
                $sheet->getColumnDimension('E')->setAutoSize(true);
                $sheet->getColumnDimension('F')->setAutoSize(true);
                $sheet->getColumnDimension('G')->setAutoSize(true);
                $sheet->getColumnDimension('H')->setAutoSize(true);
                $sheet->getColumnDimension('I')->setAutoSize(true);
                $sheet->getColumnDimension('J')->setAutoSize(true);
                $sheet->getColumnDimension('K')->setAutoSize(true);

                // Форматируем числовые значения
                $numericColumns = ['D', 'E', 'F', 'G', 'H', 'I', 'J'];
                foreach ($numericColumns as $col) {
                    for ($i = 3; $i <= $row; $i++) {
                        $sheet->getStyle($col . $i)->getNumberFormat()->setFormatCode('#,##0');
                    }
                }
            }

            // Формируем имя файла для скачивания
            $fileName = ($month && !empty($month)) 
                ? 'Грузчики_' . $monthNames[$month] . '_' . $year . '.xlsx'
                : 'Грузчики_' . $year . '_годовой.xlsx';
        }

        // Создаем временный файл
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $tempFile = tempnam(sys_get_temp_dir(), 'excel');
        $writer->save($tempFile);

        // Отправляем файл на скачивание
        return Yii::$app->response->sendFile(
            $tempFile,
            $fileName,
            [
                'mimeType' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'inline' => false
            ]
        )->on(Response::EVENT_AFTER_SEND, function() use ($tempFile) {
            @unlink($tempFile); // Удаляем временный файл после отправки
        });
    }
}

}
