<?php

namespace app\modules\backend\controllers;

use Yii;
use app\common\models\Sales;

class DebtController extends BaseController
{
    public function actionIndex()
    {
        $supplierDebts = $this->getSupplierDebts();
        $clientDebts = $this->getClientDebts();

        return $this->render('index', [
            'supplierDebts' => $supplierDebts,
            'clientDebts' => $clientDebts
        ]);
    }

    private function getSupplierDebts()
    {
        $sql = "
            WITH supplier_total_invoices AS (
                SELECT 
                    s.id as supplier_id,
                    s.full_name as supplier_name,
                    COALESCE(SUM(i.total_amount), 0) as total_invoice_amount
                FROM supplier s
                LEFT JOIN invoice i ON s.id = i.supplier_id AND i.deleted_at IS NULL
                WHERE s.deleted_at IS NULL
                GROUP BY s.id, s.full_name
            ),
            supplier_total_payments AS (
                SELECT 
                    s.id as supplier_id,
                    COALESCE(SUM(sp.amount), 0) as total_paid_amount
                FROM supplier s
                LEFT JOIN supplier_payments sp ON s.id = sp.supplier_id AND sp.deleted_at IS NULL
                WHERE s.deleted_at IS NULL
                GROUP BY s.id
            )
            SELECT 
                sti.supplier_id,
                sti.supplier_name,
                sti.total_invoice_amount,
                COALESCE(stp.total_paid_amount, 0) as total_paid_amount,
                (sti.total_invoice_amount - COALESCE(stp.total_paid_amount, 0)) as debt_amount
            FROM supplier_total_invoices sti
            LEFT JOIN supplier_total_payments stp ON sti.supplier_id = stp.supplier_id
            WHERE (sti.total_invoice_amount - COALESCE(stp.total_paid_amount, 0)) != 0
            ORDER BY debt_amount DESC
        ";

        return Yii::$app->db->createCommand($sql)->queryAll();
    }

    private function getClientDebts()
    {
        $sql = "
            WITH client_total_sales AS (
                SELECT 
                    c.id as client_id,
                    c.full_name as client_name,
                    COALESCE(SUM(s.total_sum), 0) as total_sales_amount
                FROM client c
                LEFT JOIN sales s ON c.id = s.client_id 
                    AND s.deleted_at IS NULL 
                    AND s.status = :status_confirmed
                WHERE c.deleted_at IS NULL
                GROUP BY c.id, c.full_name
            ),
            client_total_payments AS (
                SELECT 
                    c.id as client_id,
                    COALESCE(SUM(cp.summa), 0) as total_paid_amount
                FROM client c
                LEFT JOIN client_payments cp ON c.id = cp.client_id AND cp.deleted_at IS NULL
                WHERE c.deleted_at IS NULL
                GROUP BY c.id
            )
            SELECT 
                cts.client_id,
                cts.client_name,
                cts.total_sales_amount,
                COALESCE(ctp.total_paid_amount, 0) as total_paid_amount,
                (cts.total_sales_amount - COALESCE(ctp.total_paid_amount, 0)) as debt_amount
            FROM client_total_sales cts
            LEFT JOIN client_total_payments ctp ON cts.client_id = ctp.client_id
            WHERE (cts.total_sales_amount - COALESCE(ctp.total_paid_amount, 0)) != 0
            ORDER BY debt_amount DESC
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':status_confirmed', Sales::STATUS_CONFIRMED);
        
        return $command->queryAll();
    }
}