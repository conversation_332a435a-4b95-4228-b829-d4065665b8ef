<?php

namespace app\modules\api\models;

use app\common\models\Product;
use Yii;
use yii\base\Model;

class SendToFreeProductsForm extends Model
{
    public $products;
    public $client;
    public $car_number;

    public function rules()
    {
        return [
            [['products', 'client'], 'required'],
            [['products'], 'validateProducts'],
            [['client'], 'string', 'max' => 250],
            [['car_number'], 'string', 'max' => 255],
        ];
    }

    public function validateProducts($attribute, $params)
    {
        if (!is_array($this->products)) {
            $this->addError($attribute, 'Неверный формат данных продуктов');
            return;
        }

        foreach ($this->products as $index => $product) {
            if (!isset($product['product_id']) || !isset($product['quantity_block'])) {
                $this->addError($attribute, "Продукт #{$index} должен содержать product_id и quantity_block");
                continue;
            }

            if (!is_numeric($product['product_id']) || $product['product_id'] <= 0) {
                $this->addError($attribute, "Неверный product_id в продукте #{$index}");
                continue;
            }

            if (!is_numeric($product['quantity_block']) || $product['quantity_block'] <= 0) {
                $this->addError($attribute, "Количество блоков должно быть больше 0 в продукте #{$index}");
                continue;
            }

            // Проверяем существование продукта
            $productModel = Product::findOne($product['product_id']);
            if (!$productModel) {
                $this->addError($attribute, "Продукт с ID {$product['product_id']} не найден");
                continue;
            }
        }
    }

    public function attributeLabels()
    {
        return [
            'products' => Yii::t('app', 'products'),
            'client' => Yii::t('app', 'client'),
            'car_number' => Yii::t('app', 'car number'),
        ];
    }
}
