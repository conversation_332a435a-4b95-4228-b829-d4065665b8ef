<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "payment_type".
 *
 * @property int $id
 * @property int|null $type
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property CashboxPaymentType[] $cashboxPaymentTypes
 */
class PaymentType extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'payment_type';
    }

    const CASH = 1;
    const TRANSFER = 2;
    const TERMINAL = 3;
    const PAYMENT_CARD = 4;

    public static function getTypeLabels()
    {
        return [
            self::CASH => Yii::t("app", "cash"), // "Наличные"
            self::TRANSFER => Yii::t("app", "transfer"), // "Перевод"
            self::TERMINAL => Yii::t("app", "terminal"), // "Терминал"
            self::PAYMENT_CARD => Yii::t("app", "payment_card"), // "Платежная карта"
        ];
    }

    public static function getTypeDescription($type)
    {
        switch ($type) {
            case self::CASH:
                return Yii::t("app", "cash");
            case self::TRANSFER:
                return Yii::t("app", "transfer");
            case self::TERMINAL:
                return Yii::t("app", "terminal");
            case self::PAYMENT_CARD:
                return Yii::t("app", "payment_card");
            default:
                return Yii::t("app", "unknown_type"); 
        }
    }
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type'], 'default', 'value' => null],
            [['type'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'type' => 'Type',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[CashboxPaymentTypes]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCashboxPaymentTypes()
    {
        return $this->hasMany(CashboxPaymentType::class, ['payment_type_id' => 'id']);
    }
}
