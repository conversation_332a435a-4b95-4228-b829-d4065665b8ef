<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "equipment_photos".
 *
 * @property int $id
 * @property int $equipment_id
 * @property string $photo_path
 * @property bool $is_main
 * @property string $created_at
 *
 * @property Equipment $equipment
 */
class EquipmentPhoto extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'equipment_photos';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['equipment_id', 'photo_path'], 'required'],
            [['equipment_id'], 'integer'],
            [['is_main'], 'boolean'],
            [['created_at'], 'safe'],
            [['photo_path'], 'string', 'max' => 255],
            [['equipment_id'], 'exist', 'skipOnError' => true, 'targetClass' => Equipment::class, 'targetAttribute' => ['equipment_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'equipment_id' => Yii::t('app', 'equipment'),
            'photo_path' => Yii::t('app', 'photo'),
            'is_main' => Yii::t('app', 'main_photo'),
            'created_at' => Yii::t('app', 'created_at'),
        ];
    }

    /**
     * Gets query for [[Equipment]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getEquipment()
    {
        return $this->hasOne(Equipment::class, ['id' => 'equipment_id']);
    }
}
