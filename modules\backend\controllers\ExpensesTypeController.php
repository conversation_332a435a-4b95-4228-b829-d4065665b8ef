<?php

namespace app\modules\backend\controllers;

use Yii;
use yii\web\Response;
use app\modules\backend\models\ExpensesType;
use yii\data\ActiveDataProvider;

/**
 * ExpensesTypeController implements the CRUD actions for ExpensesType model.
 */
class ExpensesTypeController extends BaseController
{
    public function actionIndex()
    {
        $query = ExpensesType::find()->orderBy(['id' => SORT_DESC]);
        
        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => false
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $model = new ExpensesType();

        if (Yii::$app->request->isPost) {
            if ($model->load(Yii::$app->request->post())) {

                if (!$model->validate()) {
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors()
                    ];
                }

                $expensesType = ExpensesType::find()->where(['name' => $model->name])->one();
                if($expensesType){
                    return [
                        'status' => 'error',
                        'errors' => ['name' => [Yii::t('app', 'expenses_name_already_exists')]]
                    ];
                }

                if(!$model->save()){
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors()
                    ];
                }

                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'record_successfully_created')
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }
        }

        return [
            'status' => 'success',
            'content' => $this->renderPartial('create', [
                'model' => $model,
            ])
        ];
    }

    public function actionUpdate()
{
    Yii::$app->response->format = Response::FORMAT_JSON;

    if (Yii::$app->request->isPost) {
        $postData = Yii::$app->request->post('ExpensesType');
        $id = isset($postData['id']) ? $postData['id'] : null;

        if (!$id) {
            return [
                'status' => 'error',
                'errors' => ['id' => [Yii::t('app', 'id_not_provided')]]
            ];
        }

        $model = ExpensesType::findOne($id);

        if (!$model) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'record_not_found')
            ];
        }

        if ($model->load(Yii::$app->request->post())) {
            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }

            $existingExpensesType = ExpensesType::find()
                ->where(['name' => $model->name])
                ->andWhere(['!=', 'id', $model->id])
                ->one();

            if ($existingExpensesType) {
                return [
                    'status' => 'error',
                    'errors' => ['name' => [Yii::t('app', 'expenses_name_already_exists')]]
                ];
            }

            if (!$model->save()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }

            return [
                'status' => 'success',
                'message' => Yii::t('app', 'record_successfully_updated')
            ];
        } else {
            return [
                'status' => 'error',
                'errors' => $model->getErrors()
            ];
        }
    } elseif (Yii::$app->request->isGet) {
        $id = Yii::$app->request->get('id');

        if (!$id) {
            return [
                'status' => 'error',
                'errors' => ['id' => [Yii::t('app', 'id_not_provided')]]
            ];
        }

        $model = ExpensesType::findOne($id);

        if (!$model) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'record_not_found')
            ];
        }

        return [
            'status' => 'success',
            'content' => $this->renderPartial('update', [
                'model' => $model,
            ])
        ];
    } else {
        return [
            'status' => 'error',
            'message' => Yii::t('app', 'invalid_request_method')
        ];
    }
}



    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
       
        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $model = ExpensesType::findOne($postData['ExpensesType']['id']);
    
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'record_not_found')
                ];
            }
            $model->deleted_at = date('Y-m-d H:i:s');
            if ($model->save()) {
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'record_successfully_deleted')
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = ExpensesType::findOne($id);
    
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'record_not_found')
                ];
            }
    
            return [
                'status' => 'success',
                'content' => $this->renderPartial('delete', [
                    'model' => $model,
                ])
            ];
        }
    }
}