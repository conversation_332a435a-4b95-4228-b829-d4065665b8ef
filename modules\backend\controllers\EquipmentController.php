<?php

namespace app\modules\backend\controllers;

use Yii;
use app\common\models\Equipment;
use app\common\models\EquipmentPhoto;
use app\common\models\EquipmentPart;
use app\common\models\EquipmentPartAssignment;
use app\common\models\EquipmentPartMovement;
use app\helpers\ImageHelper;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use yii\web\UploadedFile;
use yii\helpers\FileHelper;

/**
 * EquipmentController implements the CRUD actions for Equipment model.
 */
class EquipmentController extends BaseController
{
    /**
     * Lists all Equipment models.
     * @return mixed
     */
    public function actionIndex()
    {
        $sql = "
            SELECT e.*
            FROM equipment e
        ";

        $result = Yii::$app->db->createCommand($sql)->queryAll();

        if (Yii::$app->request->isAjax) {
            return $this->renderAjax('index', [
                'result' => $result,
            ]);
        }

        return $this->render('index', [
            'result' => $result,
        ]);
    }

    public function actionSearch()
    {
        if (Yii::$app->request->isAjax) {
            $status = Yii::$app->request->post('status');

            // Базовый SQL-запрос
            $sql = "
                SELECT e.*
                FROM equipment e
                WHERE e.deleted_at IS NULL
            ";

            // Параметры для SQL-запроса
            $params = [];

            // Добавляем фильтр по статусу, если он передан
            if ($status !== null && $status !== '') {
                $sql .= " AND e.status = :status";
                $params[':status'] = (int)$status; // Приводим статус к целому числу
            }

            // Выполняем запрос
            $result = Yii::$app->db->createCommand($sql, $params)->queryAll();


            if ($result) {
                $html = $this->renderPartial('_index', [
                    'result' => $result
                ]);

                return $this->asJson([
                    'status' => 'success',
                    'content' => $html
                ]);
            }

            return $this->asJson([
                'status' => 'error',
                'message' => Yii::t('app', 'No results found')
            ]);
        }
    }
    /**
     * Creates a new Equipment model.
     * @return mixed
     */    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            return [
                'content' => $this->renderPartial('create'),
            ];
        }

        if (Yii::$app->request->isPost) {
            $model = new Equipment();
            $model->scenario = 'create';
            $data = Yii::$app->request->post();

            $uploadedFiles = UploadedFile::getInstancesByName('photos');
            
            $model->name = $data['name'];
            $model->description = $data['description'];
            $model->purchase_date = $data['purchase_date'];
            $model->status = $data['status'];
            $model->created_at = date('Y-m-d H:i:s');

            $transaction = Yii::$app->db->beginTransaction();
            try {
                if($model->save()) {
                    // Обработка загруженных фотографий
                    if (!empty($uploadedFiles)) {
                        $directory = Yii::getAlias('@webroot/uploads/equipment/');
                        if (!is_dir($directory)) {
                            FileHelper::createDirectory($directory, 0777, true);
                        }                        foreach ($uploadedFiles as $index => $uploadedFile) {
                            $fileName = 'equipment_' . $model->id . '_' . time() . '_' . $index . '.' . $uploadedFile->extension;
                            
                            $photo = new EquipmentPhoto();
                            $photo->equipment_id = $model->id;
                            $photo->photo_path = $fileName;
                            $photo->is_main = ($index === 0); // Первая фотография становится главной
                            
                            if ($photo->save()) {
                                $filePath = $directory . $fileName;
                                $uploadedFile->saveAs($filePath);
                                
                                // Создаем миниатюры сразу после загрузки для ускорения последующего показа
                                $imagePath = '/uploads/equipment/' . $fileName;
                                ImageHelper::getThumbnailUrl($imagePath, 150, 150); // Маленькая миниатюра для таблицы
                                ImageHelper::getThumbnailUrl($imagePath, 800, 600); // Большая миниатюра для модального окна
                            }
                        }}

                    $transaction->commit();
                    
                    // Очищаем кэш фотографий для этого оборудования
                    Yii::$app->cache->delete("equipment_photos_{$model->id}");
                    
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'Equipment created successfully.')
                    ];
                } else {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors(),
                    ];
                }
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        }
    }
    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Equipment::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Equipment not found')
                ];
            }

            return [
                'content' => $this->renderPartial('update', [
                    'model' => $model
                ]),
            ];
        }

        if (Yii::$app->request->isPost) {
            $id = Yii::$app->request->post('id');
            $model = Equipment::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Equipment not found')
                ];
            }

            $data = Yii::$app->request->post();
            $uploadedFiles = UploadedFile::getInstancesByName('photos');

            $model->name = $data['name'];
            $model->description = $data['description'];
            $model->purchase_date = $data['purchase_date'];
            $model->status = $data['status'];

            $transaction = Yii::$app->db->beginTransaction();
            try {
                if($model->save()) {
                    // Обработка новых фотографий
                    if (!empty($uploadedFiles)) {
                        $directory = Yii::getAlias('@webroot/uploads/equipment/');
                        if (!is_dir($directory)) {
                            FileHelper::createDirectory($directory, 0777, true);
                        }

                        // Получаем количество существующих фотографий
                        $existingPhotosCount = EquipmentPhoto::find()
                            ->where(['equipment_id' => $model->id])
                            ->count();                        foreach ($uploadedFiles as $index => $uploadedFile) {
                            $fileName = 'equipment_' . $model->id . '_' . time() . '_' . ($existingPhotosCount + $index) . '.' . $uploadedFile->extension;
                            
                            $photo = new EquipmentPhoto();
                            $photo->equipment_id = $model->id;
                            $photo->photo_path = $fileName;
                            $photo->is_main = ($existingPhotosCount === 0 && $index === 0); // Первая фотография становится главной, если нет других
                            
                            if ($photo->save()) {
                                $filePath = $directory . $fileName;
                                $uploadedFile->saveAs($filePath);
                                
                                // Создаем миниатюры сразу после загрузки для ускорения последующего показа
                                $imagePath = '/uploads/equipment/' . $fileName;
                                ImageHelper::getThumbnailUrl($imagePath, 150, 150); // Маленькая миниатюра для таблицы
                                ImageHelper::getThumbnailUrl($imagePath, 800, 600); // Большая миниатюра для модального окна
                            }
                        }
                    }                    $transaction->commit();
                    
                    // Очищаем кэш фотографий для этого оборудования
                    Yii::$app->cache->delete("equipment_photos_{$model->id}");
                    
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'Equipment updated successfully.')
                    ];
                } else {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors(),
                    ];
                }
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        }
    }    public function actionGetPhoto($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        // Добавляем кэширование
        $cacheKey = "equipment_photos_$id";
        $cache = Yii::$app->cache;
        
        // Проверяем кэш (кэшируем на 30 минут)
        $cachedData = $cache->get($cacheKey);
        if ($cachedData !== false) {
            return $cachedData;
        }

        $equipment = Equipment::findOne($id);
        if ($equipment) {
            // Используем более эффективный запрос
            $photosData = EquipmentPhoto::find()
                ->select(['id', 'photo_path', 'is_main'])
                ->where(['equipment_id' => $id])
                ->orderBy(['is_main' => SORT_DESC, 'id' => SORT_ASC]) // Главная фотография первой
                ->asArray()
                ->all();
              $photos = [];
            foreach ($photosData as $photo) {
                $originalPath = '/uploads/equipment/' . $photo['photo_path'];
                $thumbnailPath = ImageHelper::getThumbnailUrl($originalPath, 800, 600); // Создаем миниатюры 800x600 для модального окна
                $smallThumbPath = ImageHelper::getThumbnailUrl($originalPath, 150, 150); // Маленькие миниатюры для превью
                
                $photos[] = [
                    'id' => $photo['id'],
                    'path' => $thumbnailPath, // Используем миниатюру вместо оригинала
                    'original_path' => $originalPath, // Сохраняем путь к оригиналу на случай если нужно
                    'small_thumb' => $smallThumbPath, // Маленькая миниатюра для превью
                    'is_main' => (bool)$photo['is_main']
                ];
            }
            
            $result = [
                'status' => 'success',
                'photos' => $photos,
                'name' => $equipment->name,
                'total' => count($photos)
            ];
            
            // Кэшируем результат на 30 минут
            $cache->set($cacheKey, $result, 1800);
            
            return $result;
        }

        $errorResult = [
            'status' => 'error',
            'message' => Yii::t('app', 'Equipment not found')
        ];
        
        // Кэшируем ошибку на короткое время (5 минут)
        $cache->set($cacheKey, $errorResult, 300);
        
        return $errorResult;
    }

    /**
     * Delete photo
     */
    public function actionDeletePhoto()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $photoId = Yii::$app->request->post('photo_id');
            $photo = EquipmentPhoto::findOne($photoId);

            if (!$photo) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Photo not found')
                ];
            }            $filePath = Yii::getAlias('@webroot/uploads/equipment/' . $photo->photo_path);
            $equipmentId = $photo->equipment_id;
            
            if ($photo->delete()) {
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
                
                // Очищаем кэш фотографий для этого оборудования
                Yii::$app->cache->delete("equipment_photos_$equipmentId");
                
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Photo deleted successfully.')
                ];
            }

            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Failed to delete photo.')
            ];
        }
    }

    /**
     * Set main photo
     */
    public function actionSetMainPhoto()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $photoId = Yii::$app->request->post('photo_id');
            $photo = EquipmentPhoto::findOne($photoId);

            if (!$photo) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Photo not found')
                ];
            }            $transaction = Yii::$app->db->beginTransaction();
            try {
                $equipmentId = $photo->equipment_id;
                
                // Сбрасываем is_main для всех фотографий этого оборудования
                EquipmentPhoto::updateAll(
                    ['is_main' => false],
                    ['equipment_id' => $equipmentId]
                );

                // Устанавливаем текущую фотографию как главную
                $photo->is_main = true;
                $photo->save();

                $transaction->commit();
                
                // Очищаем кэш фотографий для этого оборудования
                Yii::$app->cache->delete("equipment_photos_$equipmentId");
                
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Main photo set successfully.')
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }
    }

    /**
     * Get parts tab for equipment
     * @param integer $id
     * @return mixed
     */
    public function actionPartsTab($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $model = $this->findModel($id);

        // Получаем все запчасти, привязанные к этому оборудованию
        $parts = EquipmentPart::find()
            ->where(['equipment_id' => $id])
            ->andWhere(['deleted_at' => null])
            ->all();

        $currencies = \app\common\models\Currency::find()
            ->where(['deleted_at' => null])
            ->indexBy('id')
            ->all();

        return [
            'content' => $this->renderPartial('_parts_tab', [
                'model' => $model,
                'parts' => $parts,
                'currencies' => $currencies
            ])
        ];
    }

    /**
     * Display parts page for specific equipment
     * @param integer $id
     * @return mixed
     */
    public function actionParts($id)
    {
        $model = $this->findModel($id);

        // Получаем запчасти через новую таблицу назначений с суммированием количеств
        $sql = "
            SELECT
                ep.*,
                SUM(epa.quantity) as assigned_quantity,
                SUM(epa.active_quantity) as active_quantity,
                SUM(epa.repair_quantity) as repair_quantity,
                MIN(epa.installation_date) as installation_date,
                STRING_AGG(DISTINCT epa.comment, '; ') as assignment_comment,
                e.name as equipment_name,
                -- Определяем общий статус назначения для действий
                CASE
                    WHEN SUM(epa.active_quantity) > 0 THEN :active_status
                    WHEN SUM(epa.repair_quantity) > 0 THEN :repair_status
                    ELSE :active_status
                END as assignment_status
            FROM equipment_part_assignments epa
            INNER JOIN equipment_parts ep ON ep.id = epa.equipment_part_id
            LEFT JOIN equipment e ON e.id = epa.equipment_id
            WHERE ep.deleted_at IS NULL
                AND epa.equipment_id = :equipment_id
                AND epa.status IN (:active_status::integer, :repair_status::integer)
            GROUP BY ep.id, ep.name, ep.photo, ep.price, ep.quantity, ep.status, ep.created_at, ep.deleted_at, e.name
            ORDER BY ep.created_at DESC
        ";

        $result = Yii::$app->db->createCommand($sql, [
            ':equipment_id' => $id,
            ':active_status' => EquipmentPartAssignment::STATUS_ACTIVE,
            ':repair_status' => EquipmentPartAssignment::STATUS_REPAIR
        ])->queryAll();

        if (Yii::$app->request->isAjax) {
            return $this->renderAjax('parts', [
                'model' => $model,
                'result' => $result,
            ]);
        }

        return $this->render('parts', [
            'model' => $model,
            'result' => $result,
        ]);
    }

    /**
     * Search parts for specific equipment
     * @param integer $id
     * @return mixed
     */
    public function actionSearchParts($id)
    {
        if (Yii::$app->request->isAjax) {
            $model = $this->findModel($id);
            $status = Yii::$app->request->post('status');
            $search = Yii::$app->request->post('search');

            // Базовый SQL-запрос
            $sql = "
                SELECT
                    ep.*,
                    e.name as equipment_name
                FROM equipment_parts ep
                LEFT JOIN equipment e ON e.id = ep.equipment_id
                WHERE ep.deleted_at IS NULL AND ep.equipment_id = :equipment_id
            ";

            // Параметры для SQL-запроса
            $params = [':equipment_id' => $id];

            // Добавляем фильтр по статусу, если он передан
            if ($status !== null && $status !== '') {
                $sql .= " AND ep.status = :status";
                $params[':status'] = (int)$status;
            }

            // Добавляем поиск по названию, если он передан
            if ($search !== null && $search !== '') {
                $sql .= " AND ep.name ILIKE :search";
                $params[':search'] = '%' . $search . '%';
            }

            $sql .= " ORDER BY ep.created_at DESC";

            // Выполняем запрос
            $result = Yii::$app->db->createCommand($sql, $params)->queryAll();

            $currencies = \app\common\models\Currency::find()
                ->where(['deleted_at' => null])
                ->indexBy('id')
                ->all();

            if ($result) {
                $html = $this->renderPartial('_parts_index', [
                    'result' => $result,
                    'model' => $model,
                    'currencies' => $currencies
                ]);

                return $this->asJson([
                    'status' => 'success',
                    'content' => $html
                ]);
            }

            return $this->asJson([
                'status' => 'error',
                'message' => Yii::t('app', 'No results found')
            ]);
        }
    }

    public function actionChangeStatus($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $model = $this->findModel($id);

            // Получаем все запчасти, привязанные к этому оборудованию через новую схему
            $sql = "
                SELECT
                    ep.*,
                    epa.quantity as assigned_quantity,
                    epa.active_quantity,
                    epa.repair_quantity,
                    epa.installation_date,
                    epa.comment as assignment_comment,
                    epa.id as assignment_id
                FROM equipment_part_assignments epa
                INNER JOIN equipment_parts ep ON ep.id = epa.equipment_part_id
                WHERE ep.deleted_at IS NULL
                    AND epa.equipment_id = :equipment_id
                    AND epa.status IN (:active_status, :repair_status)
                ORDER BY ep.name ASC
            ";

            $parts = Yii::$app->db->createCommand($sql, [
                ':equipment_id' => $id,
                ':active_status' => EquipmentPartAssignment::STATUS_ACTIVE,
                ':repair_status' => EquipmentPartAssignment::STATUS_REPAIR
            ])->queryAll();

            return [
                'content' => $this->renderPartial('_status_form', [
                    'model' => $model,
                    'parts' => $parts
                ])
            ];
        }

        if (Yii::$app->request->isPost) {
            $model = $this->findModel($id);
            $status = Yii::$app->request->post('status');
            $selectedParts = Yii::$app->request->post('parts', []);

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $model->scenario = Equipment::SCENARIO_CHANGE_STATUS;
                $model->status = $status;

                if (!$model->save()) {
                    throw new \Exception(Yii::t('app', 'error_changing_status'));
                }

                // Если статус меняется на "Списано" (неактивный), обрабатываем запчасти
                if ($status == Equipment::STATUS_INACTIVE) {
                    // Получаем все назначения запчастей для этого оборудования
                    $assignments = EquipmentPartAssignment::find()
                        ->where(['equipment_id' => $id])
                        ->andWhere(['in', 'status', [
                            EquipmentPartAssignment::STATUS_ACTIVE,
                            EquipmentPartAssignment::STATUS_REPAIR
                        ]])
                        ->all();

                    foreach ($assignments as $assignment) {
                        $part = $assignment->equipmentPart;
                        if (!$part) continue;

                        $isSelected = in_array($part->id, $selectedParts);
                        $totalQuantity = $assignment->active_quantity + $assignment->repair_quantity;

                        if ($isSelected) {
                            // Выбранные запчасти: возвращаем на склад
                            // Количество увеличивается автоматически через EquipmentPartMovement::afterSave()

                            // Создаем запись о возврате на склад
                            $movement = new EquipmentPartMovement();
                            $movement->equipment_part_id = $part->id;
                            $movement->movement_type = EquipmentPartMovement::MOVEMENT_INCOME;
                            $movement->quantity = $totalQuantity;
                            $movement->comment = Yii::t('app', 'returned_to_warehouse_from_decommissioned_equipment', [
                                'equipment' => $model->name
                            ]);
                            $movement->created_by = Yii::$app->user->id;
                            $movement->save();

                            // Добавляем запись в историю
                            $part->addHistory(
                                EquipmentPart::ACTION_REMOVE,
                                EquipmentPartAssignment::STATUS_ACTIVE,
                                EquipmentPartAssignment::STATUS_REMOVED,
                                Yii::t('app', 'returned_to_warehouse_from_decommissioned_equipment', [
                                    'equipment' => $model->name
                                ]),
                                $id,
                                $totalQuantity
                            );
                        } else {
                            // Невыбранные запчасти: отправляем в брак
                            // Создаем запись о браке
                            $movement = new EquipmentPartMovement();
                            $movement->equipment_part_id = $part->id;
                            $movement->movement_type = EquipmentPartMovement::MOVEMENT_DEFECT;
                            $movement->quantity = $totalQuantity;
                            $movement->comment = Yii::t('app', 'defected_with_decommissioned_equipment', [
                                'equipment' => $model->name
                            ]);
                            $movement->created_by = Yii::$app->user->id;
                            $movement->save();

                            // Добавляем запись в историю
                            $part->addHistory(
                                EquipmentPart::ACTION_REMOVE,
                                EquipmentPartAssignment::STATUS_ACTIVE,
                                EquipmentPartAssignment::STATUS_REMOVED,
                                Yii::t('app', 'defected_with_decommissioned_equipment', [
                                    'equipment' => $model->name
                                ]),
                                $id,
                                $totalQuantity
                            );
                        }

                        // Помечаем назначение как удаленное
                        $assignment->status = EquipmentPartAssignment::STATUS_REMOVED;
                        $assignment->save();
                    }
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'equipment_status_changed_successfully')
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }
    }

    /**
     * Decommission equipment and handle its parts
     * @param integer $id
     * @return mixed
     */
    public function actionDecommission($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $model = $this->findModel($id);

            // Получаем все запчасти, привязанные к этому оборудованию
            $parts = \app\common\models\EquipmentPart::find()
                ->where(['equipment_id' => $id])
                ->andWhere(['deleted_at' => null])
                ->all();

            return [
                'content' => $this->renderPartial('_decommission_form', [
                    'model' => $model,
                    'parts' => $parts
                ])
            ];
        }

        if (Yii::$app->request->isPost) {
            $model = $this->findModel($id);
            $partsData = Yii::$app->request->post('parts', []);
            $comment = Yii::$app->request->post('comment');

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Меняем статус оборудования на неактивный
                $model->scenario = Equipment::SCENARIO_CHANGE_STATUS;
                $model->status = Equipment::STATUS_INACTIVE;

                if (!$model->save()) {
                    throw new \Exception(Yii::t('app', 'error_decommissioning_equipment'));
                }

                // Обрабатываем запчасти
                foreach ($partsData as $partId => $action) {
                    $part = \app\common\models\EquipmentPart::findOne($partId);
                    if ($part) {
                        $oldStatus = $part->status;
                        $oldEquipmentId = $part->equipment_id;

                        // Действие для запчасти: 0 - вывести из эксплуатации, 1 - переместить в резерв
                        if ($action == 0) {
                            // Вывести из эксплуатации
                            $part->status = \app\common\models\EquipmentPart::STATUS_INACTIVE;
                            $part->equipment_id = null;
                            $part->installation_date = null;

                            if (!$part->save()) {
                                throw new \Exception(Yii::t('app', 'error_decommissioning_part') . ': ' . $part->name);
                            }

                            // Добавляем запись в историю
                            $part->addHistory(
                                \app\common\models\EquipmentPart::ACTION_REMOVE,
                                $oldStatus,
                                \app\common\models\EquipmentPart::STATUS_INACTIVE,
                                $comment ?? Yii::t('app', 'Part decommissioned with equipment'),
                                $oldEquipmentId
                            );
                        } else {
                            // Переместить в резерв
                            $part->status = \app\common\models\EquipmentPart::STATUS_RESERVE;
                            $part->equipment_id = null;
                            $part->installation_date = null;

                            if (!$part->save()) {
                                throw new \Exception(Yii::t('app', 'error_moving_part_to_reserve') . ': ' . $part->name);
                            }

                            // Добавляем запись в историю
                            $part->addHistory(
                                \app\common\models\EquipmentPart::ACTION_RESERVE,
                                $oldStatus,
                                \app\common\models\EquipmentPart::STATUS_RESERVE,
                                $comment ?? Yii::t('app', 'Part moved to reserve when equipment decommissioned'),
                                $oldEquipmentId
                            );
                        }
                    }
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'equipment_decommissioned_successfully')
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }
    }

    /**
     * Add existing part to equipment
     * @param int $id Equipment ID
     * @return mixed
     */
    public function actionAddPartToEquipment($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $equipment = Equipment::findOne($id);
        if (!$equipment) {
            return $this->asJson(['status' => 'error', 'message' => Yii::t('app', 'Equipment not found')]);
        }

        if (Yii::$app->request->isGet) {
            // Получаем доступные запчасти с учетом новой архитектуры
            // Показываем только те запчасти, у которых есть доступное количество
            $availableParts = EquipmentPart::find()
                ->where(['deleted_at' => null])
                ->andWhere(['>', 'quantity', 0])
                ->orderBy(['name' => SORT_ASC])
                ->all();

            // Фильтруем запчасти, у которых есть доступное количество
            $filteredParts = [];
            foreach ($availableParts as $part) {
                if ($part->getAvailableQuantity() > 0) {
                    $filteredParts[] = $part;
                }
            }

            return [
                'content' => $this->renderPartial('_add_part_form', [
                    'equipment' => $equipment,
                    'availableParts' => $filteredParts
                ]),
            ];
        }

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $partId = $data['part_id'] ?? null;
            $quantity = (int)($data['quantity'] ?? 0);

            if (!$partId || $quantity <= 0) {
                return $this->asJson([
                    'status' => 'error',
                    'message' => Yii::t('app', 'Invalid part or quantity')
                ]);
            }

            $part = EquipmentPart::findOne($partId);
            if (!$part) {
                return $this->asJson([
                    'status' => 'error',
                    'message' => Yii::t('app', 'Part not found')
                ]);
            }

            // Проверяем доступное количество с учетом новой архитектуры
            if (!$part->canAssign($quantity)) {
                return $this->asJson([
                    'status' => 'error',
                    'message' => Yii::t('app', 'Insufficient quantity available. Available: {available}', [
                        'available' => $part->getAvailableQuantity()
                    ])
                ]);
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Создаем назначение запчасти на оборудование (НЕ дублируем запись в equipment_parts!)
                $assignment = new EquipmentPartAssignment();
                $assignment->equipment_id = $equipment->id;
                $assignment->equipment_part_id = $part->id;
                $assignment->quantity = $quantity;
                $assignment->installation_date = $data['installation_date'] ?? date('Y-m-d');
                $assignment->status = EquipmentPartAssignment::STATUS_ACTIVE;
                $assignment->comment = $data['comment'] ?? null;

                // ✅ ИСПРАВЛЕНИЕ: Правильно инициализируем количества по статусам
                $assignment->active_quantity = $quantity;
                $assignment->repair_quantity = 0;

                if (!$assignment->save()) {
                    throw new \Exception(Yii::t('app', 'Error creating part assignment: {errors}', [
                        'errors' => implode(', ', $assignment->getFirstErrors())
                    ]));
                }

                // Запись в историю создается автоматически в EquipmentPartAssignment::afterSave()

                $transaction->commit();
                return $this->asJson([
                    'status' => 'success',
                    'message' => Yii::t('app', 'Part successfully added to equipment')
                ]);

            } catch (\Exception $e) {
                $transaction->rollBack();
                return $this->asJson([
                    'status' => 'error',
                    'message' => $e->getMessage()
                ]);
            }
        }

        return $this->asJson(['status' => 'error', 'message' => Yii::t('app', 'Invalid request')]);
    }

    /**
     * Defect action for equipment parts
     * @param int $equipmentId
     * @param int $partId
     * @return mixed
     */
    public function actionDefectPart($equipmentId, $partId)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $equipment = $this->findModel($equipmentId);
        $part = EquipmentPart::findOne($partId);

        if (!$part) {
            return $this->asJson([
                'status' => 'error',
                'message' => Yii::t('app', 'Part not found')
            ]);
        }

        // Получаем назначение запчасти для данного оборудования
        $assignment = EquipmentPartAssignment::find()
            ->where([
                'equipment_id' => $equipmentId,
                'equipment_part_id' => $partId,
            ])
            ->andWhere(['in', 'status', [EquipmentPartAssignment::STATUS_ACTIVE, EquipmentPartAssignment::STATUS_REPAIR]])
            ->one();

        if (!$assignment) {
            return $this->asJson([
                'status' => 'error',
                'message' => Yii::t('app', 'Part is not assigned to this equipment')
            ]);
        }

        if (Yii::$app->request->isGet) {
            return [
                'content' => $this->renderPartial('_defect_equipment_part_form', [
                    'part' => $part,
                    'assignment' => $assignment,
                    'equipment' => $equipment
                ])
            ];
        }

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();

            // Валидация данных
            $errors = [];
            $quantity = (int)($data['quantity'] ?? 0);

            if ($quantity <= 0) {
                $errors['quantity'] = [Yii::t('app', 'quantity_required_positive')];
            } elseif ($quantity > $assignment->quantity) {
                $errors['quantity'] = [Yii::t('app', 'defect_quantity_exceeds_assigned', [
                    'available' => $assignment->quantity
                ])];
            }

            if (empty($data['comment'])) {
                $errors['comment'] = [Yii::t('app', 'defect_reason_required')];
            }

            if (!empty($errors)) {
                return [
                    'status' => 'error',
                    'errors' => $errors
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Создаем запись о движении (брак)
                $movement = new EquipmentPartMovement();
                $movement->equipment_part_id = $part->id;
                $movement->movement_type = EquipmentPartMovement::MOVEMENT_DEFECT;
                $movement->quantity = $quantity;
                $movement->comment = $data['comment'] . ' (Equipment: ' . $equipment->name . ')';

                if (!$movement->save()) {
                    throw new \Exception(Yii::t('app', 'Error creating defect movement: {errors}', [
                        'errors' => implode(', ', $movement->getFirstErrors())
                    ]));
                }

                // Уменьшаем количество в назначении
                if ($quantity == $assignment->quantity) {
                    // Если списываем все количество - удаляем назначение
                    $assignment->status = EquipmentPartAssignment::STATUS_REMOVED;
                    $assignment->save();
                } else {
                    // Если списываем частично - уменьшаем количество
                    $assignment->quantity -= $quantity;
                    $assignment->active_quantity -= $quantity;
                    $assignment->save();
                }

                // Добавляем запись в историю
                $part->addHistory(
                    EquipmentPart::ACTION_REMOVE,
                    null,
                    null,
                    Yii::t('app', 'Defect: {quantity} units removed from equipment: {equipment}', [
                        'quantity' => $quantity,
                        'equipment' => $equipment->name
                    ]),
                    $equipment->id
                );

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'defect_recorded_successfully')
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return $this->asJson(['status' => 'error', 'message' => Yii::t('app', 'Invalid request')]);
    }

    /**
     * Move equipment part to reserve
     * @param int $equipmentId
     * @param int $partId
     * @return mixed
     */
    public function actionReservePart($equipmentId, $partId)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $equipment = $this->findModel($equipmentId);
        $part = EquipmentPart::findOne($partId);

        if (!$part) {
            return $this->asJson([
                'status' => 'error',
                'message' => Yii::t('app', 'Part not found')
            ]);
        }

        // Получаем назначение запчасти для данного оборудования
        $assignment = EquipmentPartAssignment::find()
            ->where([
                'equipment_id' => $equipmentId,
                'equipment_part_id' => $partId,
            ])
            ->andWhere(['in', 'status', [EquipmentPartAssignment::STATUS_ACTIVE, EquipmentPartAssignment::STATUS_REPAIR]])
            ->one();

        if (!$assignment) {
            return $this->asJson([
                'status' => 'error',
                'message' => Yii::t('app', 'Part is not assigned to this equipment')
            ]);
        }

        if (Yii::$app->request->isGet) {
            return [
                'content' => $this->renderPartial('_reserve_equipment_part_form', [
                    'part' => $part,
                    'assignment' => $assignment,
                    'equipment' => $equipment
                ])
            ];
        }

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $quantity = (int)$data['quantity'];

            // Валидация количества
            $errors = [];
            if ($quantity <= 0) {
                $errors['quantity'] = [Yii::t('app', 'quantity_required_positive')];
            } elseif ($quantity > $assignment->quantity) {
                $errors['quantity'] = [Yii::t('app', 'reserve_quantity_exceeds_assigned', [
                    'available' => $assignment->quantity
                ])];
            }

            if (!empty($errors)) {
                return [
                    'status' => 'error',
                    'errors' => $errors
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Обрабатываем частичное или полное снятие с оборудования
                if ($quantity == $assignment->quantity) {
                    // Если снимаем все количество - помечаем назначение как удаленное
                    $assignment->status = EquipmentPartAssignment::STATUS_REMOVED;
                    $assignment->save();
                } else {
                    // Если снимаем частично - уменьшаем количество
                    $assignment->quantity -= $quantity;
                    $assignment->active_quantity -= $quantity;
                    $assignment->save();
                }

                // Добавляем запись в историю с указанием количества
                $part->addHistory(
                    EquipmentPart::ACTION_REMOVE,
                    null,
                    null,
                    Yii::t('app', 'Part returned to warehouse from equipment: {equipment} (quantity: {quantity})', [
                        'equipment' => $equipment->name,
                        'quantity' => $quantity
                    ]) . (!empty($data['comment']) ? '. ' . $data['comment'] : ''),
                    $equipment->id,
                    $quantity
                );

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Part returned to warehouse successfully')
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return $this->asJson(['status' => 'error', 'message' => Yii::t('app', 'Invalid request')]);
    }

    /**
     * Send equipment part to repair
     * @param int $equipmentId
     * @param int $partId
     * @return mixed
     */
    public function actionRepairPart($equipmentId, $partId)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $equipment = $this->findModel($equipmentId);
        $part = EquipmentPart::findOne($partId);

        if (!$part) {
            return $this->asJson([
                'status' => 'error',
                'message' => Yii::t('app', 'Part not found')
            ]);
        }

        // Получаем назначение запчасти для данного оборудования (с активным количеством)
        $assignment = EquipmentPartAssignment::find()
            ->where([
                'equipment_id' => $equipmentId,
                'equipment_part_id' => $partId,
            ])
            ->andWhere(['>', 'active_quantity', 0])
            ->one();

        if (!$assignment) {
            return $this->asJson([
                'status' => 'error',
                'message' => Yii::t('app', 'Part is not assigned to this equipment')
            ]);
        }

        if (Yii::$app->request->isGet) {
            return [
                'content' => $this->renderPartial('_repair_equipment_part_form', [
                    'part' => $part,
                    'assignment' => $assignment,
                    'equipment' => $equipment
                ])
            ];
        }

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $quantity = (int)$data['quantity'];

            // Валидация количества
            $errors = [];
            if ($quantity <= 0) {
                $errors['quantity'] = [Yii::t('app', 'quantity_required_positive')];
            } elseif ($quantity > ($assignment->active_quantity ?? 0)) {
                $errors['quantity'] = [Yii::t('app', 'repair_quantity_exceeds_assigned', [
                    'available' => $assignment->active_quantity ?? 0
                ])];
            }

            if (!empty($errors)) {
                return [
                    'status' => 'error',
                    'errors' => $errors
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // НОВАЯ ЛОГИКА: используем методы модели для управления количествами
                if (!$assignment->sendToRepair($quantity)) {
                    throw new \Exception('Ошибка отправки в ремонт: недостаточно активного количества');
                }

                // Добавляем запись в историю с указанием количества
                $part->addHistory(
                    EquipmentPart::ACTION_REPAIR, // Используем константу для ремонта
                    null,
                    null,
                    Yii::t('app', 'Part sent to repair with equipment: {equipment} (quantity: {quantity})', [
                        'equipment' => $equipment->name,
                        'quantity' => $quantity
                    ]) . (!empty($data['comment']) ? '. ' . $data['comment'] : ''),
                    $equipment->id,
                    $quantity
                );

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Part sent to repair successfully')
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return $this->asJson(['status' => 'error', 'message' => Yii::t('app', 'Invalid request')]);
    }

    /**
     * Return equipment part to work from repair
     * @param int $equipmentId
     * @param int $partId
     * @return mixed
     */
    public function actionActivatePart($equipmentId, $partId)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $equipment = $this->findModel($equipmentId);
        $part = EquipmentPart::findOne($partId);

        if (!$part) {
            return $this->asJson([
                'status' => 'error',
                'message' => Yii::t('app', 'Part not found')
            ]);
        }

        // Получаем назначение запчасти для данного оборудования (с количеством в ремонте)
        $assignment = EquipmentPartAssignment::find()
            ->where([
                'equipment_id' => $equipmentId,
                'equipment_part_id' => $partId,
            ])
            ->andWhere(['>', 'repair_quantity', 0])
            ->one();

        if (!$assignment) {
            return $this->asJson([
                'status' => 'error',
                'message' => Yii::t('app', 'Part is not in repair for this equipment')
            ]);
        }

        if (Yii::$app->request->isGet) {
            return [
                'content' => $this->renderPartial('_activate_equipment_part_form', [
                    'part' => $part,
                    'assignment' => $assignment,
                    'equipment' => $equipment
                ])
            ];
        }

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $quantity = (int)$data['quantity'];

            // Валидация количества
            $errors = [];
            if ($quantity <= 0) {
                $errors['quantity'] = [Yii::t('app', 'quantity_required_positive')];
            } elseif ($quantity > ($assignment->repair_quantity ?? 0)) {
                $errors['quantity'] = [Yii::t('app', 'return_quantity_exceeds_repair', [
                    'available' => $assignment->repair_quantity ?? 0
                ])];
            }

            if (!empty($errors)) {
                return [
                    'status' => 'error',
                    'errors' => $errors
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // НОВАЯ ЛОГИКА: используем методы модели для управления количествами
                if (!$assignment->returnFromRepair($quantity)) {
                    throw new \Exception('Ошибка возврата из ремонта: недостаточно количества в ремонте');
                }

                // Добавляем запись в историю с указанием количества
                $part->addHistory(
                    EquipmentPart::ACTION_ACTIVATE,
                    null,
                    null,
                    Yii::t('app', 'Part returned to work on equipment: {equipment} (quantity: {quantity})', [
                        'equipment' => $equipment->name,
                        'quantity' => $quantity
                    ]) . (!empty($data['comment']) ? '. ' . $data['comment'] : ''),
                    $equipment->id,
                    $quantity
                );

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Part returned to work successfully')
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return $this->asJson(['status' => 'error', 'message' => Yii::t('app', 'Invalid request')]);
    }

    /**
     * Display history of specific part on specific equipment
     * @param integer $equipmentId
     * @param integer $partId
     * @return mixed
     */
    public function actionPartHistory($equipmentId, $partId)
    {
        $equipment = $this->findModel($equipmentId);
        $part = EquipmentPart::findOne($partId);

        if (!$part) {
            throw new NotFoundHttpException(Yii::t('app', 'Part not found'));
        }

        // Получаем историю действий для этой запчасти на этом оборудовании
        $historyQuery = "
            SELECT
                eph.*,
                ep.name as part_name,
                ep.photo as part_photo,
                e.name as equipment_name,
                u.username as user_name
            FROM equipment_part_history eph
            INNER JOIN equipment_parts ep ON ep.id = eph.equipment_part_id
            LEFT JOIN equipment e ON e.id = eph.equipment_id
            LEFT JOIN users u ON u.id = eph.created_by
            WHERE eph.equipment_part_id = :part_id
                AND eph.equipment_id = :equipment_id
            ORDER BY eph.created_at DESC
        ";

        $historyData = Yii::$app->db->createCommand($historyQuery, [
            ':part_id' => $partId,
            ':equipment_id' => $equipmentId
        ])->queryAll();

        // Получаем движения (брак) связанные с этим оборудованием
        $movementsQuery = "
            SELECT
                epm.*,
                ep.name as part_name,
                u.username as user_name
            FROM equipment_part_movements epm
            INNER JOIN equipment_parts ep ON ep.id = epm.equipment_part_id
            LEFT JOIN users u ON u.id = epm.created_by
            WHERE epm.equipment_part_id = :part_id
                AND (epm.comment LIKE :equipment_pattern OR epm.comment LIKE :equipment_pattern2)
                AND epm.movement_type = :defect_type
            ORDER BY epm.created_at DESC
        ";

        $movementsData = Yii::$app->db->createCommand($movementsQuery, [
            ':part_id' => $partId,
            ':equipment_pattern' => '%Equipment: ' . $equipment->name . '%',
            ':equipment_pattern2' => '%' . $equipment->name . '%',
            ':defect_type' => EquipmentPartMovement::MOVEMENT_DEFECT
        ])->queryAll();

        // Получаем текущие назначения
        $assignments = EquipmentPartAssignment::find()
            ->where([
                'equipment_id' => $equipmentId,
                'equipment_part_id' => $partId
            ])
            ->andWhere(['in', 'status', [
                EquipmentPartAssignment::STATUS_ACTIVE,
                EquipmentPartAssignment::STATUS_REPAIR
            ]])
            ->all();

        if (Yii::$app->request->isAjax) {
            return $this->renderAjax('part-history', [
                'equipment' => $equipment,
                'part' => $part,
                'historyData' => $historyData,
                'movementsData' => $movementsData,
                'assignments' => $assignments,
            ]);
        }

        return $this->render('part-history', [
            'equipment' => $equipment,
            'part' => $part,
            'historyData' => $historyData,
            'movementsData' => $movementsData,
            'assignments' => $assignments,
        ]);
    }

    /**
     * Генерирует миниатюры для всех существующих фотографий оборудования
     * Этот метод можно вызвать для оптимизации уже загруженных изображений
     */
    public function actionGenerateThumbnails()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $processed = 0;
        $errors = 0;
        
        $photos = EquipmentPhoto::find()->all();
        
        foreach ($photos as $photo) {
            $imagePath = '/uploads/equipment/' . $photo->photo_path;
            $fullPath = Yii::getAlias('@webroot') . $imagePath;
            
            if (file_exists($fullPath)) {
                try {
                    // Создаем миниатюры разных размеров
                    ImageHelper::getThumbnailUrl($imagePath, 80, 80);   // Для таблицы (очень маленькие)
                    ImageHelper::getThumbnailUrl($imagePath, 150, 150); // Для превью
                    ImageHelper::getThumbnailUrl($imagePath, 300, 300); // Средние
                    ImageHelper::getThumbnailUrl($imagePath, 800, 600); // Для модального окна
                    $processed++;
                } catch (\Exception $e) {
                    $errors++;
                    Yii::error("Failed to generate thumbnail for {$photo->photo_path}: " . $e->getMessage());
                }
            } else {
                $errors++;
                Yii::warning("File not found: {$fullPath}");
            }
        }
        
        return [
            'status' => 'success',
            'message' => "Обработано фотографий: {$processed}, ошибок: {$errors}",
            'processed' => $processed,
            'errors' => $errors
        ];
    }

    protected function findModel($id)
    {
        if (($model = Equipment::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException(Yii::t('app', 'The requested page does not exist.'));
    }
}
