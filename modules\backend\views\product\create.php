<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var app\common\models\Product $model */
?>

<div class="position-form">
    <form id="product-create-form">
        <?= Html::hiddenInput(Yii::$app->request->csrfParam, Yii::$app->request->csrfToken) ?>
        
        <div class="form-group mt-2">
            <label for="name"><?= Yii::t('app', 'product_name') ?> <span class="text-danger">*</span></label>
            <input type="text" id="name" name="ProductCreateForm[name]" maxlength="255" class="form-control" required>
            <div class="error-container" id="name-error"></div>
        </div>

        <div class="form-group mt-2">
            <label for="price"><?= Yii::t('app', 'product_price') ?> <span class="text-danger">*</span></label>
            <input type="text" id="price" name="ProductCreateForm[price]" class="form-control formatted-numeric-input" required>
            <div class="error-container" id="price-error"></div>
        </div>

        <div class="form-group mt-2">
            <label for="block_quantity"><?= Yii::t('app', 'product_block_quantity') ?> <span class="text-danger">*</span></label>
            <input type="number" id="block_quantity" name="ProductCreateForm[block_quantity]" class="form-control" required>
            <div class="error-container" id="block_quantity-error"></div>
        </div>

        <div class="form-group mt-2">
            <label for="priority"><?= Yii::t('app', 'product_priority') ?> <span class="text-danger">*</span></label>
            <input type="number" id="priority" name="ProductCreateForm[priority]" class="form-control" required>
            <div class="error-container" id="priority-error"></div>
        </div>

        <div class="form-group mt-3">
            <div class="checkbox">
                <label>
                    <input type="checkbox" id="type" name="ProductCreateForm[type]" value="1" required>
                    <?= Yii::t('app', 'is_fizzy') ?>
                </label>
            </div>
        </div>
    </form>
</div>



