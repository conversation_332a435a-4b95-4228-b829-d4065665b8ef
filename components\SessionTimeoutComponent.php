<?php

namespace app\components;

use Yii;
use yii\base\Component;
use yii\base\Event;
use yii\web\Application;

class SessionTimeoutComponent extends Component
{
    public function init()
    {
        parent::init();
        
        Event::on(Application::class, Application::EVENT_BEFORE_REQUEST, function () {
            if (!Yii::$app->user->isGuest) {
                $session = Yii::$app->session;
                
                // Проверяем, установлено ли время последней активности
                if ($session->has('last_activity')) {
                    $timeout = $session->get('timeout', 1800); // По умолчанию 30 минут
                    $lastActivity = $session->get('last_activity');
                    
                    // Если прошло больше времени, чем timeout, выходим из системы
                    if (time() - $lastActivity > $timeout) {
                        Yii::$app->user->logout();
                        
                        // Перенаправляем на страницу входа с сообщением
                        Yii::$app->session->setFlash('error', Yii::t('app', 'session_expired'));
                        Yii::$app->response->redirect(['/site/login'])->send();
                        Yii::$app->end();
                    }
                }
                
                // Обновляем время последней активности
                $session->set('last_activity', time());
                $session->set('timeout', Yii::$app->session->timeout);
            }
        });
    }
} 