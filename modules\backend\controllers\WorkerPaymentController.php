<?php

namespace app\modules\backend\controllers;

use app\modules\backend\models\Worker;
use app\modules\backend\models\WorkerFinances;
use app\modules\backend\models\WorkerDebt;
use app\modules\backend\models\WorkerSalary;
use Yii;
use yii\web\Response;

// Подключаем хелпер для работы с датами
require_once Yii::getAlia<PERSON>('@app/helpers/date-formatter.php');

class WorkerPaymentController extends BaseController
{
    public function actionIndex()
    {
        $month = Yii::$app->request->get('month', date('Y-m'));
        $currentLanguage = getCurrentLanguage();
        $monthName = formatMonthName($month, $currentLanguage);
        
        $sql = "SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц (только наличные бонусы)
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) as bonus,

            -- Отпускные за месяц (только наличные отпускные)
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) as vacation_pay,

            -- Аванс за месяц (аванс наличными + наличная зарплата)
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0) as advance,

            -- Оплата по пластиковой карте (все безналичные платежи)
            COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :one_time_payment_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) as card_payment,

            -- Оплата наличными (зарплата наличная) - для обратной совместимости
            COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            -- Остаток к выплате: базовый оклад + бонусы + отпускные
            -- минус все авансы (включая наличную зарплату)  + все безналичные выплаты + удержания долга
            (ws.amount) +
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) -
            (
                -- Авансы (наличные + наличная зарплата)
                COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0)
            ) -
            -- Все безналичные выплаты (карта)
            (
                COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :one_time_payment_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0)
            ) -
            -- Удержания долга за месяц
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN cashbox_detail cd ON cd.worker_finance_id = wf.id AND cd.deleted_at IS NULL
        LEFT JOIN cashbox cb ON cb.id = cd.cashbox_id AND cb.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";
        
        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':month', $month);
        $command->bindValue(':bonus_type', WorkerFinances::TYPE_BONUS);
        $command->bindValue(':vacation_type', WorkerFinances::TYPE_VACATION_PAY);
        $command->bindValue(':advance_type', WorkerFinances::TYPE_ADVANCE);
        $command->bindValue(':salary_type', WorkerFinances::TYPE_SALARY);
        $command->bindValue(':cash_salary_type', WorkerFinances::TYPE_CASH_SALARY);
        $command->bindValue(':one_time_payment_type', WorkerFinances::TYPE_ONE_TIME_PAYMENT);
        $command->bindValue(':debt_payment_type', WorkerFinances::TYPE_DEBT_PAYMENT);
        
        $result = $command->queryAll();
        
        // Добавляем проверку наличия выплат для каждого работника
        $updateService = new \app\modules\backend\services\worker_payment\WorkerPaymentUpdateService();
        foreach ($result as &$worker) {
            $existingPayments = $updateService->getExistingPayments($worker['worker_id'], $month);
            $worker['has_payments'] = $existingPayments['status'] === 'success' && $existingPayments['count'] > 0;
        }
        
        return $this->render('index', [
            'result' => $result,
            'month' => $month,
            'monthName' => $monthName
        ]);
    }
    
    public function actionSearch()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $month = Yii::$app->request->post('month', date('Y-m'));
        
      
        $sql = "SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц (только наличные бонусы)
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) as bonus,

            -- Отпускные за месяц (только наличные отпускные)
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) as vacation_pay,

            -- Аванс за месяц (аванс наличными + наличная зарплата)
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0) as advance,

            -- Оплата по пластиковой карте (все безналичные платежи)
            COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :one_time_payment_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) as card_payment,

            -- Оплата наличными (зарплата наличная) - для обратной совместимости
            COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            -- Остаток к выплате: базовый оклад + бонусы + отпускные
            -- минус все авансы (включая наличную зарплату)  + все безналичные выплаты + удержания долга
            (ws.amount) +
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) -
            (
                -- Авансы (наличные + наличная зарплата)
                COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0)
            ) -
            -- Все безналичные выплаты (карта)
            (
                COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :one_time_payment_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0)
            ) -
            -- Удержания долга за месяц
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN cashbox_detail cd ON cd.worker_finance_id = wf.id AND cd.deleted_at IS NULL
        LEFT JOIN cashbox cb ON cb.id = cd.cashbox_id AND cb.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";
        
        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':month', $month);
        $command->bindValue(':bonus_type', WorkerFinances::TYPE_BONUS);
        $command->bindValue(':vacation_type', WorkerFinances::TYPE_VACATION_PAY);
        $command->bindValue(':advance_type', WorkerFinances::TYPE_ADVANCE);
        $command->bindValue(':salary_type', WorkerFinances::TYPE_SALARY);
        $command->bindValue(':cash_salary_type', WorkerFinances::TYPE_CASH_SALARY);
        $command->bindValue(':one_time_payment_type', WorkerFinances::TYPE_ONE_TIME_PAYMENT);
        $command->bindValue(':debt_payment_type', WorkerFinances::TYPE_DEBT_PAYMENT);
        
        $result = $command->queryAll();
        
        // Добавляем проверку наличия выплат для каждого работника
        $updateService = new \app\modules\backend\services\worker_payment\WorkerPaymentUpdateService();
        foreach ($result as &$worker) {
            $existingPayments = $updateService->getExistingPayments($worker['worker_id'], $month);
            $worker['has_payments'] = $existingPayments['status'] === 'success' && $existingPayments['count'] > 0;
        }
        
        return [
            'status' => 'success',
            'data' => $result
        ];
    }
    
    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $service = new \app\modules\backend\services\worker_payment\WorkerPaymentService();
        $formData = $service->getFormData();
        
        return [
            "status" => true,
            "content" => $this->renderPartial('create', $formData)
        ];
    }
    
    public function actionStore()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $service = new \app\modules\backend\services\worker_payment\WorkerPaymentService();
        $result = $service->createPayment(Yii::$app->request->post());
        
        return $result;
    }
    
    public function actionGetWorkerInfo()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $workerId = Yii::$app->request->get('worker_id');
        $month = Yii::$app->request->get('month', date('Y-m'));
        
        if (!$workerId) {
            return [
                'status' => 'error',
                'message' => 'Не указан ID работника'
            ];
        }

        $service = new \app\modules\backend\services\worker_payment\WorkerPaymentService();
        $workerInfo = $service->getWorkerSalaryInfo($workerId);
        $paymentsInfo = $service->getWorkerPaymentsForMonth($workerId, $month);

        if ($workerInfo['status'] === 'error') {
            return $workerInfo;
        }

        // Получаем информацию о долге
        $debt = WorkerDebt::find()
            ->where(['worker_id' => $workerId])
            ->andWhere(['>', 'amount', 0])
            ->andWhere(['deleted_at' => null])
            ->sum('amount') ?? 0;

        return [
            'status' => 'success',
            'worker' => $workerInfo['worker'],
            'salary' => $workerInfo['salary'],
            'has_salary' => $workerInfo['has_salary'],
            'payments' => $paymentsInfo['payments_by_type'] ?? [],
            'payment_methods_by_type' => $this->getPaymentMethodsByType($workerId, $month),
            'total_paid' => (float)($paymentsInfo['total_paid'] ?? 0),
            'debt' => (float)$debt
        ];
    }
    
    public function actionExport()
    {
        $month = Yii::$app->request->get('month', date('Y-m'));
        
        $sql = "SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) as card_payment,
            
            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (непогашенные долги)
            COALESCE(wd.total_debt, 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as debt_deduction,
            
            -- Вычисляем получено на руки (фактически выплачено работнику)
            -- Остаток к выплате: базовый оклад + бонусы + отпускные
            -- минус все авансы (включая наличную зарплату) + все безналичные выплаты + удержания долга
            (ws.amount) +
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) +
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) -
            (
                -- Авансы (наличные + наличная зарплата)
                COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month AND cb.id = 3 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0)
            ) -
            (
                -- Все безналичные выплаты (карта)
                COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0) +
                COALESCE(SUM(CASE WHEN wf.type = :one_time_payment_type AND wf.month = :month AND cb.id = 2 THEN wf.amount END), 0)
            ) -
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN cashbox_detail cd ON cd.worker_finance_id = wf.id AND cd.deleted_at IS NULL
        LEFT JOIN cashbox cb ON cb.id = cd.cashbox_id AND cb.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt
        ORDER BY w.full_name";
        
        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':month', $month);
        $command->bindValue(':bonus_type', WorkerFinances::TYPE_BONUS);
        $command->bindValue(':vacation_type', WorkerFinances::TYPE_VACATION_PAY);
        $command->bindValue(':advance_type', WorkerFinances::TYPE_ADVANCE);
        $command->bindValue(':salary_type', WorkerFinances::TYPE_SALARY);
        $command->bindValue(':cash_salary_type', WorkerFinances::TYPE_CASH_SALARY);
        $command->bindValue(':one_time_payment_type', WorkerFinances::TYPE_ONE_TIME_PAYMENT);
        $command->bindValue(':debt_payment_type', WorkerFinances::TYPE_DEBT_PAYMENT);
        
        $result = $command->queryAll();

        // 2. Формируем массив для Excel (шапка + строки)
        $data = [];
        $header = [
            'ФИО',
            'Должность',
            'Оклад',
            'Бонус',
            'Отпускные',
            'Аванс',
            'Оплата по карте',
            'Долг',
            'Удержания долга',
            'Получено на руки',
            'Роспись'
        ];
        $data[] = $header;

        foreach ($result as $row) {
            $totalAdvanceWithCash = ($row['advance'] ?? 0) + ($row['cash_payment'] ?? 0);

            $data[] = [
                $row['full_name'],
                $row['position'],
                (int)$row['salary'],
                (int)$row['bonus'],
                (int)$row['vacation_pay'],
                (int)$totalAdvanceWithCash,
                (int)$row['card_payment'],
                (int)$row['debt'],
                (int)$row['debt_deduction'],
                (int)$row['received_amount'],
                '' // колонка для подписи
            ];
        }

        // 3. Генерируем Excel через PhpSpreadsheet
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Payments ' . $month);

        // Записываем данные
        $sheet->fromArray($data, null, 'A1');

        $rowCount = count($data);
        $lastColumn = 'K'; // 11 колонок (A-K)

        // Формат чисел без десятичных
        $sheet->getStyle("C2:J{$rowCount}")
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        // Бордюр для всей таблицы
        $styleArray = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => 'FF000000']
                ]
            ]
        ];
        $sheet->getStyle("A1:{$lastColumn}{$rowCount}")->applyFromArray($styleArray);

        // Жирный шрифт для заголовка
        $sheet->getStyle('A1:' . $lastColumn . '1')->getFont()->setBold(true);

        // Автоширина столбцов
        foreach (range('A', $lastColumn) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // 4. Отдаём файл
        $filename = 'worker_payment_report_' . $month . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }
    
    public function actionEdit()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $workerId = Yii::$app->request->get('worker_id');
        $month = Yii::$app->request->get('month', date('Y-m'));
        
        if (!$workerId) {
            return [
                'status' => 'error',
                'message' => 'Не указан ID работника'
            ];
        }
        
        $updateService = new \app\modules\backend\services\worker_payment\WorkerPaymentUpdateService();
        $formData = $updateService->getEditFormData($workerId, $month);
        
        if (isset($formData['status']) && $formData['status'] === 'error') {
            return $formData;
        }
        
        return [
            "status" => true,
            "content" => $this->renderPartial('edit', $formData)
        ];
    }
    
    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $updateService = new \app\modules\backend\services\worker_payment\WorkerPaymentUpdateService();
        $result = $updateService->updatePayment(Yii::$app->request->post());

        return $result;
    }

    /**
     * Возвращает массив разбивки выплат по базовому типу и методу оплаты
     */
    private function getPaymentMethodsByType($workerId, $month)
    {
        $payments = WorkerFinances::find()
            ->where([
                'worker_id' => $workerId,
                'month' => $month,
                'deleted_at' => null
            ])->all();

        $result = [];

        foreach ($payments as $payment) {
            $baseType = $payment->type;
            $methodId = \app\common\models\PaymentType::CASH; // По умолчанию наличные

            // Для зарплаты используем старую логику
            if ($payment->type == WorkerFinances::TYPE_CASH_SALARY) {
                $baseType = WorkerFinances::TYPE_SALARY;
                $methodId = \app\common\models\PaymentType::CASH;
            } elseif ($payment->type == WorkerFinances::TYPE_SALARY) {
                $baseType = WorkerFinances::TYPE_SALARY;
                $methodId = \app\common\models\PaymentType::PAYMENT_CARD;
            } else {
                // Для всех остальных типов платежей определяем способ оплаты через таблицу expenses
                $expense = \app\modules\backend\models\Expenses::find()
                    ->where(['worker_finance_id' => $payment->id])
                    ->andWhere(['deleted_at' => null])
                    ->one();

                if ($expense && $expense->payment_type) {
                    $methodId = $expense->payment_type;
                }
            }

            if (!isset($result[$baseType])) {
                $result[$baseType] = [];
            }

            if (!isset($result[$baseType][$methodId])) {
                $result[$baseType][$methodId] = 0;
            }

            $result[$baseType][$methodId] += $payment->amount;
        }

        return $result;
    }
}
