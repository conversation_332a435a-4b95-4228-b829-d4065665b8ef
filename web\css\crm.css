body{
	font-family: 'PT Sans Caption', sans-serif;
}

.layout{
	width: 100%;
	height: 100%;
	display: flex;
	background: #FDFAE9;
}
/*.left{
	width: 65px;
	height: 100vh;
	background: #1B3446;
}*/

.main{
	width: 100%;
	height: 100vh;
	/*overflow:hidden;*/
	position: relative;
}
.main-top{
	width: 100%;
	height: 65px;
	border-bottom: 1px solid #e8eaeb;
	position: fixed;
	background: #FFF;
	display: flex;
	align-items: center;
	z-index: 10;
}
.main-body{
	width: 100%;
	margin-top: 65px;
	padding: 0 15px 15px 15px;
	position: relative;
}

.click-block{
	background: red;
	position: absolute;
	width: 10%;
	height: 52px;
	top: 0;
	left: 0;
	z-index: 9999;
	cursor: progress;
}
.right_block{
	position: fixed;
	background: #f2f2f2;
	width: calc(100% - 250px);
	height: 100%;
	top: 0;
	left: 10000px;
	z-index: 10;
	transition: .3s ease-in-out;
}
.leed_number{
	font-size: 18px;
	font-weight: bold;
	vertical-align: middle;
}
.top_right{
	padding: 10px;
	background: #1B3446;
}

.tag{
	padding: 2px 5px;
	height: 21px;
    color: #fff;
    border: 1px solid #38525d;
    border-radius: 3px;
    display: inline-block;
    margin-top: 5px;
}
.notag{
    color: #38525d;
    border: 1px solid #38525d;
    border-radius: 3px;	
    display: inline-block;
}
.notag_icon{
	vertical-align: text-top;
}
.notag_text{
	font-size: 11px;
	height: 100%;
	padding: 2px 5px;
	cursor: pointer;
}
.btn-right{
	width: 10%;
	height: 100%;
	display: flex;
	align-items: center;
	border-left: 1px solid #e8eaeb;
	padding: 0 10px;
}

.btn-left{
	width: 30%;
	height: 100%;
	display: flex;
	align-items: center;
	border-right: 1px solid #e8eaeb;
	padding: 0 10px;
}

.btn-center{
	width: 60%;
	height: 100%;
	display: flex;
	align-items: center;
	padding: 0 10px;
}

.size-20{
	font-size: 20px;
}


.search{
	border-top: 0 !important;
	border-right: 0 !important;
	border-radius: 0 !important;
}

#basic-addon1{
	border-radius: 0;
}

.view-right{
	border-right: 1px solid #e8eaeb;
	height: calc(100vh - 105px);
}

.number_card{
	font-size: 15px;
    line-height: 17px;
    color: #0057a9;
}

.open_modal{
	line-height: 18px;
}

.client_name{
	color: #363b44;
}

.client_phone{
	color: #707070;
	font-size: 12px;
	border: 1px solid #707070;
	border-radius: 3px;
	padding: 0 3px;
}

.hr-border{
  margin-top: 0;
  height: 2px;
  border: none;
}

.board .hr-border1, .hr-border1{
	background: #ccc;
}

.board .hr-border2, .hr-border2{
	background: #fffeb2;
}

.board .hr-border3, .hr-border3{
	background: #ffeab2;
}

.board .hr-border4, .hr-border4{
	background: #ffce5a;
}

.board .hr-border5, .hr-border5{
	background: #99ccff;
}

.board .hr-border6, .hr-border6{
	background: #ff8f92;
}

.voronka{
	background: #16303C;
	border-radius: 5px;
	height: 4px;
	display: flex;
	overflow: hidden;
}

.voronka_title{
	color: #FFF;
}

.section_item{
	width: 100%;
	height: 100%;
}

.center_right{
	height: 100%;
	border: 1px solid;
	background: #FFF;
	padding: 15px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.list_info{
	min-height: 20px;
	width: 100%;
	display: flex;
	margin-bottom: 15px;
}

.list_title{
	width: 40%;
}

.list_value{
	width: 60%;
	display: flex;
	justify-content: space-between;
}

.left-menu{
	position: fixed;
	width: 16px;
	height: calc(100% - 65px);
	background: #1b34469c;
	left: 0;
	z-index: 10;
	border-right: 1px solid #FFF;
	transition: .3s ease-in-out;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.left-menu:hover{
	background: #002650;
}

.left-menu:hover .circle_icon{
	background: #002650;
}

.circle_icon{
	background: #99ccff;
	width: 22px;
	height: 22px;
	border-radius: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 6px;
	margin-top: 20px;
	color: #FFF;
	border: 1px solid #FFF;
	transition: .3s ease-in-out;
	position: absolute;
	top: 0;
	left: 0;
}

.menu_body{
	padding: 10px;
	display: none;
}

.user_name{
	border-radius: 3px;
	width: 100%;
	min-height: 30px;
	display: flex;
	align-items: center;
}

.user_icon{
	width: 36px;
	height: 36px;
	border-radius: 5px;
	background: linear-gradient(#006644, #00875a);
	display: flex;
	justify-content: center;
	align-items: center;
	color: #FFF;
	font-size: 20px;
	text-transform: uppercase;
	font-weight: bold;
	margin-right: 10px;
}

.user{
	font-size: 13px;
	color: #FFF;
}
.role{
	font-size: 11px;
	color: #FFF;	
}

.user_close{
	margin-left: auto;
	color: #FFF;
	font-weight: bold;
	width: 30px;
	height: 30px;
	border-radius: 5px;
	transition: .3s ease-in-out;
	display: flex;
	justify-content: center;
	align-items: center;
}

.user_close:hover{
	background: rgba(255, 255, 255, .3);
}

.board_lists{
	display: flex;
	flex-direction: column;
	height: 70vh;
	overflow-y: auto;
}

.list_item{
	width: 100%;
	min-height: 30px;
	border: solid 1px #CCC;
	border-radius: 5px;
	display: flex;
	align-items: center;
	padding: 0 15px;
	margin-bottom: 15px;
	transition: .3s ease-in-out;
	color: #CCC !important;
	text-decoration: none;
	font-size: 12px !important;
}

.list_active{
	border: solid 1px #FFF;
	color: #FFF !important;
	background: rgba(255, 255, 255, .3);
}

.list_item:hover{
	border: solid 1px #FFF;
	color: #FFF !important;
	background: rgba(255, 255, 255, .3);
	text-decoration: none;
}

.other_moduls{
	width: 100%;
	display: none;
	padding: 10px;
	text-align: center;
}

.other_moduls a{
	text-decoration: none;
}

.red_block{
	background: #ff000026 !important;
}

.yellow_block{
	background: #ffeb3b5c !important;
}

.history_block{
	width: 100%;
	height: 100%;
}

.top_history_block{
  width: 100%;
  height: calc(100vh - 71px);
  overflow-x: hidden;
  overflow-y: auto;
  margin-bottom: 15px;
}

.bottom_history_block{
	width: 100%;
	padding-top: 20px;
}

.right-menu{
	position: fixed;
	width: 0;
	height: calc(100% - 65px);
	background: #FFF;
	right: 0;
	z-index: 10;
	border-left: 1px solid #f2f2f2;
	transition: .3s ease-in-out;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	box-shadow: 0 -10 10px #ccc;
}

.circle_user{
	width: 30px;
	height: 30px;
	border-radius: 100%;
	display: inline-block;
	background: linear-gradient(#002650, #00875a);
	display: flex;
	justify-content: center;
	align-items: center;
	text-transform: uppercase;
	color: #FFF;
}

.overflow_block{
	overflow: auto;
	max-height: 50vh;
}

#districts{
	width: 100%;
	min-height: 100px;
	margin-bottom: 25px;
}

.open_modal2{
	padding: 5px;
	border: 1px solid #ccc;
	background: #f2f2f2;
	border-radius: 5px;
	font-size: 10px;
	cursor: pointer;
	transition: .3s;
}

.open_modal2:hover{
	background: #FDFAE9;
}

.open_modal3{
	padding: 5px;
	background: #FDFAE9;
	border: 1px solid #ccc;	
	border-radius: 5px;
	transition: .3s;
	font-size: 10px;
}

.search_block{
	padding: 10px;
}

.clone_block{
	position: absolute;
	width: 30px;
	height: 30px;
	right: 5px;
	top: 5px;
	border-radius: 100%;
	border: 1px solid #FFF;
	background: rgba(0.8, 0.8, 0.8, .5);
	color: #FFF;
	display: none;
	justify-content: center;
	align-items: center;
	font-size: 20px;
}

.change_section{
	width: 30px;
	height: 30px;
	border-radius: 100%;
	border: 1px solid #FFF;
	background: rgba(0.8, 0.8, 0.8, .5);
	color: #FFF;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 20px;
	margin-left: auto;
	margin-right: 5px;
	cursor: pointer;
	transition: .3s ease-in-out;
}
.change_section:hover{
	background: rgba(0.8, 0.8, 0.8, .7);
}

.change_user{
	width: 30px;
	height: 30px;
	border-radius: 100%;
	border: 1px solid #FFF;
	background: rgba(0.8, 0.8, 0.8, .5);
	color: #FFF;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 20px;
	margin-left: auto;
	margin-right: 5px;
	cursor: pointer;
	transition: .3s ease-in-out;
}
.change_user:hover{
	background: rgba(0.8, 0.8, 0.8, .7);
}

.open_modal:hover .clone_block{
	display: flex;
}

/*---------- loader -------------*/

.btnLoader {
	display: none;
	justify-content: center;
	align-items: center;
	gap: 1rem;
	background-color: inherit;
	width: 100%;
	height: 100vh;
	position: fixed;
	left: 0;
	top: 65px;
	z-index: 1049;
	background: rgba(253,250,233, 0.8);
}
.btnLoader span {
  --dot1: #788c9f;
  --dot2: #788c9f33;
  --dot3: #788c9f66;
  --dot4: #788c9fb3;
  --duration: 700ms;
  --easing: linear;
  --delay: 1ms;
  width: 32px;
  height: 32px;
  border-radius: 50%;
}
.btnLoader span:nth-child(1) {
  background-color: var(--dot1);
  animation: var(--duration) var(--easing) var(--delay) infinite normal flash1;
}
.btnLoader span:nth-child(2) {
  background-color: var(--dot2);
  animation: var(--duration) var(--easing) var(--delay) infinite normal flash2;
}
.btnLoader span:nth-child(3) {
  background-color: var(--dot3);
  animation: var(--duration) var(--easing) var(--delay) infinite normal flash3;
}
.btnLoader span:nth-child(4) {
  background-color: var(--dot4);
  animation: var(--duration) var(--easing) var(--delay) infinite normal flash4;
}

@keyframes flash1 {
  0%, 100% { background-color: var(--dot1) }
  25% { background-color: var(--dot2) }
  50% { background-color: var(--dot3) }
  75% { background-color: var(--dot4) }
}
@keyframes flash2 {
  0%, 100% { background-color: var(--dot4) }
  25% { background-color: var(--dot1) }
  50% { background-color: var(--dot2) }
  75% { background-color: var(--dot3) }
}
@keyframes flash3 {
  0%, 100% { background-color: var(--dot3) }
  25% { background-color: var(--dot4) }
  50% { background-color: var(--dot1) }
  75% { background-color: var(--dot2) }
}
@keyframes flash4 {
  0%, 100% { background-color: var(--dot2) }
  25% { background-color: var(--dot3) }
  50% { background-color: var(--dot4) }
  75% { background-color: var(--dot1) }
}



.fs-16{
	font-size: 16px !important;
}

.right-menu-task{
	position: fixed;
	width: 0;
	height: calc(100% - 65px);
	background: #FFF;
	right: 0;
	z-index: 10;
	border-left: 1px solid #f2f2f2;
	transition: .3s ease-in-out;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	box-shadow: 0 -10 10px #ccc;
}