<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "free_product_detail".
 *
 * @property int $id
 * @property int $free_product_id
 * @property int $product_id
 * @property int $quantity
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property FreeProducts $freeProduct
 * @property Product $product
 */
class FreeProductDetail extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'free_product_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['free_product_id', 'product_id', 'quantity'], 'required'],
            [['free_product_id', 'product_id', 'quantity'], 'default', 'value' => null],
            [['free_product_id', 'product_id', 'quantity'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            [['free_product_id'], 'exist', 'skipOnError' => true, 'targetClass' => FreeProducts::class, 'targetAttribute' => ['free_product_id' => 'id']],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'free_product_id' => Yii::t('app', 'free product'),
            'product_id' => Yii::t('app', 'product'),
            'quantity' => Yii::t('app', 'quantity'),
            'created_at' => Yii::t('app', 'created at'),
            'deleted_at' => Yii::t('app', 'deleted at'),
        ];
    }

    /**
     * Gets query for [[FreeProduct]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getFreeProduct()
    {
        return $this->hasOne(FreeProducts::class, ['id' => 'free_product_id']);
    }

    /**
     * Gets query for [[Product]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }
}
