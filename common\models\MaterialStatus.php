<?php

namespace app\common\models;

use Yii;
use app\modules\backend\models\Users;

/**
 * This is the model class for table "material_status".
 *
 * @property int $id
 * @property int $material_id
 * @property int $status_group_id
 * @property int $quantity
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property InvoiceDetail $invoiceDetail
 * @property Material $material
 */
class MaterialStatus extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'material_status';
    }

   




    public function rules()
    {
        return [
            [['material_id', 'quantity', 'status_group_id'], 'required'],
            [['material_id', 'quantity'], 'default', 'value' => null],
            [['material_id', 'status_group_id', 'quantity'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            [['material_id'], 'exist', 'skipOnError' => true, 'targetClass' => Material::class, 'targetAttribute' => ['material_id' => 'id']],
            [['status_group_id'], 'exist', 'skipOnError' => true, 'targetClass' => MaterialStatusGroup::class, 'targetAttribute' => ['status_group_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'material_id' => 'Material ID',
            'status' => 'Status',
            'quantity' => 'Quantity',
            'deleted_at' => 'Deleted At',
        ];
    }

   

    /**
     * Gets query for [[Material]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterial()
    {
        return $this->hasOne(Material::class, ['id' => 'material_id']);
    }

    /**
     * Gets query for [[StatusGroup]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getStatusGroup()
    {
        return $this->hasOne(MaterialStatusGroup::class, ['id' => 'status_group_id']);
    }
 
}
