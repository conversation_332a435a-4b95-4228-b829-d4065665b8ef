<?php

namespace app\common\models;

use app\modules\backend\models\Users;
use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "product_storage_history".
 *
 * @property int $id
 * @property int $product_storage_id
 * @property int $product_id
 * @property int $quantity
 * @property string $type
 * @property string $created_at
 * @property int|null $add_user_id
 * @property int|null $original_storage_id
 * @property string|null $deleted_at
 *
 * @property Users $addUser
 * @property Product $product
 * @property ProductStorage $productStorage
 * @property ProductStorage $originalStorage
 */
class ProductStorageHistory extends ActiveRecord
{
    const TYPE_INCOME = 'income';
    const TYPE_OUTCOME = 'outcome';
    const TYPE_RETURN = 'return';
    const TYPE_REPACKAGING = 'repackaging';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product_storage_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['product_storage_id', 'product_id', 'quantity', 'type'], 'required'],
            [['product_storage_id', 'product_id', 'quantity', 'add_user_id', 'original_storage_id'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            [['type'], 'string', 'max' => 50],
            [['add_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['add_user_id' => 'id']],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
            [['product_storage_id'], 'exist', 'skipOnError' => true, 'targetClass' => ProductStorage::class, 'targetAttribute' => ['product_storage_id' => 'id']],
            [['original_storage_id'], 'exist', 'skipOnError' => true, 'targetClass' => ProductStorage::class, 'targetAttribute' => ['original_storage_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'product_storage_id' => 'Product Storage ID',
            'product_id' => 'Product ID',
            'quantity' => 'Quantity',
            'type' => 'Type',
            'created_at' => 'Created At',
            'add_user_id' => 'Add User ID',
            'original_storage_id' => 'Original Storage ID',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[AddUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAddUser()
    {
        return $this->hasOne(Users::class, ['id' => 'add_user_id']);
    }

    /**
     * Gets query for [[Product]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }

    /**
     * Gets query for [[ProductStorage]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProductStorage()
    {
        return $this->hasOne(ProductStorage::class, ['id' => 'product_storage_id']);
    }

    /**
     * Gets query for [[OriginalStorage]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getOriginalStorage()
    {
        return $this->hasOne(ProductStorage::class, ['id' => 'original_storage_id']);
    }
}
