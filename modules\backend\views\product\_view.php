<?php
use yii\helpers\Html;
?>

<style>
    .card {
        background-color: white !important;
    }
    .table th {
        background-color: #f8f9fa; /* Светлый фон для заголовков таблицы */
    }
    .badge-success {
        background-color: #28a745; /* Зеленый цвет для активного статуса */
    }
    .badge-danger {
        background-color: #dc3545; /* Красный цвет для неактивного статуса */
    }
    .table-responsive {
        max-height: 300px;
        overflow-y: auto;
    }
    .table-responsive thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 1;
    }
    .table tbody tr {
        height: 48px;
    }
</style>

<div class="product-view">
    <!-- Основная информация о продукте -->
      
             <h5 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'product_info') ?></h5>
                <div>
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th style="width: 200px;"><?= Yii::t('app', 'product_name') ?></th>
                                <td><?= Html::encode($model->name) ?></td>
                            </tr>
                            <tr>
                                <th><?= Yii::t('app', 'product_block_quantity') ?></th>
                                <td><?= Html::encode($model->size) ?></td>
                            </tr>
                            <tr>
                                <th><?= Yii::t('app', 'added_date') ?></th>
                                <td><?= date('d.m.Y', strtotime($model->created_at)) ?></td>
                            </tr>
                            <tr>
                                <th><?= Yii::t('app', 'status') ?></th>
                                <td>
                                    <span class="badge <?= $model->deleted_at == NULL ? 'badge-success' : 'badge-danger' ?>">
                                        <?= $model->deleted_at == NULL ? Yii::t('app', 'active') : Yii::t('app', 'inactive') ?>
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

    <!-- История изменения цен -->
  
            <h5 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'price_history') ?></h5>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th><?= Yii::t('app', 'price') ?></th>
                            <th><?= Yii::t('app', 'price_start_date') ?></th>
                            <th><?= Yii::t('app', 'end_date') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($priceHistory as $price): ?>
                            <tr>
                                <td><?= Html::encode($price['price']) ?></td>
                                <td><?= date('d.m.Y', strtotime($price['start_date'])) ?></td>
                                <td>
                                    <?= $price['end_date'] === '9999-12-31' 
                                        ? '<span class="badge badge-success">' . Yii::t('app', 'current_price') . '</span>'
                                        : date('d.m.Y', strtotime($price['end_date'])) 
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tableContainer = document.querySelector('.table-responsive');
    const table = tableContainer.querySelector('table');
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');
    const maxRows = 5;
    const rowHeight = 48; // Фиксированная высота строки

    if (rows.length > maxRows) {
        tableContainer.style.maxHeight = `${(maxRows * rowHeight) + 48}px`; // +48px для заголовка
        tableContainer.style.overflowY = 'auto';
    } else {
        tableContainer.style.maxHeight = 'none';
        tableContainer.style.overflowY = 'visible';
    }
});
</script>