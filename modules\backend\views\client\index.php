<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\assets\Select2Asset;

DataTablesAsset::register($this);
Select2Asset::register($this);

$this->title = Yii::t("app", "clients_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");

?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('product_keeper')): ?>
                <a href="#" class="btn btn-primary client-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "add_client") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'client-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="client-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "ID") ?></th>
                        <th><?= Yii::t("app", "client_name") ?></th>
                        <th><?= Yii::t("app", "client_region_id") ?></th>
                        <th><?= Yii::t("app", "phone_number") ?></th>
                        <th><?= Yii::t("app", "phone_number_2") ?></th>
                        <th><?= Yii::t("app", "client_balance") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                        <td><?= Html::encode($model['id']) ?></td>
                        <td><?= Html::encode($model['full_name']) ?></td>
                        <td><?= Html::encode($model['region_name']) ?></td>
                        <td><?= Html::encode($model['phone_number']) ?></td>
                        <td><?= Html::encode($model['phone_number_2']) ?></td>
                        <td><?= number_format($model['balance'], 0, '.', ',') ?></td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">



                                    <a href="<?= Url::to(['/backend/client/view', 'id' => $model['id']]) ?>" class="dropdown-item">
                                        <?= Yii::t("app", "view") ?>
                                    </a>

                                    <?php if(empty($model['deleted_at'])): ?>
                                        <a href="#" class="dropdown-item client-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>

                                        <a href="#" class="dropdown-item contract-create" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "client_contract") ?>
                                        </a>

                                        <a href="#" class="dropdown-item client-pay" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "pay") ?>
                                        </a>

                                        <a href="#" class="dropdown-item client-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model['id']) ?>">
                                            <span class="red-text"><?= Yii::t("app", "delete") ?></span>
                                        </a>

                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "add_client") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "edit_client") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "client_delete") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "client_pay") ?>"></div>
<div id="five" data-text="<?= Yii::t("app", "client_contract") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');
    var four = $('#four').attr('data-text');
    var five = $('#five').attr('data-text');
    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";
    var all = "{$all}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#client-grid-view')) {
            $('#client-grid-view').DataTable().destroy();
        }

        $('#client-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[0, 'asc']],
            "columnDefs": [
                {
                    "targets": [3, 4, 5, 6],
                    "orderable": false
                }
            ]

        });
    }

    $(document).off('pjax:end').on('pjax:end', function() {
        initializeDataTable();
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.stopPropagation();
            if (!$(this).attr('href') || $(this).attr('href') === '#') {
                e.preventDefault();
            }
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }




    function initializeClientContract() {
        $(document).off('click.contract-create').on('click.contract-create', '.contract-create', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/client/contract',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(five);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("client-contract-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.client-contract-button').on('click.client-contract-button', '.client-contract-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#client-contract-form').serialize();
                $.ajax({
                    url: '/backend/client/contract',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#client-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }




    function initializeClientCreate() {
        $(document).off('click.client-create').on('click.client-create', '.client-create', function() {
            $.ajax({
                url: '/backend/client/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("client-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.client-create-button').on('click.client-create-button', '.client-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#client-create-form').serialize();
                $.ajax({
                    url: '/backend/client/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#client-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeClientUpdate() {
        $(document).off('click.client-update').on('click.client-update', '.client-update', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/client/update',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(two);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("client-update-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.client-update-button').on('click.client-update-button', '.client-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#client-update-form').serialize();
                $.ajax({
                    url: '/backend/client/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#client-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeClientPay() {
        $(document).off('click.client-pay').on('click.client-pay', '.client-pay', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/client/pay',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(four);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("client-pay-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.client-pay-button').on('click.client-pay-button', '.client-pay-button', function() {
            submitClientPayForm($(this));
        });

        $(document).off('keypress.client-pay-form').on('keypress.client-pay-form', '#client-pay-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                submitClientPayForm($('.client-pay-button'));
            }
        });

        function submitClientPayForm(button) {
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#client-pay-form').serialize();
                $.ajax({
                    url: '/backend/client/pay',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#client-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        }
    }

    function initializeClientDelete() {
        $(document).off('click.client-delete').on('click.client-delete', '.client-delete', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/client/delete',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal-delete .modal-title').html(three);
                    $('#ideal-mini-modal-delete .modal-body').html(response.content);
                    $('#ideal-mini-modal-delete .mini-button').addClass("client-delete-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.client-delete-button').on('click.client-delete-button', '.client-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#client-delete-form').serialize();
                $.ajax({
                    url: '/backend/client/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#client-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }


    initializeDataTable();
    initializeDropdown();
    initializeClientCreate();
    initializeClientUpdate();
    initializeClientPay();
    initializeClientContract();
    initializeClientDelete();

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
