<?php

namespace app\modules\backend\controllers;

use Yii;
use app\common\models\Material;
use yii\data\ArrayDataProvider;
use yii\web\Response;

class MaterialController extends BaseController
{
    public function actionIndex()
    {
        $query = Material::find()
            ->select([
                'material.*'
            ])
            ->asArray()
            ->all();

        return $this->render('index', [
            'result' => $query,
        ]);
    }

    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new Material();
            $model->load(Yii::$app->request->post());
            if ($model->validate()) {
                $material = Material::findOne(['name' => $model->name]);
                if ($material) {
                    return [
                        'status' => 'error',
                        'errors' => ['name' => Yii::t('app', 'Material already exists.')],
                    ];
                }

                $model->save();

                return [
                    'status'=> 'success',
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $model = new Material();
            return [
                "status" => 'success',
                "content" => $this->renderPartial('create', ['model' => $model])
            ];
        }
    }

    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $model = Material::findOne($postData['Material']['id']);
            
            if (!$model) {
                return ['status' => 'error', 'message' => Yii::t('app', 'Material not found.')];
            }

            $model->load($postData);
            
            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }

            $material = Material::findOne(['name' => $model->name]);
            if ($material && $material->id != $model->id) {
                return [
                    'status' => 'error',
                    'errors' => ['name' => Yii::t('app', 'Material already exists.')],
                ];
            }

            if ($model->save()) {
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Material updated successfully.'),
                ];
            } else {
                return [
                    'status' => 'fail',
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Material::findOne($id);
            
            if (!$model) {
                return ['status' => 'error', 'message' => Yii::t('app', 'Material not found.')];
            }

            return [
                "status" => true,
                "content" => $this->renderPartial('update', ['model' => $model])
            ];
        }
    }

    public function actionDelete()
    {
       Yii::$app->response->format = Response::FORMAT_JSON;

       if(Yii::$app->request->isPost) {
        $data = Yii::$app->request->post();
            $id = $data['Material']['id'];
            $model = Material::findOne($id);
            if ($model) {
                $model->deleted_at = date('Y-m-d H:i:s');
                if ($model->save()) {
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'Material deleted successfully.'),
                    ];
                } else {
                    return [
                        'status' => 'fail',
                        'errors' => $model->getErrors(),
                    ];
                }
            } else {
                return ['status' => 'error', 'message' => Yii::t('app', 'Material not found.')];
            }
       } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Material::findOne($id);
            if (!$model) {
                return ['status' => 'error', 'message' => Yii::t('app', 'Material not found.')];
            }
            return [
                "status" => true,
                "content" => $this->renderPartial('delete', ['model' => $model])
            ];
       }
    }
}
