<?php

namespace app\modules\api\models;

use yii\base\Model;

class ReleaseFinishedProductForm extends Model
{
    public $products = [];


    public function rules()
    {
        return [
           [['products'], 'required'],
           ['products', 'validateProducts'],
        ];
    }

    public function validateProducts($attribute, $params)
    {
        foreach ($this->products as $product) {

            if(!isset($product['product_id']) || !isset($product['quantity'])) {
                $this->addError($attribute, 'Заполните все поля');
            }
            if ($product['quantity'] < 1) {
                $this->addError($attribute, 'Количество не может быть меньше 1');
            }
        }
    }

    public function attributeLabels()
    {
        return [
            'product_id' => 'Продукт',
            'quantity' => 'Количество',
        ];
    }
}
