<?php
use yii\helpers\Html;
use app\common\models\ClientPayments;
use yii\widgets\LinkPager;
?>

<style>
    input[type="date"].form-control {
        width: 150px;
    }

    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }

    .pagination {
        margin: 10px 0;
        display: flex;
        justify-content: center;
    }

    .pagination li {
        display: inline-block;
        margin: 0 2px;
    }

    .pagination li a,
    .pagination li span {
        padding: 6px 12px;
        border: 1px solid #ddd;
        text-decoration: none;
        color: #337ab7;
    }

    .pagination li.active span {
        background-color: #337ab7;
        color: white;
        border-color: #337ab7;
    }

    /* Стили для модального окна */
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1050 !important;
    }

    .modal {
        z-index: 1055 !important;
    }

    .modal-content {
        background-color: #fff;
        border-radius: 0.3rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        position: relative;
        z-index: 1056 !important;
    }

    .modal-header {
        border-bottom: 1px solid #dee2e6;
        background-color: #f8f9fa;
    }

    .modal-footer {
        border-top: 1px solid #dee2e6;
        background-color: #f8f9fa;
    }

    .modal-body {
        background-color: #fff;
    }

    /* Убираем возможные конфликты с другими элементами */
    .card-body {
        position: relative;
        z-index: 1;
    }

    .table-responsive {
        position: relative;
        z-index: 1;
    }
</style>

<?php
// Регистрируем необходимые скрипты Bootstrap
$this->registerCss("
    body.modal-open {
        overflow: hidden;
        padding-right: 0 !important;
    }
");

$this->registerJsFile('https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js', [
    'depends' => [\yii\web\JqueryAsset::class]
]);

$this->registerJs("
    // Установка начальных значений дат
    document.getElementById('date_from').value = '" . $dateFrom . "';
    document.getElementById('date_to').value = '" . $dateTo . "';

    // Обработчик нажатия на кнопку поиска
    document.getElementById('search-button').addEventListener('click', function() {
        var dateFrom = document.getElementById('date_from').value;
        var dateTo = document.getElementById('date_to').value;
        var currentUrl = new URL(window.location.href);
        
        // Обновляем параметры URL
        currentUrl.searchParams.set('date_from', dateFrom);
        currentUrl.searchParams.set('date_to', dateTo);
        currentUrl.searchParams.set('sales_page', '1');
        currentUrl.searchParams.set('payments_page', '1');
        
        // Перенаправляем на новый URL
        window.location.href = currentUrl.toString();
    });

    // Функция для обновления URL при переключении страниц
    function updatePage(type, page) {
        var currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set(type + '_page', page);
        window.location.href = currentUrl.toString();
    }

    // Обработчик нажатия на кнопку экспорта
    document.getElementById('export-excel-button').addEventListener('click', function() {
        var dateFrom = document.getElementById('date_from').value;
        var dateTo = document.getElementById('date_to').value;
        
        // Подготавливаем HTML для модального окна
        var modalContent = `
            <div class='row'>
                <div class='col-md-6'>
                    <div class='form-group'>
                        <label for='modal_date_from'>" . Yii::t('app', 'From Date') . "</label>
                        <input type='date' class='form-control' id='modal_date_from' value='${dateFrom}' required>
                    </div>
                </div>
                <div class='col-md-6'>
                    <div class='form-group'>
                        <label for='modal_date_to'>" . Yii::t('app', 'To Date') . "</label>
                        <input type='date' class='form-control' id='modal_date_to' value='${dateTo}' required>
                    </div>
                </div>
            </div>
        `;
        
        // Устанавливаем заголовок и содержимое модального окна
        $('#ideal-mini-modal-excel .modal-title').text('" . Yii::t('app', 'Export to Excel') . "');
        $('#ideal-mini-modal-excel .modal-body').html(modalContent);
        
        // Показываем модальное окно
        $('#ideal-mini-modal-excel').modal('show');
        
        // Обработчик нажатия на кнопку экспорта в модальном окне
        $('#ideal-mini-modal-excel .mini-button').off('click').on('click', function() {
            var modalDateFrom = document.getElementById('modal_date_from').value;
            var modalDateTo = document.getElementById('modal_date_to').value;
            var clientId = '" . $model['id'] . "';
            
            // Создаем форму динамически
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = '" . \yii\helpers\Url::to(['client/excel-client-data']) . "';
            form.target = '_blank';
            
            // Добавляем необходимые поля
            var inputDateFrom = document.createElement('input');
            inputDateFrom.type = 'hidden';
            inputDateFrom.name = 'start_date';
            inputDateFrom.value = modalDateFrom;
            form.appendChild(inputDateFrom);
            
            var inputDateTo = document.createElement('input');
            inputDateTo.type = 'hidden';
            inputDateTo.name = 'end_date';
            inputDateTo.value = modalDateTo;
            form.appendChild(inputDateTo);
            
            var inputClientId = document.createElement('input');
            inputClientId.type = 'hidden';
            inputClientId.name = 'client_id';
            inputClientId.value = clientId;
            form.appendChild(inputClientId);
            
            // Добавляем CSRF токен
            var csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '" . Yii::$app->request->csrfParam . "';
            csrfToken.value = '" . Yii::$app->request->getCsrfToken() . "';
            form.appendChild(csrfToken);
            
            // Добавляем форму в документ, отправляем и удаляем
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
            
            // Закрываем модальное окно
            $('#ideal-mini-modal-excel').modal('hide');
        });
    });
");
?>

<div class="client-view">
    <!-- Основная информация о клиенте -->
        <div class="card-body">

                <div class="row align-items-center mb-3">
                    <div class="col-md-6">
                        <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
                    </div>

                            <div class="col-md-6">
                                <?php if (Yii::$app->user->can('admin')): ?>
                                    <div class="d-flex justify-content-end align-items-center gap-2">
                                        <!-- Date inputs -->
                                        <div class="d-flex gap-2" style="min-width: 200px;">
                                            <input type="date" id="date_from" class="form-control" placeholder="<?= Yii::t('app', 'From Date') ?>">
                                            <input type="date" id="date_to" class="form-control" placeholder="<?= Yii::t('app', 'To Date') ?>">
                                        </div>

                                        <!-- Filter button -->
                                        <button type="button" class="btn btn-primary" id="search-button">
                                            <?= Yii::t('app', 'search') ?>
                                        </button>
                                        
                                        <!-- Export to Excel button -->
                                        <button type="button" class="btn btn-success" id="export-excel-button">
                                            <?= Yii::t('app', 'Export to Excel') ?>
                                        </button>
                                    </div>
                                <?php endif ?>
                            </div>
                </div>





        <h5  style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'Client Information') ?></h5>

            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'full_name') ?></th>
                        <th><?= Yii::t('app', 'phone_number') ?></th>
                        <?php if ($model['phone_number_2']): ?>
                            <th><?= Yii::t('app', 'Additional Phone') ?></th>
                        <?php endif; ?>
                        <th><?= Yii::t('app', 'client_region') ?></th>
                        <th><?= Yii::t('app', 'account_number') ?></th>
                        <th><?= Yii::t('app', 'client_balance') ?></th>
                        <th><?= Yii::t('app', 'organization') ?></th>
                        <th><?= Yii::t('app', 'legal_address') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><?= Html::encode($model['full_name']) ?></td>
                        <td><?= Html::encode($model['phone_number']) ?></td>
                        <?php if ($model['phone_number_2']): ?>
                            <td><?= Html::encode($model['phone_number_2']) ?></td>
                        <?php endif; ?>
                        <td><?= Html::encode($model['region_name']) ?></td>
                        <td><?= Html::encode($model['account_number']) ?></td>
                        <td class="total-value"><?= number_format($model['balance'], 0, '.', ' ') ?></td>
                        <td><?= Html::encode($model['organization']) ?></td>
                        <td><?= Html::encode($model['legal_address']) ?></td>
                    </tr>
                </tbody>
            </table>
    </div>


    <?php if (!empty($contracts)): ?>
        <div class="card-body">
            <h5  style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'contract') ?></h5>
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'contract') ?></th>
                        <th><?= Yii::t('app', 'contract_date_start') ?></th>
                        <th><?= Yii::t('app', 'contract_date_end') ?></th>
                        <th><?= Yii::t('app', 'contract_status') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($contracts as $contract): ?>
                        <tr>
                            <td><?= Html::encode($contract['contract']) ?></td>
                            <td><?= Html::encode($contract['start_date']) ?></td>
                            <td><?= Html::encode($contract['end_date']) ?></td>
                            <td>
                            <span class="<?= $contract['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?php
                                $statusText = $contract['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                echo $statusText;
                                ?>
                            </span>
                        </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>

    <!-- История покупок -->
    <div class="card-body">
    <h5  style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'sell_history') ?></h5>

        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'id') ?></th>
                        <th><?= Yii::t('app', 'arrival_time') ?></th>
                        <th><?= Yii::t('app', 'leave_time') ?></th>
                        <th><?= Yii::t('app', 'driver') ?></th>
                        <th><?= Yii::t('app', 'car_number') ?></th>
                        <th><?= Yii::t('app', 'user_who_load') ?></th>
                        <?php foreach ($products as $product): ?>
                            <th class="text-center"><?= Html::encode($product['name']) ?></th>
                        <?php endforeach; ?>
                        <th><?= Yii::t('app', 'total_quantity') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($sales as $sale): ?>
                        <tr>
                            <td><?= Html::encode($sale['id']) ?></td>
                            <td><?= Html::encode($sale['arrival_time']) ?></td>
                            <td><?= Html::encode($sale['departure_time']) ?></td>
                            <td><?= Html::encode($sale['driver']) ?></td>
                            <td><?= Html::encode($sale['car_number']) ?></td>
                            <td><?= Html::encode($sale['accepted_user']) ?></td>
                            <?php $totalQuantity = 0; ?>
                            <?php foreach ($products as $product): ?>
                                <?php $quantity = $sale['products'][$product['id']]['quantity'] ?? 0; ?>
                                <td class="text-center"><?= $quantity ?></td>
                                <?php $totalQuantity += $quantity; ?>
                            <?php endforeach; ?>
                            <td class="text-center"><strong><?= $totalQuantity ?></strong></td>
                        </tr>
                    <?php endforeach; ?>
                    <tr>
                        <td colspan="6"><strong><?= Yii::t('app', 'amount') ?></strong></td>
                        <?php $grandTotal = 0; ?>
                        <?php foreach ($products as $product): ?>
                            <?php 
                            $totalSum = array_sum(array_map(fn($sale) => $sale['products'][$product['id']]['total'] ?? 0, $sales));
                            ?>
                            <td class="text-center"><strong><?= number_format($totalSum, 0, '.', ' ') ?></strong></td>
                            <?php $grandTotal += $totalSum; ?>
                        <?php endforeach; ?>
                        <td class="text-center"><strong><?= number_format($grandTotal, 0, '.', ' ') ?></strong></td>
                    </tr>

                </tbody>
            </table>

            <!-- Пагинация для продаж -->
            <div class="pagination">
                <?php
                $totalPages = ceil($totalSales / $pageSize);
                for ($i = 1; $i <= $totalPages; $i++):
                ?>
                    <li class="<?= $i == $salesPage ? 'active' : '' ?>">
                        <?php if ($i == $salesPage): ?>
                            <span><?= $i ?></span>
                        <?php else: ?>
                            <a href="javascript:void(0)" onclick="updatePage('sales', <?= $i ?>)"><?= $i ?></a>
                        <?php endif; ?>
                    </li>
                <?php endfor; ?>
            </div>
        </div>
    </div>

    <?php if (!empty($payments)): ?>
        <div class="card-body">
        <h5  style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'Payment History') ?></h5>

            <div class="table-responsive">
                <table class="table table-bordered table-striped table-hover">
                    <thead>
                        <tr>
                            <th><?= Yii::t('app', 'payment_created_at') ?></th>
                            <th><?= Yii::t('app', 'added_by') ?></th>
                            <th><?= Yii::t('app', 'type') ?></th>
                            <th class="text-end"><?= Yii::t('app', 'amount') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $totalPayments = 0;
                        foreach ($payments as $payment): 
                        ?>
                            <tr>
                                <td><?= date('d.m.Y H:i', strtotime($payment['created_at'])) ?></td>
                                <td><?= Html::encode($payment['added_by']) ?></td>
                                <td><?= Html::encode(ClientPayments::getTypeLabel($payment['type'])) ?></td>
                                <td class="text-end total-value"><?= number_format($payment['summa'], 0, '.', ' ') ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <!-- Пагинация для платежей -->
                <div class="pagination">
                    <?php
                    $totalPages = ceil($totalPayments / $pageSize);
                    for ($i = 1; $i <= $totalPages; $i++):
                    ?>
                        <li class="<?= $i == $paymentsPage ? 'active' : '' ?>">
                            <?php if ($i == $paymentsPage): ?>
                                <span><?= $i ?></span>
                            <?php else: ?>
                                <a href="javascript:void(0)" onclick="updatePage('payments', <?= $i ?>)"><?= $i ?></a>
                            <?php endif; ?>
                        </li>
                    <?php endfor; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (!empty($balanceHistory)): ?>
        <div class="card-body">
        <h5  style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'Balance History') ?></h5>

            <div class="table-responsive">
                <table class="table table-bordered table-striped table-hover">
                    <thead>
                        <tr>
                            <th><?= Yii::t('app', 'Date') ?></th>
                            <th><?= Yii::t('app', 'type') ?></th>
                            <th class="text-end"><?= Yii::t('app', 'Old Amount') ?></th>
                            <th class="text-end"><?= Yii::t('app', 'New Amount') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($balanceHistory as $history): ?>
                            <tr>
                                <td><?= date('d.m.Y H:i', strtotime($history['created_at'])) ?></td>
                                <td><?= Html::encode(ClientPayments::getTypeLabel($history['type'])) ?></td>
                                <td class="text-end"><?= number_format($history['old_amount'], 0, '.', ' ') ?></td>
                                <td class="text-end total-value"><?= number_format($history['amount'], 0, '.', ' ') ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

</div>

