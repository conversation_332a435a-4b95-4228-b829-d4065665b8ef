<?php

namespace app\modules\backend\controllers;

use app\common\models\Sales;
use app\common\models\SalesDetail;
use Exception;
use Yii;

class PrintInvoiceController extends BaseController
{
    /**
     * Показать печатную накладную
     */
    public function actionView($id)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        try {
            // Получаем данные по накладной
            $sales = Sales::find()
                ->with([
                    'client',
                    'salesDetails' => function($query) {
                        $query->with('product')->andWhere(['deleted_at' => null]);
                    },
                    'bonus' => function($query) {
                        $query->with('product')->andWhere(['deleted_at' => null]);
                    }
                ])
                ->where(['id' => $id])
                ->one();

            if (!$sales) {
                return [
                    'status' => 'error',
                    'message' => 'Накладная не найдена'
                ];
            }

            // Определяем номер корешка: если уже есть - используем его, если нет - предлагаем следующий
            if (!empty($sales->print_number) && $sales->print_number !== '0') {
                // Используем уже сохраненный номер корешка
                $printNumber = $sales->print_number;
            } else {
                // Предлагаем следующий доступный номер корешка
                $printNumber = $this->getNextPrintNumber();
            }

            // Подготавливаем данные для отображения
            $printData = $this->preparePrintData($sales);

            return [
                'status' => 'success',
                'content' => $this->renderPartial('view', [
                    'sales' => $sales,
                    'printNumber' => $printNumber,
                    'printData' => $printData
                ])
            ];

        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Подготовить данные для печатной накладной
     */
    private function preparePrintData($sales)
    {
        // Статические цены (как на втором фото)
        $staticPrices = [
            '0.33' => [
                '1-narx' => 950,
                '2-narx' => 950,
                '3-narx' => 950,
                '4-narx' => 950,
                '5-narx' => 975
            ],
            '0.5 gaz' => [
                '1-narx' => 990,
                '2-narx' => 1100,
                '3-narx' => 1100,
                '4-narx' => 1100,
                '5-narx' => 1175
            ],
            '0.5 bezgaz' => [
                '1-narx' => 990,
                '2-narx' => 1100,
                '3-narx' => 1100,
                '4-narx' => 1100,
                '5-narx' => 1175
            ],
            '1 gaz' => [
                '1-narx' => 1350,
                '2-narx' => 1500,
                '3-narx' => 1500,
                '4-narx' => 1500,
                '5-narx' => 1575
            ],
            '1 bezgaz' => [
                '1-narx' => 1350,
                '2-narx' => 1500,
                '3-narx' => 1500,
                '4-narx' => 1500,
                '5-narx' => 1575
            ],
            '1.5 gaz' => [
                '1-narx' => 1665,
                '2-narx' => 1850,
                '3-narx' => 1850,
                '4-narx' => 1850,
                '5-narx' => 1925
            ],
            '1.5 bezgaz' => [
                '1-narx' => 1665,
                '2-narx' => 1850,
                '3-narx' => 1850,
                '4-narx' => 1850,
                '5-narx' => 1925
            ],
            '5' => [
                '1-narx' => 3500,
                '2-narx' => 3800,
                '3-narx' => 4000,
                '4-narx' => 3500,
                '5-narx' => 4400
            ],
            '10' => [
                '1-narx' => 5000,
                '2-narx' => 5500,
                '3-narx' => 6000,
                '4-narx' => 5000,
                '5-narx' => 6500
            ]
        ];

        $printItems = [];
        $bonusItems = [];
        $totalWeight = 0;

        // Обрабатываем обычные продукты
        foreach ($sales->salesDetails as $detail) {
            $productName = $detail->product->name;
            $quantity = $detail->quantity;
            $size = $detail->product->size ?? 1;
            $blocks = $size > 0 ? round($quantity / $size, 1) : 0;
            
            // Определяем тип продукта по названию
            $printType = $this->matchProductToPrintType($productName);
            
            if ($printType) {
                if (!isset($printItems[$printType])) {
                    $printItems[$printType] = [
                        'blocks' => 0,
                        'quantity' => 0,
                        'weight' => $this->getProductWeight($printType)
                    ];
                }
                
                $printItems[$printType]['blocks'] += $blocks;
                $printItems[$printType]['quantity'] += $quantity;
                $totalWeight += $quantity * $this->getProductWeight($printType);
            }
        }

        // Обрабатываем бонусные продукты отдельно
        foreach ($sales->bonus as $bonus) {
            $productName = $bonus->product->name;
            $quantity = $bonus->quantity;
            $size = $bonus->product->size ?? 1;
            $blocks = $size > 0 ? round($quantity / $size, 1) : 0;
            
            // Определяем тип продукта по названию
            $printType = $this->matchProductToPrintType($productName);
            
            if ($printType) {
                if (!isset($bonusItems[$printType])) {
                    $bonusItems[$printType] = [
                        'blocks' => 0,
                        'quantity' => 0,
                        'weight' => $this->getProductWeight($printType)
                    ];
                }
                
                $bonusItems[$printType]['blocks'] += $blocks;
                $bonusItems[$printType]['quantity'] += $quantity;
                $totalWeight += $quantity * $this->getProductWeight($printType);
            }
        }

        return [
            'items' => $printItems,
            'bonusItems' => $bonusItems,
            'totalWeight' => $totalWeight,
            'prices' => $staticPrices
        ];
    }

    /**
     * Сопоставление названия продукта с типом для печати
     */
    private function matchProductToPrintType($productName)
    {
        $productName = mb_strtolower(trim($productName));
        
        // Правила сопоставления
        if (strpos($productName, '0.33') !== false || strpos($productName, '0,33') !== false) {
            return '0.33';
        }
        
        // 0.5 литра - улучшенное распознавание для узбекских названий
        if (strpos($productName, '0.5') !== false || strpos($productName, '0,5') !== false) {
            // Проверяем на газированность - ищем явные указания на газированность
            if (strpos($productName, 'газланган') !== false || 
                strpos($productName, 'gazlangan') !== false ||
                strpos($productName, 'газли') !== false ||
                strpos($productName, 'gazli') !== false) {
                return '0.5 gaz';
            }
            // Проверяем на безгазовость
            else if (strpos($productName, 'безгаз') !== false || 
                     strpos($productName, 'bezgaz') !== false ||
                     strpos($productName, 'без газ') !== false) {
                return '0.5 bezgaz';
            }
            // По умолчанию считаем безгазовым если не указано обратное
            else {
                return '0.5 bezgaz';
            }
        }
        
        // 1.5 литра - улучшенное распознавание
        if (strpos($productName, '1.5') !== false || strpos($productName, '1,5') !== false || strpos($productName, '1.75') !== false) {
            // Проверяем на газированность - ищем явные указания на газированность
            if (strpos($productName, 'газланган') !== false || 
                strpos($productName, 'gazlangan') !== false ||
                strpos($productName, 'газли') !== false ||
                strpos($productName, 'gazli') !== false) {
                return '1.5 gaz';
            }
            // Проверяем на безгазовость
            else if (strpos($productName, 'безгаз') !== false || 
                     strpos($productName, 'bezgaz') !== false ||
                     strpos($productName, 'без газ') !== false) {
                return '1.5 bezgaz';
            }
            // По умолчанию считаем безгазовым если не указано обратное
            else {
                return '1.5 bezgaz';
            }
        }
        
        // 1 литр и Palmyra - улучшенное распознавание
        if (strpos($productName, '1.0') !== false || strpos($productName, '1,0') !== false || 
            (strpos($productName, '1 ') !== false && strpos($productName, '1.5') === false) ||
            strpos($productName, '1 litr') !== false ||
            (strpos($productName, 'palmyra') !== false && strpos($productName, '1.75') === false && strpos($productName, '55') === false)) {
            // Проверяем на газированность - ищем явные указания на газированность
            if (strpos($productName, 'газланган') !== false || 
                strpos($productName, 'gazlangan') !== false ||
                strpos($productName, 'газли') !== false ||
                strpos($productName, 'gazli') !== false) {
                return '1 gaz';
            }
            // Проверяем на безгазовость
            else if (strpos($productName, 'безгаз') !== false || 
                     strpos($productName, 'bezgaz') !== false ||
                     strpos($productName, 'без газ') !== false) {
                return '1 bezgaz';
            }
            // По умолчанию считаем безгазовым если не указано обратное
            else {
                return '1 bezgaz';
            }
        }
        
      
        // 5 литров
        if (strpos($productName, '5') !== false) {
            return '5';
        }
        
        // 10 литров
        if (strpos($productName, '10') !== false) {
            return '10';
        }
        
        return null;
    }

    /**
     * Получить вес продукта (в кг)
     */
    private function getProductWeight($printType)
    {
        $weights = [
            '0.33' => 0.33,
            '0.5 gaz' => 0.5,
            '0.5 bezgaz' => 0.5,
            '1 gaz' => 1.0,
            '1 bezgaz' => 1.0,
            '1.5 gaz' => 1.5,
            '1.5 bezgaz' => 1.5,
            '5' => 5.0,
            '10' => 10.0
        ];
        
        return $weights[$printType] ?? 0;
    }


    /**
     * Получает следующий доступный номер корешка
     * Ищет последнюю последовательность номеров за текущий день и предлагает следующий
     * @return int
     */
    private function getNextPrintNumber()
    {
        try {
            $today = date('Y-m-d');

            // Получаем все записи за сегодня с номерами корешков, отсортированные по времени создания
            $todayRecords = Sales::find()
                ->select(['print_number', 'created_at'])
                ->where(['not', ['print_number' => null]])
                ->andWhere(['not', ['print_number' => '']])
                ->andWhere(['not', ['print_number' => '0']])
                ->andWhere(['>=', 'created_at', $today . ' 00:00:00'])
                ->andWhere(['<=', 'created_at', $today . ' 23:59:59'])
                ->orderBy(['created_at' => SORT_DESC])
                ->asArray()
                ->all();

            if (empty($todayRecords)) {
                // Если за сегодня нет записей, ищем за предыдущие дни с умным анализом
                for ($i = 1; $i <= 7; $i++) {
                    $checkDate = date('Y-m-d', strtotime("-{$i} days"));
                    $dayRecords = Sales::find()
                        ->select(['print_number', 'created_at'])
                        ->where(['not', ['print_number' => null]])
                        ->andWhere(['not', ['print_number' => '']])
                        ->andWhere(['not', ['print_number' => '0']])
                        ->andWhere(['>=', 'created_at', $checkDate . ' 00:00:00'])
                        ->andWhere(['<=', 'created_at', $checkDate . ' 23:59:59'])
                        ->orderBy(['created_at' => SORT_DESC])
                        ->asArray()
                        ->all();

                    if (!empty($dayRecords)) {
                        // Применяем ту же логику анализа последовательности
                        return $this->analyzeSequenceAndGetNext($dayRecords);
                    }
                }
                return 1;
            }

            // Применяем анализ последовательности для записей за сегодня
            $suggestedNumber = $this->analyzeSequenceAndGetNext($todayRecords);

            // ВАЖНО: Проверяем, что предложенный номер действительно уникален
            // Если номер уже используется, ищем следующий свободный
            while (!$this->isPrintNumberUnique($suggestedNumber)) {
                $suggestedNumber++;
            }

            return $suggestedNumber;

        } catch (Exception $e) {
            // В случае ошибки возвращаем 1
            Yii::error('Ошибка при получении следующего номера корешка: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Анализирует последовательность номеров и возвращает следующий номер
     * @param array $records Записи с номерами корешков, отсортированные по времени создания (убывание)
     * @return int
     */
    private function analyzeSequenceAndGetNext($records)
    {
        if (empty($records)) {
            return 1;
        }

        // Анализируем последовательность номеров для определения текущей нумерации
        $numbers = array_map('intval', array_column($records, 'print_number'));
        $timestamps = array_column($records, 'created_at');

        // Определяем текущую последовательность нумерации
        // Ищем момент, когда началась новая нумерация
        $currentSequenceNumbers = [];

        // Ищем последнюю последовательность (от новых записей к старым)
        // Последовательность - это группа номеров, которые идут подряд по возрастанию
        for ($i = 0; $i < count($numbers); $i++) {
            $currentNumber = $numbers[$i];

            if ($i === 0) {
                // Первый номер всегда добавляем
                $currentSequenceNumbers[] = $currentNumber;
            } else {
                $previousNumber = $numbers[$i - 1];

                // Если текущий номер = предыдущий - 1, то это продолжение последовательности
                // Например: 3, 2, 1 - это последовательность
                if ($currentNumber == $previousNumber - 1) {
                    $currentSequenceNumbers[] = $currentNumber;
                } else {
                    // Если номер не следующий в последовательности, прерываем
                    break;
                }
            }
        }

        if (!empty($currentSequenceNumbers)) {
            $maxInCurrentSequence = max($currentSequenceNumbers);
            $result = $maxInCurrentSequence + 1;

            // Логируем для отладки
            Yii::error("analyzeSequenceAndGetNext: текущая последовательность " . implode(',', $currentSequenceNumbers) . ", максимум: $maxInCurrentSequence, результат: $result", 'print-invoice-debug');

            return $result;
        }

        // Если не удалось определить последовательность, берем максимальный номер
        $maxNumber = max($numbers);
        $result = $maxNumber + 1;

        // Логируем для отладки
        Yii::error("analyzeSequenceAndGetNext: не удалось определить последовательность, максимальный номер: $maxNumber, результат: $result", 'print-invoice-debug');

        return $result;
    }

    /**
     * Проверяет, уникален ли номер корешка в рамках текущей последовательности
     * @param string $printNumber
     * @param int|null $excludeId ID записи, которую нужно исключить из проверки
     * @return bool
     */
    private function isPrintNumberUnique($printNumber, $excludeId = null)
    {
        try {
            $today = date('Y-m-d');

            // Логируем начало проверки
            $logMessage = "\n=== ПРОВЕРКА УНИКАЛЬНОСТИ НОМЕРА $printNumber (excludeId: $excludeId) ===\n";
            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

            // Получаем все записи за сегодня
            $todayRecords = Sales::find()
                ->select(['print_number', 'created_at', 'id'])
                ->where(['not', ['print_number' => null]])
                ->andWhere(['not', ['print_number' => '']])
                ->andWhere(['not', ['print_number' => '0']])
                ->andWhere(['>=', 'created_at', $today . ' 00:00:00'])
                ->andWhere(['<=', 'created_at', $today . ' 23:59:59'])
                ->orderBy(['created_at' => SORT_DESC])
                ->asArray()
                ->all();

            // Логируем найденные записи
            $logMessage = "Найдено записей за сегодня: " . count($todayRecords) . "\n";
            foreach ($todayRecords as $record) {
                $logMessage .= "  ID: {$record['id']}, Номер: {$record['print_number']}, Время: {$record['created_at']}\n";
            }
            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

            if (empty($todayRecords)) {
                file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                    date('Y-m-d H:i:s') . " Нет записей за сегодня - номер уникален\n", FILE_APPEND);
                return true; // Если нет записей, номер уникален
            }

            // Определяем текущую последовательность (как в analyzeSequenceAndGetNext)
            $numbers = array_map('intval', array_column($todayRecords, 'print_number'));
            $currentSequenceNumbers = [];

            $logMessage = "Анализ последовательности. Номера по времени: " . implode(', ', $numbers) . "\n";
            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

            for ($i = 0; $i < count($numbers); $i++) {
                $currentNumber = $numbers[$i];

                if ($i === 0) {
                    $currentSequenceNumbers[] = $currentNumber;
                    $logMessage = "Шаг $i: Добавляем первый номер $currentNumber в последовательность\n";
                } else {
                    $previousNumber = $numbers[$i - 1];

                    if ($currentNumber == $previousNumber - 1) {
                        $currentSequenceNumbers[] = $currentNumber;
                        $logMessage = "Шаг $i: Номер $currentNumber = $previousNumber - 1, добавляем в последовательность\n";
                    } else {
                        $logMessage = "Шаг $i: Номер $currentNumber != $previousNumber - 1, прерываем последовательность\n";
                        file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                            date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);
                        break;
                    }
                }
                file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                    date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);
            }

            $logMessage = "Текущая последовательность: [" . implode(', ', $currentSequenceNumbers) . "]\n";
            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

            // Проверяем, есть ли номер в текущей последовательности
            $printNumberInt = intval($printNumber);
            $isInCurrentSequence = in_array($printNumberInt, $currentSequenceNumbers);

            $logMessage = "Проверяемый номер: $printNumberInt\n";
            $logMessage .= "Входит в текущую последовательность: " . ($isInCurrentSequence ? 'ДА' : 'НЕТ') . "\n";
            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

            // Если номер есть в текущей последовательности, проверяем excludeId
            if ($isInCurrentSequence && $excludeId !== null) {
                $logMessage = "Номер в последовательности, проверяем excludeId: $excludeId\n";
                file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                    date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

                // Находим запись с этим номером в текущей последовательности
                foreach ($todayRecords as $record) {
                    if (intval($record['print_number']) == $printNumberInt &&
                        in_array(intval($record['print_number']), $currentSequenceNumbers)) {
                        $logMessage = "Найдена запись с номером $printNumberInt, ID: {$record['id']}\n";
                        file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                            date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

                        // Если это та же запись, которую исключаем, то номер уникален
                        if ($record['id'] == $excludeId) {
                            $logMessage = "ID совпадает с excludeId - номер уникален\n";
                            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);
                            return true;
                        }
                    }
                }
            }

            // Номер уникален, если его нет в текущей последовательности
            $result = !$isInCurrentSequence;
            $logMessage = "РЕЗУЛЬТАТ: номер " . ($result ? 'УНИКАЛЕН' : 'НЕ УНИКАЛЕН') . "\n";
            $logMessage .= "=== КОНЕЦ ПРОВЕРКИ ===\n\n";
            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

            return $result;

        } catch (Exception $e) {
            // В случае ошибки считаем номер не уникальным для безопасности
            Yii::error('Ошибка при проверке уникальности номера корешка: ' . $e->getMessage());
            return false;
        }
    }

    public function actionSavePrintNumber()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $id = Yii::$app->request->post('id');
        $printNumber = Yii::$app->request->post('print_number');

        // Логируем начало сохранения
        $logMessage = "\n### СОХРАНЕНИЕ НОМЕРА КОРЕШКА ###\n";
        $logMessage .= "ID накладной: $id\n";
        $logMessage .= "Запрашиваемый номер: $printNumber\n";
        file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
            date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

        // Проверяем, что номер корешка не пустой
        if (empty($printNumber) || $printNumber == '0' || trim($printNumber) === '') {
            $logMessage = "ОШИБКА: Номер корешка пустой\n";
            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);
            return [
                'success' => false,
                'message' => 'Номер корешка не может быть пустым'
            ];
        }

        // Проверяем уникальность номера корешка за текущий день
        $logMessage = "Проверяем уникальность номера $printNumber...\n";
        file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
            date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

        $isUnique = $this->isPrintNumberUnique($printNumber, $id);

        $logMessage = "Результат проверки уникальности: " . ($isUnique ? 'УНИКАЛЕН' : 'НЕ УНИКАЛЕН') . "\n";
        file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
            date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

        if (!$isUnique) {
            $nextNumber = $this->getNextPrintNumber();

            $logMessage = "Номер $printNumber не уникален. Предлагаемый номер: $nextNumber\n";
            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

            return [
                'success' => false,
                'message' => "Номер корешка {$printNumber} уже используется сегодня. Предлагаемый номер: {$nextNumber}",
                'suggested_number' => $nextNumber
            ];
        }

        $sales = Sales::findOne($id);
        if (!$sales) {
            $logMessage = "ОШИБКА: Накладная с ID $id не найдена\n";
            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);
            return [
                'success' => false,
                'message' => 'Накладная не найдена'
            ];
        }

        $logMessage = "Сохраняем номер $printNumber для накладной ID $id\n";
        file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
            date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);

        $sales->print_number = $printNumber;
        if ($sales->save(false)) {
            $logMessage = "УСПЕХ: Номер корешка $printNumber сохранен для накладной ID $id\n";
            $logMessage .= "### КОНЕЦ СОХРАНЕНИЯ ###\n\n";
            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);
            return [
                'success' => true,
                'message' => 'Номер корешка сохранен'
            ];
        } else {
            $logMessage = "ОШИБКА: Не удалось сохранить номер корешка $printNumber\n";
            file_put_contents(Yii::getAlias('@runtime/print_debug.log'),
                date('Y-m-d H:i:s') . " " . $logMessage, FILE_APPEND);
            return [
                'success' => false,
                'message' => 'Ошибка при сохранении номера корешка'
            ];
        }
    }

    
} 