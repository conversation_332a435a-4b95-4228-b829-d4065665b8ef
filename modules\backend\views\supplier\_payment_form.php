<?php
use app\assets\Select2Asset;
use app\common\models\Cashbox;
use app\common\models\CurrencyCourse;
use yii\bootstrap5\Html;

Select2Asset::register($this);
?>

<div class="supplier-payment-form">
    <form id="supplier-pay-form">
        <input type="hidden" name="SupplierPayForm[supplier_id]" value="<?= $model->supplier_id ?>">

        <?php if ($supplier->currency_id == 1): ?>
            <div class="form-group">
                <label for="currency"><?= Yii::t('app', 'currency') ?></label>
                <input type="text" id="currency" name="SupplierPayForm[currency]" class="form-control" value="<?= Html::encode($currencyName) ?>" disabled>
            </div>

            <div class="form-group">
                <label for="course"><?= Yii::t('app', 'course') ?></label>
                <input type="text" id="course" name="SupplierPayForm[course]" class="form-control formatted-numeric-input" value="<?= $courseModel->course ?>">
                <div class="error-container" id="course-error"></div>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="cashbox_id"><?= Yii::t('app', 'cashbox') ?></label>
                    <select id="cashbox_id" name="SupplierPayForm[cashbox_id]" class="form-control select2" required>
                        <option value=""><?= Yii::t('app', 'select') ?></option>
                        <?php
                        $cashboxes = Cashbox::find()->where(['deleted_at' => null])->all();
                        foreach ($cashboxes as $cashbox): ?>
                            <option value="<?= $cashbox->id ?>"
                                    data-balance="<?= $cashbox->balance ?>">
                                <?= $cashbox->title ?> (<?= $cashbox->getPaymentTypesString() ?>) - Баланс: <?= number_format($cashbox->balance, 2) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="error-container" id="cashbox_id-error"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="type"><?= Yii::t('app', 'payment type') ?></label>
                    <select id="type" name="SupplierPayForm[type]" class="form-control select2" required>
                        <option value=""><?= Yii::t('app', 'select_type') ?></option>
                        <!-- Опции будут загружены динамически через JavaScript -->
                    </select>
                    <div class="error-container" id="type-error"></div>
                </div>
            </div>
        </div>

        <!-- Информация о платеже -->
        <div id="supplier-payment-info" class="row mb-3" style="display: none;">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> <?= Yii::t('app', 'payment_info') ?></h6>
                    <div id="supplier-payment-details"></div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="summa"><?= Yii::t('app', 'amount') ?></label>
            <input type="text" id="summa" name="SupplierPayForm[amount]" class="form-control formatted-numeric-input" step="0.01" min="0" required>
            <div class="error-container" id="amount-error"></div>
        </div>

    </form>
    <div id="form-error-message" style="display: none;"></div>
</div>

<script>
// Получаем курсы валют для JavaScript
<?php
$courses = [];
$allCourses = CurrencyCourse::find()
    ->where(['<=', 'start_date', date('Y-m-d')])
    ->andWhere([
        'or',
        ['>', 'end_date', date('Y-m-d')],
        ['end_date' => null]
    ])
    ->andWhere(['deleted_at' => null])
    ->all();

foreach ($allCourses as $course) {
    $courses[$course->currency_id] = $course->course;
}

// Убеждаемся что курс базовой валюты (доллар) присутствует
if (!isset($courses[1])) {
    $courses[1] = 1; // Доллар - базовая валюта
}
?>
window.supplierCurrencyCourses = <?= json_encode($courses) ?>;
window.supplierCurrencyId = <?= $supplier->currency_id ?>;

$(document).ready(function() {
    $('.select2').select2({
        width: '100%',
        placeholder: '<?= Yii::t("app", "select") ?>',
        allowClear: true,
        dropdownParent: $('#ideal-mini-modal'),
        language: {
            noResults: function() {
                return "<?= Yii::t('app', 'Nothing found') ?>";
            },
            searching: function() {
                return "<?= Yii::t('app', 'Searching...') ?>";
            }
        },
        minimumInputLength: 0,
        minimumResultsForSearch: 1
    });

    // Инициализация форматирования числовых полей
    $('.formatted-numeric-input').on('input', function() {
        var value = $(this).val().replace(/\s+/g, '');
        var formattedValue = value.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
        $(this).val(formattedValue);
    });

    // Функция для загрузки доступных типов платежей по выбранной кассе
    function loadPaymentTypesByCashbox(cashboxId) {
        if (!cashboxId) {
            $('#type').empty().append('<option value=""><?= Yii::t("app", "select_type") ?></option>');
            return;
        }

        $.ajax({
            url: '<?= \yii\helpers\Url::to(['/backend/expenses/get-payment-types-by-cashbox']) ?>',
            type: 'GET',
            data: { cashbox_id: cashboxId },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    var paymentTypeSelect = $('#type');
                    paymentTypeSelect.empty();
                    paymentTypeSelect.append('<option value=""><?= Yii::t("app", "select_type") ?></option>');

                    $.each(response.payment_types, function(index, paymentType) {
                        paymentTypeSelect.append(
                            '<option value="' + paymentType.type + '">' + paymentType.label + '</option>'
                        );
                    });

                    // Обновляем select2
                    paymentTypeSelect.trigger('change');
                } else {
                    console.error('Ошибка загрузки типов платежей:', response.message);
                    $('#type').empty().append('<option value=""><?= Yii::t("app", "select_type") ?></option>');
                }
            },
            error: function() {
                console.error('Ошибка AJAX запроса для загрузки типов платежей');
                $('#type').empty().append('<option value=""><?= Yii::t("app", "select_type") ?></option>');
            }
        });
    }

    // Обработчик изменения кассы
    $('#cashbox_id').on('change', function() {
        var cashboxId = $(this).val();
        loadPaymentTypesByCashbox(cashboxId);
        updateSupplierPaymentInfo();
    });

    function updateSupplierPaymentInfo() {
        var cashboxSelect = $('#cashbox_id');
        var amountInput = $('#summa');

        var cashboxBalance = cashboxSelect.find(':selected').data('balance');

        var amountValue = amountInput.val() || '';
        var cleanAmount = amountValue.replace(/[\s\u00A0]/g, '').replace(',', '.');
        var amount = parseFloat(cleanAmount) || 0;

        if (!cashboxSelect.val() || amount <= 0) {
            $('#supplier-payment-info').hide();
            return;
        }

        // Для поставщиков в долларах нужно конвертировать в сомы
        var amountInSom = amount;
        if (window.supplierCurrencyId == 1) {
            var course = parseFloat($('#course').val()) || 1;
            amountInSom = amount * course;
        }

        var details = `
            <p><strong>Оплата поставщику:</strong> ${formatNumber(amount)} ${window.supplierCurrencyId == 1 ? 'USD' : 'UZS'}</p>
            ${window.supplierCurrencyId == 1 ? `<p><strong>Будет списано с кассы:</strong> ${formatNumber(amountInSom)} UZS (курс: ${$('#course').val()})</p>` : `<p><strong>Будет списано с кассы:</strong> ${formatNumber(amountInSom)} UZS</p>`}
            <p><strong>Баланс кассы:</strong> ${formatNumber(cashboxBalance)} UZS</p>
            ${cashboxBalance < amountInSom ? '<p class="text-danger"><strong>Недостаточно средств в кассе!</strong></p>' : ''}
        `;

        $('#supplier-payment-details').html(details);
        $('#supplier-payment-info').show();
    }
    
    function formatNumber(num) {
        return Number(num).toLocaleString('ru-RU', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    // Обновляем информацию при изменении полей
    $('#summa, #course').on('change keyup', updateSupplierPaymentInfo);
});
</script>