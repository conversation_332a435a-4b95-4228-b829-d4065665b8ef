<?php

namespace app\modules\backend\controllers;

use app\common\models\CashboxDetail;
use app\modules\backend\models\SupplierPayForm;
use Yii;
use yii\web\Response;
use app\common\models\Supplier;
use app\common\models\SupplierBalance;
use app\common\models\SupplierPayments;
use app\common\models\SupplierBalanceHistory;
use yii\web\NotFoundHttpException;
use app\common\models\Invoice;
use app\modules\backend\models\Expenses;
use app\modules\backend\models\ExpensesType;
use app\common\models\Tracking;
use app\common\models\Cashbox;
use app\common\models\Currency;
use app\common\models\CurrencyCourse;


class SupplierController extends BaseController
{
    public function actionIndex()
    {
        $query = Supplier::find()
            ->select([
                'supplier.*',
                'COALESCE(supplier_balance.amount, 0) as balance',
                'currency.name as currency_name'
            ])
            ->leftJoin('supplier_balance', 'supplier.id = supplier_balance.supplier_id')
            ->leftJoin('currency', 'supplier.currency_id = currency.id')
            ->orderBy(['supplier.id' => SORT_DESC]);

        $result = $query->asArray()->all();

        return $this->render('index', [
            'result' => $result
        ]);
    }

    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
       
        if (Yii::$app->request->isPost) {
            $model = new Supplier();
            if ($model->load(Yii::$app->request->post()) && $model->validate()) {
               
                if (!empty($model->phone_number)) {
                    $model->phone_number = str_replace(' ', '', $model->phone_number);
                    if (!str_starts_with($model->phone_number, '+998')) {
                        $model->phone_number = '+998' . $model->phone_number;
                    }
                }
                
                if (!empty($model->phone_number_2)) {
                    $model->phone_number_2 = str_replace(' ', '', $model->phone_number_2);
                    if (!str_starts_with($model->phone_number_2, '+998')) {
                        $model->phone_number_2 = '+998' . $model->phone_number_2;
                    }
                }
                $model->created_at = date('Y-m-d H:i:s');
                if(!$model->save(false)) {
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors()
                    ];
                }

                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'record_successfully_created')
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $model = new Supplier();
            return [
                'status' => 'success',
                'content' => $this->renderPartial('create', [
                    'model' => $model
                ])
            ];
        }
    }

    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        if (Yii::$app->request->isPost) {
            $id = Yii::$app->request->post('Supplier')['id'];
            $model = $this->findModel($id);
            
            if ($model->load(Yii::$app->request->post()) && $model->validate()) {
                if (!empty($model->phone_number)) {
                    $model->phone_number = str_replace(' ', '', $model->phone_number);
                    if (!str_starts_with($model->phone_number, '+998')) {
                        $model->phone_number = '+998' . $model->phone_number;
                    }
                }
                
                if (!empty($model->phone_number_2)) {
                    $model->phone_number_2 = str_replace(' ', '', $model->phone_number_2);
                    if (!str_starts_with($model->phone_number_2, '+998')) {
                        $model->phone_number_2 = '+998' . $model->phone_number_2;
                    }
                }
                if(!$model->save(false)) {
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors()
                    ];
                }
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'record_successfully_updated')
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = $this->findModel($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'record_not_found')
                ];
            }

            if (str_starts_with($model->phone_number, '+998')) {
                $model->phone_number = substr($model->phone_number, 4);
            }

            if (str_starts_with($model->phone_number_2, '+998')) {
                $model->phone_number_2 = substr($model->phone_number_2, 4);
            }

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model
                ])
            ];
        }
    }


    public function actionView()
    {
        $id = Yii::$app->request->get('id');
        if (Yii::$app->request->isGet) {
            
            $model = Supplier::find()
                ->select([
                    'supplier.*',
                    'COALESCE(supplier_balance.amount, 0) as balance',
                    'currency.name as currency_name'
                ])
                ->leftJoin('supplier_balance', 'supplier.id = supplier_balance.supplier_id')
                ->leftJoin('currency', 'supplier.currency_id = currency.id')
                ->where(['supplier.id' => $id])
                ->asArray()
                ->one();

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'record_not_found')
                ];
            }

            // Get supplier payments with user info
            $payments = SupplierPayments::find()
                ->select([
                    'supplier_payments.*',
                    'users.full_name as user_name',
                    'currency.name as currency_name'
                ])
                ->leftJoin('users', 'supplier_payments.add_user_id = users.id')
                ->leftJoin('supplier', 'supplier_payments.supplier_id = supplier.id')
                ->leftJoin('currency', 'supplier.currency_id = currency.id')
                ->where(['supplier_payments.supplier_id' => $id])
                ->orderBy(['supplier_payments.created_at' => SORT_DESC])
                ->asArray()
                ->all();


            // Get balance history
            $balanceHistory = SupplierBalanceHistory::find()
                ->select([
                    'supplier_balance_history.*',
                    'currency.name as currency_name'
                ])
                ->leftJoin('supplier', 'supplier_balance_history.supplier_id = supplier.id')
                ->leftJoin('currency', 'supplier.currency_id = currency.id')
                ->where(['supplier_balance_history.supplier_id' => $id])
                ->orderBy(['supplier_balance_history.created_at' => SORT_DESC])
                ->asArray()
                ->all();

            // Get related invoices if they exist
            $invoices = Invoice::find()
                ->select([
                    'invoice.*',
                    'users.username as accepted_by'
                ])
                ->leftJoin('users', 'invoice.accept_user_id = users.id')
                ->where(['invoice.supplier_id' => $id])
                ->orderBy(['invoice.created_at' => SORT_DESC])
                ->asArray()
                ->all();

                // pre($payments);
            return $this->render('view', [
                    'model' => $model,
                    'payments' => $payments,
                    'balanceHistory' => $balanceHistory,
                    'invoices' => $invoices
            ]);
            
        
        }
    }

    protected function findModel($id)
    {
        if (($model = Supplier::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }



    public function actionPay()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
    
        if (Yii::$app->request->isPost) {
            $model = new SupplierPayForm();
            $data = Yii::$app->request->post();
    
            $model->load($data, 'SupplierPayForm');
            if ($model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $supplier = Supplier::findOne($model->supplier_id);
                    if (!$supplier || !$supplier->currency_id) {
                        throw new \Exception('Поставщик или валюта не найдены');
                    }
    
                    // Определяем сумму в сомах
                    $amountInSom = $model->amount;
                    if ($supplier->currency_id == 1) { 
                        if (empty($model->course)) {
                            throw new \Exception('Не указан курс валюты');
                        }

                        $currentCourse = CurrencyCourse::find()
                            ->where(['currency_id' => 2])
                            ->andWhere(['<=', 'start_date', date('Y-m-d')])
                            ->andWhere(['>', 'end_date', date('Y-m-d')])
                            ->andWhere(['deleted_at' => null])
                            ->orderBy(['start_date' => SORT_DESC])
                            ->one();

                        if (!$currentCourse || $currentCourse->course != $model->course) {
                            if ($currentCourse) {
                                $currentCourse->deleted_at = date('Y-m-d');
                                $currentCourse->end_date = date('Y-m-d');
                                if (!$currentCourse->save()) {
                                    throw new \Exception('Ошибка при обновлении старого курса');
                                }
                            }

                            $newCourse = new CurrencyCourse();
                            $newCourse->currency_id = 2;
                            $newCourse->course = $model->course;
                            $newCourse->start_date = date('Y-m-d');
                            $newCourse->end_date = '9999-12-31';
                            $newCourse->created_at = date('Y-m-d H:i:s');
                            
                            if (!$newCourse->save()) {
                                throw new \Exception('Ошибка при создании нового курса');
                            }
                        }

                        $amountInSom = $model->amount * $model->course;
                    }
    
                    $expense = new Expenses();
                    $expense->expense_type_id = ExpensesType::find()->where(['name' => 'Taminotchi uchun'])->one()->id;
                    $expense->add_user_id = Yii::$app->user->id;
                    $expense->payment_type = $model->type;
                    $expense->cashbox_id = $model->cashbox_id;
                    $expense->description = 'Тўлов: ' . $model->supplier_id;
                    $expense->summa = $amountInSom;
                    $expense->status = Expenses::TYPE_ACCEPTED;
                    $expense->created_at = date('Y-m-d H:i:s');
                    
                    if (!$expense->save()) {
                        throw new \Exception('Ошибка при сохранении расхода: ' . json_encode($expense->getErrors(), JSON_UNESCAPED_UNICODE));
                    }
    
                    $payment = new SupplierPayments();
                    $payment->supplier_id = $model->supplier_id;
                    $payment->amount = $model->amount;
                    $payment->type = $model->type;
                    $payment->created_at = date('Y-m-d H:i:s');
                    $payment->add_user_id = Yii::$app->user->id;
                    $payment->expense_id = $expense->id;
                    
                    if (!$payment->save()) {
                        throw new \Exception('Ошибка при сохранении платежа: ' . json_encode($payment->getErrors(), JSON_UNESCAPED_UNICODE));
                    }
    
                    $supplierBalance = SupplierBalance::findOne(['supplier_id' => $model->supplier_id]);
                    if (!$supplierBalance) {
                        $supplierBalance = new SupplierBalance();
                        $supplierBalance->supplier_id = $model->supplier_id;
                        $supplierBalance->amount = 0;
                    }
                    
                    $oldAmount = $supplierBalance->amount;
                    $supplierBalance->amount += $model->amount;
                    
                    if (!$supplierBalance->save()) {
                        throw new \Exception('Ошибка при обновлении баланса поставщика');
                    }
    
                    $balanceHistory = new SupplierBalanceHistory();
                    $balanceHistory->supplier_id = $model->supplier_id;
                    $balanceHistory->amount = $supplierBalance->amount;
                    $balanceHistory->old_amount = $oldAmount;
                    $balanceHistory->type = $model->type;
                    
                    if (!$balanceHistory->save()) {
                        throw new \Exception('Ошибка при сохранении истории баланса');
                    }
    
                    $tracking = new Tracking();
                    $tracking->progress_type = Tracking::PAY_FOR_SUPPLIER;
                    $tracking->process_id = $supplierBalance->id;
                    $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                    $tracking->created_at = date('Y-m-d H:i:s');
                    
                    if (!$tracking->save()) {
                        throw new \Exception('Ошибка при сохранении отслеживания');
                    }
    
                                                    // Получаем выбранную кассу
                    $cashbox = Cashbox::findOne($model->cashbox_id);
                    if (!$cashbox) {
                        throw new \Exception('Касса не найдена');
                    }

                    // Проверяем достаточность средств в кассе (используем сумму в сомах)
                    if ($cashbox->balance < $amountInSom) {
                        throw new \Exception('Недостаточно средств в кассе');
                    }

                    // Создаем запись в cashbox_detail
                    $cashboxDetail = new CashboxDetail();
                    $cashboxDetail->cashbox_id = $cashbox->id;
                    $cashboxDetail->add_user_id = Yii::$app->user->id;
                    $cashboxDetail->type = $model->type; // Используем тип платежа из модели
                    $cashboxDetail->amount = $amountInSom; // Используем сумму в сомах
                    $cashboxDetail->created_at = date('Y-m-d H:i:s');

                    if (!$cashboxDetail->save()) {
                        throw new \Exception('Ошибка при сохранении деталей кассы');
                    }

                    // Обновляем баланс кассы
                    $cashbox->balance -= $amountInSom;
                    if (!$cashbox->save()) {
                        throw new \Exception('Ошибка при обновлении баланса кассы');
                    }
    
                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => 'Платеж успешно создан'
                    ];
    
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    Yii::error($e->getMessage());
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ];
                }
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = new SupplierPayForm();
            $supplier = Supplier::findOne($id);
            if (!$supplier) {
                return [
                    'status' => 'error',
                    'message' => 'Поставщик не найден'
                ];
            }
    
            $currencyName = Currency::findOne($supplier->currency_id)->name;
            $model->supplier_id = $id;
    
            $courseModel = null;
            if ($supplier->currency_id == 1) {
                $courseModel = CurrencyCourse::find()
                    ->where(['currency_id' => 2])
                    ->andWhere(['<=', 'start_date', date('Y-m-d')])
                    ->andWhere(['>', 'end_date', date('Y-m-d')])
                    ->andWhere(['deleted_at' => null])
                    ->orderBy(['start_date' => SORT_DESC])
                    ->one();

                    if(!$courseModel){
                        return [
                            'status' => 'error',
                            'message' => Yii::t('app', 'course_not_found')
                        ];
                   }
            }

           
            
            return [
                'status' => 'success',
                'content' => $this->renderPartial('_payment_form', [
                    'model' => $model,
                    'currencyName' => $currencyName,
                    'courseModel' => $courseModel,
                    'supplier' => $supplier
                ])
            ];
        }
        
        return [
            'status' => 'error',
            'message' => 'Неверный запрос'
        ];
    }

    

    
}
