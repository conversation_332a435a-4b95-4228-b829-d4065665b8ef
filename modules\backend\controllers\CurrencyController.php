<?php

namespace app\modules\backend\controllers;

use app\common\models\Currency;
use app\common\models\CurrencyCourse;
use Yii;
use yii\data\SqlDataProvider;
use yii\web\Response;

class CurrencyController extends BaseController
{


    public function actionView(): string
    {
        $sql = "
        SELECT c.id, c.name, c.is_main, c.created_at, c.deleted_at, cc.id AS course_id, cc.course, cc.start_date
        FROM currency c
        LEFT JOIN LATERAL (
            SELECT id, course, start_date
            FROM currency_course
            WHERE currency_id = c.id 
            AND deleted_at IS NULL 
            AND start_date <= CURRENT_DATE
            AND end_date > CURRENT_DATE
            ORDER BY start_date DESC, id DESC
            LIMIT 1
        ) cc ON TRUE
        ORDER BY c.created_at DESC
    ";

        $dataProvider = new SqlDataProvider([
            'sql' => $sql,
            'pagination' => [
                'pageSize' => 50,
            ],
        ]);

        return $this->render('view', [
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionCourses($id)
    {
        $currency = Currency::findOne($id);

        if (!$currency) {
            return [
                'status' => 'fail',
                'message' => 'Currency not found.',
            ];
        }

        $courses = CurrencyCourse::find()
            ->where(['currency_id' => $currency->id])
            ->orderBy(['id' => SORT_DESC])
            ->all();


        return $this->render('courses', [
            'courses' => $courses,
            'currency' => $currency,
        ]);
    }

    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        if(Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $id = $data['CurrencyCourse']['id'];
            $model = CurrencyCourse::findOne($id);
            if (!$model) {
                return [
                    'status' => 'fail',
                    'message' => 'CurrencyCourse not found.',
                ];
            }

            $currentStartDate = $data['CurrencyCourse']['start_date'];
            $today = new \DateTime('today');
            
            if ($currentStartDate < $today->format('Y-m-d')) {
                return [
                    'status' => 'fail',
                    'message' => Yii::t('app', 'Cannot set course for past date'),
                ];
            }

            $existingCourse = CurrencyCourse::find()
                ->where(['currency_id' => $model->currency_id])
                ->andWhere(['start_date' => $currentStartDate])
                ->andWhere(['end_date' => '9999-12-31'])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['!=', 'id', $id])
                ->one();

            if ($existingCourse) {
                return [
                    'status' => 'fail',
                    'message' => Yii::t('app', 'Course for this date already exists'),
                ];
            }

            $linkedCurrencies = ['Сўм'];
            $currency = Currency::findOne($model->currency_id);
            
            if (in_array($currency->name, $linkedCurrencies)) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    foreach ($linkedCurrencies as $currencyName) {
                        $linkedCurrency = Currency::findOne(['name' => $currencyName]);
                        if ($linkedCurrency) {
                            $currentCourse = CurrencyCourse::find()
                                ->where(['currency_id' => $linkedCurrency->id])
                                ->andWhere(['<=', 'start_date', date('Y-m-d')])
                                ->andWhere(['>', 'end_date', date('Y-m-d')])
                                ->andWhere(['deleted_at' => null])
                                ->orderBy(['start_date' => SORT_DESC])
                                ->one();
            
                            if ($currentCourse) {
                                $currentCourse->end_date = $currentStartDate; 
                                if (!$currentCourse->save(false)) {
                                    throw new \Exception('Ошибка при обновлении текущего курса: ' . json_encode($currentCourse->errors));
                                }
            
                                $newCourse = new CurrencyCourse();
                                $newCourse->currency_id = $linkedCurrency->id;
                                $newCourse->course = $data['CurrencyCourse']['course'];
                                $newCourse->start_date = $currentStartDate;
                                $newCourse->created_at = date('Y-m-d H:i:s');
                                $newCourse->end_date = '9999-12-31';
                                if (!$newCourse->save()) {
                                    throw new \Exception('Ошибка при создании нового курса: ' . json_encode($newCourse->errors));
                                }
                            }
                        }
                    }
                    $transaction->commit();
                    Yii::$app->response->format = Response::FORMAT_JSON;
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'The rate has been successfully updated for all related currencies.'),
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'fail',
                        'message' => 'An error occurred while updating the rate: ' . $e->getMessage(),
                    ];
                }
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Currency::findOne($id);
            if (!$model) {
                return [
                    'status' => 'fail',
                    'message' => 'Currency not found.',
                ];
            }
            
            // Получаем активный курс
            $courseModel = CurrencyCourse::find()
                ->where(['currency_id' => $model->id])
                ->andWhere(['<=', 'start_date', date('Y-m-d')])
                ->andWhere(['>', 'end_date', date('Y-m-d')])
                ->andWhere(['deleted_at' => null])
                ->orderBy(['start_date' => SORT_DESC])
                ->one();

            if (!$courseModel) {
                return [
                    'status' => 'fail',
                    'message' => 'Currency course not found.',
                ];
            }

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model,
                    'courseModel' => $courseModel,
                ]),
            ];
        }
    }


}