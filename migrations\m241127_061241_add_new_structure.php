<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%supplier}}`.
 */
class m241127_061241_add_new_structure extends Migration
{
  
    public function safeUp()
    {


        // Table: users
        $this->createTable('users', [
            'id' => $this->bigPrimaryKey(),
            'username' => $this->string(255)->notNull()->unique(),
            'full_name' => $this->string(255),
            'role' => $this->integer()->notNull(),
            'access_token' => $this->string(255)->unique()->null(),
            'password' => $this->string(255)->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $auth = Yii::$app->authManager;

        $adminRole = $auth->createRole('admin');
        $adminRole->description = 'Administrator';
        $auth->add($adminRole);

        $rawKeeperRole = $auth->createRole('raw_keeper');
        $rawKeeperRole->description = 'Raw keeper';
        $auth->add($rawKeeperRole);

        $manufacturerRole = $auth->createRole('manufacturer');
        $manufacturerRole->description = 'Manufacturer';
        $auth->add($manufacturerRole);

        $productKeeperRole = $auth->createRole('product_keeper');
        $productKeeperRole->description = 'Product keeper';
        $auth->add($productKeeperRole);

        $salesRole = $auth->createRole('sales');
        $salesRole->description = 'Sales';
        $auth->add($salesRole);

        $securityRole = $auth->createRole('security');
        $securityRole->description = 'Security';
        $auth->add($securityRole);



        $this->insert('users', [
            'username' => 'admin',
            'role' => 1,
            'full_name' => 'Administrator',
            'password' => Yii::$app->security->generatePasswordHash('admin123'),
            'created_at' => new \yii\db\Expression('NOW()'),
        ]);

        $adminUserId = $this->db->getLastInsertID();
        $auth->assign($adminRole, $adminUserId);



        $this->createTable('{{%currency}}', [
            'id' => $this->bigPrimaryKey(),
            'name' => $this->string(255),
            'is_main' => $this->boolean()->defaultValue(false),
            'end_date' => $this->date(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null()
        ]);

        $this->createTable('{{%currency_course}}', [
            'id' => $this->bigPrimaryKey(),
            'currency_id' => $this->bigInteger(),
            'course' => $this->double(),
            'start_date' => $this->date(),
            'end_date' => $this->date(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null()
        ]);

        $this->addForeignKey('fk-currency-course-currency', '{{%currency_course}}', 'currency_id', '{{%currency}}', 'id', 'CASCADE', 'CASCADE');

        $this->insert('{{%currency}}', [
            'name' => 'Dollar',
            'is_main' => true,
            'created_at' => new \yii\db\Expression('CURRENT_TIMESTAMP'),
            'end_date' => '9999-12-31',
        ]);

        $dollarId = $this->db->getLastInsertID();

        $this->insert('{{%currency_course}}', [
            'currency_id' => $dollarId,
            'course' => 1.0,
            'start_date' => new \yii\db\Expression('CURRENT_DATE'),
            'created_at' => new \yii\db\Expression('CURRENT_TIMESTAMP'),
        ]);

        // Добавляем So'm
        $this->insert('{{%currency}}', [
            'name' => 'Сўм',
            'is_main' => false,
            'created_at' => new \yii\db\Expression('CURRENT_TIMESTAMP'),
            'end_date' => '9999-12-31',
        ]);

        $somId = $this->db->getLastInsertID();

        $this->insert('{{%currency_course}}', [
            'currency_id' => $somId,
            'course' => 0,
            'start_date' => new \yii\db\Expression('CURRENT_DATE'),
            'created_at' => new \yii\db\Expression('CURRENT_TIMESTAMP'),
            'end_date' => '9999-12-31',
        ]);


        $this->createTable('supplier', [
            'id' => $this->bigPrimaryKey(),
            'full_name' => $this->string(255)->notNull(),
            'phone_number' => $this->string(20)->notNull(),
            'phone_number_2' => $this->string(20),
            'account_number' => $this->string(30),
            'address' => $this->text(),
            'currency_id' => $this->bigInteger()->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-supplier_currency_id',
            'supplier',
            'currency_id',
            'currency',
            'id',
            'CASCADE'
        );

        // Table: supplier_balance
        $this->createTable('supplier_balance', [
            'id' => $this->bigPrimaryKey(),
            'supplier_id' => $this->bigInteger()->notNull(),
            'amount' => $this->decimal(10, 2)->notNull(),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);
        $this->addForeignKey(
            'fk-supplier_balance-supplier_id',
            'supplier_balance',
            'supplier_id',
            'supplier',
            'id',
            'CASCADE'
        );

        // Table: supplier_balance_history
        $this->createTable('supplier_balance_history', [
            'id' => $this->bigPrimaryKey(),
            'supplier_id' => $this->bigInteger()->notNull(),
            'amount' => $this->decimal(10, 2)->notNull(),
            'old_amount' => $this->decimal(10, 2)->notNull(),
            'type' => $this->integer()->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);
        $this->addForeignKey(
            'fk-supplier_balance_history-supplier_id',
            'supplier_balance_history',
            'supplier_id',
            'supplier',
            'id',
            'CASCADE'
        );

        // Table: supplier_payments
        $this->createTable('supplier_payments', [
            'id' => $this->bigPrimaryKey(),
            'supplier_id' => $this->bigInteger()->notNull(),
            'amount' => $this->decimal(10, 2)->notNull(),
            'type' => $this->integer()->notNull(),
            'add_user_id' => $this->bigInteger(),
            'expense_id' => $this->bigInteger(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-supplier_payments-add_user_id',
            'supplier_payments',
            'add_user_id',
            'users',
            'id',
            'SET NULL'
        );


        $this->addForeignKey(
            'fk-supplier_payments-supplier_id',
            'supplier_payments',
            'supplier_id',
            'supplier',
            'id',
            'CASCADE'
        );

        // Table: position
        $this->createTable('position', [
            'id' => $this->bigPrimaryKey(),
            'name' => $this->string(100)->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);



        // Table: equipment
        $this->createTable('equipment', [
            'id' => $this->bigPrimaryKey(),
            'name' => $this->string(255)->notNull(),
            'photo' => $this->string(255)->null(),
            'description' => $this->text(),
            'purchase_date' => $this->date(),
            'initial_cost' => $this->decimal(10, 2)->notNull(),
            'useful_life_years' => $this->integer()->notNull(),
            'status' => $this->string(50)->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Table: client
        $this->createTable('client', [
            'id' => $this->bigPrimaryKey(),
            'full_name' => $this->string(255)->notNull(),
            'phone_number' => $this->string(20),
            'phone_number_2' => $this->string(20),
            'account_number' => $this->string(255),
            'address' => $this->text(),
            'region_id' => $this->bigInteger(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);


        // Table: region
        $this->createTable('region', [
            'id' => $this->bigPrimaryKey(),
            'name' => $this->string(255)->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Add foreign key for client.region_id -> region.id
        $this->addForeignKey(
            'fk-client-region_id',
            'client',
            'region_id',
            'region',
            'id',
            'SET NULL'
        );

        // Table: product
        $this->createTable('product', [
            'id' => $this->bigPrimaryKey(),
            'name' => $this->string(255)->notNull(),
            'size' => $this->integer()->notNull(),
            'description' => $this->text(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);


        $this->createTable('product_storage', [
            'id' => $this->bigPrimaryKey(),
            'product_id' => $this->bigInteger()->notNull(),
            'quantity' => $this->integer()->notNull(),
            'enter_date' => $this->date(),
            'add_user_id' => $this->bigInteger(),
            'accepted_user_id' => $this->bigInteger(),
            'accepted_at' => $this->timestamp()->null(),
            'updated_date' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);



        $this->addForeignKey(
            'fk_product_storage_add_user_id',
            'product_storage',
            'add_user_id',
            'users',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_product_storage_accepted_user_id',
            'product_storage',
            'accepted_user_id',
            'users',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-product_storage-product_id',
            'product_storage',
            'product_id',
            'product',
            'id',
            'CASCADE'
        );

        // Table: material
        $this->createTable('material', [
            'id' => $this->bigPrimaryKey(),
            'name' => $this->string(255)->notNull(),
            'description' => $this->text(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

       
        $this->createTable('product_ingredients', [
            'id' => $this->bigPrimaryKey(),
            'product_id' => $this->bigInteger()->notNull(),
            'material_id' => $this->bigInteger()->notNull(),
            'start_date' => $this->timestamp(),
            'end_date' => $this->timestamp()->defaultValue('9999-12-31'),
        ]);

        $this->createTable('material_production',[
            'id' => $this->bigPrimaryKey(),
            'material_id' => $this->bigInteger()->notNull(),
            'quantity' => $this->integer()->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Table: invoice
        $this->createTable('invoice', [
            'id' => $this->bigPrimaryKey(),
            'invoice_number' => $this->string(50)->notNull(),
            'supplier_id' => $this->bigInteger()->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'total_amount' => $this->decimal(10, 2)->notNull(),
            'description' => $this->text(),
            'accept_user_id' => $this->bigInteger(),
            'accepted_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-invoice-supplier_id',
            'invoice',
            'supplier_id',
            'supplier',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-invoice-accept_user_id',
            'invoice',
            'accept_user_id',
            'users',
            'id',
            'SET NULL'
        );

        // Table: invoice_detail
        $this->createTable('invoice_detail', [
            'id' => $this->bigPrimaryKey(),
            'invoice_id' => $this->bigInteger()->notNull(),
            'material_id' => $this->bigInteger()->notNull(),
            'price' => $this->decimal(10, 2)->null(),
            'quantity' => $this->integer()->notNull(),
            'remainder_quantity' => $this->integer(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-invoice_detail-invoice_id',
            'invoice_detail',
            'invoice_id',
            'invoice',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-invoice_detail-material_id',
            'invoice_detail',
            'material_id',
            'material',
            'id',
            'CASCADE'
        );

        // Table: client_payments
        $this->createTable('client_payments', [
            'id' => $this->bigPrimaryKey(),
            'client_id' => $this->bigInteger()->notNull(),
            'add_user_id' => $this->bigInteger()->notNull(),
            'type' => $this->integer()->notNull(),
            'summa' => $this->decimal(10, 2)->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-client_payments-client_id',
            'client_payments',
            'client_id',
            'client',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-client_payments-add_user_id',
            'client_payments',
            'add_user_id',
            'users',
            'id',
            'CASCADE'
        );

        // Table: material_defect
        $this->createTable('material_defect', [
            'id' => $this->bigPrimaryKey(),
            'material_id' => $this->bigInteger()->notNull(),
            'quantity' => $this->integer()->notNull(),
            'add_user_id' => $this->bigInteger()->null(),
            'accepted_user_id' => $this->bigInteger()->null(),
            'source' => $this->integer()->null(),
            'description' => $this->text(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'accepted_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-material_defect-material_id',
            'material_defect',
            'material_id',
            'material',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-material_defect-accepted_user_id',
            'material_defect',
            'accepted_user_id',
            'users',
            'id',
            'CASCADE'
        );




        $this->createTable('payment_type', [
            'id' => $this->bigPrimaryKey(),
            'type' => $this->integer()->null(),
            'created_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);
    
        $this->insert('payment_type', ['id' => 1, 'type' => 1, 'created_at' => date('Y-m-d H:i:s')]);
        $this->insert('payment_type', ['id' => 2, 'type' => 2, 'created_at' => date('Y-m-d H:i:s')]);
        $this->insert('payment_type', ['id' => 3, 'type' => 3, 'created_at' => date('Y-m-d H:i:s')]);
    
        $this->createTable('cashbox', [
            'id' => $this->bigPrimaryKey(),
            'name' => $this->integer()->null(),
            'balance' => $this->decimal(10, 2)->defaultValue(0.00),
            'created_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);
    
        $this->createTable('cashbox_payment_type', [
            'cashbox_id' => $this->bigInteger()->notNull(),
            'payment_type_id' => $this->bigInteger()->notNull(),
            'created_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);
    
        $this->addForeignKey(
            'fk_cashbox_payment_type_cashbox',
            'cashbox_payment_type',
            'cashbox_id',
            'cashbox',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk_cashbox_payment_type_payment',
            'cashbox_payment_type',
            'payment_type_id',
            'payment_type',
            'id',
            'CASCADE',
            'CASCADE'
        );
    
        // Добавляем две кассы
        $this->insert('cashbox', ['id' => 1, 'name' => 1, 'balance' => 0.00, 'created_at' => date('Y-m-d H:i:s')]);
        $this->insert('cashbox', ['id' => 2, 'name' => 2, 'balance' => 0.00, 'created_at' => date('Y-m-d H:i:s')]);
    
        $this->insert('cashbox_payment_type', ['cashbox_id' => 1, 'payment_type_id' => 1, 'created_at' => date('Y-m-d H:i:s')]);
        $this->insert('cashbox_payment_type', ['cashbox_id' => 2, 'payment_type_id' => 2, 'created_at' => date('Y-m-d H:i:s')]);
        $this->insert('cashbox_payment_type', ['cashbox_id' => 2, 'payment_type_id' => 3, 'created_at' => date('Y-m-d H:i:s')]);
    
        

        // Table: sales
        $this->createTable('sales', [
            'id' => $this->bigPrimaryKey(),
            'client_id' => $this->bigInteger()->notNull(),
            'sell_user_id' => $this->bigInteger()->notNull(),
            'confirm_user_id' => $this->bigInteger(),
            'status' => $this->integer()->null(),
            'driver' => $this->string(50)->null(),
            'car_number' => $this->string(50)->null(),
            'total_sum' => $this->decimal(10, 2)->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'started_at' => $this->timestamp()->null(),
            'completed_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-sales-client_id',
            'sales',
            'client_id',
            'client',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-sales-sell_user_id',
            'sales',
            'sell_user_id',
            'users',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-sales-confirm_user_id',
            'sales',
            'confirm_user_id',
            'users',
            'id',
            'SET NULL'
        );

        // Table: sales_detail
        $this->createTable('sales_detail', [
            'id' => $this->bigPrimaryKey(),
            'sale_id' => $this->bigInteger()->notNull(),
            'product_id' => $this->bigInteger()->notNull(),
            'quantity' => $this->integer()->notNull(),
            'unit_price' => $this->decimal(10, 2)->notNull(),
            'total_price' => $this->decimal(10, 2)->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-sales_detail-sale_id',
            'sales_detail',
            'sale_id',
            'sales',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-sales_detail-product_id',
            'sales_detail',
            'product_id',
            'product',
            'id',
            'CASCADE'
        );

        // Table: worker
        $this->createTable('worker', [
            'id' => $this->bigPrimaryKey(),
            'full_name' => $this->string(255)->notNull(),
            'address' => $this->string(255),
            'phone_number' => $this->string(20),
            'phone_number_2' => $this->string(20),
            'position_id' => $this->bigInteger()->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-worker-position_id',
            'worker',
            'position_id',
            'position',
            'id',
            'CASCADE'
        );

        $this->createTable('worker_salary', [
            'id' => $this->bigPrimaryKey(),
            'worker_id' => $this->bigInteger()->notNull(),
            'amount' => $this->decimal(10, 2)->notNull(),
            'start_date' => $this->date()->notNull(),
            'end_date' => $this->date(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-worker_salary-worker_id',
            'worker_salary',
            'worker_id',
            'worker',
            'id',
            'CASCADE'
        );

        // Table: worker_finances
        $this->createTable('worker_finances', [
            'id' => $this->bigPrimaryKey(),
            'worker_id' => $this->bigInteger()->notNull(),
            'month' => $this->string(10)->notNull(),
            'type' => $this->string(50)->notNull(),
            'amount' => $this->decimal(10, 2)->notNull(),
            'description' => $this->text(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-worker_finances-worker_id',
            'worker_finances',
            'worker_id',
            'worker',
            'id',
            'CASCADE'
        );

        $this->createTable('cashbox_detail', [
            'id' => $this->bigPrimaryKey(),
            'cashbox_id' => $this->bigInteger()->notNull(),
            'add_user_id' => $this->bigInteger()->null(),
            'type' => $this->integer()->null(),
            'worker_finance_id' => $this->bigInteger()->null(),
            'amount' => $this->decimal(10, 2),
            'created_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-cashbox-detail-cashbox_id',
            'cashbox_detail',
            'cashbox_id',
            'cashbox',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-cashbox-detail-add_user_id',
            'cashbox_detail',
            'add_user_id',
            'users',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-cashbox-detail-worker_finance_id',
            'cashbox_detail',
            'worker_finance_id',
            'worker_finances',
            'id',
            'CASCADE'
        );


        $this->createTable('expenses_type', [
            'id' => $this->bigPrimaryKey(),
            'name' => $this->string(255)->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->insert('expenses_type', [
            'name' => 'Oylik',
            'created_at' => new \yii\db\Expression('NOW()'),
        ]);

        $this->insert('expenses_type', [
            'name' => 'Avans',
            'created_at' => new \yii\db\Expression('NOW()'),
        ]);

        $this->insert('expenses_type', [
            'name' => 'Bonus',
            'created_at' => new \yii\db\Expression('NOW()'),
        ]);


        $this->insert('expenses_type', [
            'name' => 'Taminotchi uchun',
            'created_at' => new \yii\db\Expression('NOW()'),
        ]);

        


        $this->createTable('expenses', [
            'id' => $this->bigPrimaryKey(),
            'expense_type_id' => $this->bigInteger(),
            'add_user_id' => $this->bigInteger()->notNull(),
            'status' => $this->boolean(),
            'description' => $this->text(),
            'summa' => $this->decimal(10, 2),
            'payment_type' => $this->integer(),
            'worker_finance_id' => $this->bigInteger(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-expenses-worker_finance_id',
            'expenses',
            'worker_finance_id',
            'worker_finances',
            'id',
            'CASCADE'
        );

        // Foreign key: expense_type_id -> expenses_type(id)
        $this->addForeignKey(
            'fk-expenses-expense_type_id',
            'expenses',
            'expense_type_id',
            'expenses_type',
            'id',
            'CASCADE'
        );

        // Foreign key: add_user_id -> users(id)
        $this->addForeignKey(
            'fk-expenses-add_user_id',
            'expenses',
            'add_user_id',
            'users',
            'id',
            'CASCADE'
        );


        // Table: material_storage
        $this->createTable('material_storage', [
            'id' => $this->bigPrimaryKey(),
            'material_id' => $this->bigInteger()->notNull(),
            'quantity' => $this->integer()->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-material_storage-material_id',
            'material_storage',
            'material_id',
            'material',
            'id',
            'CASCADE'
        );



        $this->createTable('material_storage_history', [
            'id' => $this->bigPrimaryKey(),
            'material_storage_id' => $this->bigInteger()->notNull(),
            'material_id' => $this->bigInteger()->notNull(),
            'invoice_detail_id' => $this->bigInteger()->null(),
            'quantity' => $this->integer()->notNull(),
            'type' => $this->integer()->null(),
            'add_user_id' => $this->bigInteger()->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-material_storage_history-invoice_detail_id',
            'material_storage_history',
            'invoice_detail_id',
            'invoice_detail',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-material_storage_history-material_storage_id',
            'material_storage_history',
            'material_storage_id',
            'material_storage',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-material_storage_history-material_id',
            'material_storage_history',
            'material_id',
            'material',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-material_storage_history-add_user_id',
            'material_storage_history',
            'add_user_id',
            'users',
            'id',
            'CASCADE'
        );

        // Table: client_balance
        $this->createTable('client_balance', [
            'id' => $this->bigPrimaryKey(),
            'client_id' => $this->bigInteger()->notNull(),
            'amount' => $this->decimal(10, 2)->notNull(),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);

        $this->addForeignKey(
            'fk-client_balance-client_id',
            'client_balance',
            'client_id',
            'client',
            'id',
            'CASCADE'
        );

        // Table: client_balance_history
        $this->createTable('client_balance_history', [
            'id' => $this->bigPrimaryKey(),
            'client_id' => $this->bigInteger()->notNull(),
            'amount' => $this->decimal(10, 2)->notNull(),
            'old_amount' => $this->decimal(10, 2)->notNull(),
            'type' => $this->string(50)->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);

        $this->addForeignKey(
            'fk-client_balance_history-client_id',
            'client_balance_history',
            'client_id',
            'client',
            'id',
            'CASCADE'
        );

        // Table: tracking
        $this->createTable('tracking', [
            'id' => $this->bigPrimaryKey(),
            'progress_type' => $this->integer()->notNull(),
            'process_id' => $this->bigInteger()->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'status' => $this->integer(),
            'accepted_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // No foreign key defined for process_id; linkage depends on the type of tracking.

        // Table: product_price
        $this->createTable('product_price', [
            'id' => $this->bigPrimaryKey(),
            'product_id' => $this->bigInteger()->notNull(),
            'price' => $this->decimal(10, 2)->notNull(),
            'start_date' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'end_date' => $this->timestamp()->null(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-product_price-product_id',
            'product_price',
            'product_id',
            'product',
            'id',
            'CASCADE'
        );


        $this->createTable('security_records', [
            'id' => $this->bigPrimaryKey(),
            'car_number' => $this->string(20)->notNull(),
            'driver_full_name' => $this->string(255)->notNull(),
            'image' => $this->string(255)->null(),
            'add_user_id' => $this->bigInteger()->notNull(),
            'accepted_user_id' => $this->bigInteger()->null(),
            'accepted_at' => $this->timestamp()->null(),
            'client_id' => $this->bigInteger()->null(),
            'description' => $this->text()->null(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-security_records-client_id',
            'security_records',
            'client_id',
            'client',
            'id',
            'SET NULL'
        );

        $this->addForeignKey(
            'fk-security_records-accepted_user_id',
            'security_records',
            'accepted_user_id',
            'users',
            'id',
            'SET NULL'
        );

        $this->addForeignKey(
            'fk-security_records-add_user_id',
            'security_records',
            'add_user_id',
            'users',
            'id',
            'CASCADE'
        );

        // Table: action_logs
        $this->createTable('action_logs', [
            'id' => $this->bigPrimaryKey(),
            'user_id' => $this->bigInteger()->notNull(),
            'action_type' => $this->string(50),
            'table_name' => $this->string(255),
            'old_data' => $this->json()->null(),
            'new_data' => $this->json()->null(),
            'action_time' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);

        $this->addForeignKey(
            'fk-action_logs-user_id',
            'action_logs',
            'user_id',
            'users',
            'id',
            'CASCADE'
        );

        $this->createTable('material_status_group', [
            'id' => $this->bigPrimaryKey(),
            'add_user_id' => $this->integer()->notNull(),
            'status' => $this->integer()->null(),
            'created_at' => $this->dateTime()->notNull(),
            'accepted_user_id' => $this->integer(),
            'accepted_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
        ]);

        $this->addForeignKey(
            'fk-material_status_group-add_user_id',
            'material_status_group',
            'add_user_id',
            'users',
            'id'
        );

        $this->addForeignKey(
            'fk-material_status_group-accepted_user_id',
            'material_status_group',
            'accepted_user_id',
            'users',
            'id'
        );


        $this->createTable('material_status', [
            'id' => $this->bigPrimaryKey(),
            'status_group_id' => $this->bigInteger()->notNull(),
            'material_id' => $this->bigInteger()->notNull(),
            'deleted_at' => $this->timestamp()->null(),
            'created_at' => $this->timestamp()->null(),
            'quantity' => $this->integer()->notNull(),
        ]);


        
        $this->addForeignKey(
            'fk-material_status-material_id',
            'material_status',
            'material_id',
            'material',
            'id',
            'CASCADE'
        );
        
        
        $this->addForeignKey(
            'fk-material_status-status_group_id',
            'material_status',
            'status_group_id',
            'material_status_group',
            'id',
            'CASCADE'
        );

        $this->createTable('product_defect', [
            'id' => $this->bigPrimaryKey(),
            'product_id' => $this->bigInteger()->notNull(),
            'quantity' => $this->integer()->notNull(),
            'add_user_id' => $this->bigInteger()->null(),
            'accepted_user_id' => $this->bigInteger()->null(),
            'description' => $this->text(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'accepted_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-product_defect-product_id',
            'product_defect',
            'product_id',
            'product',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-product_defect-add_user_id',
            'product_defect',
            'add_user_id',
            'users',
            'id',
            'SET NULL'
        );

        $this->addForeignKey(
            'fk-product_defect-accepted_user_id',
            'product_defect',
            'accepted_user_id',
            'users',
            'id',
            'SET NULL'
        );



        $this->createTable('{{%product_storage_history}}', [
            'id' => $this->bigPrimaryKey(),
            'product_storage_id' => $this->bigInteger()->notNull(),
            'product_id' => $this->bigInteger()->notNull(),
            'quantity' => $this->integer()->notNull(),
            'type' => $this->string(50)->notNull(),
            'created_at' => $this->timestamp()->notNull(),
            'add_user_id' => $this->bigInteger(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Внешние ключи
        $this->addForeignKey(
            'fk-product_storage_history-product_storage_id',
            '{{%product_storage_history}}',
            'product_storage_id',
            '{{%product_storage}}',
            'id'
        );

        $this->addForeignKey(
            'fk-product_storage_history-product_id',
            '{{%product_storage_history}}',
            'product_id',
            '{{%product}}',
            'id'
        );

        $this->addForeignKey(
            'fk-product_storage_history-add_user_id',
            '{{%product_storage_history}}',
            'add_user_id',
            '{{%users}}',
            'id'
        );



        
    

    }

   
    public function safeDown()
    {


        $this->dropForeignKey('fk_cashbox_payment_type_cashbox', 'cashbox_payment_type');
        $this->dropForeignKey('fk_cashbox_payment_type_payment', 'cashbox_payment_type');
        $this->dropTable('cashbox_payment_type');
        $this->dropTable('cashbox');
        $this->dropTable('payment_type');


        
        $this->dropTable('supplier');
        $this->dropTable('supplier_balance');
        $this->dropTable('supplier_balance_history');
        $this->dropTable('supplier_payments');
        $this->dropTable('position');
        $this->dropTable('material_status_group');



        $auth = Yii::$app->authManager;
        $auth->remove($auth->getRole('admin'));
        $this->dropTable('users');


        $this->dropForeignKey('fk-expenses-expense_type_id', 'expenses');
        $this->dropForeignKey('fk-expenses-add_user_id', 'expenses');
        $this->dropTable('expenses');

        $this->dropTable('product');
        $this->dropTable('region');
        $this->dropTable('client');
        $this->dropTable('equipment');
        $this->dropTable('expenses_type');


        $this->dropForeignKey('fk-product_storage-product_id', 'product_storage');
        $this->dropForeignKey('fk-invoice-supplier_id', 'invoice');
        $this->dropForeignKey('fk-invoice-accept_user_id', 'invoice');
        $this->dropForeignKey('fk-invoice_detail-invoice_id', 'invoice_detail');
        $this->dropForeignKey('fk-invoice_detail-material_id', 'invoice_detail');
        $this->dropForeignKey('fk-client_payments-client_id', 'client_payments');
        $this->dropForeignKey('fk-client_payments-add_user_id', 'client_payments');
        $this->dropForeignKey('fk-material_defect-material_id', 'material_defect');
        $this->dropForeignKey('fk-material_defect-accepted_user_id', 'material_defect');

        $this->dropTable('material_defect');
        $this->dropTable('material');
        $this->dropTable('client_payments');
        $this->dropTable('invoice_detail');
        $this->dropTable('invoice');
        $this->dropTable('product_storage');
        $this->dropTable('product_storage_history');

        


        $this->dropForeignKey('fk-sales-client_id', 'sales');
        $this->dropForeignKey('fk-sales-sell_user_id', 'sales');
        $this->dropForeignKey('fk-sales-confirm_user_id', 'sales');
        $this->dropForeignKey('fk-sales_detail-sale_id', 'sales_detail');
        $this->dropForeignKey('fk-sales_detail-product_id', 'sales_detail');
        $this->dropForeignKey('fk-worker-position_id', 'worker');
        $this->dropForeignKey('fk-worker_finances-worker_id', 'worker_finances');
        $this->dropForeignKey('fk-worker_finances-cashbox_id', 'worker_finances');
        $this->dropForeignKey('fk-material_storage-material_id', 'material_storage');

        $this->dropTable('material_storage');
        $this->dropTable('worker_finances');
        $this->dropTable('worker');
        $this->dropTable('sales_detail');
        $this->dropTable('sales');
        $this->dropTable('cashbox');

        $this->dropForeignKey('fk-material_storage_history-material_storage_id', 'material_storage_history');
        $this->dropForeignKey('fk-material_storage_history-material_id', 'material_storage_history');
        $this->dropForeignKey('fk-client_balance-client_id', 'client_balance');
        $this->dropForeignKey('fk-client_balance_history-client_id', 'client_balance_history');
        $this->dropForeignKey('fk-product_price-product_id', 'product_price');

        $this->dropTable('product_price');
        $this->dropTable('tracking');
        $this->dropTable('client_balance_history');
        $this->dropTable('client_balance');
        $this->dropTable('material_storage_history');

        $this->dropForeignKey('fk-security_records-add_user_id', 'security_records');
        $this->dropForeignKey('fk-action_logs-user_id', 'action_logs');

        $this->dropTable('action_logs');
        $this->dropTable('security_records');

        $this->dropForeignKey('fk-material_status-material_id', 'material_status');
        $this->dropForeignKey('fk-material_status-user_id', 'material_status');
        $this->dropForeignKey('fk-material_status-accepted_user_id', 'material_status');

        $this->dropTable('material_status');


    }

    
}
