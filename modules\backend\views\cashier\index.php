<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\assets\Select2Asset;

DataTablesAsset::register($this);
Select2Asset::register($this);

$this->title = Yii::t("app", "cashier_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");

$acceptText = Yii::t("app", "accept");
$detailText = Yii::t("app", "detail");

?>

<style>
    #cashier-grid-pjax.loading {
        position: relative;
        min-height: 200px;
    }
    
    #cashier-grid-pjax.loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1000;
    }
</style>

<div class="card">
    <div class="card-body">
        <div class="row align-items-center mb-3">
            <div class="col-md-6">
                <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
            </div>
        </div>

        <?php Pjax::begin(['id' => 'cashier-grid-pjax']); ?>
        <?php if($expenses): ?>
            <div>
                <table id="cashier-grid-view" class="table table-bordered table-striped compact">
                    <thead>
                        <tr>
                            <th><?= Yii::t("app", "expense_type") ?></th>
                            <th><?= Yii::t("app", "added_by") ?></th>
                            <th><?= Yii::t("app", "description") ?></th>
                            <th><?= Yii::t("app", "amount") ?></th>
                            <th><?= Yii::t("app", "created_at") ?></th>
                            <th><?= Yii::t("app", "actions") ?></th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($expenses as $model): ?>
                        <tr>
                            <td><?= Html::encode($model['expense_name'] ?? '') ?></td>
                            <td><?= Html::encode($model['added_by'] ?? '') ?></td>
                            <td><?= Html::encode($model['description'] ?? '') ?></td>
                            <td><?= Html::encode(date('Y-m-d H:i:s', strtotime($model['summa'] ?? ''))) ?></td>
                            <td><?= !empty($model['created_at']) ? date('Y-m-d H:i:s', strtotime($model['created_at'])) : '' ?></td>
                            <td>
                                    

                                    <?php if ($model['deleted_at'] == NULL): ?>
                                        <a href="#" class="badge badge-info dropdown-toggle expenses-accept" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                                <span class="red-text"><?= Yii::t("app", "accept") ?></span>
                                        </a>
                                    <?php endif; ?>

                                       
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p><?= Yii::t('app', 'no_data_available') ?></p>
        <?php endif; ?>
        <?php Pjax::end(); ?>
    </div>
</div>

<div id="one" data-text="<?= Yii::t("app", "accept_expense") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "expense_detail") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initDataTable() {
        if (!$.fn.DataTable.isDataTable('#cashier-grid-view')) {
            $('#cashier-grid-view').DataTable({
                "language": {
                    "search": searchLabel,
                    "lengthMenu": lengthMenuLabel,
                    "zeroRecords": zeroRecordsLabel,
                    "info": infoLabel,
                    "infoEmpty": infoEmptyLabel,
                    "infoFiltered": infoFilteredLabel
                },
                "order": [[4, 'desc']],
                "columnDefs": [


                    {
                        "targets": 4,
                        "type": "date-eu",
                        "render": function(data, type, row) {
                            if (type === 'sort') {
                                var parts = data.split(' ');
                                var dateParts = parts[0].split('.');
                                return dateParts[2] + dateParts[1] + dateParts[0] + parts[1].replace(':', '');
                            }
                            return data;
                        }
                    },
                    {
                        "targets": [5],
                        "orderable": false
                    }
                ],
                "pageLength": 25,
                "responsive": true
            });
        }
    }

    function initializeExpensesAccept() {
        $(document).off('click.expenses-accept', '.expenses-accept').on('click.expenses-accept', '.expenses-accept', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/cashier/accept',
                data: {id: id},
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("expenses-accept-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.expenses-accept-button', '.expenses-accept-button').on('click.expenses-accept-button', '.expenses-accept-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#expenses-accept-form').serialize();
                $.ajax({
                    url: '/backend/cashier/accept',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response.status == 'success') {
                            button.prop('disabled', false);
                            $.pjax.reload({container: '#cashier-grid-pjax'});
                            $('#ideal-mini-modal').modal('hide');
                            iziToast.success({
                                message: String(response.message), 
                                position: 'topRight'
                            });
                        } else {
                            iziToast.error({
                                title: 'Ошибка',
                                message: String(response && response.message ? response.message : 'Неизвестная ошибка'), 
                                position: 'topRight'
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });

        $(document).off('keypress.expenses-accept-button').on('keypress.expenses-accept-button', '#expenses-accept-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('.expenses-accept-button').trigger('click');
            }
        });
    }

    function initializeAll() {
        initDataTable();
        initializeExpensesAccept();
    }

    initializeAll();

    $(document).on('pjax:complete', function(event) {
        if (event.target.id === 'cashier-grid-pjax') {
            initializeAll();
        }
    });
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>