<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%sales_return}}`.
 */
class m250225_145335_create_sales_return_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%sales_return}}', [
            'id' => $this->primaryKey(),
            'sale_id' => $this->integer()->notNull(),
            'product_id' => $this->integer()->notNull(),
            'quantity' => $this->decimal(10, 2)->notNull(),
            'unit_price' => $this->decimal(10, 2)->notNull(),
            'total_price' => $this->decimal(10, 2)->notNull(),
            'status' => $this->tinyInteger()->defaultValue(0),
            'comment' => $this->text(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Создаем индексы
        $this->createIndex(
            'idx-sales_return-sale_id',
            '{{%sales_return}}',
            'sale_id'
        );

        $this->createIndex(
            'idx-sales_return-product_id',
            '{{%sales_return}}',
            'product_id'
        );

        // Добавляем внешние ключи
        $this->addForeignKey(
            'fk-sales_return-sale_id',
            '{{%sales_return}}',
            'sale_id',
            '{{%sales}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-sales_return-product_id',
            '{{%sales_return}}',
            'product_id',
            '{{%product}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-sales_return-product_id', '{{%sales_return}}');
        $this->dropForeignKey('fk-sales_return-sale_id', '{{%sales_return}}');
        $this->dropTable('{{%sales_return}}');
    }
}
