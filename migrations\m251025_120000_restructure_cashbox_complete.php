<?php

use yii\db\Migration;

/**
 * Полная реструктуризация касс - одна касса работает со всеми типами платежей
 */
class m251025_120000_restructure_cashbox_complete extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Получаем существующие колонки
        $columns = $this->db->getTableSchema('cashbox')->columnNames;
        
        // 1. Добавляем колонки если их нет
        if (!in_array('title', $columns)) {
            $this->addColumn('cashbox', 'title', $this->string()->null());
        }
        
        if (!in_array('currency_id', $columns)) {
            $this->addColumn('cashbox', 'currency_id', $this->integer()->notNull()->defaultValue(2));
            
            $this->addForeignKey(
                'fk_cashbox_currency',
                'cashbox',
                'currency_id',
                'currency',
                'id',
                'CASCADE'
            );
        }


        // 3. Создаем таблицу cashbox_payment_type если её нет
        $tables = $this->db->schema->getTableNames();
        if (!in_array('cashbox_payment_type', $tables)) {
            $this->createTable('cashbox_payment_type', [
                'cashbox_id' => $this->bigInteger()->notNull(),
                'payment_type_id' => $this->bigInteger()->notNull(),
                'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
                'deleted_at' => $this->timestamp()->null(),
            ]);

            $this->addForeignKey(
                'fk_cashbox_payment_type_cashbox',
                'cashbox_payment_type',
                'cashbox_id',
                'cashbox',
                'id',
                'CASCADE'
            );

            $this->addForeignKey(
                'fk_cashbox_payment_type_payment_type',
                'cashbox_payment_type',
                'payment_type_id',
                'payment_type',
                'id',
                'CASCADE'
            );

            $this->createIndex(
                'uq_cashbox_payment_type',
                'cashbox_payment_type',
                ['cashbox_id', 'payment_type_id'],
                true
            );
        }

        // 4. Заполняем связи: каждая касса со всеми типами платежей
        $this->execute("
            INSERT INTO cashbox_payment_type (cashbox_id, payment_type_id, created_at)
            SELECT 
                c.id as cashbox_id,
                pt.id as payment_type_id,
                CURRENT_TIMESTAMP as created_at
            FROM cashbox c
            CROSS JOIN payment_type pt
            WHERE c.deleted_at IS NULL 
            AND pt.deleted_at IS NULL
            AND NOT EXISTS (
                SELECT 1 FROM cashbox_payment_type cpt 
                WHERE cpt.cashbox_id = c.id 
                AND cpt.payment_type_id = pt.id 
                AND cpt.deleted_at IS NULL
            )
        ");

        // 5. Удаляем старые колонки
        if (in_array('name', $columns)) {
            $this->dropColumn('cashbox', 'name');
        }
        
        // Удаляем payment_type_id если есть (из предыдущих миграций)
        if (in_array('payment_type_id', $columns)) {
            try {
                $this->dropForeignKey('fk_cashbox_payment_type', 'cashbox');
            } catch (Exception $e) {
                // Ключ может не существовать
            }
            $this->dropColumn('cashbox', 'payment_type_id');
        }

        // 6. Создаем индекс для уникальности касс
        try {
            $this->createIndex(
                'uq_cashbox_title_currency',
                'cashbox',
                ['title', 'currency_id'],
                true
            );
        } catch (Exception $e) {
            // Индекс может уже существовать
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Возвращаем старую структуру
        $this->addColumn('cashbox', 'name', $this->integer()->null());
        
        // Восстанавливаем значения name
        $this->execute("
            UPDATE cashbox SET name = CASE 
                WHEN title LIKE '%аличн%' THEN 1
                WHEN title LIKE '%езналичн%' THEN 2
                ELSE 1
            END
        ");

        // Удаляем новые элементы
        try {
            $this->dropIndex('uq_cashbox_title_currency', 'cashbox');
        } catch (Exception $e) {}
        
        try {
            $this->dropForeignKey('fk_cashbox_currency', 'cashbox');
        } catch (Exception $e) {}
        
        $this->dropTable('cashbox_payment_type');
        $this->dropColumn('cashbox', 'currency_id');
        $this->dropColumn('cashbox', 'title');
    }
} 