<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "client_special_prices".
 *
 * @property int $id
 * @property int $client_id
 * @property int $product_id
 * @property float|null $special_price
 * @property float|null $sell_price
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 *
 * @property Client $client
 * @property Product $product
 */
class ClientSpecialPrices extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'client_special_prices';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['client_id', 'product_id', 'special_price'], 'required'],
            [['client_id', 'product_id'], 'default', 'value' => null],
            [['client_id', 'product_id'], 'integer'],
            [['special_price', 'sell_price'], 'number'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['client_id'], 'exist', 'skipOnError' => true, 'targetClass' => Client::class, 'targetAttribute' => ['client_id' => 'id']],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'client_id' => Yii::t('app', 'client'),
            'product_id' => Yii::t('app', 'product'),
            'special_price' => Yii::t('app', 'special_price'),
            'sell_price' => Yii::t('app', 'sell_price'),
            'created_at' => Yii::t('app', 'created_at'),
            'updated_at' => Yii::t('app', 'updated_at'),
            'deleted_at' => Yii::t('app', 'deleted_at'),
        ];
    }

    /**
     * Gets query for [[Client]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClient()
    {
        return $this->hasOne(Client::class, ['id' => 'client_id']);
    }

    /**
     * Gets query for [[Product]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }

    public function beforeValidate()
    {
        $attributes = ['special_price', 'sell_price'];

        foreach ($attributes as $attribute) {
            if (is_string($this->$attribute) && !empty($this->$attribute)) {
                $originalValue = $this->$attribute;
                
                $cleanValue = preg_replace('/[\s\x{A0}]+/u', '', $originalValue);
                $cleanValue = str_replace(',', '.', $cleanValue);
                
                if (is_numeric($cleanValue)) {
                    $this->$attribute = floatval($cleanValue);
                }
            }
        }
        
        return parent::beforeValidate();
    }
}
