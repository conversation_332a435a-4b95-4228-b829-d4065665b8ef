<?php

namespace app\components;

use Yii;
use yii\base\Component;
use yii\helpers\Json;
use yii\httpclient\Client;
use app\common\models\Notification;
use app\common\jobs\FirebaseNotificationJob;

/**
 * Сервис для работы с Firebase Cloud Messaging (FCM)
 */
class FirebaseService extends Component
{
    /**
     * API ключ Firebase
     * @var string
     */
    public $apiKey;

    /**
     * URL для отправки уведомлений
     * @var string
     */
    public $fcmUrl = 'https://fcm.googleapis.com/fcm/send';

    /**
     * ID проекта Firebase
     * @var string
     */
    public $projectId;

    /**
     * Путь к файлу учетных данных Firebase
     * @var string
     */
    public $credentialsFile;

    /**
     * Инициализация компонента
     */
    public function init()
    {
        parent::init();

        // Инициализация API ключа
        if (empty($this->apiKey)) {
            $this->apiKey = Yii::$app->params['firebase']['apiKey'] ?? null;

            if (empty($this->apiKey)) {
                Yii::warning('Firebase API key is not set', 'firebase');
            }
        }

        // Инициализация ID проекта
        if (empty($this->projectId)) {
            $this->projectId = Yii::$app->params['firebase']['projectId'] ?? null;

            if (empty($this->projectId)) {
                Yii::warning('Firebase project ID is not set', 'firebase');
            }
        }

        // Инициализация пути к файлу учетных данных
        if (empty($this->credentialsFile)) {
            $this->credentialsFile = Yii::$app->params['firebase']['credentialsFile'] ?? null;

            if (empty($this->credentialsFile)) {
                Yii::warning('Firebase credentials file is not set', 'firebase');
            } elseif (!file_exists($this->credentialsFile)) {
                Yii::error("Firebase credentials file not found: {$this->credentialsFile}", 'firebase');
                $this->credentialsFile = null;
            }
        }
    }

    /**
     * Отправка уведомления по токену устройства
     *
     * @param string $token Токен устройства
     * @param string $title Заголовок уведомления
     * @param string $body Текст уведомления
     * @param array $data Дополнительные данные
     * @return array Результат отправки
     */
    public function sendToDevice($token, $title, $body, $data = [])
    {
        if (empty($this->apiKey)) {
            Yii::error('Firebase API key is not set', 'firebase');
            return [
                'success' => false,
                'error' => 'API key is not set'
            ];
        }

        $fields = [
            'to' => $token,
            'notification' => [
                'title' => $title,
                'body' => $body,
                'sound' => 'default',
                'badge' => '1'
            ],
            'data' => $data
        ];

        return $this->sendPushNotification($fields);
    }

    /**
     * Отправка уведомления по теме
     *
     * @param string $topic Тема
     * @param string $title Заголовок уведомления
     * @param string $body Текст уведомления
     * @param array $data Дополнительные данные
     * @return array Результат отправки
     */
    public function sendToTopic($topic, $title, $body, $data = [])
    {
        if (empty($this->apiKey)) {
            Yii::error('Firebase API key is not set', 'firebase');
            return [
                'success' => false,
                'error' => 'API key is not set'
            ];
        }

        $fields = [
            'to' => "/topics/{$topic}",
            'notification' => [
                'title' => $title,
                'body' => $body,
                'sound' => 'default',
                'badge' => '1'
            ],
            'data' => $data
        ];

        return $this->sendPushNotification($fields);
    }

    /**
     * Отправка уведомления по роли
     *
     * @param string $role Роль пользователей
     * @param string $title Заголовок уведомления
     * @param string $body Текст уведомления
     * @param array $data Дополнительные данные
     * @return array Результат отправки
     */
    public function sendToRole($role, $title, $body, $data = [])
    {
        // Отправляем уведомление по теме, соответствующей роли
        return $this->sendToTopic("role_{$role}", $title, $body, $data);
    }

    /**
     * Создание уведомления для пользователя и постановка в очередь
     *
     * @param int $user_id ID пользователя
     * @param string $title Заголовок уведомления
     * @param string $body Текст уведомления
     * @param array $data Дополнительные данные
     * @param string|null $group_id ID группы уведомлений (если null, будет сгенерирован автоматически)
     * @return bool Результат операции
     */
    public function notifyUser($user_id, $title, $body, $data = [], $group_id = null)
    {
        // Создаем запись в БД
        $notification = Notification::createForUser($title, $body, $data, $user_id, $group_id);

        if (!$notification) {
            return false;
        }

        // Ставим задачу в очередь
        $jobId = Yii::$app->queue->push(new FirebaseNotificationJob([
            'notification_id' => $notification->id
        ]));

        return $jobId !== null;
    }

    /**
     * Создание уведомления для роли и постановка в очередь
     *
     * @param string $role Роль пользователей
     * @param string $title Заголовок уведомления
     * @param string $body Текст уведомления
     * @param array $data Дополнительные данные
     * @param string|null $group_id ID группы уведомлений (если null, будет сгенерирован автоматически)
     * @return bool Результат операции
     */
    public function notifyRole($role, $title, $body, $data = [], $group_id = null)
    {
        // Создаем запись в БД
        $notification = Notification::createForRole($title, $body, $data, $role, $group_id);

        if (!$notification) {
            return false;
        }

        // Ставим задачу в очередь
        $jobId = Yii::$app->queue->push(new FirebaseNotificationJob([
            'notification_id' => $notification->id
        ]));

        return $jobId !== null;
    }

    /**
     * Отправка Push-уведомления
     *
     * @param array $fields Данные для отправки
     * @return array Результат отправки
     */
    protected function sendPushNotification($fields)
    {
        try {
            $client = new Client();
            $response = $client->createRequest()
                ->setMethod('POST')
                ->setUrl($this->fcmUrl)
                ->setHeaders([
                    'Authorization' => "key={$this->apiKey}",
                    'Content-Type' => 'application/json'
                ])
                ->setContent(Json::encode($fields))
                ->send();

            if ($response->isOk) {
                $result = $response->data;
                Yii::info('Firebase notification sent: ' . Json::encode($result), 'firebase');
                return [
                    'success' => true,
                    'result' => $result
                ];
            } else {
                Yii::error('Firebase notification failed: ' . $response->content, 'firebase');
                return [
                    'success' => false,
                    'error' => $response->content
                ];
            }
        } catch (\Exception $e) {
            Yii::error('Firebase notification exception: ' . $e->getMessage(), 'firebase');
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
