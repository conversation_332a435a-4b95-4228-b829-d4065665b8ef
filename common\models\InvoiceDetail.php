<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "invoice_detail".
 *
 * @property int $id
 * @property int $invoice_id
 * @property int $material_id
 * @property float $price
 * @property int $quantity
 * @property int|null $remainder_quantity
 * @property string|null $created_at
 * @property string|null $deleted_at
 * @property int|null $currency_id
 * @property Invoice $invoice
 * @property Material $material
 * @property Currency $currency
 */
class InvoiceDetail extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'invoice_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['invoice_id', 'material_id', 'quantity'], 'required'],
            [['invoice_id', 'material_id', 'quantity', 'remainder_quantity', 'deleted_at', 'currency_id'], 'default', 'value' => null],
            [['invoice_id', 'material_id', 'quantity', 'remainder_quantity', 'currency_id'], 'integer'],
            [['price'], 'number'],
            [['created_at'], 'safe'],
            [['invoice_id'], 'exist', 'skipOnError' => true, 'targetClass' => Invoice::class, 'targetAttribute' => ['invoice_id' => 'id']],
            [['material_id'], 'exist', 'skipOnError' => true, 'targetClass' => Material::class, 'targetAttribute' => ['material_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'invoice_id' => 'Invoice ID',
            'material_id' => 'Material ID',
            'price' => 'Price',
            'quantity' => 'Quantity',
            'remainder_quantity' => 'Remainder Quantity',
            'created_at' => 'Created At',
        ];
    }

    /**
     * Gets query for [[Invoice]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getInvoice()
    {
        return $this->hasOne(Invoice::class, ['id' => 'invoice_id']);
    }

    /**
     * Gets query for [[Material]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterial()
    {
        return $this->hasOne(Material::class, ['id' => 'material_id']);
    }
}
