<?php

if (!function_exists('formatDate')) {
    function formatDate($date)
    {
        if (empty($date)) {
            return null;
        }
        return date('d.m.Y', strtotime($date));
    }
}

if (!function_exists('formatDateTime')) {
    function formatDateTime($date)
    {
        if (empty($date)) {
            return null;
        }
        return date('d.m.Y H:i', strtotime($date));
    }
}

if (!function_exists('formatTime')) {
    function formatTime($date)
    {
        if (empty($date)) {
            return null;
        }
        return date('H:i', strtotime($date));
    }
}

if (!function_exists('toMysqlDate')) {
    function toMysqlDate($date)
    {
        if (empty($date)) {
            return null;
        }
        return date('Y-m-d', strtotime($date));
    }
}

if (!function_exists('toMysqlDateTime')) {
    function toMysqlDateTime($date)
    {
        if (empty($date)) {
            return null;
        }
        return date('Y-m-d H:i:s', strtotime($date));
    }
}

if (!function_exists('formatMonthName')) {
    function formatMonthName($monthString, $language = 'ru')
    {
        if (empty($monthString)) {
            return null;
        }
        
        // Парсим строку месяца (например, "2025-05")
        $date = DateTime::createFromFormat('Y-m', $monthString);
        if (!$date) {
            return $monthString;
        }
        
        $monthNum = (int)$date->format('n'); // Номер месяца от 1 до 12
        $year = $date->format('Y');
        
        // Массивы месяцев на разных языках
        $months = [
            'ru' => [
                1 => 'Январь', 2 => 'Февраль', 3 => 'Март', 4 => 'Апрель',
                5 => 'Май', 6 => 'Июнь', 7 => 'Июль', 8 => 'Август',
                9 => 'Сентябрь', 10 => 'Октябрь', 11 => 'Ноябрь', 12 => 'Декабрь'
            ],
            'uz' => [
                1 => 'Yanvar', 2 => 'Fevral', 3 => 'Mart', 4 => 'Aprel',
                5 => 'May', 6 => 'Iyun', 7 => 'Iyul', 8 => 'Avgust',
                9 => 'Sentyabr', 10 => 'Oktyabr', 11 => 'Noyabr', 12 => 'Dekabr'
            ]
        ];
        
        $monthName = $months[$language][$monthNum] ?? $monthString;
        return $monthName . ' ' . $year;
    }
}

if (!function_exists('getCurrentLanguage')) {
    function getCurrentLanguage()
    {
        return Yii::$app->language ?? 'ru';
    }
}
