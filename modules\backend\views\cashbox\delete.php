<?php
use yii\helpers\Html;
use yii\widgets\ActiveForm;
?>

<?php $form = ActiveForm::begin([
    'id' => 'cashbox-delete-form',
    'enableAjaxValidation' => false,
    'enableClientValidation' => false,
]); ?>
    <?= $form->field($model, 'id')->hiddenInput()->label(false) ?>
    
    <div class="card">
        <div class="card-header">
            <h6><?= Yii::t('app', 'information_about_cashbox') ?></h6>
        </div>
        <div class="card-body">
            <table class="table table-sm">
                <tr>
                    <td><strong><?= Yii::t('app', 'id') ?>:</strong></td>
                    <td><?= $model->id ?></td>
                </tr>
                <tr>
                    <td><strong><?= Yii::t('app', 'cashbox_title') ?>:</strong></td>
                    <td><?= Html::encode($model->title) ?></td>
                </tr>
                <tr>
                    <td><strong><?= Yii::t('app', 'currency') ?>:</strong></td>
                    <td><?= $model->currency ? $model->currency->name : Yii::t('app', 'not_specified') ?></td>
                </tr>
                <tr>
                    <td><strong><?= Yii::t('app', 'balance') ?>:</strong></td>
                    <td>
                        <span class="badge <?= $model->balance >= 0 ? 'badge-success' : 'badge-danger' ?>">
                            <?= number_format($model->balance, 2) ?>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong><?= Yii::t('app', 'payments_type') ?>:</strong></td>
                    <td><?= $model->getPaymentTypesString() ?></td>
                </tr>
                <tr>
                    <td><strong><?= Yii::t('app', 'created_at') ?>:</strong></td>
                    <td><?= Yii::$app->formatter->asDatetime($model->created_at) ?></td>
                </tr>
            </table>
        </div>
    </div>

    <?php if ($model->balance != 0): ?>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong><?= Yii::t('app', 'attention') ?>:</strong> 
            <?= Yii::t('app', 'cashbox_has_non_zero_balance', ['balance' => number_format($model->balance, 2)]) ?>
            <?= Yii::t('app', 'please_make_sure_all_operations_are_completed_before_deleting') ?>
        </div>
    <?php endif; ?>

    <?php
    // Проверяем наличие операций
    $hasOperations = $model->getCashboxDetails()
        ->where(['deleted_at' => null])
        ->exists();
    ?>

    <?php if ($hasOperations): ?>
        <div class="alert alert-danger">
            <i class="fas fa-ban"></i>
            <strong><?= Yii::t('app', 'error') ?>:</strong> 
            <?= Yii::t('app', 'cashbox_contains_operations_and_cannot_be_deleted') ?>
            <?= Yii::t('app', 'please_use_archiving_instead_of_deleting') ?>
        </div>
    <?php else: ?>
       
    <?php endif; ?>

<?php ActiveForm::end(); ?> 