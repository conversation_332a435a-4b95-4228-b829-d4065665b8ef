<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\assets\Select2Asset;
use app\common\models\Tracking;

DataTablesAsset::register($this);
Select2Asset::register($this);

$this->title = Yii::t("app", "supplier_material_income");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$activeText = Yii::t("app", "active");
$inactiveText = Yii::t("app", "inactive");
$all = Yii::t("app", "all");
?>


<style>
    /* Стили для конкретного select2 */
    #suplier_filter.select2 {
        min-width: 150px !important;
        width: 100% !important;
    }

    /* Стили для контейнера Select2 */
    #suplier_filter + .select2-container {
        width: 150px !important;
    }

    /* Стили для полей ввода даты */
    input[type="date"].form-control {
        width: 150px;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }

    /* Стили для загрузчика */
    .table-container {
        position: relative;
        min-height: 200px;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    .table-container.loaded {
        opacity: 1;
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        opacity: 0;
        visibility: hidden;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        border-radius: 4px;
        transition: opacity 0.3s, visibility 0.3s;
    }

    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .loading-spinner-container {
        text-align: center;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 0.8s linear infinite;
        margin-bottom: 10px;
    }

    .loading-text {
        color: #666;
        font-size: 14px;
    }

    /* Скрываем таблицу во время загрузки */
    .table-container:not(.loaded) #supplier-materials-grid-view {
        visibility: hidden;
    }

    /* Добавляем тень для оверлея */
    .loading-overlay::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        border-radius: 4px;
        pointer-events: none;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>


<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6">
            <?php if (Yii::$app->user->can('admin')): ?>
                <div class="d-flex justify-content-end align-items-center gap-2">
                    <!-- Date inputs -->
                    <div class="d-flex gap-2" style="min-width: 200px;">
                        <input type="date" id="date_from" class="form-control" placeholder="<?= Yii::t('app', 'From Date') ?>">
                        <span style="width: 10px; display: inline-block"></span>
                        <input type="date" id="date_to" class="form-control" placeholder="<?= Yii::t('app', 'To Date') ?>">
                    </div>

                     <!-- Worker filter dropdown -->
                     <select id="suplier_filter" class="form-control select2">
                        <option value=""><?= Yii::t('app', 'supliers') ?></option>
                        <?php foreach($supliers as $suplier): ?>
                            <option value="<?= $suplier->id ?>"><?= $suplier->full_name ?></option>
                        <?php endforeach; ?>
                    </select>

                    <!-- Filter button -->
                    <button type="button" class="btn btn-primary mr-2" id="search-button">
                        <?= Yii::t('app', 'search') ?>
                    </button>
                </div>
            <?php endif ?>
        </div>
    </div>

   

        <!-- Accepted Items Tab -->
            <?php Pjax::begin(['id' => 'supplier-materials-grid-pjax']); ?>
            <?php if($materials): ?>
                <div class="table-responsive table-container">
                    <!-- Добавляем оверлей с загрузчиком внутрь контейнера таблицы -->
                    <div class="loading-overlay">
                        <div class="loading-spinner-container">
                        <div class="loading-spinner"></div>
                        <div class="loading-text"><?= Yii::t("app", "loading") ?>...</div>
                    </div>
                    </div>
                    <table id="supplier-materials-grid-view" class="table table-bordered table-striped compact">
                        <thead>
                            <tr>
                                <th><?= Yii::t("app", "supplier_name") ?></th>
                                <th><?= Yii::t("app", "material_income_created_at") ?></th>
                                <th><?= Yii::t("app", "car_number") ?></th>
                                <th><?= Yii::t("app", "accepted_by") ?></th>
                                <th><?= Yii::t("app", "material_name") ?></th>
                                <th><?= Yii::t("app", "quantity") ?></th>
                                <th><?= Yii::t("app", "price") ?></th>
                                <th><?= Yii::t("app", "total_amount") ?></th>
                                <th><?= Yii::t("app", "actions") ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($materials as $material): ?>
                                <tr>
                                    <td><?= Html::encode($material['supplier_name']) ?></td>
                                    <td><?= Html::encode(date("d.m.Y h:i", strtotime($material['created_at']))) ?></td>
                                    <td><?= Html::encode($material['car_number']) ?></td>
                                    <td><?= Html::encode($material['accepted_user']) ?></td>
                                    <td><?= Html::encode($material['material_name']) ?></td>
                                    <td><?= Html::encode($material['quantity']) ?></td>
                                    <td><?= Html::encode($material['price']) ?></td>
                                    <td><?= Html::encode($material['total_amount']) ?></td>

                                    <td>
                                        <?php 
                                            $tracking = Tracking::find()
                                                ->where(['process_id' => $material['id']])
                                                ->andWhere(['progress_type' => Tracking::TYPE_MATERIAL_INCOME])
                                                ->andWhere(['is', 'accepted_at', null])
                                                ->andWhere(['is', 'deleted_at', null])
                                                ->one();
                                        ?>

                                        <?php if ($tracking): ?>
                                        <div class="dropdown d-inline">
                                            <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                                <?php echo Yii::t("app", "detail"); ?>
                                            </a>
                                            <div class="dropdown-menu">
                                                <a href="#" class="dropdown-item income-material-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($material['id']) ?>">
                                                    <?= Yii::t("app", "edit") ?>
                                                </a>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
            <?php endif ?>
            <?php Pjax::end(); ?>
   

<div id="one" data-text="<?= Yii::t("app", "edit_income_material") ?>"></div>

<?php
$js = <<<JS
(function($) {
    // Конфигурационные переменные
    const config = {
        searchLabel: "{$searchLabel}",
        lengthMenuLabel: "{$lengthMenuLabel}",
        zeroRecordsLabel: "{$zeroRecordsLabel}",
        infoLabel: "{$infoLabel}",
        infoEmptyLabel: "{$infoEmptyLabel}",
        infoFilteredLabel: "{$infoFilteredLabel}",
        activeText: "{$activeText}",
        inactiveText: "{$inactiveText}",
        all: "{$all}"
    };

    // CSS для плавной анимации и загрузки
        // Функции для управления состоянием загрузки
    function showLoading() {
        $('.table-container').removeClass('loaded');
        $('.loading-overlay').addClass('show');
    }

    function hideLoading() {
        // Задержка для гарантии завершения инициализации DataTable
        setTimeout(() => {
            $('.table-container').addClass('loaded');
            $('.loading-overlay').removeClass('show');
        }, 300);
    }

    // Настройки DataTable
    const tableOptions = {
        language: {
            search: config.searchLabel,
            lengthMenu: config.lengthMenuLabel,
            zeroRecords: config.zeroRecordsLabel,
            info: config.infoLabel,
            infoEmpty: config.infoEmptyLabel,
            infoFiltered: config.infoFilteredLabel
        },
        pageLength: 50,
        order: [[1, 'desc']],
        columnDefs: [{ targets: [8], orderable: false },
        {
                    "targets": 1,
                    "type": "date-eu",
                    "render": function(data, type, row) {
                        if (type === 'sort') {
                            var parts = data.split(' ');
                            var dateParts = parts[0].split('.');
                            var timePart = parts.length > 1 ? parts[1].replace(':', '') : '0000';
                            return dateParts[2] + dateParts[1] + dateParts[0] + timePart;
                        }
                        return data;
                    }
                }
       ],


        deferRender: true,
        drawCallback: function() {
            hideLoading();
        }
    };

    // Инициализация DataTable
    function initializeDataTable() {
        const table = $('#supplier-materials-grid-view');
        
        if (!table.length) return;
        
        showLoading();
        
        if ($.fn.DataTable.isDataTable(table)) {
            table.DataTable().destroy();
        }
        
        table.show().DataTable(tableOptions);
    }

    // Инициализация dropdown
    function initializeDropdown() {
        $(document)
            .off('click.dropdown')
            .on('click.dropdown', '.dropdown-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).siblings('.dropdown-menu').toggleClass('show');
            })
            .on('click.dropdown-item', '.dropdown-item', function(e) {
                e.preventDefault();
                e.stopPropagation();
            })
            .on('click.dropdown-close', function(e) {
                if (!$(e.target).closest('.dropdown').length) {
                    $('.dropdown-menu').removeClass('show');
                }
            });
    }

    // Инициализация Select2
    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }

    // Обработка обновления материала
    function initializeIncomeMaterial() {
        $(document)
            .off('click.income-material-update')
            .on('click.income-material-update', '.income-material-update', function() {
                const id = $(this).data('id');
                showLoading();
                
                $.ajax({
                    url: '/backend/supplier-finance/update',
                    data: { id },
                    dataType: 'json',
                    type: 'GET',
                    success: function(response) {
                        $('#ideal-mini-modal .modal-title').html($("#one").data("text"));
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').addClass("income-material-update-button");
                        initializeSelect2();
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    },
                    complete: function() {
                        hideLoading();
                    }
                });
            });

        $(document)
            .off('click.income-material-update-button')
            .on('click.income-material-update-button', '.income-material-update-button', function() {
                const button = $(this);
                if (button.prop('disabled')) return;

                button.prop('disabled', true);
                showLoading();
                
                const formData = $('#income-material-update-form').serialize();

                $.ajax({
                    url: '/backend/supplier-finance/update',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response?.status === 'success') {
                            $('#ideal-mini-modal .close').trigger('click');
                            $.pjax.reload({
                                container: '#supplier-materials-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response?.errors) {
                            $.each(response.errors, (field, errors) => {
                                $(`#\${field}-error`).css('color', 'red').text(errors.join(', '));
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                        hideLoading();
                    }
                });
            });
    }

    // Обработка поиска
    function initializeSearch() {
        $('#search-button').off('click').on('click', function() {
            showLoading();
            
            const dateFrom = $('#date_from').val();
            const dateTo = $('#date_to').val();
            const supplierId = $('#suplier_filter').val();

            if ($.fn.DataTable.isDataTable('#supplier-materials-grid-view')) {
                $('#supplier-materials-grid-view').DataTable().destroy();
            }

            $.ajax({
                url: '/backend/supplier-finance/search',
                type: 'POST',
                data: { 
                    date_from: dateFrom, 
                    date_to: dateTo, 
                    supplier_id: supplierId, 
                    type: 'materials' 
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('.table-responsive').html(response.content);
                        initializeDataTable();
                    } else {
                        hideLoading();
                        iziToast.error({
                            title: 'Ошибка',
                            message: response.message,
                            position: 'topRight'
                        });
                    }
                },
                error: function() {
                    hideLoading();
                    iziToast.error({
                        title: 'Ошибка',
                        message: '<?= Yii::t("app", "Error occurred while searching") ?>',
                        position: 'topRight'
                    });
                }
            });
        });
    }

    // Обработчики PJAX
    $(document)
        .on('pjax:beforeSend', function() {
            showLoading();
        })
        .on('pjax:complete', function() {
            initializeAll();
        });

    // Инициализация всех компонентов
    function initializeAll() {
        initializeDataTable();
        initializeDropdown();
        initializeSelect2();
        initializeIncomeMaterial();
        initializeSearch();
    }

    // Запуск инициализации
    initializeAll();
})(jQuery);
JS;

$this->registerJs($js, View::POS_END);
?>
