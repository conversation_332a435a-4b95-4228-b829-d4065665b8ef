<?php

return [
    // Equipment parts translations
    "equipment" => "Equipment",
    "equipment_parts" => "Equipment Parts",
    "back_to_equipment" => "Back to Equipment",
    "add_part" => "Add Part",
    "filter_by_status" => "Filter by Status",
    "search_by_name" => "Search by Name",
    "enter_part_name" => "Enter part name",
    "not_installed" => "Not Installed",
    "no_parts_for_equipment" => "No parts found for this equipment",
    "edit_part" => "Edit Part",
    "send_to_defect" => "Send to Defect",
    "Error occurred while searching" => "Error occurred while searching",
    "Error loading photo" => "Error loading photo",
    "select_part" => "Select Part",
    "available" => "Available",
    "pcs" => "pcs",
    "max_available" => "Max Available",
    "optional_comment" => "Optional Comment",
    "part_info" => "Part Information",
    "available_quantity" => "Available Quantity",
    "currency" => "UZS",
    "not_specified" => "Not Specified",
    "Error occurred while adding part" => "Error occurred while adding part",
    "Invalid part or quantity" => "Invalid part or quantity",
    "Part not found" => "Part not found",
    "Insufficient quantity available" => "Insufficient quantity available",
    "Part moved to equipment" => "Part moved to equipment",
    "Part created from reserve and installed" => "Part created from reserve and installed",
    "Quantity reduced: {quantity} units moved to equipment" => "Quantity reduced: {quantity} units moved to equipment",
    "Part successfully added to equipment" => "Part successfully added to equipment",
    "Part fully moved to equipment: {quantity} units" => "Part fully moved to equipment: {quantity} units",
    "Part created from reserve and installed: {quantity} units" => "Part created from reserve and installed: {quantity} units",
    "Quantity reduced from {old} to {new}: {moved} units moved to equipment {equipment}" => "Quantity reduced from {old} to {new}: {moved} units moved to equipment {equipment}",

    // Part history translations
    "part_history_on_equipment" => "History of part {part} on equipment {equipment}",
    "back_to_parts" => "Back to Parts",
    "not_assigned" => "Not Assigned",

    // Equipment status change with parts handling
    "parts_handling_instructions" => "Parts Handling Instructions",
    "selected_parts_returned_to_warehouse" => "Selected parts will be returned to warehouse",
    "unselected_parts_sent_to_defect" => "Unselected parts will be sent to defect",
    "select_parts_to_return_to_warehouse" => "Select parts to return to warehouse",
    "select_all" => "Select All",
    "deselect_all" => "Deselect All",
    "parts_decommission_warning" => "Warning: unselected parts will be permanently written off as defect!",
    "no_parts_attached_to_equipment" => "No parts are attached to this equipment",
    "equipment_status_changed_successfully" => "Equipment status changed successfully",
    "returned_to_warehouse_from_decommissioned_equipment" => "Returned to warehouse from decommissioned equipment: {equipment}",
    "defected_with_decommissioned_equipment" => "Defected with decommissioned equipment: {equipment}",
    "in_repair" => "in repair",
    "Error updating source part quantity" => "Error updating source part quantity",
    "Error creating new part record" => "Error creating new part record",

    'bot-debug-message' => 'The bot is undergoing technical work. Please try again later. For this, it is enough to give the bot the /start command again. If the problem persists, please contact us: ' . Yii::$app->params['support'],
    'bot-exception-message' => 'Could not verify the data sent. Please try again. For this, it is enough to give the bot the /start command. If the problem persists, please contact us: ' . Yii::$app->params['support'],
    'bot-to-order' => '✅ Place an order',
    'bot-basket' => '🛍 Shopping cart',
    'bot-additional-information' => '📝 More',
    'bot-registered-client-start-message' => '🏘 Homepage',
    'bot-procedure-for-ordering-button' => '✅ Register',
    'bot-full-name-message' => 'Enter your first and last name',
    'bot-phone-button' => '📱 Send phone number',
    'bot-phone-message' => '📱 Send your phone number using the button below',
    'bot-verification-code-resend-button' => '😕 SMS did not arrive',
    'bot-verification-code-message' => '📱 Enter the code sent to your phone number via SMS',
    'bot-verification-code-resend-message' => 'A re-verification code has been sent to your number. Please enter the code',
    'bot-verification-code-invalid-message' => 'The code was entered incorrectly. Please try again',
    'bot-location-button' => '📍 Send address',
    'bot-location-message' => '📍 Submit your address using the button below',
    'bot-verification-code-valid-message' => 'Your phone number has been verified',
    'bot-location-correct-button' => '✅ Correct',
    'bot-location-incorrect-button' => '❌ Wrong',
    'bot-location-check-message' => 'Is the address correct?' . PHP_EOL . 'Your address is: ',
    'bot-location-resend-message' => '📍 Click the button below to resend the address',
    'bot-registered-message' => '  You have successfully registered. Our managers will contact you soon and accept your order',
    'bot-product-to-basket' => '📥 Add to cart',
    'bot-back-to-home-menu' => '🏘 Homepage',
    'bot-great-choice' => 'Specify the required amount',
    'bot-home-menu' => '🏘 Homepage',
    'bot-category-message' => '🗂 Choose one of the categories',
    'basket-empty' => '😔 Your shopping cart is empty',
    'bot-summa' => '<b>💰 Amount</b>',
    'sum' => '  so\'m',
    'bot-next-step' => '👉 The next step',
    'bot-back' => '⬅️ Back',
    'bot-client-phone-number' => '<b>📱 Your phone number:</b> ',
    'bot-client-address' => '<b>📍 Your address:</b> ',
    'bot-total-sum' => '<b>💰 Order amount:</b> ',
    'bot-payment-type' => '<b>💳 Payment type:</b> ',
    'bot-client-balance' => '<b>💰 In your account:</b> ',
    'bot-confirm' => 'Confirmation',
    'bot-cancel' => '❌ Cancellation',
    'bot-order-canceled' => 'Order cancelled',
    'bot-order-accepted' => '🎉 Dear customer, your order has been accepted!',
    'bot-order-accepted-2' => 'Our couriers will deliver your order soon.',
    'bot-balance' => '💰 My balance',
    'basket-no-empty' => '📝 Items in your cart',
    'bot-my-information' => '📝 My information',
    'bot-products' => '💧 Products',
    'bot-my-orders' => '📋 My orders',
    'bot-about-us' => '🏢 About the company',
    'bot-promotions' => '🔥 Actions',
    'bot-promotions-text' => 'Shares held by the company are not currently available.',
    'bot-no-orders' => '😔 You have no orders yet',
    'bot-order-code' => '<b>#⃣ Order number:</b> ',
    'bot-order-status' => '<b>🔷 Order status:</b> ',
    'bot-order-date' => '<b>🕐 Opening time:</b> ',
    'bot-order-payment-type' => '<b>💳 Payment type:</b> ',
    'bot-order-paid-sum' => '<b>💸 Amount paid:</b> ',
    'bot-order-total-sum' => '<b>💰 Total order amount:</b> ',
    'bot-additional-features' => 'Additional information.',
    'bot-your-balance' => '<b>💰 On your balance:</b> ',
    'bot-your-information' => '📝 Your information.',
    'bot-your-name' => '<b>🔸 Your code:</b> ',
    'bot-your-phone-number' => '<b>📱 Phone:</b> ',
    'bot-your-address' => '<b>📍 Address:</b> ',
    'bot-change-name' => '🔸 Name change',
    'bot-change-phone-number' => '📱 Change phone number',
    'bot-change-address' => '📍 Change of address',
    'bot-your-language' => '<b>🔤 Selected language:</b> ',
    'bot-change-language' => '🔤 Change the language',
    'bot-send-location' => '📍 Send address',
    'bot-send-phone-number' => '📱 Send phone number',
    'bot-location-resend' => '  Resend address',
    'bot-default-message' => 'Sorry, you submitted incorrect information. Please check and resend or press /start',
    'basket' => 'Shopping cart',
    'new' => 'New',
    'bot-enter-new-name' => 'Please enter your name',
    'bot-enter-new-phone-number' => 'Please enter your phone number. Example: +************',
    'bot-enter-new-address' => '📍 Submit your address using the button below',
    'bot-product-name' => '<b>🔸 Name:</b> ',
    'bot-product-price' => '<b>💵 Price:</b> ',
    'balance-is-insufficient' => 'There are not enough funds in your account. Please top up your account first',
    'bot-registered-accept-client-start-message' => "{full_name}, your application for concluding a contract has been accepted.\n📌Your ID code: {client_code} \n💈Your tariff: {plane}\n📦Your order is 18.9L: {water_count} pcs\n 💰Payment amount: {amount} sum",
    // \n🔖Coolers for rent: {rent_cooler_count} pcs
    'bot-accept-order-client' => 'Our couriers will deliver your order as soon as possible.',
    'bot-sending-a-payment-document' => '🗳 Send a check',
    'bot-payment-document-message' => 'You can send a picture of your receipt.',
    'bot-enroll-message-header' => '<b>Confirm the order? We will place an order for you by clicking the Confirm button.</b>',
    'bot-order-list-prepare' => 'Your order list is being prepared. Please wait!',
    'bot-your-order-list' => 'List of your orders',
    'bot-your-order-not-found' => 'You have no orders or payments yet',
    'bot-client-max-water-count-limit' => 'You have reached the maximum number of waters you can order. Contact the managers to increase the limit.',
    'bot-like-message' => 'Thank you for your feedback for the quality of our service.',
    'bot-water-count' => '<b>💧 Water count:</b> ',
    'bot-dislike-message' => 'Dear customer, write down your complaint. Your opinion is very important to us in order to improve the quality of our service.',
    'bot-dislike-comment-message' => 'Dear customer, your complaint has been sent to the management.',
    'order-pdf-message' => "Dear customer, your order has been placed.\n{payment-info}\nMore: {pdf-url}",
    'your-balance' => 'Your balance: {balance}',
    'your-deposit' => 'Deposit: {deposit}',
    'your-payment' => 'Payment: {payment}',
    'order-rating-message' => "Rate the quality of service. Your opinion is important to us.\n\n{rating-url}",
    "bot-other-count" => "📥 Count of additional products",
    'order-code' => '<b>Order number: </b>',
    'client-code' => '<b>Client code: </b>',
    'order' => '<b>Order</b>',
    'submitted-water-count' => '<b>Amount of water supplied: </b>',
    'received-empty-capsule-count' => "<b>Number of received empty capsules: </b>",
    'your-capsule-count' => "<b>Number of capsules you have: </b>",
    'payments' => "<b>Payments</b>",
    'balance-in-your-account' => "<b>Balance on your account: </b>",
    'served-driver' => "<b>Courier delivered: </b>",
    'date' => "<b>Date: </b>",
    'signature' => "<b>Signature: </b>",
    'signatory' => "<b>Signatory: </b>",
    'amount' => '<b>Amount</b>',
    'payment-type' => "<b>Payment type</b>",
    'bot-continue-hint' => 'Click "👉 The next step" button to continue.',
    'Qoldiq kun' => "Rest of the day",
    "Mijozlar haqida" => "About customers",
    "Mijozlar haqida moliyaviy bo`lim" => "Financial section about clients",
    "Sizning ma'lumotlaringiz" => "Dear customer you have registered as our regular customer, do you confirm the accuracy of the following information?",
    "bot-not-send-code" => "You can send the next SMS in {second} seconds.",
    "bot-send-file" => "The sent file has been saved",
    "bot-order-already-exists" => "<b>❗️Dear customer, you have a new order.</b>\n\n🕒 Order time: {order-date}\n💧Water quantity: {water-count}",

    "Botdagi mijozlar harakati" => "",
    "Ism kiritildi" => "",
    "Telefon raqamiga kod jo'natildi" => "",
    "Tasdiqlash kodi to'g'ri kiritildi" => "",
    "Lokatsiya jo'natildi" => "",
    "Ro'yxatdan o'tdi" => "",
    "Tasdiqlash kodi noto'g'ri kiritildi" => "",
    "Tasdiqlash kodi qayta jo'natildi" => "",
    "Ma'lumotlarini tasdiqlamadi" => "",
    "Havola orqali kirdi" => "",
    "Oxirgi o'zgarish vaqti" => "",
    "Ro'yxatdan o'tgan vaqti" => "",
    "Vaqti" => "",
    "Qadami" => "",
    "Ma'lumot" => "",
    "Ma'lumot topilmadi" => "",
    "Tasdilsh kodi jo'natildi" => "",
    "Ma'lumot tasdiqlanmadi" => "",
    "Notog'ri kod kiritildi" => "",
    "Kod qayta jo'natildi" => "",
    "Ma'lumot tasdiqlandi" => "",
    "send_pay_info_message" => "Dear Customer! Your payment has been received.\n\nPayment type: {pay_type}\nPayment amount: {summa}",
    "Hurmatli mijoz siz avvalroq bizdan ro'yxatdan o'tgansiz, bu sizning ma'lumotlaringiz" => "Dear client, you have registered with us previously, this is your information",
    "/start holatida" => "in /start status",
    "Sizning ma'lumotlaringiz" => "Dear customer you have registered as our regular customer, do you confirm the accuracy of the following information?",
    "bot-update-language" => "Changing the language",
    "Sizning kodingiz" => "Your code",
    "Telefon raqamingiz" => "Your phone number",
    "Manzilingiz" => "Your address",
    "Yangi tasdiqlash kodini yuborishingiz mumkin" => "You can send a new verification code",
    "Tasdiqlash kodini yuborish" => "Send verification code",
    "Tasdiqlash" => "Confirm",
    "Bekor qilish" => "Cancel",
    "Depozit" => "Zalog",
    "Ushbu mahulotni mijozga sotishni tasdiqlaysizmi ?" => "Do you confirm the sale of this product to the client ?",
    "Arendadagi mahsulot sotildi" => "Rented product sold",
    "Rasm yuklash" => "Upload image",
    "Ideal Water platformasiga kirish uchun tasdiqlash kodingiz" => "Your confirmation code to access the Ideal Water platform",
    "Qaysi manzil bo'yicha buyurtma qilmoqchisiz ?" => "What address would you like to place your order at ?",
    "bot-choose-client" => "Which customer's address do you want to change?",
    "Qaysi mijoz bo'yicha chek yubormoqchisiz ?" => "Which customer do you want to send the check to ?",
    "Buyurtma summasi" => "Order price",
    "Suv soni" => "Water quantity",
    "To`lov sanasini xos berish" => "Specify the payment date",
    "Maxsus muddat berish" => "Submission of a special deadline",
    "Yetkaziladigan vaqt" => "Time to be delivered",
    "Yetkazish vaqti" => "Delivery time",
    "" => "",
    "" => "",
    "" => "",
    "" => "",
    "" => "",
    "" => "",
    "" => "",
    "" => "",
    "" => "",
    "defect_quantity" => "Defect Quantity",
    "defect_reason" => "Defect Reason",
    "quantity_required" => "Quantity must be greater than zero",
    "quantity_required_positive" => "Quantity must be greater than zero",
    "defect_quantity_exceeds_available" => "Defect quantity exceeds available quantity",
    "defect_reason_required" => "Defect reason is required",
    "defect_recorded_successfully" => "Defect recorded successfully",
    "Error occurred while processing defect" => "Error occurred while processing defect",
    "installed" => "Installed",
    "mixed" => "Mixed",
    "Deduct from salary" => "Deduct from salary",
    "Deduction amount" => "Deduction amount",
];