<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $materialProductions array */
/* @var $materialStorages array */
/* @var $productStorages array */
/* @var $materials array */
/* @var $products array */

$this->title = 'Корректировка остатков';
$this->params['breadcrumbs'][] = $this->title;

$materialOptions = [];
foreach ($materials as $material) {
    $materialOptions[$material['id']] = $material['name'];
}

$productOptions = [];
foreach ($products as $product) {
    $productOptions[$product['id']] = $product['name'] . ' (' . $product['size'] . ')';
}
?>

<div class="inventory-adjustment-index">
    <h1><?= Html::encode($this->title) ?></h1>

    <div class="row">
        <div class="col-md-12">
            <ul class="nav nav-tabs" id="adjustmentTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="material-production-tab" data-toggle="tab" href="#material-production" role="tab" aria-controls="material-production" aria-selected="true">Материалы в производстве</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="material-storage-tab" data-toggle="tab" href="#material-storage" role="tab" aria-controls="material-storage" aria-selected="false">Материалы на складе</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="product-storage-tab" data-toggle="tab" href="#product-storage" role="tab" aria-controls="product-storage" aria-selected="false">Готовые продукты</a>
                </li>
            </ul>

            <div class="tab-content" id="adjustmentTabsContent">
                <!-- Материалы в производстве -->
                <div class="tab-pane fade show active" id="material-production" role="tabpanel" aria-labelledby="material-production-tab">
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Корректировка материалов в производстве</h5>
                                </div>
                                <div class="card-body">
                                    <?php $form = ActiveForm::begin([
                                        'id' => 'material-production-form',
                                        'action' => Url::to(['inventory-adjustment/adjust-material-production']),
                                        'options' => ['class' => 'adjustment-form'],
                                    ]); ?>

                                    <div class="form-group">
                                        <label for="materialadjustmentform-material_id">Материал</label>
                                        <select id="materialadjustmentform-material_id" class="form-control" name="MaterialAdjustmentForm[material_id]" required>
                                            <option value="">Выберите материал</option>
                                            <?php foreach ($materialOptions as $id => $name): ?>
                                                <option value="<?= $id ?>"><?= Html::encode($name) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="materialadjustmentform-quantity">Новое общее количество</label>
                                        <input type="number" id="materialadjustmentform-quantity" class="form-control" name="MaterialAdjustmentForm[quantity]" step="0.01" min="0" required>
                                        <small class="form-text text-muted">Введите новое общее количество материала в производстве</small>
                                    </div>



                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary">Сохранить</button>
                                    </div>

                                    <?php ActiveForm::end(); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Текущие остатки материалов в производстве</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Материал</th>
                                                    <th>Количество</th>
                                                    <th>Дата</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($materialProductions as $item): ?>
                                                    <tr>
                                                        <td><?= Html::encode($item['material_name']) ?></td>
                                                        <td><?= Html::encode($item['quantity']) ?></td>
                                                        <td><?= $item['created_at'] ? Yii::$app->formatter->asDate($item['created_at'], 'php:d.m.Y') : '' ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                                <?php if (empty($materialProductions)): ?>
                                                    <tr>
                                                        <td colspan="3" class="text-center">Нет данных</td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Материалы на складе -->
                <div class="tab-pane fade" id="material-storage" role="tabpanel" aria-labelledby="material-storage-tab">
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Корректировка материалов на складе</h5>
                                </div>
                                <div class="card-body">
                                    <?php $form = ActiveForm::begin([
                                        'id' => 'material-storage-form',
                                        'action' => Url::to(['inventory-adjustment/adjust-material-storage']),
                                        'options' => ['class' => 'adjustment-form'],
                                    ]); ?>

                                    <div class="form-group">
                                        <label for="materialadjustmentform-material_id-storage">Материал</label>
                                        <select id="materialadjustmentform-material_id-storage" class="form-control" name="MaterialAdjustmentForm[material_id]" required>
                                            <option value="">Выберите материал</option>
                                            <?php foreach ($materialOptions as $id => $name): ?>
                                                <option value="<?= $id ?>"><?= Html::encode($name) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="materialadjustmentform-quantity-storage">Новое общее количество</label>
                                        <input type="number" id="materialadjustmentform-quantity-storage" class="form-control" name="MaterialAdjustmentForm[quantity]" step="0.01" min="0" required>
                                        <small class="form-text text-muted">Введите новое общее количество материала на складе</small>
                                    </div>



                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary">Сохранить</button>
                                    </div>

                                    <?php ActiveForm::end(); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Текущие остатки материалов на складе</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Материал</th>
                                                    <th>Количество</th>
                                                    <th>Дата</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($materialStorages as $item): ?>
                                                    <tr>
                                                        <td><?= Html::encode($item['material_name']) ?></td>
                                                        <td><?= Html::encode($item['quantity']) ?></td>
                                                        <td><?= $item['created_at'] ? Yii::$app->formatter->asDate($item['created_at'], 'php:d.m.Y') : '' ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                                <?php if (empty($materialStorages)): ?>
                                                    <tr>
                                                        <td colspan="3" class="text-center">Нет данных</td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Готовые продукты -->
                <div class="tab-pane fade" id="product-storage" role="tabpanel" aria-labelledby="product-storage-tab">
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Корректировка готовых продуктов</h5>
                                </div>
                                <div class="card-body">
                                    <?php $form = ActiveForm::begin([
                                        'id' => 'product-storage-form',
                                        'action' => Url::to(['inventory-adjustment/adjust-product-storage']),
                                        'options' => ['class' => 'adjustment-form'],
                                    ]); ?>

                                    <div class="form-group">
                                        <label for="productadjustmentform-product_id">Продукт</label>
                                        <select id="productadjustmentform-product_id" class="form-control" name="ProductAdjustmentForm[product_id]" required>
                                            <option value="">Выберите продукт</option>
                                            <?php foreach ($productOptions as $id => $name): ?>
                                                <option value="<?= $id ?>"><?= Html::encode($name) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="productadjustmentform-quantity">Новое общее количество</label>
                                        <input type="number" id="productadjustmentform-quantity" class="form-control" name="ProductAdjustmentForm[quantity]" min="0" step="1" required>
                                        <small class="form-text text-muted">Введите новое общее количество продукта на складе</small>
                                    </div>



                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary">Сохранить</button>
                                    </div>

                                    <?php ActiveForm::end(); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Текущие остатки готовых продуктов</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Продукт</th>
                                                    <th>Количество</th>
                                                    <th>Количество блоков</th>
                                                    <th>Дата</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($productStorages as $item): ?>
                                                    <tr>
                                                        <td><?= Html::encode($item['product_name']) ?></td>
                                                        <td><?= Html::encode($item['quantity']) ?></td>
                                                        <td><?= Html::encode($item['quantity_block']) ?></td>
                                                        <td><?= $item['enter_date'] ? Yii::$app->formatter->asDate($item['enter_date'], 'php:d.m.Y') : '' ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                                <?php if (empty($productStorages)): ?>
                                                    <tr>
                                                        <td colspan="4" class="text-center">Нет данных</td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$js = <<<JS
$(document).ready(function() {
    // Обработка отправки форм через AJAX
    $('.adjustment-form').on('submit', function(e) {
        e.preventDefault();
        var form = $(this);

        $.ajax({
            url: form.attr('action'),
            type: 'post',
            data: form.serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    alert(response.message);
                    location.reload(); // Перезагружаем страницу для обновления данных
                } else {
                    var errorMessage = response.message || 'Произошла ошибка';
                    if (response.errors) {
                        errorMessage += ': ' + JSON.stringify(response.errors);
                    }
                    alert(errorMessage);
                }
            },
            error: function() {
                alert('Произошла ошибка при отправке запроса');
            }
        });
    });
});
JS;

$this->registerJs($js);
?>
