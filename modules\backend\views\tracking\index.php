<?php

use app\common\models\Invoice;
use app\common\models\Tracking;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use yii\widgets\Pjax;

$this->title = Yii::t("app", "tracking_section");

?>

        <div class="card-header">
            <h3 class="card-title"><?= Html::encode($this->title) ?></h3>
        </div>
    <div class="card-body">

        <?php Pjax::begin(['id' => 'invoice-pjax-container']); ?>
            <?php if (!empty($invoices)): ?>
            <h5 style="margin-bottom: -0.5rem;"><?= Yii::t("app", "accepted_invoices") ?></h5>
                <table class="table table-bordered table-striped compact">
                    <thead>
                        <tr>
                            <th><?= Yii::t('app', 'invoice_number') ?></th>
                            <th><?= Yii::t('app', 'supplier') ?></th>
                            <th><?= Yii::t('app', 'description') ?></th>
                            <th><?= Yii::t('app', 'created_at') ?></th>
                            <th><?= Yii::t('app', 'materials') ?></th>
                            <th><?= Yii::t('app', 'source') ?></th>
                            <th><?= Yii::t('app', 'set_price') ?></th>
                            <th><?= Yii::t('app', 'accept_income') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($invoices as $invoice): ?>
                            <?php $rowspan = count($invoice->invoiceDetails); ?>
                            <?php foreach ($invoice->invoiceDetails as $index => $detail): ?>
                                <tr>
                                    <?php if ($index === 0): ?>
                                        <td rowspan="<?= $rowspan ?>"><?= Html::encode($invoice->invoice_number) ?></td>
                                        <td rowspan="<?= $rowspan ?>"><?= Html::encode($invoice->supplier->full_name) ?></td>
                                        <td rowspan="<?= $rowspan ?>"><?= Html::encode($invoice->description) ?></td>
                                        <td rowspan="<?= $rowspan ?>"><?= Html::encode(date('d.m.Y H:i', strtotime($invoice->created_at)))?></td>
                                    <?php endif; ?>
                                    <td>
                                        <b style="color: #333;"><?= Html::encode($detail->material->name) ?> - </b>
                                        <b style="color: #333;"><?= Yii::$app->formatter->asInteger($detail->quantity) ?> <?= Yii::t('app', 'quantity_unit') ?> - </b>
                                        <b style="color: #333;">
                                            <?= $detail->price
                                                ? Yii::$app->formatter->asDecimal($detail->price, 2) . ($detail->currency_id == 1 ? ' $' : ' сўм')
                                                : Yii::t('app', 'price_not_set') ?>
                                        </b>
                                    </td>


                                    <td><?= Html::encode(Invoice::getSourceList($invoice->source)) ?></td>

                                    <?php if ($index === 0): ?>
                                        <td rowspan="<?= $rowspan ?>">
                                            <?php if($invoice->accepted_at === null): ?>
                                                <a href="#" class="badge badge-info set-price" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($invoice->id) ?>">
                                                    <?= Yii::t('app', 'set_price') ?>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                        <td rowspan="<?= $rowspan ?>">
                                            <?php if($invoice->accepted_at === null): ?>
                                                <a href="#" class="badge badge-success accept-income" style="cursor: pointer;" data-id="<?= Html::encode($invoice->id) ?>">
                                                    <?= Yii::t('app', 'accept_income') ?>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
            <?php Pjax::end(); ?>



            <?php if (!empty($result['product_release'])): ?>
                <h5 style="margin-bottom: -0.5rem;"><?= Yii::t("app", "product_release") ?></h5>
                <div class="table-responsive mb-4">
                    <table class="table table-bordered table-hover tracking-table">
                        <thead>
                            <tr>
                                <th><?= Yii::t("app", "id") ?></th>
                                <th><?= Yii::t("app", "product_name") ?></th>
                                <th><?= Yii::t("app", "quantity") ?></th>
                                <th><?= Yii::t("app", "created_at") ?></th>
                                <th><?= Yii::t("app", "accepted_at") ?></th>
                                <th><?= Yii::t("app", "actions") ?></th>
                            </tr>
                        </thead>
                        <tbody style="height:40px">
                            <?php foreach ($result['product_release'] as $item): ?>
                                <tr id="tracking-row-<?= $item['tracking_id'] ?>">
                                    <td><?= $item['tracking_id'] ?></td>
                                    <td><?= Html::encode($item['product_name']) ?></td>
                                    <td><?= Yii::$app->formatter->asInteger($item['quantity']) ?></td>
                                    <td><?= isset($item['enter_date']) ? date("d.m.Y h:i", strtotime($item['enter_date'])) : '' ?></td>
                                    <td><?= $item['accepted_at'] ? date("d.m.Y h:i", strtotime($item['accepted_at'])) : '' ?></td>
                                    <td>
                                        <?php if ($item['status'] != Tracking::STATUS_ACCEPTED): ?>
                                            <span class="badge badge-success" style="cursor: pointer;" onclick="confirmTracking(<?= $item['tracking_id'] ?>)">
                                                <?= Yii::t('app', 'accept') ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>



        <?php if (!empty($result['expenses'])): ?>
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t("app", "expenses") ?></h5>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-hover tracking-table">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "id") ?></th>
                        <th><?= Yii::t("app", "expense_type") ?></th>
                        <th><?= Yii::t("app", "amount") ?></th>
                        <th><?= Yii::t("app", "added_by") ?></th>
                        <th><?= Yii::t("app", "expenses_created_at") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody style="height:40px">
                    <?php foreach ($result['expenses'] as $item): ?>
                    <tr id="tracking-row-<?= $item['tracking_id'] ?>">
                        <td><?= $item['tracking_id'] ?></td>
                        <td><?= Html::encode($item['related_name']) ?></td>
                        <td><?= Yii::$app->formatter->asDecimal($item['amount'], 2) ?></td>
                        <td><?= Html::encode($item['added_by']) ?></td>
                        <td><?= date("d.m.Y h:i", strtotime($item['created_at'])) ?></td>
                        <td>
                            <?php if ($item['status'] != Tracking::STATUS_ACCEPTED): ?>
                            <span class="badge badge-success" style="cursor: pointer;" onclick="confirmTracking(<?= $item['tracking_id'] ?>)">
                                <?= Yii::t('app', 'accept') ?>
                            </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <?php if (!empty($result['client_payments'])): ?>
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t("app", "client_payments") ?></h5>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-hover tracking-table">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "id") ?></th>
                        <th><?= Yii::t("app", "client") ?></th>
                        <th><?= Yii::t("app", "amount") ?></th>
                        <th><?= Yii::t("app", "added_by") ?></th>
                        <th><?= Yii::t("app", "payment_created_at") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody style="height:40px">
                    <?php foreach ($result['client_payments'] as $item): ?>
                    <tr id="tracking-row-<?= $item['tracking_id'] ?>">
                        <td><?= $item['tracking_id'] ?></td>
                        <td><?= Html::encode($item['related_name']) ?></td>
                        <td><?= Yii::$app->formatter->asDecimal($item['amount'], 2) ?></td>
                        <td><?= Html::encode($item['added_by']) ?></td>
                        <td><?= date("d.m.Y h:i", strtotime($item['created_at'])) ?></td>
                        <td>
                            <?php if ($item['status'] != Tracking::STATUS_ACCEPTED): ?>
                            <span class="badge badge-success" style="cursor: pointer;" onclick="confirmTracking(<?= $item['tracking_id'] ?>)">
                                <?= Yii::t('app', 'accept') ?>
                            </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <?php if (!empty($result['supplier_payments'])): ?>
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t("app", "supplier_payments") ?></h5>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-hover tracking-table">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "id") ?></th>
                        <th><?= Yii::t("app", "supplier") ?></th>
                        <th><?= Yii::t("app", "amount") ?></th>
                        <th><?= Yii::t("app", "added_by") ?></th>
                        <th><?= Yii::t("app", "payment_created_at") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody style="height:40px">
                    <?php foreach ($result['supplier_payments'] as $item): ?>
                    <tr id="tracking-row-<?= $item['tracking_id'] ?>">
                        <td><?= $item['tracking_id'] ?></td>
                        <td><?= Html::encode($item['related_name']) ?></td>
                        <td><?= Yii::$app->formatter->asDecimal($item['amount'], 2) ?></td>
                        <td><?= Html::encode($item['added_by']) ?></td>
                        <td><?= date("d.m.Y h:i", strtotime($item['created_at'])) ?></td>
                        <td>
                            <?php if ($item['status'] != Tracking::STATUS_ACCEPTED): ?>
                            <span class="badge badge-success" style="cursor: pointer;" onclick="confirmTracking(<?= $item['tracking_id'] ?>)">
                                <?= Yii::t('app', 'accept') ?>
                            </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <?php if (!empty($result['worker_payments'])): ?>
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t("app", "worker_payments") ?></h5>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-hover tracking-table">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "id") ?></th>
                        <th><?= Yii::t("app", "worker") ?></th>
                        <th><?= Yii::t("app", "amount") ?></th>
                        <th><?= Yii::t("app", "added_by") ?></th>
                        <th><?= Yii::t("app", "payment_created_at") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody style="height:40px">
                    <?php foreach ($result['worker_payments'] as $item): ?>
                    <tr id="tracking-row-<?= $item['tracking_id'] ?>">
                        <td><?= $item['tracking_id'] ?></td>
                        <td><?= Html::encode($item['related_name']) ?></td>
                        <td><?= Yii::$app->formatter->asDecimal($item['amount'], 2) ?></td>
                        <td><?= Html::encode($item['added_by']) ?></td>
                        <td><?= date("d.m.Y h:i", strtotime($item['created_at'])) ?></td>
                        <td>
                            <?php if ($item['status'] != Tracking::STATUS_ACCEPTED): ?>
                            <span class="badge badge-success" style="cursor: pointer;" onclick="confirmTracking(<?= $item['tracking_id'] ?>)">
                                <?= Yii::t('app', 'accept') ?>
                            </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>



        <?php if (!empty($result['security_records'])): ?>
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t("app", "security_records") ?></h5>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-hover tracking-table">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "id") ?></th>
                        <th><?= Yii::t("app", "car_number") ?></th>
                        <th><?= Yii::t("app", "driver_full_name") ?></th>
                        <th><?= Yii::t("app", "region_name") ?></th>
                        <th><?= Yii::t("app", "added_by") ?></th>
                        <th><?= Yii::t("app", "accepted_by") ?></th>
                        <th><?= Yii::t("app", "security_records_created_at") ?></th>
                        <th><?= Yii::t("app", "accepted_at") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody style="height:40px">
                    <?php foreach ($result['security_records'] as $item): ?>
                    <tr id="tracking-row-<?= $item['tracking_id'] ?>">
                        <td><?= $item['tracking_id'] ?></td>
                        <td><?= Html::encode($item['car_number']) ?></td>
                        <td><?= Html::encode($item['driver_full_name']) ?></td>
                        <td><?= Html::encode($item['region_name']) ?></td>
                        <td><?= Html::encode($item['added_by']) ?></td>
                        <td><?= Html::encode($item['accepted_by']) ?></td>
                        <td><?= date("d.m.Y H:i", strtotime($item['created_at'])) ?></td>
                        <td><?= $item['accepted_at'] ? date("d.m.Y H:i", strtotime($item['accepted_at'])) : '' ?></td>
                        <td>
                            <?php if ($item['status'] != Tracking::STATUS_ACCEPTED): ?>
                            <span class="badge badge-success" style="cursor: pointer;" onclick="confirmTracking(<?= $item['tracking_id'] ?>)">
                                <?= Yii::t('app', 'accept') ?>
                            </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <?php if (!empty($result['sales'])): ?>
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t("app", "_sales") ?></h5>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-hover tracking-table">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "id") ?></th>
                        <th><?= Yii::t("app", "product_name") ?></th>
                        <th><?= Yii::t("app", "quantity") ?></th>
                        <th><?= Yii::t("app", "unit_price") ?></th>
                        <th><?= Yii::t("app", "total_price") ?></th>
                        <th><?= Yii::t("app", "client_name") ?></th>
                        <th><?= Yii::t("app", "car_number") ?></th>
                        <th><?= Yii::t("app", "driver_full_name") ?></th>
                        <th><?= Yii::t("app", "added_by") ?></th>
                        <th><?= Yii::t("app", "accepted_by") ?></th>
                        <th><?= Yii::t("app", "sales_created_at") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody style="height:40px">
                    <?php foreach ($result['sales'] as $item): ?>
                        <?php foreach ($item['products'] as $index => $product): ?>
                            <tr id="tracking-row-<?= $item['tracking_id'] ?>">
                                <?php if ($index === 0): ?>
                                    <td rowspan="<?= $item['rowspan'] ?>"><?= $item['tracking_id'] ?></td>
                                <?php endif; ?>
                                <td><?= Html::encode($product['product_name']) ?></td>
                                <td><?= Html::encode($product['quantity']) ?></td>
                                <td><?= Yii::$app->formatter->asDecimal($product['factory_price'], 2) ?></td>
                                <td><?= Yii::$app->formatter->asDecimal($product['total_price'], 2) ?></td>
                                <?php if ($index === 0): ?>
                                    <td rowspan="<?= $item['rowspan'] ?>"><?= Html::encode($item['client_name']) ?></td>
                                    <td rowspan="<?= $item['rowspan'] ?>"><?= /*$item['has_security_record'] ?*/ Html::encode($item['car_number'])?></td>
                                    <td rowspan="<?= $item['rowspan'] ?>"><?= Html::encode($item['driver_full_name']) ?></td>
                                    <td rowspan="<?= $item['rowspan'] ?>"><?= Html::encode($item['added_by']) ?></td>
                                    <td rowspan="<?= $item['rowspan'] ?>"><?= Html::encode($item['accepted_by']) ?></td>
                                    <td rowspan="<?= $item['rowspan'] ?>"><?= date("d.m.Y H:i", strtotime($item['created_at'])) ?></td>
                                    <td rowspan="<?= $item['rowspan'] ?>">
                                        <?php if ($item['status'] != Tracking::STATUS_ACCEPTED): ?>
                                            <span class="badge badge-success" style="cursor: pointer;" onclick="confirmTracking(<?= $item['tracking_id'] ?>)">
                                                <?= Yii::t('app', 'accept') ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                <?php endif; ?>
                            </tr>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <?php if (!empty($result['material_returns'])): ?>
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t("app", "material_returns") ?></h5>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-hover tracking-table">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "id") ?></th>
                        <th><?= Yii::t("app", "material_name") ?></th>
                        <th><?= Yii::t("app", "quantity") ?></th>
                        <th><?= Yii::t("app", "unit_price") ?></th>
                        <th><?= Yii::t("app", "total_price") ?></th>
                        <th><?= Yii::t("app", "added_by") ?></th>
                        <th><?= Yii::t("app", "accepted_by") ?></th>
                        <th><?= Yii::t("app", "created_at") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody style="height:40px">
                    <?php foreach ($result['material_returns'] as $item): ?>
                        <?php foreach ($item['materials'] as $index => $material): ?>
                            <tr id="tracking-row-<?= $item['tracking_id'] ?>">
                                <?php if ($index === 0): ?>
                                    <td rowspan="<?= $item['rowspan'] ?>"><?= $item['tracking_id'] ?></td>
                                <?php endif; ?>
                                <td><?= Html::encode($material['material_name']) ?></td>
                                <td><?= Html::encode($material['quantity']) ?></td>
                                <td><?= Yii::$app->formatter->asDecimal($material['unit_price'], 2) ?></td>
                                <td><?= Yii::$app->formatter->asDecimal($material['total_price'], 2) ?></td>
                                <?php if ($index === 0): ?>
                                    <td rowspan="<?= $item['rowspan'] ?>"><?= Html::encode($item['added_by']) ?></td>
                                    <td rowspan="<?= $item['rowspan'] ?>"><?= Html::encode($item['accepted_by']) ?></td>
                                    <td rowspan="<?= $item['rowspan'] ?>"><?= date("d.m.Y H:i", strtotime($item['created_at'])) ?></td>
                                    <td rowspan="<?= $item['rowspan'] ?>">
                                        <?php if ($item['status'] != Tracking::STATUS_ACCEPTED): ?>
                                            <span class="badge badge-success" style="cursor: pointer;" onclick="confirmTracking(<?= $item['tracking_id'] ?>)">
                                                <?= Yii::t('app', 'accept') ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                <?php endif; ?>
                            </tr>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>

    <!-- Раздел переупаковки продуктов -->
    <?php if (!empty($result['product_repackaging'])): ?>
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title"><?= Yii::t('app', 'product_repackaging') ?></h5>
        </div>
        <div class="card-body">
            <table class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'product') ?></th>
                        <th><?= Yii::t('app', 'quantity') ?></th>
                        <th><?= Yii::t('app', 'repackaging_reason') ?></th>
                        <th><?= Yii::t('app', 'created_at') ?></th>
                        <th><?= Yii::t('app', 'added_by') ?></th>
                        <th><?= Yii::t('app', 'status') ?></th>
                        <th><?= Yii::t('app', 'actions') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($result['product_repackaging'] as $item): ?>
                        <tr id="tracking-row-<?= $item['tracking_id'] ?>">
                            <td><?= Html::encode($item['product_name']) ?></td>
                            <td><?= Html::encode($item['quantity']) ?></td>
                            <td><?= Html::encode($item['repackaging_reason']) ?></td>
                            <td><?= Html::encode(date('d.m.Y H:i', strtotime($item['created_at']))) ?></td>
                            <td><?= Html::encode($item['added_by']) ?></td>
                            <td>
                                <?php if ($item['status'] == Tracking::STATUS_ACCEPTED): ?>
                                    <span class="badge badge-success"><?= Yii::t('app', 'accepted') ?></span>
                                <?php else: ?>
                                    <span class="badge badge-warning"><?= Yii::t('app', 'not_accepted') ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($item['status'] != Tracking::STATUS_ACCEPTED): ?>
                                    <span class="badge badge-success" style="cursor: pointer;" onclick="confirmTracking(<?= $item['tracking_id'] ?>)">
                                        <?= Yii::t('app', 'accept') ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php endif; ?>

<?php
$confirmAction = Yii::t('app', 'do_you_accept_this_action?');
$confirmationError = Yii::t('app', 'error_this_action');
$operationError = Yii::t('app', 'operation_error');
$noRecords = Yii::t('app', 'no_data_available');
$ajaxError = Yii::t('app', 'ajax_error');
$error = Yii::t('app', 'error');
$confirmIncome = Yii::t('app', 'dou_you_this_income?');

$js = <<<JS
(function($) {
    var confirmAction = '$confirmAction';
    var confirmationError = '$confirmationError';
    var operationError = '$operationError';
    var noRecords = '$noRecords';
    var ajaxError = '$ajaxError';
    var error = '$error';
    var confirmIncome = '$confirmIncome';

    // Функция подтверждения трекинга
    window.confirmTracking = function(id) {
        if (confirm(confirmAction)) {
            $.ajax({
                url: '/backend/tracking/confirm',
                type: 'POST',
                data: {id: id},
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Находим кнопку подтверждения и удаляем только её
                        var row = document.getElementById('tracking-row-' + id);
                        if (row) {
                            var actionButton = $(row).find('span.badge-success');
                            if (actionButton.length) {
                                actionButton.fadeOut('slow', function() {
                                    $(this).remove();
                                });
                            }
                        }
                    } else {
                        alert(response.message || confirmationError);
                    }
                },
                error: function(xhr, status, error) {
                    alert(operationError + ': ' + error);
                }
            });
        }
    };

    // Проверка наличия записей в таблицах
    $(document).ready(function() {
        $('.tracking-table').each(function() {
            var tbody = $(this).find('tbody');
            if (tbody.children('tr').length === 0) {
                tbody.html('<tr><td colspan=\"6\" class=\"text-center\">$noRecords</td></tr>');
            }
        });
    });

    // Установка цены
    $(document).on('click', '.set-price', function(e) {
        e.preventDefault();
        var id = $(this).data('id');
        $.ajax({
            url: '/backend/tracking/set-price',
            dataType: 'json',
            type: 'GET',
            data: { id: id },
            success: function(response) {
                $('#ideal-mini-modal .modal-title').html(response.title);
                $('#ideal-mini-modal .modal-body').html(response.content);
                $('#ideal-mini-modal').modal('show');
                $('#ideal-mini-modal .mini-button').addClass("set-price-button");
            },
            error: function(xhr, textStatus, errorThrown) {
                console.error(ajaxError + ':', xhr.statusText, errorThrown);
            }
        });
    });

    // Обработка кнопки установки цены
    $(document).off('click.set-price-button').on('click', '.set-price-button', function() {
        var button = $(this);
        if (!button.prop('disabled')) {
            button.prop('disabled', true);
            var formData = $('#price-form').serialize();
            $.ajax({
                url: '/backend/tracking/set-price',
                dataType: 'json',
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response && response.status === 'success') {
                        $.pjax.reload({container: '#invoice-pjax-container'});
                        $('.close').trigger('click');
                        iziToast.success({
                            message: response.message,
                            position: 'topRight'
                        });
                    } else {
                        $.pjax.reload({container: '#invoice-pjax-container'});
                        $('.close').trigger('click');
                        iziToast.error({
                            message: response.message,
                            position: 'topRight'
                        });
                    }
                },
                error: function(xhr) {
                    iziToast.error({
                        message: error + ': ' + (xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : xhr.statusText),
                        position: 'topRight'
                    });
                },
                complete: function() {
                    button.prop('disabled', false);
                }
            });
        }
    });

    // Подтверждение прихода
    $(document).on('click', '.accept-income', function(e) {
        e.preventDefault();
        var id = $(this).data('id');
        if (confirm('$confirmIncome')) {
            $.ajax({
                url: '/backend/tracking/accept-income',
                dataType: 'json',
                type: 'POST',
                data: { id: id },
                success: function(response) {
                    if (response.status === 'success') {
                        $.pjax.reload({container: '#invoice-pjax-container', timeout: 3000});
                        iziToast.success({
                            message: response.message,
                            position: 'topRight'
                        });
                    } else {
                        iziToast.error({
                            message: response.message,
                            position: 'topRight'
                        });
                    }
                },
                error: function(xhr) {
                    iziToast.error({
                        message: error + ': ' + (xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : xhr.statusText),
                        position: 'topRight'
                    });
                }
            });
        }
    });
})(jQuery);
JS;

$confirmUrl = Url::to(['tracking/confirm']);
$this->registerJs($js, View::POS_END);
?>