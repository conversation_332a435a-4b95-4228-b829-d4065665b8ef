<?php

namespace app\modules\api\controllers;

use Yii;
use yii\rest\Controller;
use yii\filters\auth\HttpBearerAuth;
use yii\filters\AccessControl;
use yii\web\ForbiddenHttpException;
use app\common\models\ApiResponse;
use yii\web\UnauthorizedHttpException;
use Swagger\Annotations as SWG;

/**
 * @SWG\Swagger(
 *     basePath="/",
 *     host="localhost:8080",
 *     schemes={"http"},
 *     @SWG\Info(
 *         version="1.0.0",
 *         title="Silver Zavod API",
 *         description="API для системы управления заводом",
 *         @SWG\Contact(
 *             email="<EMAIL>",
 *             name="Администратор системы"
 *         )
 *     ),
 *     @SWG\SecurityScheme(
 *         securityDefinition="Bearer",
 *         type="apiKey",
 *         name="Authorization",
 *         in="header",
 *         description="Токен доступа в формате: Bearer {token}"
 *     )
 * )
 */


class CustomBearerAuth extends HttpBearerAuth
{
    public function handleFailure($response)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $response->data = ApiResponse::response(
            Yii::t('app', 'Your request was made with invalid credentials'),
            null,
            ApiResponse::HTTP_UNAUTHORIZED
        );
        $response->statusCode = 401;
    }
}

class BaseController extends Controller
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['authenticator'] = [
            'class' => CustomBearerAuth::class,
            'except' => $this->allowedActions()
        ];

        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => $this->allowedActions(),
                    'roles' => ['?']
                ],
                [
                    'allow' => true,
                    'roles' => ['@']
                ],
            ],
        ];

        return $behaviors;
    }

    protected function allowedActions()
    {
        return [];
    }
}