<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\grid\GridView;
use yii\grid\ActionColumn;
use yii\web\View;
use app\assets\DataTablesAsset;
use yii\helpers\Url;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "worker_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");

?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('accountant')): ?>
                <a href="#" class="btn btn-primary worker-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "add_worker") ?>
                </a>
            <?php endif ?>
        </div>
    </div>


    <?php Pjax::begin(['id' => 'worker-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="worker-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th><?= Yii::t("app", "full_name") ?></th>
                    <th><?= Yii::t("app", "position") ?></th>
                    <th><?= Yii::t("app", "phone_number") ?></th>
                    <th><?= Yii::t("app", "address") ?></th>
                    <th><?= Yii::t("app", "salary") ?></th>
                    <th><?= Yii::t("app", "paid_amount") ?></th>
                    <th><?= Yii::t("app", "remaining_amount") ?></th>
                    <th><?= Yii::t("app", "status") ?></th>
                    <th><?= Yii::t("app", "created_at") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                        <td><?= Html::encode($model['full_name']) ?></td>
                        <td><?= Html::encode($model['position_name']) ?></td>
                        <td><?= Html::encode($model['phone_number']) ?></td>
                        <td><?= Html::encode($model['address']) ?></td>
                        <td><?= !empty($model['salary']) ? number_format($model['salary'], 0, '.', ',') : 0 ?></td>
                        <td><?= !empty($model['paid_amount']) ? number_format($model['paid_amount'], 0, '.', ',') : 0 ?></td>

                        <td style="color: <?= $model['debt'] > 0 ? 'green' : 'red' ?>"><?= number_format($model['debt'], 0, '.', ',') ?></td>
                        
                        <td>
                            <span class="<?= $model['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?php
                                $statusText = $model['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                echo $statusText;
                                ?>
                            </span>
                        </td>
                        <td data-order="<?= !empty($model['created_at']) ? strtotime($model['created_at']) : '' ?>">
                            <?= !empty($model['created_at']) ? Html::encode(date('d.m.Y H:i', strtotime($model['created_at']))) : 'N/A' ?>
                        </td>
                      
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <a href="<?= Url::to(['/backend/worker/view', 'id' => $model['id']]) ?>" class="dropdown-item">
                                        <?= Yii::t("app", "view") ?>
                                    </a>

                                    <?php if($model['deleted_at'] == NULL): ?>
                                        <a href="#" class="dropdown-item worker-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>
                                        <?php if ($model['deleted_at'] == NULL && $model['salary'] > 0): ?>
                                            <a href="#" class="dropdown-item worker-salary-change" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                                <?= Yii::t("app", "change_salary") ?>
                                            </a>
                                            <a href="#" class="dropdown-item worker-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model['id']) ?>">
                                                <span class="red-text"><?= Yii::t("app", "delete") ?></span>
                                            </a>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "add_worker") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "edit_worker") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "worker_delete") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "change_salary") ?>"></div>
<div id="five" data-text="<?= Yii::t("app", "view_salary") ?>"></div>


<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');
    var four = $('#four').attr('data-text');
    var five = $('#five').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#worker-grid-view')) {
            $('#worker-grid-view').DataTable().destroy();
        }
        
        $('#worker-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[8, 'desc']], 
            "columnDefs": [
                {
                    "targets": [7, 9], 
                    "orderable": false
                }
            ]
        });
    }

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            // Если это ссылка на просмотр, позволяем ей работать
            if (!$(this).hasClass('worker-update') && !$(this).hasClass('worker-delete') && !$(this).hasClass('worker-salary-change')) {
                return true;
            }
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }
    
    function initializeWorkerCreate() {
        $(document).off('click.worker-create').on('click.worker-create', '.worker-create', function() {
            $.ajax({
                url: '/backend/worker/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("worker-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.worker-create-button').on('click.worker-create-button', '.worker-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#worker-create-form').serialize();
                $.ajax({
                    url: '/backend/worker/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.message) {
                            button.prop('disabled', false);
                            $.pjax.reload({container: '#worker-grid-pjax'});
                            $('.close').trigger('click');
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeWorkerUpdate() {
        $(document).off('click.worker-update').on('click.worker-update', '.worker-update', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/worker/update',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(two);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("worker-update-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.worker-update-button').on('click.worker-update-button', '.worker-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#worker-update-form').serialize();
                $.ajax({
                    url: '/backend/worker/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $.pjax.reload({container: '#worker-grid-pjax'});
                            $('.close').trigger('click');
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeWorkerDelete() {
        $(document).off('click.worker-delete').on('click.worker-delete', '.worker-delete', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/worker/delete',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal-delete .modal-title').html(three);
                    $('#ideal-mini-modal-delete .modal-body').html(response.content);
                    $('#ideal-mini-modal-delete .mini-button').addClass("worker-delete-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.worker-delete-button').on('click.worker-delete-button', '.worker-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#worker-delete-form').serialize();
                $.ajax({
                    url: '/backend/worker/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            $.pjax.reload({container: '#worker-grid-pjax'});
                            $('.close').trigger('click');
                        }
                        button.prop('disabled', false);
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeWorkerSalaryChange() {
        $(document).off('click.worker-salary-change').on('click.worker-salary-change', '.worker-salary-change', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/worker/salary-change',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(four);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("worker-salary-change-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.worker-salary-change-button').on('click.worker-salary-change-button', '.worker-salary-change-button', function() {
            var form = $('#worker-salary-change-form').serialize();
            var button = $(this);
            button.prop('disabled', true);
            $.ajax({
                url: '/backend/worker/salary-change',
                type: 'POST',
                data: form,
                success: function(response) {
                    if (response.status === 'success') {
                        button.prop('disabled', false);
                        $('#ideal-mini-modal').modal('hide');
                        iziToast.success({
                            title: '',
                            message: response.message,
                            position: 'topRight'
                        });
                        $.pjax.reload({container: '#worker-grid-pjax'});
                    } else if (response.errors && response.errors.start_date) {
                        iziToast.error({
                            title: '',
                            message: response.errors.start_date[0],
                            position: 'topRight'
                        });
                        button.prop('disabled', false);
                    } else if (response.errors) {
                        $.each(response.errors, function(field, errors) {
                            $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                        });
                        button.prop('disabled', false);
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    button.prop('disabled', false);
                    iziToast.error({
                        title: '',
                        message: 'Error',
                        position: 'topRight'
                    });
                }
            });
        });
    }

   

    function initializeSelect2() {
        if ($.fn.select2) {
            $('.select2').select2({
                width: '100%',
                dropdownParent: $('#ideal-mini-modal')
            });
        }
    }

    function initializeAll() {
        initializeDataTable();
        initializeDropdown();
        initializeSelect2();
        initializeWorkerCreate();
        initializeWorkerUpdate();
        initializeWorkerDelete();
        initializeWorkerSalaryChange();
    }

    initializeAll();

    $(document).on('pjax:complete', function() {
        initializeAll();
    });

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
