<?php

use app\common\models\CashboxDetail;
use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\common\models\Cashbox;
use yii\helpers\Url;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "cashbox_detail");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$referar = Yii::$app->request->referrer;
?>

<style>
    .gap-2 {
        gap: 0.5rem !important;
    }
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6">
            <div class="d-flex justify-content-end align-items-center gap-2">
                <!-- Date inputs -->
                <div class="d-flex gap-2" style="min-width: 200px;">
                    <input type="date" id="start_date" class="form-control" value="<?= date('Y-m-d') ?>" placeholder="<?= Yii::t('app', 'From Date') ?>">
                    <input type="date" id="end_date" class="form-control" value="<?= date('Y-m-d') ?>" placeholder="<?= Yii::t('app', 'To Date') ?>">
                </div>

                <!-- Filter button -->
                <button type="button" class="btn btn-primary" id="search-button">
                    <?= Yii::t('app', 'search') ?>
                </button>

                <!-- Back button -->
                <a href="<?php echo $referar; ?>" class="btn btn-primary">
                    <?= Yii::t("app", "back_to_cashbox") ?>
                </a>
            </div>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'cashbox-detail-grid-pjax']); ?>
    <div id="grid-view-wrapper">
        <?php if($result): ?>
            <div>
                <table id="cashbox-detail-grid-view" class="table table-bordered table-striped compact">
                    <thead>
                        <th><?= Yii::t("app", "payment_type") ?></th>
                        <th><?= Yii::t("app", "added_user") ?></th>
                        <th><?= Yii::t("app", "balance") ?></th>
                        <th><?= Yii::t("app", "type_payment") ?></th>
                        <th><?= Yii::t("app", "payment_created_at") ?></th>
                    </thead>
                    <tbody>
                    <?php foreach ($result as $model): ?>
                        <tr>
                            <td><?= Html::encode($model['cashbox_name']) ?></td>
                            <td><?= Html::encode($model['add_user']) ?></td>
                            <td><?= Html::encode($model['amount']) ?></td>
                            <td><?= Html::encode(CashboxDetail::getPaymentTypeDescription($model['payment_type'])) ?></td>
                            <td><?= Html::encode(date('d.m.Y H:i', strtotime($model['created_at']))) ?></td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p><?= Yii::t('app', 'no_data_available') ?></p>
        <?php endif; ?>
    </div>
    <?php Pjax::end(); ?>
</div>

<?php
$searchUrl = Url::to(['cashbox/search', 'id' => $cashbox_id]);
$js = <<<JS
(function($) {
    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($('#cashbox-detail-grid-view').length && $('#cashbox-detail-grid-view tbody tr').length > 0) {
            if ($.fn.DataTable.isDataTable('#cashbox-detail-grid-view')) {
                $('#cashbox-detail-grid-view').DataTable().destroy();
            }
            
            $('#cashbox-detail-grid-view').DataTable({
                "language": {
                    "search": searchLabel,
                    "lengthMenu": lengthMenuLabel,
                    "zeroRecords": zeroRecordsLabel,
                    "info": infoLabel,
                    "infoEmpty": infoEmptyLabel,
                    "infoFiltered": infoFilteredLabel
                },
                "pageLength": 50,
                "order": [[4, 'desc']],
                "columnDefs": [
                    {
                        "targets": [2],
                        "orderable": false
                    }
                ]
            });
        }
    }

    function initializeAll() {
        initializeDataTable();
        initializeSelect2();
    }

    // Initialize everything on first load
    initializeAll();

    // Re-initialize after PJAX reloads
    $(document).on('pjax:success', function() {
        initializeAll();
    });

    $('#search-button').click(function() {
        var startDate = $('#start_date').val();
        var endDate = $('#end_date').val();

        $.ajax({
            url: '{$searchUrl}',
            type: 'POST',
            data: {
                start_date: startDate,
                end_date: endDate
            },
            success: function(response) {
                $('#grid-view-wrapper').html(response);
                initializeDataTable();
            }
        });
    });
   
    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
