<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%free_product_detail}}`.
 */
class m250901_000001_create_free_product_detail_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%free_product_detail}}', [
            'id' => $this->primaryKey(),
            'free_product_id' => $this->integer()->notNull(),
            'product_id' => $this->integer()->notNull(),
            'quantity' => $this->integer()->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-free_product_detail-free_product_id',
            '{{%free_product_detail}}',
            'free_product_id',
            '{{%free_products}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-free_product_detail-product_id',
            '{{%free_product_detail}}',
            'product_id',
            '{{%product}}',
            'id',
            'CASCADE'
        );

        // Изменяем таблицу free_products, делаем product_id необязательным
        $this->alterColumn('{{%free_products}}', 'product_id', $this->integer()->null());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-free_product_detail-free_product_id', '{{%free_product_detail}}');
        $this->dropForeignKey('fk-free_product_detail-product_id', '{{%free_product_detail}}');
        $this->dropTable('{{%free_product_detail}}');
    }
}
