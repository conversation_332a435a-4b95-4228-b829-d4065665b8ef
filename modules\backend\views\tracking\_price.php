<?php
use yii\helpers\Html;
use yii\widgets\ActiveForm;

$form = ActiveForm::begin([
    'id' => 'price-form',
    'enableAjaxValidation' => true,
]);

$currencyName = $invoice->supplier && $invoice->supplier->currency ? $invoice->supplier->currency->name : 'Default';
?>

    <?= Html::hiddenInput('invoice_id', $invoice->id) ?>
    
    <div>
        <?= Yii::t('app', 'currency') ?>: <strong><?= Html::encode($currencyName) ?></strong>
    </div>

    <?php foreach ($invoice->invoiceDetails as $detail): ?>
        <div class="form-group">
            <?= Html::label(
                $detail->material->name . ' (' . Yii::t('app', 'current_price') . ': ' . 
                ($detail->price ? Yii::$app->formatter->asDecimal($detail->price, 2) . ' ' . $currencyName : Yii::t('app', 'not_set')) . ')',
                'price-' . $detail->material_id, 
                ['class' => 'control-label']
            ) ?>
            <?= Html::input('number', "prices[{$detail->material_id}]", $detail->price, [
                'id' => 'price-' . $detail->material_id,
                'class' => 'form-control price-input', // добавляем класс для JS
                'step' => '0.01',
                'min' => '0'
            ]) ?>
        </div>
    <?php endforeach; ?>

<?php ActiveForm::end(); ?>

<!-- Добавляем JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const priceInputs = document.querySelectorAll('.price-input');
    
    priceInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            let value = this.value;
            
            // Удаляем плюс
            if (value.startsWith('+')) {
                value = value.substring(1);
            }
            
            // Запрещаем отрицательные числа
            if (value.startsWith('-')) {
                value = '';
            }

            if (value.startsWith('e')) {
                value = '';
            }
            
            // Если введён только 0 без десятичных, очищаем
            if (value === '0' && !value.includes('.')) {
                value = '';
            }
            
            // Ограничиваем до 2 знаков после точки
            if (value.includes('.')) {
                const parts = value.split('.');
                if (parts[1].length > 2) {
                    parts[1] = parts[1].substring(0, 2);
                    value = parts.join('.');
                }
            }
            
            // Убираем ведущие нули (кроме случая 0.)
            if (value.length > 1 && value.startsWith('0') && !value.startsWith('0.')) {
                value = value.replace(/^0+/, '');
            }
            
            // Устанавливаем очищенное значение
            this.value = value;
        });
        
        // Дополнительная проверка при потере фокуса
        input.addEventListener('blur', function(e) {
            if (this.value === '' || this.value === '.') {
                this.value = ''; // или другое значение по умолчанию
            }
        });
    });
});
</script>