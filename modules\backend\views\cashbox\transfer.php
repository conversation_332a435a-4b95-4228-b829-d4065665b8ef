<?php
use app\common\models\Cashbox;
use app\common\models\CurrencyCourse;
?>

<div class="cashbox-transfer-form">

    <form id="transfer-form">
        <input type="hidden" name="<?= Yii::$app->request->csrfParam ?>" value="<?= Yii::$app->request->csrfToken ?>">

        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="fromType"><?= Yii::t('app', 'from_cashbox') ?></label>
                    <select class="form-control select2" id="fromType" name="TransferForm[fromType]" required>
                        <option value=""><?= Yii::t('app', 'select') ?></option>
                        <?php foreach ($cashbox as $item): ?>
                            <option value="<?= $item->id ?>" 
                                    data-currency-id="<?= $item->currency_id ?>"
                                    data-currency-name="<?= $item->currency ? $item->currency->name : 'N/A' ?>"
                                    data-balance="<?= $item->balance ?>">
                                <?= $item->title ?> (<?= $item->currency ? $item->currency->name : 'Без валюты' ?>) - <?= number_format($item->balance, 2) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="help-block text-danger" id="fromType-error"></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="toType"><?= Yii::t('app', 'to_cashbox') ?></label>
                    <select class="form-control select2" id="toType" name="TransferForm[toType]" required>
                        <option value=""><?= Yii::t('app', 'select') ?></option>
                        <?php foreach ($cashbox as $item): ?>
                            <option value="<?= $item->id ?>"
                                    data-currency-id="<?= $item->currency_id ?>"
                                    data-currency-name="<?= $item->currency ? $item->currency->name : 'N/A' ?>"
                                    data-balance="<?= $item->balance ?>">
                                <?= $item->title ?> (<?= $item->currency ? $item->currency->name : 'Без валюты' ?>) - <?= number_format($item->balance, 2) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="help-block text-danger" id="toType-error"></div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="amount"><?= Yii::t('app', 'amount') ?></label>
                    <input type="text" class="form-control formatted-numeric-input" id="amount" name="TransferForm[amount]" 
                        required>
                    <div class="help-block text-danger" id="amount-error"></div>
                </div>
            </div>
        </div>

        <!-- Информация о конвертации -->
        <div id="conversion-info" class="row" style="display: none;">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h5><i class="fas fa-exchange-alt"></i> <?= Yii::t('app', 'currency_conversion') ?></h5>
                    <div id="conversion-details"></div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// Получаем курсы валют для JavaScript
<?php
$courses = [];
$allCourses = CurrencyCourse::find()
    ->where(['<=', 'start_date', date('Y-m-d')])
    ->andWhere([
        'or',
        ['>', 'end_date', date('Y-m-d')],
        ['end_date' => null]
    ])
    ->andWhere(['deleted_at' => null])
    ->all();

foreach ($allCourses as $course) {
    $courses[$course->currency_id] = $course->course;
}

// Убеждаемся что курс базовой валюты (доллар) присутствует
if (!isset($courses[1])) {
    $courses[1] = 1; // Доллар - базовая валюта
}
?>
window.currencyCourses = <?= json_encode($courses) ?>;

$(document).ready(function() {
    function updateConversionInfo() {
        var fromSelect = $('#fromType');
        var toSelect = $('#toType');
        var amountInput = $('#amount');
        
        var fromCurrencyId = fromSelect.find(':selected').data('currency-id');
        var toCurrencyId = toSelect.find(':selected').data('currency-id');
        var fromCurrencyName = fromSelect.find(':selected').data('currency-name');
        var toCurrencyName = toSelect.find(':selected').data('currency-name');
        
        // Правильно парсим сумму - убираем пробелы и заменяем запятую на точку
        var amountValue = amountInput.val() || '';
        var cleanAmount = amountValue.replace(/[\s\u00A0]/g, '').replace(',', '.');
        var amount = parseFloat(cleanAmount) || 0;
        
        if (!fromCurrencyId || !toCurrencyId || amount <= 0) {
            $('#conversion-info').hide();
            return;
        }
        
        if (fromCurrencyId == toCurrencyId) {
            $('#conversion-info').hide();
            return;
        }
        
        // Рассчитываем конвертацию
        var convertedAmount = calculateConversion(amount, fromCurrencyId, toCurrencyId);
        
        if (convertedAmount !== null) {
            var courseDisplay = getCourseDisplayText(fromCurrencyId, toCurrencyId, fromCurrencyName, toCurrencyName);
            
            var details = `
                <p><strong>Переводимая сумма:</strong> ${formatNumber(amount)} ${fromCurrencyName}</p>
                <p><strong>Будет зачислено:</strong> ${formatNumber(convertedAmount.amount)} ${toCurrencyName}</p>
                <p><strong>Курс:</strong> ${courseDisplay}</p>
            `;
            
            $('#conversion-details').html(details);
            $('#conversion-info').show();
        } else {
            $('#conversion-details').html('<p class="text-danger">Курс валюты не найден</p>');
            $('#conversion-info').show();
        }
    }
    
    function calculateConversion(amount, fromCurrencyId, toCurrencyId) {
        var baseCurrencyId = 1; // ID долларов - базовая валюта
        
        if (fromCurrencyId == toCurrencyId) {
            return { amount: amount, rate: 1 };
        }
        
        if (fromCurrencyId == baseCurrencyId) {
            // Из долларов в другую валюту
            var course = window.currencyCourses[toCurrencyId];
            if (!course) return null;
            return { amount: amount * course, rate: course };
        } else if (toCurrencyId == baseCurrencyId) {
            // Из другой валюты в доллары
            var course = window.currencyCourses[fromCurrencyId];
            if (!course) return null;
            return { amount: amount / course, rate: 1 / course };
        } else {
            // Между двумя не-долларовыми валютами
            var fromCourse = window.currencyCourses[fromCurrencyId];
            var toCourse = window.currencyCourses[toCurrencyId];
            
            var amountInDollars = amount / fromCourse;
            var finalAmount = amountInDollars * toCourse;
            return { amount: finalAmount, rate: toCourse / fromCourse };
        }
    }
    
    function formatNumber(num) {
        return Number(num).toLocaleString('ru-RU', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    function getCourseDisplayText(fromCurrencyId, toCurrencyId, fromCurrencyName, toCurrencyName) {
        var baseCurrencyId = 1; // Доллар
        
        if (fromCurrencyId == baseCurrencyId) {
            // Из долларов в другую валюту
            var course = window.currencyCourses[toCurrencyId];
            return `1 ${fromCurrencyName} = ${formatNumber(course)} ${toCurrencyName}`;
        } else if (toCurrencyId == baseCurrencyId) {
            // Из другой валюты в доллары  
            var course = window.currencyCourses[fromCurrencyId];
            return `1 ${toCurrencyName} = ${formatNumber(course)} ${fromCurrencyName}`;
        } else {
            // Между двумя не-долларовыми валютами
            var fromCourse = window.currencyCourses[fromCurrencyId];
            var toCourse = window.currencyCourses[toCurrencyId];
            var rate = toCourse / fromCourse;
            return `1 ${fromCurrencyName} = ${formatNumber(rate)} ${toCurrencyName}`;
        }
    }
    
    // Обновляем информацию при изменении полей
    $('#fromType, #toType, #amount').on('change keyup', updateConversionInfo);
});
</script>