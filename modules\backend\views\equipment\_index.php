<?php
use yii\helpers\Html;
?>

<table id="equipment-grid-view" class="table table-bordered table-striped compact">
    <thead>
        <th><?= Yii::t("app", "Photo") ?></th>
        <th><?= Yii::t("app", "Name") ?></th>
        <th><?= Yii::t("app", "Description") ?></th>
        <th><?= Yii::t("app", "Status") ?></th>
        <th><?= Yii::t("app", "Purchase Date") ?></th>
        <th><?= Yii::t("app", "actions") ?></th>
    </thead>
    <tbody>    <?php if (!empty($result)): ?>
        <?php foreach ($result as $model): ?>
            <?php 
            // Получаем модель оборудования с фотографиями
            $equipment = \app\common\models\Equipment::findOne($model['id']);
            $mainPhoto = $equipment ? $equipment->mainPhoto : null;
            ?>
            <tr class="equipment-row" data-equipment-id="<?= $model['id'] ?>" style="cursor: pointer;">
                <td>
                    <?php if ($mainPhoto): ?>
                        <img src="/uploads/equipment/<?= Html::encode($mainPhoto->photo_path) ?>"
                             alt="Equipment"
                             style="max-width: 50px; cursor: pointer;"
                             class="equipment-photo"
                             data-id="<?= $model['id'] ?>">
                    <?php elseif ($model['photo']): ?>
                        <!-- Fallback для старых записей -->
                        <img src="/uploads/equipment/<?= Html::encode($model['photo']) ?>"
                             alt="Equipment"
                             style="max-width: 50px; cursor: pointer;"
                             class="equipment-photo"
                             data-id="<?= $model['id'] ?>">                    <?php else: ?>
                        <img src="/images/no-image.svg" 
                             alt="No image" 
                             style="max-width: 50px; opacity: 0.5;">
                    <?php endif; ?>
                </td>
                <td><?= Html::encode($model['name']) ?></td>
                <td><?= Html::encode($model['description']) ?></td>
                <td>
                    <span class="badge badge-<?= $model['status'] == 0 ? 'danger' : ($model['status'] == 1 ? 'success' : 'warning') ?>">
                        <?= $model['status'] == 0 ? Yii::t("app", "inactive") : ($model['status'] == 1 ? Yii::t("app", "active") : Yii::t("app", "repair")) ?>
                    </span>
                </td>
                <td data-order="<?= strtotime($model['purchase_date']) ?>">
                    <?= Yii::$app->formatter->asDate($model['purchase_date']) ?>
                </td>
                <td>
                    <div class="dropdown d-inline">
                        <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                            <?php echo Yii::t("app", "detail"); ?>
                        </a>
                        <div class="dropdown-menu">
                            <?php if ($model['deleted_at'] == NULL): ?>
                                <a href="#" class="dropdown-item equipment-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                    <?= Yii::t("app", "edit") ?>
                                </a>
                                <a href="#" class="dropdown-item equipment-change-status" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                    <?= Yii::t("app", "change_status") ?>
                                </a>

                                <a href="<?= \yii\helpers\Url::to(['/backend/equipment/' . $model['id'] . '/parts']) ?>" class="dropdown-item">
                                    <?= Yii::t("app", "parts") ?>
                                </a>

                                <?php if ($model['status'] == 1): ?>
                                <a href="#" class="dropdown-item equipment-decommission" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                    <?= Yii::t("app", "decommission") ?>
                                </a>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </td>
            </tr>
        <?php endforeach; ?>
    <?php endif; ?>
    </tbody>
</table>
