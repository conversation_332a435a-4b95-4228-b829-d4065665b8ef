<?php

namespace app\common\models;

use app\modules\backend\models\Region;
use app\modules\backend\models\Users;
use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "security_records".
 *
 * @property int $id
 * @property int|null $region_id
 * @property string $car_number
 * @property string $driver_full_name
 * @property string|null $image
 * @property int $add_user_id
 * @property int|null $accepted_user_id
 * @property string|null $accepted_at
 * @property string $created_at
 * @property string|null $deleted_at
 * @property string|null $description
 *
 * @property Users $addUser
 * @property Users $acceptedUser
 * @property \app\modules\backend\models\Region $region
 */
class SecurityRecord extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'security_records';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['car_number', 'driver_full_name'], 'required'],
            [['region_id', 'add_user_id', 'accepted_user_id'], 'integer'],
            [['accepted_at', 'created_at', 'deleted_at'], 'safe'],
            [['car_number'], 'string', 'max' => 20],
            [['driver_full_name', 'image', 'description'], 'string', 'max' => 255],
            [['add_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['add_user_id' => 'id']],
            [['accepted_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['accepted_user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'region_id' => 'Регион',
            'car_number' => 'Номер машины',
            'driver_full_name' => 'ФИО водителя',
            'image' => 'Изображение',
            'add_user_id' => 'Добавил',
            'accepted_user_id' => 'Подтвердил',
            'accepted_at' => 'Время подтверждения',
            'created_at' => 'Создано',
            'deleted_at' => 'Удалено',
        ];
    }

    /**
     * Gets query for [[AddUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAddUser()
    {
        return $this->hasOne(Users::class, ['id' => 'add_user_id']);
    }

    /**
     * Gets query for [[AcceptedUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAcceptedUser()
    {
        return $this->hasOne(Users::class, ['id' => 'accepted_user_id']);
    }

    /**
     * Gets query for [[Client]].   
     *
     * @return \yii\db\ActiveQuery
     */
    public function getRegion()
    {
        return $this->hasOne(Region::class, ['id' => 'region_id']);
    }
   
}
