<?php

namespace app\modules\backend\controllers;

use app\common\models\ClientDriver;
use Yii;

class ClientDriverController extends \yii\web\Controller
{
    public function actionCreate()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        
        $model = new ClientDriver();
        
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return [
                'success' => true, 
                'id' => $model->id,
                'message' => 'Водитель успешно добавлен'
            ];
        }
        
        return [
            'success' => false, 
            'message' => 'Ошибка при сохранении: ' . json_encode($model->errors)
        ];
    }

    public function actionUpdate()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        
        $id = Yii::$app->request->post('id');
        
        if (!is_numeric($id)) {
            return [
                'success' => false,
                'message' => 'Неверный ID водителя'
            ];
        }
        
        $model = ClientDriver::findOne(['id' => (int)$id, 'deleted_at' => null]);
        if (!$model) {
            return [
                'success' => false,
                'message' => 'Водитель не найден'
            ];
        }

        // Получаем данные из POST
        $postData = Yii::$app->request->post('ClientDriver', []);
        
        // Проверяем обязательные поля
        if (empty($postData['driver'])) {
            return [
                'success' => false,
                'message' => 'Имя водителя обязательно для заполнения'
            ];
        }

        // Устанавливаем значения
        $model->driver = $postData['driver'];
        $model->car_number = $postData['car_number'] ?? null;
        
        if ($model->save()) {
            return [
                'success' => true, 
                'id' => $model->id
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Ошибка при обновлении: ' . json_encode($model->errors)
        ];
    }

    public function actionDelete()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        
        $id = Yii::$app->request->post('id');
        
        // Проверяем, что ID является числом
        if (!is_numeric($id)) {
            return [
                'success' => false,
                'message' => 'Неверный ID водителя'
            ];
        }
        
        $model = ClientDriver::findOne(['id' => (int)$id, 'deleted_at' => null]);
        
        if (!$model) {
            return [
                'success' => false,
                'message' => 'Водитель не найден'
            ];
        }
        
        $model->deleted_at = date('Y-m-d H:i:s');
        
        if ($model->save()) {
            return [
                'success' => true,
                'message' => 'Водитель успешно удален'
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Ошибка при удалении: ' . json_encode($model->errors)
        ];
    }
}