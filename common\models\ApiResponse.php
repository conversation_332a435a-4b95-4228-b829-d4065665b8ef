<?php

namespace app\common\models;
use Yii;

class ApiResponse
{
    const HTTP_OK = 200;
    const HTTP_NO_CONTENT = 204;

    const HTTP_BAD_REQUEST = 400;
    const HTTP_UNAUTHORIZED = 401;
    const HTTP_UNPROCESSABLE_ENTITY = 422;

    const HTTP_NOT_FOUND = 404;

    const HTTP_METHOD_NOT_ALLOWED = 405;

    const HTTP_INTERNAL_SERVER_ERROR = 500;

    protected static array $defaultMessages = [
        self::HTTP_OK => 'Request successful',
        self::HTTP_NO_CONTENT => 'No content available',
        self::HTTP_BAD_REQUEST => 'Bad request',
        self::HTTP_UNAUTHORIZED => 'Unauthorized access',
        self::HTTP_NOT_FOUND => 'Resource not found',
        self::HTTP_UNPROCESSABLE_ENTITY => 'Validation failed',
        self::HTTP_INTERNAL_SERVER_ERROR => 'Internal server error'
    ];

    public static function response(
        string|array $message = '',
        $data = null,
        int $code = self::HTTP_OK
    ): array {
        Yii::$app->response->statusCode = $code;
        http_response_code($code); 
    
        return [
            'message' => $message ?: self::$defaultMessages[$code] ?? 'Unknown error',
            'data' => $data ?? (object)[],
            'code' => $code
        ];
    }
    
}
