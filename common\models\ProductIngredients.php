<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "product_ingredients".
 *
 * @property int $id
 * @property int $product_id
 * @property int $material_id
 * @property bool $is_alternative
 * @property string|null $start_date
 * @property string|null $end_date
 */
class ProductIngredients extends \yii\db\ActiveRecord
{
    public $material_ids = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product_ingredients';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['product_id', 'material_id'], 'required'],
            [['product_id', 'material_id'], 'integer'],
            [['is_alternative'], 'boolean'],
            [['is_alternative'], 'default', 'value' => false],
            [['start_date', 'end_date'], 'safe'],
            ['material_ids', 'safe'],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
            [['material_id'], 'exist', 'skipOnError' => true, 'targetClass' => Material::class, 'targetAttribute' => ['material_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'product_id' => Yii::t('app', 'Product'),
            'material_id' => Yii::t('app', 'Material'),
            'material_ids' => Yii::t('app', 'Materials'),
            'is_alternative' => Yii::t('app', 'Alternative Ingredient'),
            'start_date' => Yii::t('app', 'Start Date'),
            'end_date' => Yii::t('app', 'End Date'),
        ];
    }

    /**
     * Gets query for [[Product]]
     */
    public function getProduct()
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }

    /**
     * Gets query for [[Material]]
     */
    public function getMaterial()
    {
        return $this->hasOne(Material::class, ['id' => 'material_id']);
    }

    /**
     * Получает альтернативный материал для продукта
     *
     * @param int $productId ID продукта
     * @param int $materialId ID основного материала
     * @param int $requiredQuantity Требуемое количество материала
     * @return int|null ID альтернативного материала или null, если альтернатива не найдена
     */
    public static function getAlternativeMaterial($productId, $materialId, $requiredQuantity = 1)
    {
        // Получаем категорию основного материала
        $originalMaterial = Material::findOne($materialId);
        if (!$originalMaterial) {
            return null;
        }

        $originalCategoryId = $originalMaterial->category_id;
        $originalUnitType = $originalMaterial->unit_type;

        // Получаем все альтернативные материалы для продукта
        $alternativeMaterials = self::find()
            ->select(['pi.material_id'])
            ->from('product_ingredients pi')
            ->join('JOIN', 'material m', 'm.id = pi.material_id')
            ->where([
                'pi.product_id' => $productId,
                'pi.is_alternative' => true,
                'pi.end_date' => '9999-12-31'
            ])
            ->andWhere(['m.deleted_at' => null])
            ->column();

        if (empty($alternativeMaterials)) {
            return null;
        }

        // Проверяем наличие альтернативных материалов в производстве
        foreach ($alternativeMaterials as $altMaterialId) {
            // Проверяем, что альтернативный материал имеет ту же категорию и тип единиц измерения
            $altMaterial = Material::findOne($altMaterialId);
            if (!$altMaterial) {
                continue;
            }

            // Проверяем совпадение категории (если категория указана)
            if ($originalCategoryId && $altMaterial->category_id != $originalCategoryId) {
                continue;
            }

            // Проверяем совпадение типа единиц измерения
            if ($altMaterial->unit_type != $originalUnitType) {
                continue;
            }

            $materialProductions = MaterialProduction::find()
                ->where([
                    'material_id' => $altMaterialId,
                    'deleted_at' => null
                ])
                ->andWhere(['>', 'quantity', 0])
                ->all();

            $totalAvailable = array_sum(array_column($materialProductions, 'quantity'));

            // Если альтернативный материал доступен в достаточном количестве, возвращаем его ID
            if ($totalAvailable >= $requiredQuantity) {
                return $altMaterialId;
            }
        }

        return null;
    }
}
