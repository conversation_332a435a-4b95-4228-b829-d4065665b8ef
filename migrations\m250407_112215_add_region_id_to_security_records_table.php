<?php

use yii\db\Migration;

class m250407_112215_add_region_id_to_security_records_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {

        $this->addColumn('security_records', 'region_id', $this->integer()->null());
        $this->addForeignKey(
            'fk-security_records-region_id',
            'security_records',
            'region_id',
            'region',
            'id',
            'SET NULL'
        );

        $this->dropColumn('security_records', 'client_id'   );

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('security_records', 'region_id');
        $this->addColumn('security_records', 'client_id', $this->integer()->null());
    }

   
}
