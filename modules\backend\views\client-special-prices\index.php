<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\assets\Select2Asset;

DataTablesAsset::register($this);
Select2Asset::register($this);

$this->title = Yii::t('app', 'special_prices');
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");
$next = Yii::t("app", "next");
$previous = Yii::t("app", "previous");

?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('product_keeper')): ?>
                <a href="#" class="btn btn-primary special-price-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "create_special_price") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'special-price-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="special-price-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "client") ?></th>
                        <th><?= Yii::t("app", "product") ?></th>
                        <th><?= Yii::t("app", "special_price") ?></th>
                        <th><?= Yii::t("app", "sell_price") ?></th>
                        <th><?= Yii::t("app", "created_at") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                        <td><?= Html::encode($model['client_name']) ?></td>
                        <td><?= Html::encode($model['product_name']) ?></td>
                        <td><?= Yii::$app->formatter->asDecimal($model['special_price'], 2) ?></td>
                        <td><?= Yii::$app->formatter->asDecimal($model['sell_price'], 2) ?></td>
                        <td><?= date('d.m.Y H:i', strtotime($model['created_at'])) ?></td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <a href="#" class=" dropdown-item special-price-detail" data-toggle="modal" data-target="#ideal-large-modal-without-save" data-id="<?= Html::encode($model['id']) ?>">
                                        <?= Yii::t("app", "view") ?>
                                    </a>

                                    <?php if(empty($model['deleted_at'])): ?>
                                        <a href="#" class="dropdown-item special-price-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>

                                        <a href="#" class="dropdown-item special-price-delete" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "delete") ?>
                                        </a>

                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "create_special_price") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "update_special_price") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "delete_special_price") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "detail_special_price") ?>"></div>

<?php
$js = <<<JS
(function($) {
    // Исправление проблемы с фокусом Select2 в Bootstrap модалях
    $.fn.modal.Constructor.prototype.enforceFocus = function() {};
    
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');
    var four = $('#four').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";
    var next = "{$next}";
    var previous = "{$previous}";
    var all = "{$all}";
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#special-price-grid-view')) {
            $('#special-price-grid-view').DataTable().destroy();
        }
        
        $('#special-price-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel,
                "next": next,
                "previous": previous
            },
            "pageLength": 50,
            "order": [[4, 'desc']],
            "stateSave": true,
            "stateDuration": 0,
            "stateLoadParams": function (settings, data) {
                // Восстанавливаем поиск из localStorage
                var savedSearch = localStorage.getItem('specialPriceGridSearch');
                if (savedSearch) {
                    data.search.search = savedSearch;
                }
            },
            "stateSaveParams": function (settings, data) {
                // Сохраняем поиск в localStorage
                localStorage.setItem('specialPriceGridSearch', data.search.search);
            },
            "columnDefs": [
                {
                    "targets": [5], 
                    "orderable": false
                },
                {
                    "targets": 4,
                    "type": "date-eu",
                    "render": function(data, type, row) {
                        if (type === 'sort') {
                            var parts = data.split(' ');
                            var dateParts = parts[0].split('.');
                            return dateParts[2] + dateParts[1] + dateParts[0] + parts[1].replace(':', '');
                        }
                        return data;
                    }
                }
            ]
        });
    }

    $(document).off('pjax:end').on('pjax:end', function() {
        initializeDataTable();
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.stopPropagation();
            if (!$(this).attr('href') || $(this).attr('href') === '#') {
                e.preventDefault();
            }
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            dropdownParent: $('#ideal-mini-modal'),
            placeholder: function() {
                return $(this).find('option:first').text();
            },
            allowClear: true,
            language: {
                noResults: function() {
                    return "Ничего не найдено";
                },
                searching: function() {
                    return "Поиск...";
                },
                inputTooShort: function(args) {
                    return "Введите " + (args.minimum - args.input.length) + " или более символов";
                }
            }
        });
    }

    function initializeSpecialPriceCreate() {
        $(document).off('click.special-price-create').on('click.special-price-create', '.special-price-create', function() {
            $.ajax({
                url: '/backend/client-special-prices/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("special-price-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.special-price-create-button').on('click.special-price-create-button', '.special-price-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#special-price-form').serialize();
                $.ajax({
                    url: '/backend/client-special-prices/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#special-price-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'fail') {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeSpecialPriceUpdate() {
        $(document).off('click.special-price-update').on('click.special-price-update', '.special-price-update', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/client-special-prices/update',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(two);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("special-price-update-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.special-price-update-button').on('click.special-price-update-button', '.special-price-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#special-price-form').serialize();
                $.ajax({
                    url: '/backend/client-special-prices/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#special-price-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'fail' ) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeSpecialPriceDelete() {
        $(document).off('click.special-price-delete').on('click.special-price-delete', '.special-price-delete', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/client-special-prices/delete',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(three);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("special-price-delete-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.special-price-delete-button').on('click.special-price-delete-button', '.special-price-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#special-prices-delete-form').serialize();
                $.ajax({
                    url: '/backend/client-special-prices/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#special-price-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'fail' ) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeViewDetail() {
        $(document).off('click.special-price-detail').on('click.special-price-detail', '.special-price-detail', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/client-special-prices/detail',
                dataType: 'json',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    if (response && response.status === 'success') {
                        $('#ideal-large-modal-without-save .modal-title').html(four);
                        $('#ideal-large-modal-without-save .modal-body').html(response.content);
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });
    }

    
    // Инициализация всех компонентов
    initializeDataTable();
    initializeDropdown();
    initializeSpecialPriceCreate();
    initializeSpecialPriceUpdate();
    initializeSpecialPriceDelete();
    initializeViewDetail();

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
