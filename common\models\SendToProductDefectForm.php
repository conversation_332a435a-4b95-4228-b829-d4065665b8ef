<?php

namespace app\common\models;

use yii\base\Model;

class SendToProductDefectForm extends Model
{
    public $products;
    public $description;

    public function rules()
    {
        return [
            [['products'], 'required', 'message' => 'Необходимо указать продукты'],
            [['description'], 'string'],
            [['products'], 'validateProducts'],
        ];
    }

    public function validateProducts($attribute, $params)
    {
        if (!is_array($this->products)) {
            $this->addError($attribute, 'Неверный формат данных продуктов');
            return;
        }

        foreach ($this->products as $product) {
            if (!isset($product['product_id']) || !isset($product['quantity'])) {
                $this->addError($attribute, 'Для каждого продукта необходимо указать ID и количество');
                return;
            }

            if (!is_numeric($product['product_id']) || $product['product_id'] <= 0) {
                $this->addError($attribute, 'ID продукта должен быть положительным числом');
                return;
            }

            if (!is_numeric($product['quantity']) || $product['quantity'] <= 0) {
                $this->addError($attribute, 'Количество должно быть положительным числом');
                return;
            }
        }
    }
}
