<?php

namespace app\modules\backend\controllers;

use yii\web\Controller;
use app\components\services\ReportService;
use app\modules\backend\services\report\FinanceReportService;
use app\modules\backend\services\report\ProductStockReportService;
use app\modules\backend\services\report\MaterialStockReportService;
use app\modules\backend\services\report\ProductionReportService;
use app\modules\backend\services\report\ClientDebtReportService;
use app\modules\backend\services\report\SupplierBalanceReportService;
use app\common\models\Product;
use app\common\models\Material;
use app\common\models\MaterialCategory;
use app\modules\backend\models\Region;
use yii\helpers\ArrayHelper;
use yii\db\Query;
use Yii;
use app\common\models\Cashbox;
use app\modules\backend\services\report\CashboxReportService;
use app\modules\backend\services\report\ProductSalesReportService;

class ReportController extends Controller
{
    /**
     * Главная страница отчетов с табами
     *
     * @return string
     */
    public function actionIndex()
    {
        return $this->render('index');
    }

    /**
     * Отчет по продажам
     *
     * @return string
     */
    public function actionSales()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-d'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        $productId = $request->get('product_id');
        $status = $request->get('status');

        $reportService = new ReportService();
        $reportData = $reportService->getSalesReport($startDate, $endDate, $productId, $status);

        return $this->render('sales', [
            'reportData' => $reportData,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'productId' => $productId,
            'status' => $status,
        ]);
    }

    /**
     * Отчет по финансам (расходы/доходы)
     *
     * @return string
     */
    public function actionFinance()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-d')); // Сегодняшний день
        $endDate = $request->get('end_date', date('Y-m-d'));     // Сегодняшний день

        $reportService = new FinanceReportService();
        $reportData = $reportService->getFinanceReport($startDate, $endDate);

        return $this->render('finance', [
            'reportData' => $reportData,
            'startDate' => $startDate,
            'endDate' => $endDate,
        ]);
    }

    /**
     * Отчет по остаткам продукции
     *
     * @return string
     */
    public function actionProductStock()
    {
        $request = Yii::$app->request;
        $productId = $request->get('product_id');

        $reportService = new ProductStockReportService();
        $reportData = $reportService->getProductStockReport($productId);

        $products = ArrayHelper::map(Product::find()->where(['IS', 'deleted_at', null])->all(), 'id', 'name');

        return $this->render('product-stock', [
            'reportData' => $reportData,
            'productId' => $productId,
            'products' => $products,
        ]);
    }
    /**
     * Отчет по остаткам сырья
     *
     * @return string
     */
    public function actionMaterialStock()
    {


        try {
            $reportService = new MaterialStockReportService();
            $reportData = $reportService->getMaterialStockReport();

            $materials = ArrayHelper::map(Material::find()->where(['IS', 'deleted_at', null])->all(), 'id', 'name');


            return $this->render('material-stock', [
                'reportData' => $reportData,
                'materials' => $materials
            ]);
        } catch (\Exception $e) {
            return $this->render('material-stock', [
                'reportData' => ['items' => []],
                'materials' => [],
            ]);
        }
    }

    /**
     * Отчет по производству - сегодняшние выпущенные продукты
     *
     * @return string
     */
    public function actionProduction()
    {
        $request = Yii::$app->request;
        $date = $request->get('date', date('Y-m-d')); // Только сегодняшний день
        $productId = $request->get('product_id');

        $reportService = new ProductionReportService();
        $reportData = $reportService->getTodayProductionReport($date, $productId);

        $products = ArrayHelper::map(Product::find()->where(['IS', 'deleted_at', null])->all(), 'id', 'name');

        return $this->render('production', [
            'reportData' => $reportData,
            'date' => $date,
            'productId' => $productId,
            'products' => $products,
        ]);
    }

    /**
     * Отчет по бесплатным продуктам
     *
     * @return string
     */
    public function actionFreeProducts()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-d'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        $productId = $request->get('product_id');

        $reportService = new ReportService();
        $reportData = $reportService->getFreeProductsReport($startDate, $endDate, $productId);

        $products = ArrayHelper::map(Product::find()->where(['IS', 'deleted_at', null])->all(), 'id', 'name');

        return $this->render('free-products', [
            'reportData' => $reportData,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'productId' => $productId,
            'products' => $products,
        ]);
    }

    /**
     * Отчет по задолженностям клиентов
     *
     * @return string
     */
    public function actionDebt()
    {
        $request = Yii::$app->request;
        // Отчет формируем только по должникам
        $type = 'debtors';
        $regionId = $request->get('region_id');

        $reportService = new ClientDebtReportService();
        $reportData = $reportService->getClientDebtReport($type, $regionId);

        $regions = ArrayHelper::map(
            Region::find()->where(['IS', 'deleted_at', null])->all(),
            'id',
            'name'
        );

        return $this->render('debt', [
            'reportData' => $reportData,
            'type' => $type,
            'regionId' => $regionId,
            'regions' => $regions,
        ]);
    }

    /**
     * Отчет по балансам поставщиков
     * @return string
     */
    public function actionSupplierBalance()
    {
        $reportService = new SupplierBalanceReportService();
        $reportData = $reportService->getSupplierBalanceReport();

        return $this->render('supplier-balance', [
            'reportData' => $reportData,
        ]);
    }

    /**
     * Отчет по кассе
     *
     * @return string
     */
    public function actionCashbox()
    {
        $request    = Yii::$app->request;
        $startDate  = $request->get('start_date', date('Y-m-d'));
        $endDate    = $request->get('end_date', date('Y-m-d'));
        $cashboxId  = $request->get('cashbox_id');

        $reportService = new CashboxReportService();
        $reportData    = $reportService->getCashboxReport($startDate, $endDate, $cashboxId);

        $cashboxes = ArrayHelper::map(
            Cashbox::find()->where(['IS', 'deleted_at', null])->all(),
            'id',
            'title'
        );

        return $this->render('cashbox', [
            'reportData' => $reportData,
            'startDate'  => $startDate,
            'endDate'    => $endDate,
            'cashboxId'  => $cashboxId,
            'cashboxes'  => $cashboxes,
        ]);
    }

    /**
     * Отчет охранника
     *
     * @return string
     */
    public function actionSecurity()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-d'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        $regionId = $request->get('region_id');
        $status = $request->get('status'); // pending, accepted
        $page = max(1, (int)$request->get('page', 1));
        $pageSize = 20; // Количество записей на странице

        // Получаем данные из security_records без сложного JOIN
        $query = new Query();
        $query->select([
            'sr.id',
            'sr.car_number',
            'sr.driver_full_name',
            'sr.description',
            'sr.created_at',
            'sr.accepted_at',
            'sr.image',
            'u1.username as add_user_name',
            'u2.username as accepted_user_name',
            'r.name as region_name'
        ])
            ->from(['sr' => 'security_records'])
            ->leftJoin(['u1' => 'users'], 'sr.add_user_id = u1.id')
            ->leftJoin(['u2' => 'users'], 'sr.accepted_user_id = u2.id')
            ->leftJoin(['r' => 'region'], 'sr.region_id = r.id')
            ->where(['IS', 'sr.deleted_at', null]);

        // Фильтры по дате
        if ($startDate) {
            $query->andWhere(['>=', 'sr.created_at', $startDate . ' 00:00:00']);
        }
        if ($endDate) {
            $query->andWhere(['<=', 'sr.created_at', $endDate . ' 23:59:59']);
        }

        // Фильтр по региону
        if ($regionId) {
            $query->andWhere(['sr.region_id' => $regionId]);
        }

        // Фильтр по статусу
        if ($status === 'pending') {
            $query->andWhere(['IS', 'sr.accepted_at', null]);
        } elseif ($status === 'accepted') {
            $query->andWhere(['IS NOT', 'sr.accepted_at', null]);
        }

        $query->orderBy(['sr.created_at' => SORT_DESC]);

        // Получаем общее количество записей для пагинации
        $totalCount = $query->count();

        // Применяем пагинацию
        $offset = ($page - 1) * $pageSize;
        $items = $query->limit($pageSize)->offset($offset)->all();
        
        // Получаем список водителей для поиска соответствующих счетов
        $driverNames = [];
        foreach ($items as $item) {
            if (!empty($item['driver_full_name']) && strlen(trim($item['driver_full_name'])) >= 3) {
                $driverNames[] = $item['driver_full_name'];
            }
        }
        
        // Если есть водители, ищем соответствующие счета
        $invoiceData = [];
        if (!empty($driverNames)) {
            $salesQuery = new Query();
            $salesQuery->select(['id', 'driver', 'total_sum', 'created_at'])
                ->from('sales')
                ->where(['IS', 'deleted_at', null])
                ->andWhere(['IS NOT', 'driver', null]);
            
            // Создаем условия для поиска по именам водителей
            $orConditions = ['or'];
            foreach ($driverNames as $driverName) {
                $driverName = trim($driverName);
                if (strlen($driverName) >= 5) {
                    // Для длинных имен используем LIKE
                    $orConditions[] = ['like', 'UPPER(TRIM(driver))', strtoupper($driverName)];
                    $orConditions[] = ['like', 'UPPER(TRIM(driver))', '%' . strtoupper($driverName) . '%'];
                } else {
                    // Для коротких имен только точное совпадение
                    $orConditions[] = ['=', 'UPPER(TRIM(driver))', strtoupper($driverName)];
                }
            }
            
            $salesQuery->andWhere($orConditions);
            $salesQuery->orderBy(['id' => SORT_DESC]);
            $sales = $salesQuery->all();
            
            // Создаем ассоциативный массив счетов по имени водителя
            foreach ($sales as $sale) {
                $driverKey = strtoupper(trim($sale['driver']));
                if (!isset($invoiceData[$driverKey])) {
                    $invoiceData[$driverKey] = [
                        'id' => $sale['id'],
                        'total_sum' => $sale['total_sum'],
                        'created_at' => $sale['created_at']
                    ];
                }
            }
        }
        
        // Добавляем данные о счетах к записям
        foreach ($items as &$item) {
            $item['invoice_id'] = null;
            $item['invoice_total'] = null;
            $item['invoice_date'] = null;
            
            if (!empty($item['driver_full_name'])) {
                $driverKey = strtoupper(trim($item['driver_full_name']));
                
                // Проверяем точное совпадение
                if (isset($invoiceData[$driverKey])) {
                    $item['invoice_id'] = $invoiceData[$driverKey]['id'];
                    $item['invoice_total'] = $invoiceData[$driverKey]['total_sum'];
                    $item['invoice_date'] = $invoiceData[$driverKey]['created_at'];
                    continue;
                }
                
                // Проверяем частичное совпадение для длинных имен
                if (strlen($driverKey) >= 5) {
                    foreach ($invoiceData as $key => $data) {
                        if (strpos($key, $driverKey) !== false || strpos($driverKey, $key) !== false) {
                            $item['invoice_id'] = $data['id'];
                            $item['invoice_total'] = $data['total_sum'];
                            $item['invoice_date'] = $data['created_at'];
                            break;
                        }
                    }
                }
            }
        }

        // Создаем объект пагинации
        $pagination = new \yii\data\Pagination([
            'totalCount' => $totalCount,
            'pageSize' => $pageSize,
            'page' => $page - 1,
            'defaultPageSize' => $pageSize,
            'forcePageParam' => true,
            'pageSizeParam' => false, // Отключаем параметр pageSize в URL
        ]);
        
        // Добавим отладочную информацию
        Yii::info("Total records: {$totalCount}, Page size: {$pageSize}, Current page: {$page}, Total pages: " . ceil($totalCount / $pageSize), 'pagination');
        
        // Оптимизируем подсчет статусов - делаем один запрос вместо двух
        $statusCounts = (new Query())
            ->select([
                'SUM(CASE WHEN accepted_at IS NULL THEN 1 ELSE 0 END) as pending_count',
                'SUM(CASE WHEN accepted_at IS NOT NULL THEN 1 ELSE 0 END) as accepted_count'
            ])
            ->from('security_records')
            ->where(['IS', 'deleted_at', null])
            ->one();

        $reportData = [
            'items' => $items,
            'total_count' => $totalCount,
            'pending_count' => (int)$statusCounts['pending_count'],
            'accepted_count' => (int)$statusCounts['accepted_count']
        ];

        $regions = ArrayHelper::map(
            Region::find()->where(['IS', 'deleted_at', null])->all(),
            'id',
            'name'
        );

        return $this->render('security', [
            'reportData' => $reportData,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'regionId' => $regionId,
            'status' => $status,
            'regions' => $regions,
            'pagination' => $pagination,
        ]);
    }

    /**
     * Отчет по продажам продуктов по периодам
     *
     * @return string
     */
    public function actionProductSales()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-d')); // Начальная дата
        $endDate = $request->get('end_date', date('Y-m-d')); // Конечная дата
        $startTime = $request->get('start_time', '00:00'); // Начальное время
        $endTime = $request->get('end_time', '23:59'); // Конечное время
        $productId = $request->get('product_id');

        $reportService = new ProductSalesReportService();
        $reportData = $reportService->getProductSalesReport('day', $startDate, $endDate, $productId, $startTime, $endTime);

        $products = ArrayHelper::map(
            Product::find()->where(['IS', 'deleted_at', null])->all(),
            'id',
            'name'
        );

        return $this->render('product-sales', [
            'reportData' => $reportData,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'productId' => $productId,
            'products' => $products,
        ]);
    }
}
