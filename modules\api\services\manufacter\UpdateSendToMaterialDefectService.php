<?php

namespace app\modules\api\services\manufacter;

use Yii;
use app\common\models\ActionLogger;
use app\common\models\MaterialDefect;
use app\common\models\MaterialStatusGroup;
use app\common\models\MaterialStatus;
use app\common\models\MaterialProduction;
use app\common\models\Tracking;
use app\modules\api\models\SendToMaterialDefectForm;
use yii\base\Component;

/**
 * Сервис для обновления записи о браке материалов в процессе производства
 */
class UpdateSendToMaterialDefectService extends Component
{
    /**
     * Получение информации о записи брака материала или возврата
     *
     * @param int $id ID записи (material_defect_id или group_id)
     * @param string $type Тип записи: 'defect' или 'return'
     * @return array Результат операции
     */
    public function getMaterialDefect($id, $type = 'defect')
    {
        if (!$id) {
            return [
                'success' => false,
                'message' => Yii::t('app', 'record_id_required')
            ];
        }

        if ($type === 'defect') {
            return $this->getMaterialDefectRecord($id);
        } elseif ($type === 'return') {
            return $this->getMaterialReturnRecord($id);
        }

        return [
            'success' => false,
            'message' => Yii::t('app', 'unknown_record_type')
        ];
    }

    /**
     * Получение записи о браке материала
     */
    private function getMaterialDefectRecord($materialDefectId)
    {
        if (!$materialDefectId) {
            return [
                'success' => false,
                'message' => Yii::t('app', 'record_id_required')
            ];
        }

        // Ищем запись по ID, проверяем права доступа
        $materialDefect = MaterialDefect::findOne([
            'id' => $materialDefectId,
            'deleted_at' => null,
            'add_user_id' => Yii::$app->user->id
        ]);

        if (!$materialDefect) {
            return [
                'success' => false,
                'message' => Yii::t('app', 'record_not_found_or_no_access')
            ];
        }

        // Проверка времени - можно редактировать только в течение 24 часов
        $createdTime = strtotime($materialDefect->created_at);
        $currentTime = time();
        $timeDifference = $currentTime - $createdTime;

        if ($timeDifference > 86400) { // 86400 секунд = 24 часа
            return [
                'success' => false,
                'message' => Yii::t('app', 'editing_allowed_only_24_hours')
            ];
        }

        // Получаем информацию о материале, включая единицу измерения
        $material = $materialDefect->material;

        return [
            'success' => true,
            'type' => 'defect',
            'material_defect' => [
                'id' => $materialDefect->id,
                'material_id' => $materialDefect->material_id,
                'quantity' => $materialDefect->quantity,
                'description' => $materialDefect->description,
                'created_at' => $materialDefect->created_at,
                'unit_type' => $material->unit_type,
                'unit_type_name' => $material->getUnitTypeName($material->unit_type),
                'is_returned' => false
            ]
        ];
    }

    /**
     * Получение записи о возврате материала
     */
    private function getMaterialReturnRecord($groupId)
    {
        $materialStatusGroup = MaterialStatusGroup::findOne([
            'id' => $groupId,
            'status' => MaterialStatusGroup::STATUS_RETURNED_FROM_PRODUCTION,
            'deleted_at' => null,
            'accepted_at' => null,
            'accepted_user_id' => null
        ]);

        if (!$materialStatusGroup) {
            return [
                'success' => false,
                'message' => Yii::t('app', 'return_group_not_found_or_confirmed')
            ];
        }

        // Проверка авторства - только создатель может редактировать
        if ($materialStatusGroup->add_user_id != Yii::$app->user->id) {
            return [
                'success' => false,
                'message' => Yii::t('app', 'can_edit_only_own_records')
            ];
        }

        // Проверка времени - можно редактировать только в течение 24 часов
        $createdTime = strtotime($materialStatusGroup->created_at);
        $currentTime = time();
        $timeDifference = $currentTime - $createdTime;

        if ($timeDifference > 86400) { // 86400 секунд = 24 часа
            return [
                'success' => false,
                'message' => Yii::t('app', 'editing_allowed_only_24_hours')
            ];
        }

        // Получаем материалы из группы
        $materials = MaterialStatus::find()
            ->select([
                'material_status.material_id',
                'to_char(material_status.quantity, \'FM999999999.########\') as quantity',
                'm.name as material_name',
                'm.unit_type'
            ])
            ->leftJoin('material m', 'm.id = material_status.material_id')
            ->where([
                'material_status.status_group_id' => $groupId,
                'material_status.deleted_at' => null
            ])
            ->asArray()
            ->all();

        if (empty($materials)) {
            return [
                'success' => false,
                'message' => Yii::t('app', 'materials_in_group_not_found')
            ];
        }

        // Преобразуем в формат для формы (берем первый материал как основной)
        $firstMaterial = $materials[0];

        return [
            'success' => true,
            'type' => 'return',
            'material_defect' => [
                'id' => $materialStatusGroup->id,
                'material_id' => $firstMaterial['material_id'],
                'quantity' => $firstMaterial['quantity'],
                'created_at' => $materialStatusGroup->created_at,
                'unit_type' => $firstMaterial['unit_type'],
                'unit_type_name' => \app\common\models\Material::getUnitTypeName($firstMaterial['unit_type']),
                'is_returned' => true,
                'materials' => $materials
            ]
        ];
    }

    /**
     * Обновление записи о браке материала или возврате
     *
     * @param array $postData Данные из POST-запроса
     * @return array Результат операции
     */
    public function updateMaterialDefect($postData)
    {
        $material_defect_id = isset($postData['material_defect_id']) ? $postData['material_defect_id'] : null;
        $type = isset($postData['type']) ? $postData['type'] : 'defect';

        if (!$material_defect_id) {
            return [
                'success' => false,
                'message' => Yii::t('app', 'record_id_required')
            ];
        }

        $model = new SendToMaterialDefectForm();
        $model->load($postData, '');

        if (!$model->validate()) {
            return [
                'success' => false,
                'message' => Yii::t('app', 'validation_error'),
                'errors' => $model->getErrors()
            ];
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            if ($type === 'defect') {
                return $this->updateDefectRecord($material_defect_id, $model, $transaction);
            } elseif ($type === 'return') {
                return $this->updateReturnRecord($material_defect_id, $model, $transaction);
            } else {
                throw new \Exception(Yii::t('app', 'unknown_record_type'));
            }
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Обновление записи о браке материала
     */
    private function updateDefectRecord($materialDefectId, $model, $transaction)
    {
        $materialDefect = MaterialDefect::findOne([
            'id' => $materialDefectId,
            'deleted_at' => null,
            'add_user_id' => Yii::$app->user->id
        ]);

        if (!$materialDefect) {
            throw new \Exception(Yii::t('app', 'record_not_found_or_confirmed'));
        }


        // Проверка времени - можно редактировать только в течение 24 часов
        $createdTime = strtotime($materialDefect->created_at);
        $currentTime = time();
        $timeDifference = $currentTime - $createdTime;

        if ($timeDifference > 86400) { // 86400 секунд = 24 часа
            throw new \Exception(Yii::t('app', 'editing_allowed_only_24_hours'));
        }

        $tracking = Tracking::find()
            ->where([
                'process_id' => $materialDefectId,
                'progress_type' => Tracking::TYPE_MATERIAL_DEFECT,
                'deleted_at' => null
            ])
            ->one();
        if ($tracking && $tracking->status === Tracking::STATUS_ACCEPTED) {
            throw new \Exception(Yii::t('app', 'material_already_confirmed_cannot_update'));
        }

        $newMaterial = $model->materials[0];

        // Проверяем доступность нового количества в производстве
        $this->validateMaterialInProduction($newMaterial);

        // Возвращаем старое количество в производство
        $this->returnMaterialToProduction($materialDefect->material_id, $materialDefect->quantity);

        // Уменьшаем новое количество из производства
        $this->decreaseMaterialProduction($newMaterial);

        $materialDefect->material_id = $newMaterial['material_id'];
        $materialDefect->quantity = $newMaterial['quantity'];
        $materialDefect->description = $model->description;

        // Материал в браке остается неподтвержденным до ручного подтверждения
        $materialDefect->accepted_user_id = null;
        $materialDefect->accepted_at = null;

        if (!$materialDefect->save()) {
            throw new \Exception(Yii::t('app', 'error_updating_defect') . ': ' . json_encode($materialDefect->getErrors()));
        }

        ActionLogger::actionLog(
            'update_send_to_defect',
            'material_defect',
            $materialDefect->id,
            [
                'material_id' => $materialDefect->material_id,
                'quantity' => $materialDefect->quantity,
                'description' => $materialDefect->description
            ]
        );

        $transaction->commit();
        return [
            'success' => true,
            'message' => Yii::t('app', 'defect_record_updated_successfully')
        ];
    }

    /**
     * Обновление записи о возврате материала
     */
    private function updateReturnRecord($groupId, $model, $transaction)
    {
        $materialStatusGroup = MaterialStatusGroup::findOne([
            'id' => $groupId,
            'status' => MaterialStatusGroup::STATUS_RETURNED_FROM_PRODUCTION,
            'deleted_at' => null,
            'accepted_at' => null,
            'accepted_user_id' => null
        ]);

        if (!$materialStatusGroup) {
            throw new \Exception(Yii::t('app', 'return_group_not_found_or_confirmed'));
        }

        // Проверка авторства - только создатель может редактировать
        if ($materialStatusGroup->add_user_id != Yii::$app->user->id) {
            throw new \Exception(Yii::t('app', 'can_edit_only_own_records'));
        }

        // Проверка времени - можно редактировать только в течение 24 часов
        $createdTime = strtotime($materialStatusGroup->created_at);
        $currentTime = time();
        $timeDifference = $currentTime - $createdTime;

        if ($timeDifference > 86400) { // 86400 секунд = 24 часа
            throw new \Exception(Yii::t('app', 'editing_allowed_only_24_hours'));
        }

        // Проверка статуса tracking для возврата
        $tracking = Tracking::find()
            ->where([
                'process_id' => $groupId,
                'progress_type' => Tracking::TYPE_MATERIAL_RETURN,
                'deleted_at' => null
            ])
            ->one();
        if ($tracking && $tracking->status === Tracking::STATUS_ACCEPTED) {
            throw new \Exception(Yii::t('app', 'return_already_confirmed_cannot_update'));
        }

        // Очищаем старые записи в MaterialStatus для этой группы
        MaterialStatus::deleteAll(
            ['status_group_id' => $groupId, 'deleted_at' => null]
        );

        // Создаем новые записи MaterialStatus на основе обновленных данных
        foreach ($model->materials as $material) {
            $materialStatus = new MaterialStatus();
            $materialStatus->status_group_id = $groupId;
            $materialStatus->material_id = $material['material_id'];
            $materialStatus->quantity = $material['quantity'];
            $materialStatus->created_at = date('Y-m-d H:i:s');

            if (!$materialStatus->save()) {
                throw new \Exception(Yii::t('app', 'error_creating_material_status') . ': ' . json_encode($materialStatus->getErrors()));
            }
        }

        ActionLogger::actionLog(
            'update_material_return',
            'material_status_group',
            $materialStatusGroup->id,
            [
                'materials' => $model->materials,
                'note' => 'Обновление заявки на возврат материала'
            ]
        );

        $transaction->commit();
        return [
            'success' => true,
            'message' => Yii::t('app', 'material_return_request_updated_successfully')
        ];
    }

    /**
     * Проверяет наличие достаточного количества материала в производстве
     *
     * @param array $material Данные материала
     * @throws \Exception
     */
    private function validateMaterialInProduction($material)
    {
        $totalInProduction = MaterialProduction::find()
            ->where([
                'material_id' => $material['material_id'],
                'deleted_at' => null
            ])
            ->sum('quantity') ?? 0;

        if ($totalInProduction < $material['quantity']) {
            throw new \Exception(Yii::t('app', 'insufficient_material_in_production') . ". " .
                Yii::t('app', 'available') . ": {$totalInProduction}, " .
                Yii::t('app', 'required') . ": {$material['quantity']}");
        }
    }

    /**
     * Возвращает материал в производство
     *
     * @param int $materialId ID материала
     * @param float $quantity Количество для возврата
     * @throws \Exception
     */
    private function returnMaterialToProduction($materialId, $quantity)
    {
        $materialProduction = MaterialProduction::find()
            ->where([
                'material_id' => $materialId,
                'deleted_at' => null,
                'DATE(created_at)' => date('Y-m-d')
            ])
            ->one();

        if (!$materialProduction) {
            $materialProduction = new MaterialProduction();
            $materialProduction->material_id = $materialId;
            $materialProduction->quantity = 0;
            $materialProduction->created_at = date('Y-m-d H:i:s');
        }

        $materialProduction->quantity += $quantity;
        $materialProduction->updated_at = date('Y-m-d H:i:s');

        if (!$materialProduction->save()) {
            throw new \Exception(Yii::t('app', 'error_returning_material_to_production') . ': ' . json_encode($materialProduction->getErrors()));
        }
    }

    /**
     * Уменьшает количество материала в производстве
     *
     * @param array $material Данные материала
     * @throws \Exception
     */
    private function decreaseMaterialProduction($material)
    {
        if ($material['quantity'] <= 0) {
            throw new \Exception(Yii::t('app', 'quantity_must_be_positive') . ": {$material['quantity']}");
        }

        $remainingQuantity = $material['quantity'];

        $materialProductions = MaterialProduction::find()
            ->where([
                'material_id' => $material['material_id'],
                'deleted_at' => null
            ])
            ->andWhere(['>', 'quantity', 0])
            ->orderBy(['created_at' => SORT_ASC])
            ->all();

        if (empty($materialProductions)) {
            throw new \Exception(Yii::t('app', 'no_materials_available_in_production'));
        }


        foreach ($materialProductions as $production) {
            if ($remainingQuantity <= 0) break;

            $quantityToDeduct = min($production->quantity, $remainingQuantity);
            $oldQuantity = $production->quantity;

            $production->quantity -= $quantityToDeduct;
            $production->updated_at = date('Y-m-d H:i:s');
            $remainingQuantity -= $quantityToDeduct;


            if (!$production->save()) {
                $errors = json_encode($production->getErrors());
                Yii::error("Ошибка сохранения записи ID {$production->id}: {$errors}", 'material_defect');
                throw new \Exception(Yii::t('app', 'error_updating_production') . ": {$errors}");
            }
        }

        if ($remainingQuantity > 0) {
            $message = Yii::t('app', 'could_not_deduct_full_quantity') . ". " . Yii::t('app', 'remaining') . ": {$remainingQuantity}";
            Yii::error($message, 'material_defect');
            throw new \Exception($message);
        }
    }
}
