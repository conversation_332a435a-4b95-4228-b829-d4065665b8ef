<?php
use yii\helpers\Html;
use yii\web\View;

$this->title = Yii::t("app", "debts");
?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title"><?= Html::encode($this->title) ?></h3>
    </div>
    <div class="card-body">
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t("app", "supplier_debts") ?></h5>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "supplier") ?></th>
                        <th><?= Yii::t("app", "total_amount") ?></th>
                        <th><?= Yii::t("app", "paid_amount") ?></th>
                        <th><?= Yii::t("app", "debt_amount") ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($supplierDebts as $debt): ?>
                        <tr>
                            <td><?= Html::encode($debt['supplier_name']) ?></td>
                            <td><?= Yii::$app->formatter->asDecimal($debt['total_invoice_amount'], 2) ?></td>
                            <td><?= Yii::$app->formatter->asDecimal($debt['total_paid_amount'], 2) ?></td>
                            <td><?= Yii::$app->formatter->asDecimal($debt['debt_amount'], 2) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t("app", "client_debts") ?></h5>
        <div class="table-responsive mb-4">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "client") ?></th>
                        <th><?= Yii::t("app", "total_amount") ?></th>
                        <th><?= Yii::t("app", "paid_amount") ?></th>
                        <th><?= Yii::t("app", "debt_amount") ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($clientDebts as $debt): ?>
                        <tr>
                            <td><?= Html::encode($debt['client_name']) ?></td>
                            <td><?= Yii::$app->formatter->asDecimal($debt['total_sales_amount'], 2) ?></td>
                            <td><?= Yii::$app->formatter->asDecimal($debt['total_paid_amount'], 2) ?></td>
                            <td><?= Yii::$app->formatter->asDecimal($debt['debt_amount'], 2) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
