<?php

use app\common\models\Cashbox;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use app\common\models\CashboxDetail;

/* @var $this yii\web\View */
/* @var $reportData array */
/* @var $startDate string */
/* @var $endDate string */
/* @var $cashboxId int|null */
/* @var $cashboxes array */

$this->title = Yii::t('app', 'cashbox_report');
?>

<style>
    .text-right{ text-align:right; }
    .amount-in{ color:#28a745; font-weight:bold; }
    .amount-out{ color:#dc3545; font-weight:bold; }
    .no-wrap{ white-space:nowrap; }
    .table-responsive{ overflow-x:auto; }
</style>

<div class="row align-items-center mb-3">
    <div class="col-md-6">
        <h3 class="mb-0"><?= Yii::t('app', 'cashbox_report') ?></h3>
    </div>
    <div class="col-md-6 d-flex justify-content-end">
        <?php $form = ActiveForm::begin([
            'method'  => 'get',
            'action'  => ['/backend/report/cashbox'],
            'options' => ['class' => 'd-inline-flex align-items-center']
        ]); ?>
            <?= Html::dropDownList('cashbox_id', $cashboxId, $cashboxes, [
                'class' => 'form-control mr-2 select2',
                'style' => 'width:220px;',
                'prompt' => Yii::t('app', 'all_cashboxes')
            ]) ?>
            <input type="date" name="start_date" class="form-control mr-2" style="width:160px;" value="<?= Html::encode($startDate) ?>">
            <input type="date" name="end_date" class="form-control mr-2" style="width:160px;" value="<?= Html::encode($endDate) ?>">
            <?= Html::submitButton(Yii::t('app', 'search'), ['class' => 'btn btn-primary']) ?>
        <?php ActiveForm::end(); ?>
    </div>
</div>

<?php $items = $reportData['items'] ?? []; ?>
<?php if (!empty($items)): ?>
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered table-striped table-hover mb-0">
                    <thead class="thead-light">
                        <tr>
                            <th><?= Yii::t('app', 'payment_date') ?></th>
                            <th><?= Yii::t('app', 'cashbox') ?></th>
                            <th><?= Yii::t('app', 'user') ?></th>
                            <th><?= Yii::t('app', 'type_payment') ?></th>
                            <th class="text-right">
                                <?= Yii::t('app', 'amount') ?>
                            </th>
                            <th><?= Yii::t('app', 'currency') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($items as $row): ?>
                            <?php $isIn = (int)$row['type'] === CashboxDetail::TYPE_IN; ?>
                            <tr>
                                <td class="no-wrap"><?= date('d.m.Y H:i', strtotime($row['created_at'])) ?></td>
                                <td>
                                    <?= Html::encode($row['cashbox_name']) ?>
                                    <small class="text-dark d-block">
                                        <?= Yii::t('app', 'balance') ?>: <?= Yii::$app->formatter->asDecimal(Cashbox::findOne($row['cashbox_id'])->balance ?? 0, 0) ?> UZS
                                    </small>
                                </td>
                                <td><?= Html::encode($row['user_name'] ?: '-') ?></td>
                                <td><?= Html::encode(CashboxDetail::getPaymentTypeDescription($row['type'])) ?></td>
                                <td class="text-right <?= $isIn ? 'amount-in' : 'amount-out' ?>">
                                    <?= Yii::$app->formatter->asDecimal($row['amount'], 2) ?>
                                </td>
                                <td>UZS</td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="alert alert-info text-center">
        <i class="fas fa-info-circle mr-2"></i>
        <?= Yii::t('app', 'no_data_found') ?>
    </div>
<?php endif; ?>

<script>
    // валидация года в date input (аналогично другим отчётам)
    document.querySelectorAll('input[type="date"]').forEach(input => {
        input.addEventListener('input', function(e){
            let val = e.target.value;
            if (val) {
                let parts = val.split('-');
                if(parts[0].length > 4){ parts[0] = parts[0].slice(0,4); }
                if(parseInt(parts[0]) > 9999){ parts[0] = '9999'; }
                e.target.value = parts.join('-');
            }
        });
    });
</script> 