<style>
    .card {
        background-color: white !important;
    }
    .table th {
        background-color: #f8f9fa;
    }
    .badge-success {
        background-color: #28a745;
    }
    .badge-danger {
        background-color: #dc3545;
    }
    .table-responsive {
        max-height: 300px;
        overflow-y: auto;
    }
    .table-responsive thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 1;
    }
    .table tbody tr {
        height: 48px;
    }
</style>

<div class="table-responsive">
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th><?= Yii::t('app', 'client') ?></th>
                <th><?= Yii::t('app', 'product') ?></th>
                <th><?= Yii::t('app', 'special_price') ?></th>
                <th><?= Yii::t('app', 'created_at') ?></th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><?= $model->client->full_name ?></td>
                <td><?= $model->product->name ?></td>
                <td><?= $model->sell_price ?></td>
                <td><?= date('d.m.Y h:i', strtotime($model->created_at)) ?></td>
            </tr>
        </tbody>
    </table>
</div>

<?php
$js = <<<JS
document.addEventListener('DOMContentLoaded', function() {
    const tableContainer = document.querySelector('.table-responsive');
    const table = tableContainer.querySelector('table');
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');
    const maxRows = 5;
    const rowHeight = 48;

    if (rows.length > maxRows) {
        tableContainer.style.maxHeight = `\${(maxRows * rowHeight) + 48}px`;
        tableContainer.style.overflowY = 'auto';
        tableContainer.style.overflowX = 'hidden';
    } else {
        tableContainer.style.maxHeight = 'none';
        tableContainer.style.overflowY = 'visible';
    }
});
JS;
$this->registerJs($js);
?>
