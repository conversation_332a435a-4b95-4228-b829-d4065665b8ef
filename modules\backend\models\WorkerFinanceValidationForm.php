<?php

namespace app\modules\backend\models;

use Yii;

class WorkerFinanceValidationForm extends \yii\base\Model
{
    public $worker_id;
    public $month;
    public $amount;
    public $description;
    public $payment_type;
    public $type;
    public $cashbox_id;
    public $deduct_from_salary;
    public $deduct_amount;

    public function rules()
    {
        return [
            [['worker_id', 'type', 'amount', 'payment_type', 'cashbox_id'], 'required'],
            ['month', 'required', 'when' => function($model) {
                return in_array($model->payment_type, [
                    WorkerFinances::TYPE_SALARY,
                    WorkerFinances::TYPE_ADVANCE,
                    WorkerFinances::TYPE_BONUS,
                    WorkerFinances::TYPE_VACATION_PAY
                ]);
            }],
            [['worker_id', 'type', 'payment_type', 'cashbox_id'], 'integer'],
            [['amount', 'deduct_amount'], 'number'],
            [['description'], 'string'],
            [['created_at', 'deleted_at'], 'safe'],
            [['month'], 'string', 'max' => 10],
            [['deduct_from_salary'], 'boolean'],
            [['worker_id'], 'exist', 'skipOnError' => true, 'targetClass' => Worker::class, 'targetAttribute' => ['worker_id' => 'id']],
        ];
    }

    public function attributeLabels()
    {
        return [
            'worker_id' => Yii::t('app', 'worker'),
            'month' => Yii::t('app', 'month'),
            'type' => Yii::t('app', 'type'),
            'amount' => Yii::t('app', 'amount'),
            'description' => Yii::t('app', 'description'),
            'payment_type' => Yii::t('app', 'payment type'),
            'cashbox_id' => Yii::t('app', 'cashbox'),
            'deduct_from_salary' => Yii::t('app', 'deduct from salary'),
            'due_date' => Yii::t('app', 'due date'),
        ];
    }


    public function beforeValidate()
    {
        $attributes = ['amount', 'deduct_amount'];

        foreach ($attributes as $attribute) {
            if (is_string($this->$attribute) && !empty($this->$attribute)) {
                $originalValue = $this->$attribute;
                
                $cleanValue = preg_replace('/[\s\x{A0}]+/u', '', $originalValue);
                $cleanValue = str_replace(',', '.', $cleanValue);
                
                if (is_numeric($cleanValue)) {
                    $this->$attribute = floatval($cleanValue);
                }
            }
        }
        
        return parent::beforeValidate();
    }
}