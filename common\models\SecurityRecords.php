<?php

namespace app\common\models;

use app\modules\backend\models\Users;
use Yii;

/**
 * This is the model class for table "security_records".
 *
 * @property int $id
 * @property string $car_number
 * @property string $driver_full_name
 * @property string|null $image
 * @property int $add_user_id
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property User $addUser
 */
class SecurityRecords extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'security_records';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['car_number', 'driver_full_name', 'add_user_id'], 'required'],
            [['add_user_id'], 'default', 'value' => null],
            [['add_user_id'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            [['car_number'], 'string', 'max' => 20],
            [['driver_full_name', 'image'], 'string', 'max' => 255],
            [['add_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['add_user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'car_number' => 'Car Number',
            'driver_full_name' => 'Driver Full Name',
            'image' => 'Image',
            'add_user_id' => 'Add User ID',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[AddUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAddUser()
    {
        return $this->hasOne(User::class, ['id' => 'add_user_id']);
    }
}
