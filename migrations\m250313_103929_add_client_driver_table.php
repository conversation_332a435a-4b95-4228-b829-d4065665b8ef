<?php

use yii\db\Migration;

class m250313_103929_add_client_driver_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('client_driver', [
            'id' => $this->primaryKey(),
            'client_id' => $this->integer()->notNull(),
            'driver' => $this->string()->null(),
            'car_number' => $this->string()->null(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-client_driver-client_id',
            'client_driver',
            'client_id',
            'client',
            'id',
            'CASCADE'
        );

        $this->addColumn('client', 'organization', $this->string()->null());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('client_driver');
        $this->dropColumn('client', 'organization');
    }

   
}
