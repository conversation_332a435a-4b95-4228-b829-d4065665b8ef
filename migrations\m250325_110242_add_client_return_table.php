<?php

use yii\db\Migration;

class m250325_110242_add_client_return_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
      $this->createTable('client_return', [
        'id' => $this->primaryKey(),
        'client_id' => $this->integer()->notNull(),
        'created_at' => $this->timestamp()->defaultExpression('NOW()'),
        'deleted_at' => $this->timestamp()->null(),
      ]);
  
      $this->addForeignKey('fk_client_return_client', 'client_return', 'client_id', 'client', 'id');
  
  
      $this->createTable('client_contract', [
        'id' => $this->primaryKey(),
        'client_id' => $this->integer()->notNull(),
        'contract' => $this->string()->notNull(),
        'type' => $this->integer()->null(),
        'start_date' => $this->date()->defaultExpression('CURRENT_DATE'),
        'end_date' => $this->date()->null(),
        'created_at' => $this->timestamp()->defaultExpression('NOW()'),
        'deleted_at' => $this->timestamp()->null(),
      ]);
  
      $this->addForeignKey('fk_client_contract_client', 'client_contract', 'client_id', 'client', 'id');
  
  
      $this->addColumn('client', 'legal_address', $this->string()->null());
    }
  

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk_client_return_client', 'client_return');
        $this->dropTable('client_return');

        $this->dropForeignKey('fk_client_contract_client', 'client_contract');
        $this->dropTable('client_contract');

        $this->dropColumn('client', 'legal_address');
    }
}
