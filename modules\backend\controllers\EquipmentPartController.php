<?php

namespace app\modules\backend\controllers;

use Yii;
use app\common\models\EquipmentPart;
use app\common\models\EquipmentPartPhoto;
use app\common\models\EquipmentPartAssignment;
use app\common\models\EquipmentPartHistory;
use app\common\models\EquipmentPartMovement;
use app\common\models\Equipment;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use yii\web\UploadedFile;
use yii\helpers\FileHelper;

/**
 * EquipmentPartController implements the CRUD actions for EquipmentPart model.
 */
class EquipmentPartController extends BaseController
{
    /**
     * Lists all EquipmentPart models.
     * @return mixed
     */
    public function actionIndex($equipment_id = null)
    {
        // SQL-запрос для показа запчастей с информацией о назначениях и последних движениях
        $sql = "
            SELECT
                ep.id,
                ep.name,
                ep.photo,
                ep.quantity,
                ep.price,
                ep.currency_id,
                ep.unit_type,
                ep.last_purchase_date,
                MAX(ep.created_at) as created_at,
                ep.comment,
                COALESCE(SUM(CASE WHEN epa.status = :active_status THEN epa.quantity ELSE 0 END), 0) as assigned_quantity,
                COALESCE(COUNT(CASE WHEN epa.status = :active_status THEN 1 END), 0) as installations_count,
                COALESCE(MAX(epm.created_at), ep.created_at) as last_movement_date
            FROM equipment_parts ep
            LEFT JOIN equipment_part_assignments epa ON ep.id = epa.equipment_part_id
            LEFT JOIN equipment_part_movements epm ON ep.id = epm.equipment_part_id AND epm.movement_type = :income_movement_type
            WHERE ep.deleted_at IS NULL
        ";

        // Параметры для SQL-запроса
        $params = [
            ':active_status' => EquipmentPartAssignment::STATUS_ACTIVE,
            ':income_movement_type' => EquipmentPartMovement::MOVEMENT_INCOME
        ];

        // Если передан параметр equipment_id, показываем только назначения этого оборудования
        if ($equipment_id !== null) {
            $sql = "
                SELECT
                    ep.*,
                    ep.currency_id,
                    ep.unit_type,
                    epa.quantity as assigned_quantity,
                    epa.installation_date,
                    epa.status as assignment_status,
                    epa.comment as assignment_comment,
                    epa.id as assignment_id,
                    e.name as equipment_name
                FROM equipment_part_assignments epa
                INNER JOIN equipment_parts ep ON ep.id = epa.equipment_part_id
                LEFT JOIN equipment e ON e.id = epa.equipment_id
                WHERE ep.deleted_at IS NULL
                    AND epa.equipment_id = :equipment_id
                    AND epa.status IN (:active_status, :repair_status)
                ORDER BY epa.created_at DESC
            ";
            $params[':equipment_id'] = (int)$equipment_id;
            $params[':repair_status'] = EquipmentPartAssignment::STATUS_REPAIR;
        } else {
            // Группируем по запчастям для общего списка
            $sql .= " GROUP BY ep.id, ep.name, ep.photo, ep.quantity, ep.price, ep.currency_id, ep.unit_type, ep.last_purchase_date, ep.created_at, ep.comment ORDER BY last_movement_date DESC, ep.created_at DESC";
        }

        // Выполняем запрос
        $result = Yii::$app->db->createCommand($sql, $params)->queryAll();

        // Получаем список активного оборудования для фильтра
        $equipments = Equipment::find()
            ->where(['status' => Equipment::STATUS_ACTIVE])
            ->andWhere(['deleted_at' => null])
            ->all();

        $currencies = \app\common\models\Currency::find()
            ->where(['deleted_at' => null])
            ->indexBy('id')
            ->all();

        if (Yii::$app->request->isAjax) {
            return $this->renderAjax('index', [
                'result' => $result,
                'equipments' => $equipments,
                'currencies' => $currencies,
            ]);
        }

        return $this->render('index', [
            'result' => $result,
            'equipments' => $equipments,
            'currencies' => $currencies,
        ]);
    }

    /**
     * Search equipment parts by status and source type
     * @return mixed
     */
    public function actionSearch()
    {
        if (Yii::$app->request->isAjax) {
            $status = Yii::$app->request->post('status');
            $sourceType = Yii::$app->request->post('source_type');
            $equipmentId = Yii::$app->request->post('equipment_id');

            // Если фильтр по оборудованию, показываем детальный список
            if ($equipmentId !== null && $equipmentId !== '') {
                $sql = "
                    SELECT
                        ep.*,
                        ep.currency_id,
                        ep.unit_type,
                        e.name as equipment_name
                    FROM equipment_parts ep
                    LEFT JOIN equipment e ON e.id = ep.equipment_id
                    WHERE ep.deleted_at IS NULL AND ep.equipment_id = :equipment_id
                ";
                $params = [':equipment_id' => (int)$equipmentId];

                // Добавляем фильтр по статусу, если он передан
                if ($status !== null && $status !== '') {
                    $sql .= " AND ep.status = :status";
                    $params[':status'] = (int)$status;
                }

                // Добавляем фильтр по типу источника, если он передан
                if ($sourceType !== null && $sourceType !== '') {
                    $sql .= " AND ep.source_type = :source_type";
                    $params[':source_type'] = (int)$sourceType;
                }

                $sql .= " ORDER BY ep.created_at DESC";
            } else {
                // Группированный запрос для общего списка с информацией о последних движениях
                $sql = "
                    SELECT
                        MIN(ep.id) as id,
                        ep.name,
                        ep.photo,
                        ep.currency_id,
                        ep.unit_type,
                        SUM(ep.quantity) as quantity,
                        AVG(ep.price) as price,
                        MAX(ep.last_purchase_date) as last_purchase_date,
                        MAX(ep.created_at) as created_at,
                        ep.comment,
                        COUNT(CASE WHEN ep.equipment_id IS NOT NULL THEN 1 END) as installed_count,
                        COUNT(*) as total_count,
                        COALESCE(MAX(epm.created_at), MAX(ep.created_at)) as last_movement_date
                    FROM equipment_parts ep
                    LEFT JOIN equipment_part_movements epm ON ep.id = epm.equipment_part_id AND epm.movement_type = :income_movement_type
                    WHERE ep.deleted_at IS NULL
                ";
                $params = [':income_movement_type' => EquipmentPartMovement::MOVEMENT_INCOME];

                // Для группированного запроса фильтры применяются по-другому
                $havingConditions = [];

                if ($status !== null && $status !== '') {
                    // Фильтр по статусу в группированном запросе
                    $sql .= " AND ep.status = :status";
                    $params[':status'] = (int)$status;
                }

                if ($sourceType !== null && $sourceType !== '') {
                    // Фильтр по типу источника в группированном запросе
                    $sql .= " AND ep.source_type = :source_type";
                    $params[':source_type'] = (int)$sourceType;
                }

                $sql .= " GROUP BY ep.name, ep.photo, ep.comment, ep.currency_id, ep.unit_type ORDER BY last_movement_date DESC, MAX(ep.created_at) DESC";
            }

            // Выполняем запрос
            $result = Yii::$app->db->createCommand($sql, $params)->queryAll();

            // Получаем список активного оборудования для фильтра
            $equipments = Equipment::find()
                ->where(['status' => Equipment::STATUS_ACTIVE])
                ->andWhere(['deleted_at' => null])
                ->all();

            if ($result) {
                $html = $this->renderPartial('_index', [
                    'result' => $result
                ]);

                return $this->asJson([
                    'status' => 'success',
                    'content' => $html,
                    'equipments' => $equipments
                ]);
            }

            return $this->asJson([
                'status' => 'error',
                'message' => Yii::t('app', 'No results found')
            ]);
        }
    }

    /**
     * Creates a new EquipmentPart model.
     * @return mixed
     */
    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $equipments = Equipment::find()
                ->where(['status' => Equipment::STATUS_ACTIVE])
                ->andWhere(['deleted_at' => null])
                ->all();

            $currencies = \app\common\models\Currency::find()
                ->where(['deleted_at' => null])
                ->all();

            return [
                'content' => $this->renderPartial('create', [
                    'equipments' => $equipments,
                    'currencies' => $currencies
                ]),
            ];
        }        if (Yii::$app->request->isPost) {
            $model = new EquipmentPart();
            $model->scenario = EquipmentPart::SCENARIO_CREATE;
            $data = Yii::$app->request->post();

            // Получаем загруженные файлы
            $uploadedFiles = UploadedFile::getInstancesByName('photos');
            if (empty($uploadedFiles)) {
                return [
                    'status' => 'error',
                    'errors' => [
                        'photos' => [Yii::t('app', 'At least one photo is required')]
                    ],
                ];
            }

            $model->name = $data['name'];
            $model->comment = $data['comment'] ?? null;
            $model->price = 0; // Устанавливаем цену = 0 по умолчанию
            $model->quantity = 0; // Устанавливаем количество = 0 по умолчанию
            $model->currency_id = !empty($data['currency_id']) ? $data['currency_id'] : null;
            $model->unit_type = $data['unit_type'];
            $model->source_type = $data['source_type'];
            $model->status = $data['status'];
            $model->equipment_id = !empty($data['equipment_id']) ? $data['equipment_id'] : null;
            $model->installation_date = !empty($data['installation_date']) ? $data['installation_date'] : null;
            $model->created_at = date('Y-m-d H:i:s');
            
            // Временно оставляем старое поле photo для совместимости (используем имя первого файла)
            $firstName = 'part_' . time() . '.' . $uploadedFiles[0]->extension;
            $model->photo = $firstName;

            $transaction = Yii::$app->db->beginTransaction();
            try {
                if($model->save()) {
                    // Сохраняем все загруженные фотографии
                    $photosUploaded = false;
                    foreach ($uploadedFiles as $index => $file) {
                        $isMain = ($index === 0); // Первая фотография будет главной
                        if (EquipmentPartPhoto::saveUploadedFile($file, $model->id, $isMain)) {
                            $photosUploaded = true;
                        }
                    }

                    if (!$photosUploaded) {
                        throw new \Exception('Failed to upload photos');
                    }

                    // Сохраняем первую фотографию в старое поле для совместимости
                    $directory = Yii::getAlias('@webroot/uploads/equipment_parts/');
                    if (!is_dir($directory)) {
                        FileHelper::createDirectory($directory, 0777, true);
                    }
                    $uploadedFiles[0]->saveAs($directory . $firstName);

                    // Добавляем запись в историю о создании
                    $model->addHistory(
                        EquipmentPart::ACTION_CREATE,
                        null,
                        $model->status,
                        Yii::t('app', 'Part created and added to reserve')
                    );

                    // Если запчасть сразу устанавливается на оборудование, добавляем запись об установке
                    if ($model->equipment_id) {
                        $model->addHistory(
                            EquipmentPart::ACTION_INSTALL,
                            null,
                            $model->status,
                            Yii::t('app', 'Part installed to equipment')
                        );
                    }

                    // Обрабатываем приход запчастей, если указано количество
                    if (!empty($data['income_quantity']) && $data['income_quantity'] > 0) {
                        $movement = new EquipmentPartMovement();
                        $movement->equipment_part_id = $model->id;
                        $movement->movement_type = EquipmentPartMovement::MOVEMENT_INCOME;
                        $movement->quantity = $data['income_quantity'];
                        
                        // Обрабатываем цену: если пустая строка или 0, то null
                        $pricePerUnit = $data['income_price_per_unit'] ?? null;
                        if ($pricePerUnit === '' || $pricePerUnit === '0' || $pricePerUnit == 0) {
                            $pricePerUnit = null;
                        }
                        $movement->price_per_unit = $pricePerUnit;
                        $movement->comment = $data['income_comment'] ?? Yii::t('app', 'Initial income when creating part');

                        if (!$movement->save()) {
                            throw new \Exception('Failed to save income movement: ' . implode(', ', $movement->getFirstErrors()));
                        }
                    }

                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'Equipment part created successfully.')
                    ];
                } else {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors(),
                    ];
                }
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        }
    }

    /**
     * Updates an existing EquipmentPart model.
     * @return mixed
     */
    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = EquipmentPart::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Equipment part not found')
                ];
            }

            $equipments = Equipment::find()
                ->where(['status' => Equipment::STATUS_ACTIVE])
                ->andWhere(['deleted_at' => null])
                ->all();

            $currencies = \app\common\models\Currency::find()
                ->where(['deleted_at' => null])
                ->all();

            return [
                'content' => $this->renderPartial('update', [
                    'model' => $model,
                    'equipments' => $equipments,
                    'currencies' => $currencies
                ]),
            ];
        }

        if (Yii::$app->request->isPost) {
            $id = Yii::$app->request->post('id');
            $model = EquipmentPart::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Equipment part not found')
                ];
            }

            $data = Yii::$app->request->post();
            $oldPhoto = $model->photo;
            $oldEquipmentId = $model->equipment_id;
            $oldStatus = $model->status;
            $oldQuantity = $model->quantity;

            $uploadedFile = UploadedFile::getInstanceByName('photo');
            if ($uploadedFile) {
                $fileName = 'part_' . time() . '.' . $uploadedFile->extension;
                $model->photo = $fileName;
            }

            $model->name = $data['name'];
            $model->comment = $data['comment'] ?? null;
            $model->price = $data['price'] ?? 0;
            $model->currency_id = !empty($data['currency_id']) ? $data['currency_id'] : null;
            $model->unit_type = $data['unit_type'];
            $model->quantity = $data['quantity'] ?? 0;
            $model->source_type = $data['source_type'];
            $model->status = $data['status'];
            $model->equipment_id = !empty($data['equipment_id']) ? $data['equipment_id'] : null;
            $model->installation_date = !empty($data['installation_date']) ? $data['installation_date'] : null;

          
            $model->last_purchase_date = date('Y-m-d');
            

            $transaction = Yii::$app->db->beginTransaction();
            try {
                if($model->save()) {
                    // Сохраняем фото если оно было загружено
                    if ($uploadedFile) {
                        $directory = Yii::getAlias('@webroot/uploads/equipment_parts/');
                        if (!is_dir($directory)) {
                            FileHelper::createDirectory($directory, 0777, true);
                        }

                        if ($oldPhoto && file_exists($directory . $oldPhoto)) {
                            unlink($directory . $oldPhoto);
                        }

                        $uploadedFile->saveAs($directory . $fileName);
                    }

                    // Добавляем запись в историю если изменился статус или оборудование
                    if ($oldStatus != $model->status || $oldEquipmentId != $model->equipment_id) {
                        // Если изменилось оборудование
                        if ($oldEquipmentId != $model->equipment_id) {
                            if ($model->equipment_id) {
                                // Установка на новое оборудование
                                $model->addHistory(
                                    EquipmentPart::ACTION_INSTALL,
                                    $oldStatus,
                                    $model->status,
                                    Yii::t('app', 'Part installed to equipment'),
                                    $model->equipment_id
                                );
                            } else {
                                // Снятие с оборудования
                                $model->addHistory(
                                    EquipmentPart::ACTION_REMOVE,
                                    $oldStatus,
                                    $model->status,
                                    Yii::t('app', 'Part removed from equipment'),
                                    $oldEquipmentId
                                );
                            }
                        }
                        // Если изменился только статус
                        else if ($oldStatus != $model->status) {
                            $actionType = $model->status == EquipmentPart::STATUS_RESERVE ?
                                EquipmentPart::ACTION_RESERVE :
                                ($model->status == EquipmentPart::STATUS_ACTIVE ?
                                    EquipmentPart::ACTION_INSTALL :
                                    EquipmentPart::ACTION_REMOVE);

                            $model->addHistory(
                                $actionType,
                                $oldStatus,
                                $model->status,
                                Yii::t('app', 'Part status changed')
                            );
                        }
                    }

                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'Equipment part updated successfully.')
                    ];
                } else {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors(),
                    ];
                }
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        }
    }

    /**
     * Attach part to equipment
     * @return mixed
     */
    public function actionAttach()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = EquipmentPart::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Equipment part not found')
                ];
            }

            $equipments = Equipment::find()
                ->where(['status' => Equipment::STATUS_ACTIVE])
                ->andWhere(['deleted_at' => null])
                ->all();

            return [
                'content' => $this->renderPartial('_attach_form', [
                    'model' => $model,
                    'equipments' => $equipments
                ]),
            ];
        }

        if (Yii::$app->request->isPost) {
            $id = Yii::$app->request->post('id');
            $model = EquipmentPart::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Equipment part not found')
                ];
            }

            $data = Yii::$app->request->post();
            $equipmentId = $data['equipment_id'];

            // Списание на общие нужды
            if ($equipmentId == 0) {
                $quantityToWriteOff = $data['quantity'] ?? 1; // по умолчанию списываем 1, если не указано

                if ($model->quantity < $quantityToWriteOff) {
                    return [
                        'status' => 'error',
                        'message' => Yii::t('app', 'Not enough quantity to write off')
                    ];
                }

                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $movement = new EquipmentPartMovement();
                    $movement->equipment_part_id = $model->id;
                    $movement->movement_type = EquipmentPartMovement::MOVEMENT_OUTCOME;
                    $movement->quantity = $quantityToWriteOff;
                    $movement->comment = Yii::t('app', 'Written off for general needs');
                    
                    if ($movement->save()) {
                        $model->addHistory(
                            EquipmentPart::ACTION_WRITE_OFF,
                            $model->status,
                            $model->status,
                            Yii::t('app', 'Written off for general needs') . ' (' . $quantityToWriteOff . ' ' . Yii::t('app', 'pcs') . ')'
                        );
                        $transaction->commit();
                        return [
                            'status' => 'success',
                            'message' => Yii::t('app', 'Successfully written off')
                        ];
                    } else {
                        $transaction->rollBack();
                        return [
                            'status' => 'error',
                            'errors' => $movement->getErrors(),
                        ];
                    }
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage(),
                    ];
                }
            } else { // Старая логика привязки к оборудованию
                $oldEquipmentId = $model->equipment_id;
                $oldStatus = $model->status;

                $model->scenario = EquipmentPart::SCENARIO_ATTACH;
                $model->equipment_id = $equipmentId;
                $model->installation_date = $data['installation_date'];
                $model->status = EquipmentPart::STATUS_ACTIVE;

                $transaction = Yii::$app->db->beginTransaction();
                try {
                    if($model->save()) {
                        // Добавляем запись в историю
                        $model->addHistory(
                            EquipmentPart::ACTION_INSTALL,
                            $oldStatus,
                            EquipmentPart::STATUS_ACTIVE,
                            Yii::t('app', 'Part attached to equipment'),
                            $model->equipment_id
                        );

                        $transaction->commit();
                        return [
                            'status' => 'success',
                            'message' => Yii::t('app', 'Equipment part attached successfully.')
                        ];
                    } else {
                        $transaction->rollBack();
                        return [
                            'status' => 'error',
                            'errors' => $model->getErrors(),
                        ];
                    }
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage(),
                    ];
                }
            }
        }
    }

    /**
     * Detach part from equipment
     * @return mixed
     */
    public function actionDetach()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = EquipmentPart::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Equipment part not found')
                ];
            }

            if (!$model->equipment_id) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Equipment part is not attached to any equipment')
                ];
            }

            return [
                'content' => $this->renderPartial('_detach_form', [
                    'model' => $model,
                ]),
            ];
        }

        if (Yii::$app->request->isPost) {
            $id = Yii::$app->request->post('id');
            $model = EquipmentPart::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Equipment part not found')
                ];
            }

            if (!$model->equipment_id) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Equipment part is not attached to any equipment')
                ];
            }

            $data = Yii::$app->request->post();
            $oldEquipmentId = $model->equipment_id;
            $oldStatus = $model->status;

            $model->scenario = EquipmentPart::SCENARIO_DETACH;
            $model->equipment_id = null;
            $model->installation_date = null;
            $model->status = $data['status']; // STATUS_RESERVE или STATUS_INACTIVE

            $transaction = Yii::$app->db->beginTransaction();
            try {
                if($model->save()) {
                    // Добавляем запись в историю
                    $actionType = $model->status == EquipmentPart::STATUS_RESERVE ?
                        EquipmentPart::ACTION_RESERVE :
                        EquipmentPart::ACTION_REMOVE;

                    $model->addHistory(
                        $actionType,
                        $oldStatus,
                        $model->status,
                        $data['comment'] ?? Yii::t('app', 'Part detached from equipment'),
                        $oldEquipmentId
                    );

                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'Equipment part detached successfully.')
                    ];
                } else {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors(),
                    ];
                }
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        }
    }

    /**
     * Change status of an equipment part
     * @param integer $id
     * @return mixed
     */
    public function actionChangeStatus($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            return [
                'content' => $this->renderPartial('_status_form', [
                    'model' => $this->findModel($id)
                ])
            ];
        }

        if (Yii::$app->request->isPost) {
            $model = $this->findModel($id);
            $status = Yii::$app->request->post('status');
            $comment = Yii::$app->request->post('comment');

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $oldStatus = $model->status;
                $model->scenario = EquipmentPart::SCENARIO_CHANGE_STATUS;
                $model->status = $status;

                // Если статус меняется на неактивный или резерв, снимаем с оборудования
                if (($status == EquipmentPart::STATUS_INACTIVE || $status == EquipmentPart::STATUS_RESERVE) && $model->equipment_id) {
                    $model->equipment_id = null;
                    $model->installation_date = null;
                }

                if (!$model->save()) {
                    throw new \Exception(Yii::t('app', 'error_changing_status'));
                }

                // Добавляем запись в историю
                $actionType = $model->status == EquipmentPart::STATUS_RESERVE ?
                    EquipmentPart::ACTION_RESERVE :
                    ($model->status == EquipmentPart::STATUS_ACTIVE ?
                        EquipmentPart::ACTION_INSTALL :
                        EquipmentPart::ACTION_REMOVE);

                $model->addHistory(
                    $actionType,
                    $oldStatus,
                    $model->status,
                    $comment ?? Yii::t('app', 'Status changed')
                );

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'status_changed_successfully')
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }
    }

    /**
     * Get photo of an equipment part
     * @param integer $id
     * @return mixed
     */   
     public function actionGetPhoto($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $part = EquipmentPart::findOne($id);
        if ($part) {
            // Получаем все фотографии запчасти
            $allPhotos = EquipmentPartPhoto::find()
                ->where(['equipment_part_id' => $id, 'deleted_at' => null])
                ->orderBy(['is_main' => SORT_DESC, 'sort_order' => SORT_ASC])
                ->all();
            
            $photos = [];
            
            // Преобразуем в массив URL-ов
            foreach ($allPhotos as $photo) {
                $photos[] = [
                    'url' => $photo->photoUrl,
                    'original_name' => $photo->original_name
                ];
            }
            
            // Если нет фотографий в новой системе, используем старое поле
            if (empty($photos) && $part->photo) {
                $photos[] = [
                    'url' => '/uploads/equipment_parts/' . $part->photo,
                    'original_name' => $part->photo
                ];
            }
            
            if (!empty($photos)) {
                return [
                    'status' => 'success',
                    'photos' => $photos,
                    'name' => $part->name
                ];
            }
        }
        
        return [
            'status' => 'error',
            'message' => Yii::t('app', 'Photo not found')
        ];
    }

    /**
     * Get history of an equipment part actions and movements
     * @param integer $id
     * @return mixed
     */
    public function actionHistory($id)
    {
        $model = $this->findModel($id);

        // Получаем параметры фильтрации из запроса
        $startDate = Yii::$app->request->get('start_date');
        $endDate = Yii::$app->request->get('end_date');

        // Получаем историю действий с количеством из назначений (исключаем создание и снятие запчасти)
        $sql = "
            SELECT
                eph.*,
                CASE
                    WHEN eph.action_type = 1 THEN
                        COALESCE(eph.quantity, epa.quantity)
                    WHEN eph.action_type = 3 THEN
                        COALESCE(eph.quantity, epa.quantity)
                    WHEN eph.action_type = 5 THEN
                        eph.quantity
                    WHEN eph.action_type = 6 THEN
                        eph.quantity
                    ELSE eph.quantity
                END as assignment_quantity
            FROM equipment_part_history eph
            LEFT JOIN equipment_part_assignments epa ON (
                eph.equipment_part_id = epa.equipment_part_id
                AND eph.equipment_id = epa.equipment_id
                AND eph.action_type IN (1, 3, 5, 6)
                AND ABS(EXTRACT(EPOCH FROM (eph.created_at - epa.created_at))) < 60
            )
            WHERE eph.equipment_part_id = :part_id
            AND eph.action_type NOT IN (2, 4)
        ";

        $params = [':part_id' => $id];

        // Применяем фильтр по дате для истории, если он задан
        if ($startDate && $endDate) {
            // Преобразуем даты в формат Y-m-d для SQL
            $startDateFormatted = date('Y-m-d 00:00:00', strtotime($startDate));
            $endDateFormatted = date('Y-m-d 23:59:59', strtotime($endDate));

            $sql .= " AND eph.created_at >= :start_date AND eph.created_at <= :end_date";
            $params[':start_date'] = $startDateFormatted;
            $params[':end_date'] = $endDateFormatted;
        }

        $sql .= " ORDER BY eph.created_at DESC";

        // Выполняем запрос для истории
        $historyData = Yii::$app->db->createCommand($sql, $params)->queryAll();

        // Получаем движения (приход, расход, брак)
        $movementsQuery = EquipmentPartMovement::find()
            ->where(['equipment_part_id' => $id])
            ->with(['createdBy']);

        // Применяем фильтр по дате для движений, если он задан
        if ($startDate && $endDate) {
            $movementsQuery->andWhere(['>=', 'created_at', $startDateFormatted])
                           ->andWhere(['<=', 'created_at', $endDateFormatted]);
        }

        // Получаем результаты движений
        $movements = $movementsQuery->orderBy(['created_at' => SORT_DESC])->all();

        // Подсчитываем статистику по браку
        $defectStats = EquipmentPartMovement::find()
            ->where(['equipment_part_id' => $id, 'movement_type' => EquipmentPartMovement::MOVEMENT_DEFECT])
            ->select(['SUM(quantity) as total_defect', 'COUNT(*) as defect_count'])
            ->asArray()
            ->one();

        return $this->render('history', [
            'model' => $model,
            'historyData' => $historyData,
            'movements' => $movements,
            'defectStats' => $defectStats
        ]);
    }

    /**
     * Finds the EquipmentPart model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return EquipmentPart the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = EquipmentPart::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException(Yii::t('app', 'The requested page does not exist.'));
    }

    /**
     * Income action for equipment part
     * @return mixed
     */
    public function actionIncome()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = $this->findModel($id);

            return [
                'content' => $this->renderPartial('_income_form', [
                    'model' => $model
                ])
            ];
        }

        if (Yii::$app->request->isPost) {
            $id = Yii::$app->request->post('id');
            $model = $this->findModel($id);
            $data = Yii::$app->request->post();

            // Валидация данных
            $errors = [];
            if (empty($data['quantity']) || !is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                $errors['quantity'] = [Yii::t('app', 'quantity_required')];
            }

            if (!empty($errors)) {
                return [
                    'status' => 'error',
                    'errors' => $errors
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Создаем запись о движении
                $movement = new EquipmentPartMovement();
                $movement->equipment_part_id = $model->id;
                $movement->movement_type = EquipmentPartMovement::MOVEMENT_INCOME;
                $movement->quantity = $data['quantity'];
                // Обрабатываем цену: если пустая строка или 0, то null
                $pricePerUnit = $data['price_per_unit'] ?? null;
                if ($pricePerUnit === '' || $pricePerUnit === '0' || $pricePerUnit == 0) {
                    $pricePerUnit = null;
                }
                $movement->price_per_unit = $pricePerUnit;
                $movement->comment = $data['comment'] ?? null;

                if (!$movement->save()) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'errors' => $movement->getErrors()
                    ];
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'income_recorded_successfully')
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }
    }

    /**
     * Defect action for equipment part
     * @return mixed
     */
    public function actionDefect()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = $this->findModel($id);

            return [
                'content' => $this->renderPartial('_defect_form', [
                    'model' => $model
                ])
            ];
        }

        if (Yii::$app->request->isPost) {
            $id = Yii::$app->request->post('id') ?: Yii::$app->request->get('id');
            $model = $this->findModel($id);
            $data = Yii::$app->request->post();

            // Валидация данных
            $errors = [];
            if (empty($data['quantity']) || !is_numeric($data['quantity']) || $data['quantity'] <= 0) {
                $errors['quantity'] = [Yii::t('app', 'quantity_required_positive')];
            } elseif ($data['quantity'] > $model->quantity) {
                $errors['quantity'] = [Yii::t('app', 'defect_quantity_exceeds_available')];
            }

            if (empty($data['comment'])) {
                $errors['comment'] = [Yii::t('app', 'defect_reason_required')];
            }

            if (!empty($errors)) {
                return [
                    'status' => 'error',
                    'errors' => $errors
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Создаем запись о движении
                $movement = new EquipmentPartMovement();
                $movement->equipment_part_id = $model->id;
                $movement->movement_type = EquipmentPartMovement::MOVEMENT_DEFECT;
                $movement->quantity = $data['quantity'];
                $movement->comment = $data['comment'];

                if (!$movement->save()) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'errors' => $movement->getErrors()
                    ];
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'defect_recorded_successfully')
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }
    }

    /**
     * Управление фотографиями запчасти
     */
    public function actionPhotos()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $id = Yii::$app->request->get('id');
        $model = $this->findModel($id);
        
        if (Yii::$app->request->isGet) {
            // Получаем все фотографии запчасти
            $photos = $model->photos;
            
            return [
                'content' => $this->renderPartial('photos', [
                    'model' => $model,
                    'photos' => $photos
                ]),
            ];
        }
        
        if (Yii::$app->request->isPost) {
            $action = Yii::$app->request->post('action');
            
            switch ($action) {
                case 'add':
                    return $this->addPhotos($model);
                case 'delete':
                    return $this->deletePhoto();
                case 'set_main':
                    return $this->setMainPhoto();
                case 'reorder':
                    return $this->reorderPhotos();
                default:
                    return ['status' => 'error', 'message' => 'Unknown action'];
            }
        }
    }

    /**
     * Добавление новых фотографий
     */
    private function addPhotos($model)
    {
        $uploadedFiles = UploadedFile::getInstancesByName('photos');
        if (empty($uploadedFiles)) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'No photos selected')
            ];
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $uploadedCount = 0;
            foreach ($uploadedFiles as $file) {
                if (EquipmentPartPhoto::saveUploadedFile($file, $model->id)) {
                    $uploadedCount++;
                }
            }

            if ($uploadedCount > 0) {
                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Photos uploaded successfully'),
                    'uploaded_count' => $uploadedCount
                ];
            } else {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Failed to upload photos')
                ];
            }
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Удаление фотографии
     */
    private function deletePhoto()
    {
        $photoId = Yii::$app->request->post('photo_id');
        $photo = EquipmentPartPhoto::findOne($photoId);
        
        if (!$photo) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Photo not found')
            ];
        }

        if ($photo->softDelete()) {
            return [
                'status' => 'success',
                'message' => Yii::t('app', 'Photo deleted successfully')
            ];
        } else {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Failed to delete photo')
            ];
        }
    }

    /**
     * Установка главной фотографии
     */
    private function setMainPhoto()
    {
        $photoId = Yii::$app->request->post('photo_id');
        $photo = EquipmentPartPhoto::findOne($photoId);
        
        if (!$photo) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Photo not found')
            ];
        }

        if ($photo->setAsMain()) {
            return [
                'status' => 'success',
                'message' => Yii::t('app', 'Main photo set successfully')
            ];
        } else {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Failed to set main photo')
            ];
        }
    }

    /**
     * Изменение порядка фотографий
     */
    private function reorderPhotos()
    {
        $photoOrders = Yii::$app->request->post('photo_orders', []);
        
        $transaction = Yii::$app->db->beginTransaction();
        try {
            foreach ($photoOrders as $photoId => $order) {
                EquipmentPartPhoto::updateAll(
                    ['sort_order' => $order],
                    ['id' => $photoId]
                );
            }
            
            $transaction->commit();
            return [
                'status' => 'success',
                'message' => Yii::t('app', 'Photo order updated successfully')
            ];
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Write off action for equipment part
     * @return mixed
     */
    public function actionWriteOff()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = $this->findModel($id);

            return [
                'content' => $this->renderPartial('_write_off_form', [
                    'model' => $model
                ])
            ];
        }

        if (Yii::$app->request->isPost) {
            $id = Yii::$app->request->post('id');
            $model = $this->findModel($id);
            $data = Yii::$app->request->post();

            $quantityToWriteOff = $data['quantity'] ?? 1;
            $recipient = $data['recipient'] ?? '';
            $comment = $data['comment'] ?? '';

            if (empty($recipient)) {
                return [
                    'status' => 'error',
                    'errors' => ['recipient' => [Yii::t('app', 'Recipient cannot be blank.')]],
                ];
            }

            if ($model->quantity < $quantityToWriteOff) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Not enough quantity to write off')
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $movement = new EquipmentPartMovement();
                $movement->equipment_part_id = $model->id;
                $movement->movement_type = EquipmentPartMovement::MOVEMENT_OUTCOME;
                $movement->quantity = $quantityToWriteOff;
                $movement->recipient_name = $recipient;
                $movement->comment = $comment;
                
                if ($movement->save()) {
                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'Successfully written off')
                    ];
                } else {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'errors' => $movement->getErrors(),
                    ];
                }
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        }
    }
}
