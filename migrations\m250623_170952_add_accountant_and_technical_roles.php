<?php

use yii\db\Migration;

class m250623_170952_add_accountant_and_technical_roles extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $auth = Yii::$app->authManager;

        $accountantRole = $auth->createRole('accountant');
        $accountantRole->description = 'Бухгалтер';
        $auth->add($accountantRole);

        $technicalRole = $auth->createRole('technical_staff');
        $technicalRole->description = 'Технический сотрудник';
        $auth->add($technicalRole);

        $this->insert('users', [
            'username' => 'buxgalter',
            'role' => 'accountant',
            'full_name' => 'Бухгалтер',
            'password' => Yii::$app->security->generatePasswordHash('buxgalter123'),
            'created_at' => new \yii\db\Expression('NOW()'),
        ]);

        $accountantUserId = $this->db->getLastInsertID();
        $auth->assign($accountantRole, $accountantUserId);

        $this->insert('users', [
            'username' => 'technical',
            'role' => 'technical_staff',
            'full_name' => 'Технический сотрудник',
            'password' => Yii::$app->security->generatePasswordHash('technical123'),
            'created_at' => new \yii\db\Expression('NOW()'),
        ]);

        $technicalUserId = $this->db->getLastInsertID();
        $auth->assign($technicalRole, $technicalUserId);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $auth = Yii::$app->authManager;
        
        $this->delete('users', ['username' => ['buxgalter', 'technical']]);
        
        $auth->remove($auth->getRole('accountant'));
        $auth->remove($auth->getRole('technical_staff'));
    }
}
