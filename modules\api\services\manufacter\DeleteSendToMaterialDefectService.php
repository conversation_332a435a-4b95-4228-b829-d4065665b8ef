<?php

namespace app\modules\api\services\manufacter;

use Yii;
use app\common\models\ActionLogger;
use app\common\models\MaterialDefect;
use app\common\models\MaterialProduction;
use app\common\models\MaterialStatusGroup;
use app\common\models\MaterialStatus;
use app\common\models\Tracking;
use yii\base\Component;

/**
 * Сервис для удаления записи о браке материалов в процессе производства
 */
class DeleteSendToMaterialDefectService extends Component
{
    /**
     * Удаление записи о браке материала или возврате
     *
     * @param int $id ID записи (material_defect_id или group_id)
     * @param string $type Тип записи: 'defect' или 'return'
     * @return array Результат операции
     */
    public function deleteMaterialDefect($id, $type = 'defect')
    {
        if (!$id) {
            return [
                'success' => false,
                'message' => Yii::t('app', 'record_id_required')
            ];
        }

        if ($type === 'defect') {
            return $this->deleteMaterialDefectRecord($id);
        } elseif ($type === 'return') {
            return $this->deleteMaterialReturnRecord($id);
        }

        return [
            'success' => false,
            'message' => Yii::t('app', 'unknown_record_type')
        ];
    }

    /**
     * Удаление записи о браке материала
     */
    private function deleteMaterialDefectRecord($materialDefectId)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $materialDefect = MaterialDefect::findOne([
                'id' => $materialDefectId,
                'deleted_at' => null,
                'add_user_id' => Yii::$app->user->id
            ]);

            if (!$materialDefect) {
                throw new \Exception(Yii::t('app', 'record_not_found_or_confirmed'));
            }

            // Проверка времени - можно удалять только в течение 24 часов
            $createdTime = strtotime($materialDefect->created_at);
            $currentTime = time();
            $timeDifference = $currentTime - $createdTime;

            if ($timeDifference > 86400) { // 86400 секунд = 24 часа
                throw new \Exception(Yii::t('app', 'deletion_allowed_only_24_hours'));
            }

            $tracking = Tracking::find()
                ->where([
                    'process_id' => $materialDefectId,
                    'progress_type' => Tracking::TYPE_MATERIAL_DEFECT,
                    'deleted_at' => null
                ])
                ->one();

            if ($tracking && $tracking->status === Tracking::STATUS_ACCEPTED) {
                throw new \Exception(Yii::t('app', 'material_already_confirmed_cannot_delete'));
            }

            // КРИТИЧЕСКИ ВАЖНО: Возвращаем материал в производство при удалении
            $this->returnMaterialToProduction($materialDefect->material_id, $materialDefect->quantity);

            if ($tracking) {
                $tracking->deleted_at = date('Y-m-d H:i:s');
                if (!$tracking->save()) {
                    throw new \Exception(Yii::t('app', 'error_deleting_tracking_record'));
                }
            }

            $materialDefect->deleted_at = date('Y-m-d H:i:s');
            if (!$materialDefect->save()) {
                throw new \Exception(Yii::t('app', 'error_deleting_defect_record'));
            }

            ActionLogger::actionLog(
                'delete_send_to_defect',
                'material_defect',
                $materialDefect->id,
                [
                    'material_id' => $materialDefect->material_id,
                    'quantity' => $materialDefect->quantity,
                    'description' => $materialDefect->description,
                    'is_processed' => $materialDefect->is_processed
                ]
            );

            $transaction->commit();
            return [
                'success' => true,
                'message' => Yii::t('app', 'defect_record_deleted_successfully')
            ];
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Удаление записи о возврате материала
     */
    private function deleteMaterialReturnRecord($groupId)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $materialStatusGroup = MaterialStatusGroup::findOne([
                'id' => $groupId,
                'deleted_at' => null,
                'status' => MaterialStatusGroup::STATUS_RETURNED_FROM_PRODUCTION,
                'add_user_id' => Yii::$app->user->id
            ]);

            if (!$materialStatusGroup) {
                throw new \Exception(Yii::t('app', 'return_record_not_found'));
            }

            // Проверка времени - можно удалять только в течение 24 часов
            $createdTime = strtotime($materialStatusGroup->created_at);
            $currentTime = time();
            $timeDifference = $currentTime - $createdTime;

            if ($timeDifference > 86400) { // 86400 секунд = 24 часа
                throw new \Exception(Yii::t('app', 'deletion_allowed_only_24_hours'));
            }

            // Проверяем статус в Tracking
            $tracking = Tracking::find()
                ->where([
                    'process_id' => $groupId,
                    'progress_type' => Tracking::TYPE_MATERIAL_RETURN,
                    'deleted_at' => null
                ])
                ->one();

            if ($tracking && $tracking->status === Tracking::STATUS_ACCEPTED) {
                throw new \Exception(Yii::t('app', 'return_already_confirmed_cannot_delete'));
            }

            // Удаляем связанные записи MaterialStatus
            $materialStatuses = MaterialStatus::find()
                ->where([
                    'status_group_id' => $groupId,
                    'deleted_at' => null
                ])
                ->all();

            foreach ($materialStatuses as $materialStatus) {
                $materialStatus->delete();
            }

            // Удаляем tracking запись
            if ($tracking) {
                $tracking->deleted_at = date('Y-m-d H:i:s');
                if (!$tracking->save()) {
                    throw new \Exception(Yii::t('app', 'error_deleting_tracking_record'));
                }
            }

            // Удаляем группу
            $materialStatusGroup->deleted_at = date('Y-m-d H:i:s');
            if (!$materialStatusGroup->save()) {
                throw new \Exception(Yii::t('app', 'error_deleting_return_record'));
            }

            ActionLogger::actionLog(
                'delete_material_return',
                'material_status_group',
                $materialStatusGroup->id,
                [
                    'status' => $materialStatusGroup->status,
                    'materials_count' => count($materialStatuses)
                ]
            );

            $transaction->commit();
            return [
                'success' => true,
                'message' => Yii::t('app', 'return_record_deleted_successfully')
            ];
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Возвращает материал в производство при удалении записи о браке
     *
     * @param int $materialId ID материала
     * @param float $quantity Количество для возврата
     * @throws \Exception
     */
    private function returnMaterialToProduction($materialId, $quantity)
    {
        $materialProduction = MaterialProduction::find()
            ->where([
                'material_id' => $materialId,
                'deleted_at' => null,
                'DATE(created_at)' => date('Y-m-d')
            ])
            ->one();

        if (!$materialProduction) {
            $materialProduction = new MaterialProduction();
            $materialProduction->material_id = $materialId;
            $materialProduction->quantity = 0;
            $materialProduction->created_at = date('Y-m-d H:i:s');
        }

        $materialProduction->quantity += $quantity;
        $materialProduction->updated_at = date('Y-m-d H:i:s');

        if (!$materialProduction->save()) {
            throw new \Exception(Yii::t('app', 'error_returning_material_to_production') . ': ' . json_encode($materialProduction->getErrors()));
        }

        Yii::info("Материал возвращен в производство при удалении брака: материал ID {$materialId}, количество: {$quantity}", 'material_defect');
    }
}
