<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $reportData array */
/* @var $date string */
/* @var $productId int */
/* @var $products array */

$this->title = Yii::t('app', 'production_report');

?>

<style>
    .text-right {
        text-align: right;
    }

    .text-center {
        text-align: center;
    }
</style>

<div class="row align-items-center mb-3">
    <div class="col-md-6">
        <h3 class="mb-0"><?= Yii::t('app', 'production_report_today') ?></h3>
    </div>
    <div class="col-md-6 d-flex justify-content-end">
        <?php $form = ActiveForm::begin([
            'method' => 'get',
            'action' => ['/backend/report/production'],
            'options' => ['class' => 'd-inline-flex align-items-center']
        ]); ?>
        <?= Html::input('date', 'date', $date, [
            'class' => 'form-control mr-2',
            'style' => 'width: 150px;'
        ]) ?>
        <?= Html::dropDownList(
            'product_id',
            $productId,
            ['' => Yii::t('app', 'all_products')] + $products,
            [
                'class' => 'form-control mr-2',
                'style' => 'width: 200px;'
            ]
        ) ?>
        <?= Html::submitButton('<i class="fas fa-search"></i> ' . Yii::t('app', 'search'), ['class' => 'btn btn-primary']) ?>
        <?php ActiveForm::end(); ?>
    </div>
</div>


<!-- Таблица выпущенных продуктов -->
<?php if (!empty($reportData['produced_products']['items'])): ?>
    <div class="mb-4">
        <div class="card">

            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th><?= Yii::t('app', 'product_name') ?></th>
                                <th class="text-center"><?= Yii::t('app', 'quantity') ?></th>
                                <th class="text-center"><?= Yii::t('app', 'production_time') ?></th>
                                <th class="text-center"><?= Yii::t('app', 'added_by_production') ?></th>
                                <th class="text-center"><?= Yii::t('app', 'accepted_by') ?></th>
                                <th class="text-center"><?= Yii::t('app', 'status') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reportData['produced_products']['items'] as $item): ?>
                                <tr>
                                    <td>
                                        <strong><?= Html::encode($item['product_name']) ?></strong>
                                    </td>
                                    <td class="text-right">
                                        <span class="badge badge-primary">
                                            <?= Html::encode($item['quantity']) ?>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <?= date('H:i', strtotime($item['enter_date'])) ?>
                                        <br>
                                        <small class="text-muted"><?= date('d.m.Y', strtotime($item['enter_date'])) ?></small>
                                        <?php if (isset($item['entry_count']) && $item['entry_count'] > 1): ?>
                                            <br>
                                            <small class="text-muted">(<?= $item['entry_count'] ?> записей)</small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?= Html::encode($item['added_by']) ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($item['accepted_at']): ?>
                                            <?= Html::encode($item['accepted_by']) ?>
                                            <br>
                                            <small class="text-muted"><?= date('H:i d.m.Y', strtotime($item['accepted_at'])) ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($item['status'] === 'Принят'): ?>
                                            <span class="badge badge-success"><?= Html::encode($item['status']) ?></span>
                                        <?php else: ?>
                                            <span class="badge badge-warning"><?= Html::encode($item['status']) ?></span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="alert alert-info text-center mt-3">
        <i class="fas fa-info-circle mr-2"></i> <?= Yii::t('app', 'no_data_found') ?>.
    </div>
<?php endif; ?>