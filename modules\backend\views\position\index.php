<?php

use app\common\models\Cashbox;
use app\common\models\Sizes;
use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\grid\GridView;
use yii\grid\ActionColumn;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "position_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");

?>

    <div class="card-body">

            <div class="row align-items-center mb-3">
                <div class="col-md-6">
                    <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
                </div>

                <div class="col-md-6 text-right">
                    <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('accountant')): ?>
                        <a href="#" class="btn btn-primary position-create" data-toggle="modal" data-target="#ideal-mini-modal">
                            <?= Yii::t("app", "add_position") ?>
                        </a>
                    <?php endif ?>
                </div>
            </div>

            <?php Pjax::begin(['id' => 'position-grid-pjax']); ?>
        <?php if($dataProvider->models): ?>

            <div>
                <table id="position-grid-view" class="table table-bordered table-striped compact">
                    <thead>
                        <th><?= Yii::t("app", "position_name") ?></th>
                        <th><?= Yii::t("app", "created_at") ?></th>
                        <th><?= Yii::t("app", "status") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </thead>
                    <tbody>
                    <?php foreach ($dataProvider->models as $model): ?>

                            <tr>
                                <td><?= Html::encode($model->name) ?></td>
                                <td data-order="<?= !empty($model->created_at) ? strtotime($model->created_at) : '' ?>">
                                    <?= !empty($model->created_at) ? Html::encode(date('d.m.Y H:i', strtotime($model->created_at))) : 'N/A' ?>
                                </td>

                                <td>
                                    <span class="<?= $model['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                            <?php
                                            $statusText = $model['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                            echo $statusText;
                                            ?>
                                    </span>
                                </td>

                                <td>
                                    <div class="dropdown d-inline">

                                        <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                            <?php echo Yii::t("app", "detail"); ?>
                                        </a>

                                        <div class="dropdown-menu">

                                            <a href="#" class="dropdown-item position-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model->id) ?>">
                                                    <?= Yii::t("app", "edit") ?>
                                                </a>
                                              <?php if ($model->deleted_at == NULL): ?>
                                                <a href="#" class="dropdown-item position-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model->id) ?>">
                                                        <span class="red-text"><?= Yii::t("app", "delete") ?></span>
                                                </a>
                                                <?php endif; ?>

                                        </div>
                                    </div>
                                </td>

                            </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

        <?php else: ?>
            <p><?= Yii::t('app', 'no_data_available') ?></p>
        <?php endif; ?>
        <?php Pjax::end(); ?>

  </div>

<div id="one" data-text="<?= Yii::t("app", "add_position") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "edit_position") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "position_delete") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#position-grid-view')) {
            $('#position-grid-view').DataTable().destroy();
        }
        
        $('#position-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[1, 'desc']], // Сортировка по дате создания по умолчанию
            "columnDefs": [
                {
                    "targets": [2, 3], // Столбцы статуса и действий
                    "orderable": false
                }
            ]
        });
    }

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }
    
    function initializePositionCreate() {
        $(document).off('click.position-create').on('click.position-create', '.position-create', function() {
            $.ajax({
                url: '/backend/position/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("position-create-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.position-create-button').on('click.position-create-button', '.position-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#position-create-form').serialize();
                $.ajax({
                    url: '/backend/position/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.success) {
                            $.pjax.reload({container: '#position-grid-pjax'});
                            $('.close').trigger('click');
                        } else if (response && response.errors) {
                            $.each(response.errors, function(key, value) {
                                 $('#' + key + '-error').html('<span class="error-text">' + value.join('<br>') + '</span>');
                            });
                            $('#ideal-mini-modal .modal-body').text(response.content);
                        } else {
                            $.pjax.reload({container: '#position-grid-pjax'});
                            $('.close').trigger('click');
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });

        $(document).off('keypress.position-create-button').on('keypress.position-create-button', '#position-create-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('.position-create-button').trigger('click');
            }
        });
    }

    function initializePositionUpdate() {
        $(document).off('click.position-update').on('click.position-update', '.position-update', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/position/update',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    if (response) {
                        $('#ideal-mini-modal .modal-title').html(two);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').addClass("position-update-button");
                        initializeSelect2();
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.position-update-button').on('click.position-update-button', '.position-update-button', function() {
            if (!$(this).prop('disabled')) {
                var formData = $('#position-update-form').serialize();
                $.ajax({
                    url: '/backend/position/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.status === 'success') {
                            $.pjax.reload({ container: '#position-grid-pjax' });
                            $('.close').trigger('click');
                        } else {
                            $('#ideal-mini-modal .modal-body').html(response.content);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });

        $(document).off('keypress.position-update-button').on('keypress.position-update-button', '#position-update-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('.position-update-button').trigger('click');
            }
        });
    }

    function initializePositionDelete() {
        $(document).off('click.position-delete').on('click.position-delete', '.position-delete', function() {
            var id = $(this).attr("data-id");
            $('#ideal-mini-modal-delete .mini-button').removeClass("btn-primary");
            $.ajax({
                url: '/backend/position/delete',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal-delete .modal-title').html(three);
                        $('#ideal-mini-modal-delete .modal-body').html(response.content);
                        $('#ideal-mini-modal-delete .mini-button').addClass("btn-danger delete-position-button");
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.delete-position-button').on('click.delete-position-button', '.delete-position-button', function() {
            if (!$(this).prop('disabled')) {
                $('.delete-position-button').prop('disabled', true);
                var formData = $('#position-delete-form').serialize();
                $.ajax({
                    url: '/backend/position/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.status === 'success') {
                            $.pjax.reload({ container: '#position-grid-pjax' });
                            $('.delete-position-button').prop('disabled', false);
                            $('.close').trigger('click');
                        } else if (response.status === 'error') {
                            $('#ideal-mini-modal-delete .modal-body').html(response.content);
                            iziToast.error({
                                timeout: 5000,
                                icon: 'fas fa-exclamation-triangle',
                                message: response.message,
                                position: 'topRight',
                                onOpening: function(instance, toast) {
                                    toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                                }
                            });
                            $('.delete-position-button').prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        $('.delete-position-button').prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });

        $(document).off('keypress.position-delete-button').on('keypress.position-delete-button', '#position-delete-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('.position-delete-button').trigger('click');
            }
        });
    }

    function initializeAll() {
        initializeDataTable();
        initializeDropdown();
        initializeSelect2();
        initializePositionCreate();
        initializePositionUpdate();
        initializePositionDelete();
    }
    
    $(document).ready(initializeAll);
    
    $(document).on('pjax:complete', initializeAll);

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
