<?php

namespace app\modules\api;

/**
 * api module definition class
 */
class ApiModule extends \yii\base\Module
{
    /**
     * {@inheritdoc}
     */
    public $controllerNamespace = 'app\modules\api\controllers';

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        
        \Yii::$app->user->identityClass = 'app\modules\api\models\Users';
        \Yii::$app->user->enableSession = false; 
        \Yii::$app->user->loginUrl = null; 
    }
}
