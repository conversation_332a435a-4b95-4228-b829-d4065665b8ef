<?php

use yii\db\Migration;

/**
 * <PERSON>les adding columns to table `{{%equipment_parts}}`.
 */
class m251003_000001_add_currency_and_unit_to_equipment_parts extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Добавляем колонку currency_id
        $this->addColumn('{{%equipment_parts}}', 'currency_id', $this->bigInteger()->null()->after('price'));
        // Добавляем колонку unit_type (1 – шт, 2 – кг, 3 – л)
        $this->addColumn('{{%equipment_parts}}', 'unit_type', $this->smallInteger()->null());

        // Индекс и внешний ключ для currency_id
        $this->createIndex('idx-equipment_parts-currency_id', '{{%equipment_parts}}', 'currency_id');
        $this->addForeignKey('fk-equipment_parts-currency_id', '{{%equipment_parts}}', 'currency_id', '{{%currency}}', 'id', 'SET NULL', 'CASCADE');

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем FK и индекс
        $this->dropForeignKey('fk-equipment_parts-currency_id', '{{%equipment_parts}}');
        $this->dropIndex('idx-equipment_parts-currency_id', '{{%equipment_parts}}');

        // Удаляем колонки
        $this->dropColumn('{{%equipment_parts}}', 'currency_id');
        $this->dropColumn('{{%equipment_parts}}', 'unit_type');
    }
} 