<?php
use yii\helpers\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<form id="part-attach-form">
    <input type="hidden" name="id" value="<?= $model->id ?>">
    
    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'Equipment') ?></label>
        <select class="form-control select2" name="equipment_id" required>
            <option value=""><?= Yii::t('app', 'select_equipment') ?></option>
            <option value="0"><?= Yii::t('app', 'write_off_for_general_needs') ?></option>
            <?php foreach ($equipments as $equipment): ?>
                <option value="<?= $equipment->id ?>"><?= Html::encode($equipment->name) ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'Quantity') ?></label>
        <input type="number" class="form-control" name="quantity" value="1" min="1" required>
    </div>

    <div class="form-group">
        <label class="form-label"><?= Yii::t('app', 'Installation Date') ?></label>
        <input type="date" class="form-control" name="installation_date" value="<?= date('Y-m-d') ?>" required>
    </div>
</form>

<script>
$(document).ready(function() {
    var equipmentIdField = $('select[name="equipment_id"]');
    var installationDateField = $('input[name="installation_date"]').parent();

    function toggleInstallationDate() {
        if (equipmentIdField.val() == 0) {
            installationDateField.hide();
            installationDateField.find('input').prop('required', false);
        } else {
            installationDateField.show();
            installationDateField.find('input').prop('required', true);
        }
    }

    toggleInstallationDate();

    equipmentIdField.on('change', function() {
        toggleInstallationDate();
    });
});
</script>
