<?php

namespace app\modules\backend\controllers;

use app\modules\backend\models\TransferForm;
use Yii;
use app\common\models\Cashbox;
use app\common\models\CashboxPaymentType;
use app\common\models\Currency;
use app\common\models\PaymentType;
use yii\data\ArrayDataProvider;
use yii\web\Response;

class CashboxController extends BaseController
{
    public function actionIndex()
    {
        $positions = Cashbox::find()->where(['deleted_at' => null])->all();

        $dataProvider = new ArrayDataProvider([
            'allModels' => $positions,
            'pagination' => [
                'pageSize' => 50,
            ],
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $model = new Cashbox();

            if (!$model->load($data)) {
                return [
                    'status' => 'error',
                    'errors' => ['Не удалось загрузить данные.']
                ];
            }

            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }

            // Проверяем уникальность комбинации title + currency_id
            $existingCashbox = Cashbox::find()
                ->where([
                    'title' => $model->title,
                    'currency_id' => $model->currency_id,
                    'deleted_at' => null
                ])
                ->one();

            if ($existingCashbox) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'cashbox_with_this_title_and_currency_already_exists')
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $model->balance = 0;
                $model->created_at = date('Y-m-d H:i:s');

                if (!$model->save()) {
                    throw new \Exception('Ошибка при сохранении кассы: ' . json_encode($model->getErrors()));
                }

                // Автоматически связываем кассу со всеми типами платежей
                $paymentTypes = PaymentType::find()->where(['deleted_at' => null])->all();
                foreach ($paymentTypes as $paymentType) {
                    $cashboxPaymentType = new CashboxPaymentType();
                    $cashboxPaymentType->cashbox_id = $model->id;
                    $cashboxPaymentType->payment_type_id = $paymentType->id;
                    $cashboxPaymentType->created_at = date('Y-m-d H:i:s');

                    if (!$cashboxPaymentType->save()) {
                        throw new \Exception('Ошибка при связывании типа платежа: ' . json_encode($cashboxPaymentType->getErrors()));
                    }
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'cashbox_successfully_created_and_linked_to_all_payment_types'),
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        } elseif (Yii::$app->request->isGet) {
            $model = new Cashbox();
            $currencies = Currency::find()->where(['deleted_at' => null])->all();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('create', [
                    'model' => $model,
                    'currencies' => $currencies,
                ])
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Неподдерживаемый метод запроса.',
        ];
    }

    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $id = $data['Cashbox']['id'];
            $model = Cashbox::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'cashbox_not_found'),
                ];
            }

            $oldTitle = $model->title;
            $oldCurrency = $model->currency_id;

            if (!$model->load($data)) {
                return [
                    'status' => 'error',
                    'errors' => [Yii::t('app', 'Failed to load data.')]
                ];
            }   

            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }

            // Проверяем уникальность если изменились title или currency_id
            if ($oldTitle != $model->title || $oldCurrency != $model->currency_id) {
                $existingCashbox = Cashbox::find()
                    ->where([
                        'title' => $model->title,
                        'currency_id' => $model->currency_id,
                        'deleted_at' => null
                    ])
                    ->andWhere(['!=', 'id', $id])
                    ->one();

                if ($existingCashbox) {
                    return [
                        'status' => 'error',
                        'message' => Yii::t('app', 'cashbox_with_this_title_and_currency_already_exists'),
                    ];
                }
            }

            if ($model->save()) {
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'cashbox_successfully_updated'),
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }
        } elseif (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Cashbox::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'cashbox_not_found'),
                ];
            }

            $currencies = Currency::find()->where(['deleted_at' => null])->all();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model,
                    'currencies' => $currencies,
                ])
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Неподдерживаемый метод запроса.',
        ];
    }

    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $id = $data['Cashbox']['id'];
            $model = Cashbox::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'cashbox_not_found'),
                ];
            }

            // Проверяем, есть ли операции в этой кассе
            $hasOperations = $model->getCashboxDetails()
                ->where(['deleted_at' => null])
                ->exists();

            if ($hasOperations) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'cannot_delete_cashbox_with_operations_use_soft_delete'),
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Удаляем связи с типами платежей
                CashboxPaymentType::updateAll(
                    ['deleted_at' => date('Y-m-d H:i:s')],
                    ['cashbox_id' => $model->id, 'deleted_at' => null]
                );

                // Удаляем кассу
                $model->deleted_at = date('Y-m-d H:i:s');
                if (!$model->save()) {
                    throw new \Exception(Yii::t('app', 'error_deleting_cashbox'));
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'cashbox_successfully_deleted'),
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        } elseif (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Cashbox::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'cashbox_not_found'),
                ];
            }

            return [
                'status' => 'success',
                'content' => $this->renderPartial('delete', [
                    'model' => $model,
                ])
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Неподдерживаемый метод запроса.',
        ];
    }

    public function actionDetail($id){
        
        $sql = "
            SELECT 
                cd.amount,
                c.title AS cashbox_name,
                cd.created_at,
                u.full_name AS add_user, 
                cd.type as payment_type
            FROM cashbox_detail cd
            LEFT JOIN cashbox c ON c.id = cd.cashbox_id
            left join users u on cd.add_user_id = u.id
            WHERE cd.cashbox_id = :id 
                AND cd.deleted_at is null
                AND DATE(cd.created_at) = CURRENT_DATE
            order by cd.created_at desc
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':id', $id);
        $result = $command->queryAll();

        return $this->render('detail', [
            'result' => $result,
            'cashbox_id' => $id
        ]);
    }

    public function actionSearch($id)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        
        $startDate = Yii::$app->request->post('start_date', date('Y-m-d'));
        $endDate = Yii::$app->request->post('end_date', date('Y-m-d'));

        $sql = "
            SELECT 
                cd.amount,
                c.title AS cashbox_name,
                cd.created_at,
                u.full_name AS add_user, 
                cd.type as payment_type
            FROM cashbox_detail cd
            LEFT JOIN cashbox c ON c.id = cd.cashbox_id
            left join users u on cd.add_user_id = u.id
            WHERE cd.cashbox_id = :id 
                AND cd.deleted_at is null
        ";

        if ($startDate) {
            $sql .= " AND DATE(cd.created_at) >= :start_date";
        }
        if ($endDate) {
            $sql .= " AND DATE(cd.created_at) <= :end_date";
        }

        $sql .= " ORDER BY cd.created_at DESC";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':id', $id);
        
        if ($startDate) {
            $command->bindValue(':start_date', $startDate);
        }
        if ($endDate) {
            $command->bindValue(':end_date', $endDate);
        }

        $result = $command->queryAll();

        return $this->renderAjax('_detail', [
            'result' => $result
        ]);
    }

    public function actionTransfer()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $model = new TransferForm();

            if (!$model->load($data)) {
                return [
                    'status' => 'error',
                    'errors' => [Yii::t('app', 'failed_to_load_data')]
                ];
            }

            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }

            if ($model->transfer()) {
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'transfer_completed_successfully'),
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }
        } elseif (Yii::$app->request->isGet) {
            $cashbox = Cashbox::find()->where(['deleted_at' => null])->all();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('transfer', [
                    'cashbox' => $cashbox,
                ])
            ];
        }

        return [
            'status' => 'error',
            'message' => Yii::t('app', 'unsupported_request_method'),
        ];
    }
}