<?php

namespace app\common\models;

use Yii;

/**
 * @SWG\Definition(
 *   definition="Material",
 *   @SWG\Property(property="id", type="integer"),
 *   @SWG\Property(property="name", type="string"),
 *   @SWG\Property(property="description", type="string"),
 *   @SWG\Property(property="unit_type", type="integer"),
 *   @SWG\Property(property="category_id", type="integer")
 * )
 * This is the model class for table "material".
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property int|null $unit_type
 * @property int|null $category_id
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property InvoiceDetail[] $invoiceDetails
 * @property MaterialCategory $category
 * @property MaterialDefect[] $materialDefects
 * @property MaterialStorageHistory[] $materialStorageHistories
 * @property MaterialStorage[] $materialStorages
 */
class Material extends \yii\db\ActiveRecord
{
    // Единицы измерения
    public const UNIT_TYPE_PIECE = 1; // Штука
    public const UNIT_TYPE_KG = 2;    // Килограмм
    public const UNIT_TYPE_LITER = 3; // Литр

    /**
     * Возвращает список всех единиц измерения
     * @return array
     */
    public static function getUnitTypeList()
    {
        return [
            self::UNIT_TYPE_PIECE => Yii::t('app', 'unit_piece'),
            self::UNIT_TYPE_KG => Yii::t('app', 'unit_kg'),
            self::UNIT_TYPE_LITER => Yii::t('app', 'unit_liter'),
        ];
    }

    /**
     * Возвращает название единицы измерения по коду
     * @param int $unitType
     * @return string
     */
    public static function getUnitTypeName($unitType)
    {
        $list = self::getUnitTypeList();
        return $list[$unitType] ?? Yii::t('app', 'unknown');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'material';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name'], 'required'],
            [['description'], 'string'],
            [['unit_type', 'category_id'], 'integer'],
            [['unit_type'], 'default', 'value' => self::UNIT_TYPE_PIECE],
            [['unit_type'], 'in', 'range' => array_keys(self::getUnitTypeList())],
            [['created_at', 'deleted_at'], 'safe'],
            [['name'], 'string', 'max' => 255],
            [['category_id'], 'exist', 'skipOnError' => true, 'targetClass' => MaterialCategory::class, 'targetAttribute' => ['category_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => Yii::t('app', 'material_name'),
            'description' => Yii::t('app', 'material_description'),
            'unit_type' => Yii::t('app', 'unit_type'),
            'category_id' => Yii::t('app', 'category'),
            'created_at' => Yii::t('app', 'created_at'),
            'deleted_at' => Yii::t('app', 'deleted_at'),
        ];
    }

    /**
     * Gets query for [[InvoiceDetails]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getInvoiceDetails()
    {
        return $this->hasMany(InvoiceDetail::class, ['material_id' => 'id']);
    }

    /**
     * Gets query for [[MaterialDefects]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterialDefects()
    {
        return $this->hasMany(MaterialDefect::class, ['material_id' => 'id']);
    }


    /**
     * Gets query for [[MaterialStorageHistories]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterialStorageHistories()
    {
        return $this->hasMany(MaterialStorageHistory::class, ['material_id' => 'id']);
    }

    /**
     * Gets query for [[MaterialStorages]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterialStorages()
    {
        return $this->hasMany(MaterialStorage::class, ['material_id' => 'id']);
    }

    /**
     * Gets query for [[Category]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCategory()
    {
        return $this->hasOne(MaterialCategory::class, ['id' => 'category_id']);
    }
}
