<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\grid\GridView;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "expense_type_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");

?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('product_keeper') || Yii::$app->user->can('accountant')): ?>
                <a href="#" class="btn btn-primary expenses-type-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "add_expense_type") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'expenses-type-grid-pjax']); ?>
    <?php if($dataProvider->models): ?>
        <div>
            <table id="expenses-type-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th><?= Yii::t("app", "expense_type_name") ?></th>
                    <th><?= Yii::t("app", "expenses_type_created_at") ?></th>
                    <th><?= Yii::t("app", "status") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($dataProvider->models as $model): ?>
                    <tr>
                    <td>
                        <?php
                            $expenseMapping = [
                                'Oylik' => 'salary',
                                'Avans' => 'advance',
                                'Bonus' => 'bonus',
                                'Debt' => 'debt',
                                'One time payment' => 'one_time_payment',
                            ];

                            $expenseType = $model->name ?? null;

                            echo Yii::t('app', match ($expenseType) {
                                'Oylik' => 'salary',
                                'Avans' => 'advance',
                                'Bonus' => 'bonus',
                                'Debt' => 'debt',
                                'One time payment' => 'one_time_payment',
                                default => $expenseType,
                            });
                        ?>
                    </td>
                        <td data-order="<?= !empty($model->created_at) ? strtotime($model->created_at) : '' ?>">
                            <?= !empty($model->created_at) ? Html::encode(date('d-m-Y H:i', strtotime($model->created_at))) : 'N/A' ?>
                        </td>
                        <td>
                            <span class="<?= $model['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?php
                                $statusText = $model['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                echo $statusText;
                                ?>
                            </span>
                        </td>
                        <td>

                            <?php if($model->name != 'Oylik' && $model->name != 'Avans' && $model->name != 'Taminotchi uchun' && $model->name != 'Bonus' && $model->name != 'Debt' && $model->name != 'One time payment'):  ?>
                                <?php if ($model->deleted_at == NULL): ?>
                                <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                               
                                    <a href="#" class="dropdown-item expenses-type-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model->id) ?>">
                                        <?= Yii::t("app", "edit") ?>
                                    </a>
                                    
                                        <a href="#" class="dropdown-item expenses-type-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model->id) ?>">
                                            <span class="red-text"><?= Yii::t("app", "delete") ?></span>
                                        </a>
                                    
                                </div>
                            </div>
                              <?php endif; ?>
                            <?php endif; ?>

                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "add_expense_type") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "edit_expense_type") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "expense_type_delete") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#expenses-type-grid-view')) {
            $('#expenses-type-grid-view').DataTable().destroy();
        }
        
        $('#expenses-type-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[1, 'desc']], // Sort by creation date by default
            "columnDefs": [
                {
                    "targets": [2, 3], // Status and actions columns
                    "orderable": false
                }
            ]
        });
    }

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }
    
    function initializeExpensesTypeCreate() {
        $(document).off('click.expenses-type-create').on('click.expenses-type-create', '.expenses-type-create', function() {
            $.ajax({
                url: '/backend/expenses-type/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("expenses-type-create-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.expenses-type-create-button').on('click.expenses-type-create-button', '.expenses-type-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#expenses-type-create-form').serialize();
                $.ajax({
                    url: '/backend/expenses-type/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $.pjax.reload({container: '#expenses-type-grid-pjax'});
                            $('.close').trigger('click');
                        } else if (response && response.status === 'error') {
                            // Очистка предыдущих ошибок
                            $('#name-error').html('').hide();

                            // Отображение новых ошибок
                            $.each(response.errors, function(field, errors) {
                                var errorContainer = $('#name-error');
                                if (errorContainer.length > 0) {
                                    errorContainer.html(errors.join('<br>')).css('color', 'red').show();
                                }
                            });

                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });

            }
        });

        $(document).off('keypress.expenses-type-create-form').on('keypress.expenses-type-create-form', '#expenses-type-create-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('.expenses-type-create-button').trigger('click');
            }
        });
    }

    function initializeExpensesTypeUpdate() {
        $(document).off('click.expenses-type-update').on('click.expenses-type-update', '.expenses-type-update', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/expenses-type/update',
                data: { id: id },
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal .modal-title').html(two);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').addClass("expenses-type-update-button");
                    } else {
                        iziToast.error({
                            title: 'Хато!',
                            message: response.message,
                            position: 'topRight',
                            timeout: 3000
                        });
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.expenses-type-update-button').on('click.expenses-type-update-button', '.expenses-type-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#expenses-type-update-form').serialize();
                $.ajax({
                    url: '/backend/expenses-type/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $.pjax.reload({container: '#expenses-type-grid-pjax'});
                            $('.close').trigger('click');
                        } else if (response && response.status === 'error') {
                            $('.help-block').html('');
                            $.each(response.errors, function(field, errors) {
                                $('#' + field).next('.help-block').html(errors.join('<br>'));
                            });
                        }
                        button.prop('disabled', false);
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });

        $(document).off('keypress.expenses-type-update-form').on('keypress.expenses-type-update-form', '#expenses-type-update-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('.expenses-type-update-button').trigger('click');
            }
        });
    }

    function initializeExpensesTypeDelete() {
        $(document).off('click.expenses-type-delete').on('click.expenses-type-delete', '.expenses-type-delete', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/expenses-type/delete',
                data: { id: id },
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal-delete .modal-title').html(three);
                        $('#ideal-mini-modal-delete .modal-body').html(response.content);
                        $('#ideal-mini-modal-delete .mini-button').addClass("expenses-type-delete-button");
                    } else {
                        iziToast.error({
                            title: 'Хато!',
                            message: response.message,
                            position: 'topRight',
                            timeout: 3000
                        });
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.expenses-type-delete-button').on('click.expenses-type-delete-button', '.expenses-type-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#expenses-type-delete-form').serialize();
                $.ajax({
                    url: '/backend/expenses-type/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $.pjax.reload({container: '#expenses-type-grid-pjax'});
                            $('.close').trigger('click');
                        } else if (response && response.status === 'error') {
                            $('.help-block').html('');
                            $.each(response.errors, function(field, errors) {
                                $('#' + field).next('.help-block').html(errors.join('<br>'));
                            });
                            button.prop('disabled', false);
                        }
                        button.prop('disabled', false);
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });

        $(document).off('keypress.expenses-type-delete-form').on('keypress.expenses-type-delete-form', '#expenses-type-delete-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('.expenses-type-delete-button').trigger('click');
            }
        });
    }

    function initializeAll() {
        initializeDataTable();
        initializeDropdown();
        initializeExpensesTypeCreate();
        initializeExpensesTypeUpdate();
        initializeExpensesTypeDelete();
    }
    
    $(document).ready(initializeAll);
    $(document).on('pjax:complete', initializeAll);
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>