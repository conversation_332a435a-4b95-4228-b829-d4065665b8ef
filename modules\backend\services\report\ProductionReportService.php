<?php

namespace app\modules\backend\services\report;

use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * Сервис для формирования отчетов по производству
 */
class ProductionReportService
{
    /**
     * Получить отчет по выпущенным продуктам за указанную дату (сегодня по умолчанию)
     * 
     * @param string $date
     * @param int|null $productId
     * @return array
     */
    public function getTodayProductionReport($date = null, $productId = null)
    {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $producedProducts = $this->getProducedProductsData($date, $productId);

        return [
            'produced_products' => $producedProducts,
            'summary' => [
                'totalProducts' => $producedProducts['total_products'] ?? 0,
                'totalQuantityProduced' => $producedProducts['total_quantity_produced'] ?? 0
            ]
        ];
    }

    public function getProductionReport($startDate = null, $endDate = null, $productId = null, $workerId = null)
    {
        $materialsAvailable = $this->getMaterialsAvailableData($productId);

        return [
            'materials_available' => $materialsAvailable,
            'summary' => [
                'totalMaterialTypes' => $materialsAvailable['total_materials'] ?? 0,
                'totalQuantityAvailable' => $materialsAvailable['total_quantity_available'] ?? 0
            ]
        ];
    }

    /**
     * Получить данные по выпущенным продуктам за указанную дату
     * 
     * @param string $date
     * @param int|null $productId
     * @return array
     */
    protected function getProducedProductsData($date, $productId = null)
    {
        $query = new Query();
        $query->select([
            'p.id as product_id',
            'p.name as product_name',
            'SUM(psh.quantity) as quantity',
            'MIN(psh.created_at) as first_enter_date',
            'MAX(psh.created_at) as last_enter_date',
            'COUNT(psh.id) as entry_count',
            'MAX(u_added.full_name) as added_by',
            'MAX(ps.accepted_at) as accepted_at',
            'MAX(u_accepted.full_name) as accepted_by',
            new \yii\db\Expression("CASE
                    WHEN MAX(ps.accepted_at) IS NOT NULL AND MAX(ps.accepted_user_id) IS NOT NULL THEN 'Принят'
                    ELSE 'Ожидает приемки'
                END as status")
        ])
            ->from(['psh' => 'product_storage_history'])
            ->join('LEFT JOIN', ['ps' => 'product_storage'], 'ps.id = psh.product_storage_id')
            ->join('LEFT JOIN', ['p' => 'product'], 'p.id = psh.product_id')
            ->join('LEFT JOIN', ['u_added' => 'users'], 'u_added.id = psh.add_user_id')
            ->join('LEFT JOIN', ['u_accepted' => 'users'], 'u_accepted.id = ps.accepted_user_id')
            ->where([
                'psh.type' => 'income',
                'psh.deleted_at' => null
            ])
            ->andWhere(['IS NOT', 'psh.add_user_id', null])
            // Фильтруем по дате в тайм-зоне Asia/Tashkent без лишнего двойного преобразования
            ->andWhere(new \yii\db\Expression("CAST(psh.created_at AT TIME ZONE 'Asia/Tashkent' AS DATE) = :date", [':date' => $date]));

        if ($productId) {
            $query->andWhere(['psh.product_id' => $productId]);
        }

        // Добавляем группировку по продукту
        $query->groupBy(['p.id', 'p.name']);
        $query->orderBy(['quantity' => SORT_DESC]);

        $items = $query->all();

        // Форматируем
        $formattedItems = [];
        $totalQuantity = 0;
        foreach ($items as $item) {
            $qty = (float)$item['quantity'];
            $totalQuantity += $qty;
            // Форматируем количество без десятичных знаков, если они равны нулю
            $formattedQty = (floor($qty) == $qty) ? number_format($qty, 0, '.', ' ') : number_format($qty, 2, '.', ' ');

            $formattedItems[] = [
                'product_id' => $item['product_id'],
                'product_name' => $item['product_name'] ?: 'Неопределенный продукт',
                'quantity' => $formattedQty,
                'enter_date' => $item['first_enter_date'],
                'last_enter_date' => $item['last_enter_date'],
                'entry_count' => $item['entry_count'],
                'added_by' => $item['added_by'] ?: '-',
                'accepted_at' => $item['accepted_at'],
                'accepted_by' => $item['accepted_by'] ?: '-',
                'status' => $item['status']
            ];
        }

        return [
            'items' => $formattedItems,
            'total_products' => count($formattedItems),
            'total_quantity_produced' => $totalQuantity
        ];
    }

    /**
     * Получить данные по наличию материалов в производстве
     * 
     * @param int|null $productId
     * @return array
     */
    protected function getMaterialsAvailableData($productId = null)
    {
        $query = new Query();
        $query->select([
            'm.id as material_id',
            'm.name as material_name',
            'SUM(mp.quantity) as available_quantity',
            'm.unit_type as unit'
        ])
            ->from(['mp' => 'material_production'])
            ->leftJoin(['m' => 'material'], 'mp.material_id = m.id')
            ->andWhere(['IS', 'mp.deleted_at', null])
            ->andWhere(['IS', 'm.deleted_at', null]);

        if ($productId) {
            // Фильтр по продукту через ингредиенты
            $query->leftJoin(['pi' => 'product_ingredients'], 'pi.material_id = m.id')
                ->andWhere(['pi.product_id' => $productId]);
        }

        // Группируем по материалу
        $query->groupBy(['m.id', 'm.name', 'm.unit_type'])
            ->orderBy(['available_quantity' => SORT_DESC, 'm.name' => SORT_ASC]);

        $items = $query->all();

        // Получаем данные о браке для каждого материала
        $defectData = $this->getDefectData();

        // Преобразуем результат для удобства отображения
        $formattedItems = [];
        foreach ($items as $item) {
            $materialId = $item['material_id'];
            $defectQuantity = isset($defectData[$materialId]) ? $defectData[$materialId] : 0;

            $formattedItems[] = [
                'material_id' => $materialId,
                'material_name' => $item['material_name'] ?: 'Неопределенный материал',
                'available_quantity' => number_format((float)$item['available_quantity'], 0, '.', ''),
                'defect_quantity' => $defectQuantity,
                'unit' => $this->getUnitName($item['unit']),
            ];
        }

        return [
            'items' => $formattedItems,
            'total_materials' => count($formattedItems),
            'total_quantity_available' => array_sum(array_column($formattedItems, 'available_quantity')),
            'total_defect' => array_sum(array_column($formattedItems, 'defect_quantity'))
        ];
    }

    /**
     * Получить данные о браке по материалам
     * 
     * @return array
     */
    protected function getDefectData()
    {
        $query = new Query();
        $query->select([
            'md.material_id',
            'SUM(md.quantity) as defect_quantity'
        ])
            ->from(['md' => 'material_defect'])
            ->where(['IS', 'md.deleted_at', null])
            ->groupBy(['md.material_id']);

        $items = $query->all();

        $defectData = [];
        foreach ($items as $item) {
            $defectData[$item['material_id']] = (int)$item['defect_quantity'];
        }

        return $defectData;
    }

    /**
     * Получить название единицы измерения
     * 
     * @param int $unitType
     * @return string
     */
    protected function getUnitName($unitType)
    {
        switch ($unitType) {
            case 1:
                return 'шт';
            case 2:
                return 'кг';
            case 3:
                return 'л';
            case 4:
                return 'м';
            default:
                return 'шт';
        }
    }
}
