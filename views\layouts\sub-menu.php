<?php

$current_url = Yii::$app->controller->action->uniqueId;

//-------- SECTION Constants ------//
const DASHBOARD = 1;
const REFERENCES = 'Ma\'lumotnomalar';
const OPERATIONS = 3;
const REPORTS = 'Hisobotlar';
const DOCUMENTS = "Xujjatlar";
const SETTINGS = 'Sozlamalar';
const WORKING_DAYS = 'Ish kunlari';
const FINANCE = 'Moliya';
const ARCHIVES = 'Arxiv';
const BONUSES = 'Bonuslar';
const PENALTY = 'Jarimalar';
const SALARY = 'Maosh kiritish';
const FINE = 'Jarimalar';
const PROMOTION = "Rag'batlantirish";
const BONUS_TYPE = "Bonus turi";
const PENALTY_TYPE = "Jarima turi";
const ADVANCE = "Avans";
const EMPLOYE_CASH = "UshlanmaЙlar";
const CASHBOX = "Kassa bo'limi";
const CONVERSION = "Konvertatsiya";
const CURRENCY = "Valyutalar";
const CURRENCY_RATIO = "Valyutalar nisbati";
const TRANSACTION = "Pul O'tkazma";
const DOCUMENTS_NOT_ACCEPTED = "Tasdiqlanmagan hujjatlar";
const WAGES = "Oylik maosh bo'limi";
const WAGES_CREATE = "Oylik kiritish";
const COMMUNALS = "Oylik kiritish";
const EXPENSES = "Kassa xarajatlari";
const EXPENSES_CREATE = "Kassa xarajatlari kiritish";
const TOOLS_EXPENCE = "Assosiy vositalarga xarajatlari";
const TAXES_CASHBOX = "Soliqlarni to'lash";
const DEPARTAMENT = "Bo'limlar"; 
const POSITION = "Lavozimlar";
const SCHEDULE = "Ish grafiklari";
const PERCENT = "Extimollik darajalari";
const HALLMAIN = "Zallar";
const PAYMENTTYPE = "To'lov turlari";
const SPEAKERS = "Spikerlar";
const CONTROL = "Nazorat";


// Menyuni aktivligini aniqlab beradi
function navActive($section_url, $label): string
{
    return ($section_url[Yii::$app->controller->action->uniqueId] === $label) ? 'active' : '';
}

// Sub menyuni aktivligini aniqlab beradi
function subNavActive(string $controller, array $actions = []): string
{
    foreach ($actions as $item) {
        if (Yii::$app->controller->action->uniqueId === $controller . '/' . $item) {
            return 'active';
        }
    }
    return '';
}

//--------  HR -----//

//---- Clients ----//
$client_url = [
    'client/index' => REFERENCES, // Mijozlar
    'client/view' => REFERENCES,
    'client/archives' => REFERENCES,
    'client/transfers' => REFERENCES,
    'client/client-company' => REFERENCES,
];



if ($current_url === 'site/index') {
    $arr = [];
}




/**
 * Sub navbar
 *  ```
 *   'label' => REFERENCES,
 *   'icon' => '<i class="fa fa-info-circle"></i>',
 *   'show' => true,
 *   'add_class' => navActive($hr_url),
 *   'sub_menu' => []
 *
 * ```
 *
 * label - string (required) menyu nomi
 * icon - string (optional, default = '') menyu nomi oldidagi icon
 * show - bool (optional, default = true) menyuni ko'rsatish ko'rsatmaslikni belgilaydi
 * add_class - string (required) menyuga active clasini va boshqa classlarni qo'shish uchun
 * sub_menu - array (required) menyu ichidagi submenyularni kiritish uchun
 * ```
 * 'sub_menu' => [
 *      'label' => 'Kadrlar bazasi',
 *      'url' => 'preprofile/index',
 *      'show' => true (options, default = true),
 *      'add_class' => 'active' (sub menyu aktivligini ko'rsatish uchun)
 * ]
 *
 * ```
 */

if (array_key_exists($current_url, $client_url)) {
    $arr = [
        [
            'label' => REFERENCES,
            'icon' => '<i class="fa fa-info-circle"></i>',
            'add_class' => navActive($client_url, REFERENCES),
            'sub_menu' => [
                [
                    'label' => 'Mijozlar ro\'yxati',
                    'url' => 'client/index',
                    'add_class' => subNavActive('client', ['index', 'view']),
                ],
                [
                    'label' => 'Arxiv mijozlar',
                    'url' => 'client/archives',
                    'add_class' => subNavActive('client', ['archives']),
                ],
                [
                    'label' => 'Mijozni boshqa menejerga o`tkazish',
                    'url' => 'client/transfers',
                    'add_class' => subNavActive('client', ['transfers']),
                ],
                [
                    'label' => 'Mijoz firmalari',
                    'url' => 'client/client-company',
                    'add_class' => subNavActive('client', ['client-company']),
                ],
            ]
        ],
    ];
}





if (!empty($arr)) :
    $sub_navbar = Yii::$app->subNavbar->nav($arr); ?>
    <nav class="navbar navbar-secondary navbar-expand-lg">
        <div class="container-fluid">
            <?= $sub_navbar ?>
        </div>
    </nav>
<?php endif; ?>
