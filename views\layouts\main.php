<?php $this->beginPage() ?>
<?php
use app\assets\AppAsset;
AppAsset::register($this);
?>
<!DOCTYPE html>
<html lang="<?= Yii::$app->language ?>" class="h-100">
<head>
    <meta charset="<?= Yii::$app->charset ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">
    <?php $this->registerCsrfMetaTags() ?>
    <title><?php echo Yii::$app->params['projectName']; ?> | Platform</title>
    <?php $this->head() ?>
</head>
<body class="d-flex flex-column layout-3">
<?php $this->beginBody() ?>
<div id="app" class="d-flex flex-column h-100"> <!-- Добавили h-100 и flex-column -->
    <div class="flex-grow-1 d-flex flex-column"> <!-- Растягиваем основной контейнер -->
        <!-- <navbar> -->
        <?= $this->render('menu', [
            'content' => $content
        ]) ?>
        <!-- </navbar> -->

        <!-- <main> -->
        <div class="main-content flex-grow-1" style="padding-top: 80px;"> <!-- Растягиваем контент -->
            <section class="section container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <?php if (Yii::$app->getSession()->getAllFlashes()) : ?>
                            <?php foreach (Yii::$app->getSession()->getAllFlashes() as $key => $value) : ?>
                                <div class="alert alert-danger alert-dismissable" role="alert">
                                    <?= Yii::$app->session->getFlash($key) ?>
                                </div>
                            <?php endforeach ?>
                        <?php endif ?>
                    </div>
                </div>
                <div class="card d-flex flex-column" style="height: 100%; min-height: calc(100vh - 100px);">
                    <div class="card-head">
                        <?= $this->render('sub-menu') ?>
                    </div>
                    <div class="card-body flex-grow-1"> <!-- Растягиваем содержимое карточки -->
                        <?= $content ?>
                    </div>
                </div>
            </section>
        </div>
        <!-- </main> -->
    </div>
</div>

<!-- <modal> -->
<?= $this->render('modal') ?>
<!-- </modal> -->

<style>
    .help-block {
        color: red;
    }
    /*.summary{
        display: none;
    }*/
</style>

<?php $this->endBody() ?>
</body>
</html>
<?php $this->endPage() ?>