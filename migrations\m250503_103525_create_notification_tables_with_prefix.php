<?php

use yii\db\Migration;

class m250503_103525_create_notification_tables_with_prefix extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Основная таблица уведомлений
        $this->createTable('{{%system_notification}}', [
            'id' => $this->primaryKey(),
            'group_id' => $this->string(50)->null()->comment('ID группы уведомлений для синхронизации статуса прочтения'),
            'role' => $this->string(50)->null()->comment('Роль пользователей, которым адресовано уведомление'),
            'title' => $this->string(255)->notNull()->comment('Заголовок уведомления'),
            'body' => $this->text()->notNull()->comment('Текст уведомления'),
            'data' => $this->text()->null()->comment('JSO<PERSON> с дополнительными данными'),
            'is_sent' => $this->boolean()->defaultValue(false)->comment('Флаг отправки через Firebase'),
            'created_at' => $this->dateTime()->notNull()->comment('Дата создания'),
            'updated_at' => $this->dateTime()->null()->comment('Дата обновления'),
        ]);

        // Таблица статусов прочтения уведомлений пользователями
        $this->createTable('{{%system_notification_read_status}}', [
            'id' => $this->primaryKey(),
            'notification_id' => $this->integer()->notNull()->comment('ID уведомления'),
            'user_id' => $this->integer()->notNull()->comment('ID пользователя'),
            'is_read' => $this->boolean()->defaultValue(false)->comment('Флаг прочтения'),
            'read_at' => $this->dateTime()->null()->comment('Дата прочтения'),
        ]);

        // Создаем индексы для быстрого поиска
        $this->createIndex('idx-system_notification-group_id', '{{%system_notification}}', 'group_id');
        $this->createIndex('idx-system_notification-role', '{{%system_notification}}', 'role');
        $this->createIndex('idx-system_notification-is_sent', '{{%system_notification}}', 'is_sent');

        $this->createIndex('idx-system_notification_read_status-notification_id', '{{%system_notification_read_status}}', 'notification_id');
        $this->createIndex('idx-system_notification_read_status-user_id', '{{%system_notification_read_status}}', 'user_id');
        $this->createIndex('idx-system_notification_read_status-is_read', '{{%system_notification_read_status}}', 'is_read');
        $this->createIndex('idx-system_notification_read_status-notification_user', '{{%system_notification_read_status}}', ['notification_id', 'user_id'], true);

        // Добавляем внешние ключи
        $this->addForeignKey(
            'fk-system_notification_read_status-notification_id',
            '{{%system_notification_read_status}}',
            'notification_id',
            '{{%system_notification}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-system_notification_read_status-user_id',
            '{{%system_notification_read_status}}',
            'user_id',
            '{{%users}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Сначала удаляем таблицу со статусами прочтения (из-за внешнего ключа)
        $this->dropTable('{{%system_notification_read_status}}');

        // Затем удаляем основную таблицу уведомлений
        $this->dropTable('{{%system_notification}}');

        return true;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250503_103525_create_notification_tables_with_prefix cannot be reverted.\n";

        return false;
    }
    */
}
