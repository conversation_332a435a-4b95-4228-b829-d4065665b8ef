<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;

/**
 * Модель для работы со статусами прочтения уведомлений
 *
 * @property int $id ID записи
 * @property int $notification_id ID уведомления
 * @property int $user_id ID пользователя
 * @property bool $is_read Флаг прочтения
 * @property string|null $read_at Дата прочтения
 *
 * @property-read \app\common\models\Notification $notification
 * @property-read \app\modules\backend\models\Users $user
 */
class NotificationReadStatus extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%system_notification_read_status}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['notification_id', 'user_id'], 'required'],
            [['notification_id', 'user_id'], 'integer'],
            [['is_read'], 'boolean'],
            [['read_at'], 'safe'],
            [['notification_id', 'user_id'], 'unique', 'targetAttribute' => ['notification_id', 'user_id']],
            [['notification_id'], 'exist', 'skipOnError' => true, 'targetClass' => Notification::class, 'targetAttribute' => ['notification_id' => 'id']],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => \app\modules\backend\models\Users::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'notification_id' => 'Уведомление',
            'user_id' => 'Пользователь',
            'is_read' => 'Прочитано',
            'read_at' => 'Дата прочтения',
        ];
    }

    /**
     * Получить связанное уведомление
     *
     * @return \yii\db\ActiveQuery
     */
    public function getNotification()
    {
        return $this->hasOne(Notification::class, ['id' => 'notification_id']);
    }

    /**
     * Получить связанного пользователя
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(\app\modules\backend\models\Users::class, ['id' => 'user_id']);
    }

    /**
     * Пометить уведомление как прочитанное
     *
     * @return bool
     */
    public function markAsRead()
    {
        if ($this->is_read) {
            return true; // Уже прочитано
        }

        $this->is_read = true;
        $this->read_at = new Expression('NOW()');
        return $this->save(false);
    }
}
