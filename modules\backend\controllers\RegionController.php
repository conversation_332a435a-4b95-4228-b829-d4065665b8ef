<?php

namespace app\modules\backend\controllers;

use Yii;
use app\modules\backend\models\Region;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\data\ActiveDataProvider;
use yii\filters\AccessControl;
use yii\web\Response;
use yii\helpers\ArrayHelper;
use yii\data\ArrayDataProvider;

/**
 * RegionController implements the CRUD actions for Region model.
 */
class RegionController extends BaseController
{
    

    /**
     * Lists all Region models.
     * @return mixed
     */
    public function actionIndex()
    {
        $positions = Region::find()->all();

        $dataProvider = new ArrayDataProvider([
            'allModels' => $positions,
            'pagination' => [
                'pageSize' => 50,
            ],
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Creates a new Region model.
     * @return mixed
     */
    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new Region();
            $model->load(Yii::$app->request->post());
            if ($model->validate()) {
                $model->save();

                return [
                    'success' => true,
                    'message' => Yii::t('app', 'Region created successfully.'),
                    'data' => $model->attributes,
                ];
            } else {
                return [
                    'success' => false,
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $model = new Region();
            return [
                "status" => true,
                "content" => $this->renderPartial('create', ['model' => $model])
            ];
        }
    }

    /**
     * Updates an existing Region model.
     * @param int $id ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $model = Region::find()->where(['id' => $postData['Region']['id']])->one();
            if (!$model) {
                return ['status' => 'error', 'message' => Yii::t('app', 'Region not found.')];
            }

            $model->load($postData);

            if (isset($postData['Region']['status'])) {
                if ($postData['Region']['status'] == 1) {
                    $model->deleted_at = null;
                } else {
                    $model->deleted_at = date('Y-m-d H:i:s');
                }
            }

            if ($model->save()) {
                return [
                    'success' => true,
                    'message' => Yii::t('app', 'Region updated successfully.'),
                ];
            } else {
                return [
                    'success' => false,
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = $this->findModel($id);
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Region not found.'),
                ];
            } else {
                return [
                    "status" => true,
                    "content" => $this->renderPartial('update', ['model' => $model]),
                ];
            }
        }
    }

    /**
     * Deletes an existing Region model.
     * @param int $id ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $model = Region::findOne($postData['Region']['id']);
            if (!$model) {
                return ['status' => 'error', 'message' => Yii::t('app', 'Region not found.')];
            }

            $model->deleted_at = date('Y-m-d H:i:s');
            if ($model->save()) {
                return [
                    'success' => true,
                    'message' => Yii::t('app', 'Region deleted successfully.'),
                ];
            } else {
                return [
                    'success' => false,
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = $this->findModel($id);
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Region not found.'),
                ];
            }

            return [
                "status" => 'success',
                "content" => $this->renderPartial('delete', ['model' => $model]),
            ];
        }

        return ['status' => 'error', 'message' => Yii::t('app', 'Invalid request method.')];
    }

    /**
     * Finds the Region model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Region the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Region::findOne(['id' => $id, 'deleted_at' => null])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException(Yii::t('app', 'The requested page does not exist.'));
    }
}
