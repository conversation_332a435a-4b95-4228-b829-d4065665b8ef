<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\LinkPager;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t('app', 'client_balance_adjustment');
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");

?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-12">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
            <p class="text-muted"><?= Yii::t('app', 'client_balance_adjustment_description') ?></p>
        </div>
    </div>

    <!-- Форма поиска и фильтров -->
    <div class="card mb-3">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label"><?= Yii::t('app', 'search') ?></label>
                    <input type="text" 
                           class="form-control" 
                           id="search" 
                           name="search" 
                           value="<?= Html::encode($search ?? '') ?>" 
                           placeholder="<?= Yii::t('app', 'search_by_name_phone_region') ?>">
                </div>
                <div class="col-md-3">
                    <label for="balance_filter" class="form-label"><?= Yii::t('app', 'balance_filter') ?></label>
                    <select class="form-control" id="balance_filter" name="balance_filter">
                        <option value=""><?= Yii::t('app', 'all_balances') ?></option>
                        <option value="debt" <?= ($balanceFilter ?? '') === 'debt' ? 'selected' : '' ?>><?= Yii::t('app', 'debt_only') ?></option>
                        <option value="prepayment" <?= ($balanceFilter ?? '') === 'prepayment' ? 'selected' : '' ?>><?= Yii::t('app', 'prepayment_only') ?></option>
                        <option value="no_balance" <?= ($balanceFilter ?? '') === 'no_balance' ? 'selected' : '' ?>><?= Yii::t('app', 'no_balance_only') ?></option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> <?= Yii::t('app', 'search') ?>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <a href="<?= Url::to(['/backend/client-balance-adjustment/index']) ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> <?= Yii::t('app', 'clear') ?>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <?php if($clients): ?>
        <!-- Информация о результатах поиска -->
        <?php if (!empty($search) || !empty($balanceFilter)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <?= Yii::t('app', 'search_results_info', [
                    'total' => $pagination->totalCount,
                    'search' => $search,
                    'filter' => $balanceFilter
                ]) ?>
                <?php if (!empty($search)): ?>
                    <strong><?= Yii::t('app', 'search_term') ?>:</strong> "<?= Html::encode($search) ?>"
                <?php endif; ?>
                <?php if (!empty($balanceFilter)): ?>
                    <strong><?= Yii::t('app', 'filter') ?>:</strong> 
                    <?php
                    $filterLabels = [
                        'debt' => Yii::t('app', 'debt_only'),
                        'prepayment' => Yii::t('app', 'prepayment_only'),
                        'no_balance' => Yii::t('app', 'no_balance_only')
                    ];
                    echo isset($filterLabels[$balanceFilter]) ? $filterLabels[$balanceFilter] : $balanceFilter;
                    ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <div>
            <table id="client-balance-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "ID") ?></th>
                        <th><?= Yii::t("app", "client_name") ?></th>
                        <th><?= Yii::t("app", "client_region_id") ?></th>
                        <th><?= Yii::t("app", "phone_number") ?></th>
                        <th><?= Yii::t("app", "current_balance") ?></th>
                        <th><?= Yii::t("app", "balance_status") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($clients as $client): ?>
                    <?php 
                        $balance = (float)$client['balance'];
                        $balanceClass = '';
                        $balanceStatus = '';
                        
                        if ($balance > 0) {
                            $balanceClass = 'text-danger';
                            $balanceStatus = Yii::t('app', 'debt');
                        } elseif ($balance < 0) {
                            $balanceClass = 'text-success';
                            $balanceStatus = Yii::t('app', 'prepayment');
                        } else {
                            $balanceClass = 'text-muted';
                            $balanceStatus = Yii::t('app', 'no_balance');
                        }
                    ?>
                    <tr>
                        <td><?= Html::encode($client['id']) ?></td>
                        <td><?= Html::encode($client['full_name']) ?></td>
                        <td><?= Html::encode($client['region_name']) ?></td>
                        <td><?= Html::encode($client['phone_number']) ?></td>
                        <td class="<?= $balanceClass ?>">
                            <?= number_format(abs($balance), 2, '.', ' ') ?>
                        </td>
                        <td>
                            <span class="badge badge-<?= $balance > 0 ? 'danger' : ($balance < 0 ? 'success' : 'secondary') ?>">
                                <?= $balanceStatus ?>
                            </span>
                        </td>
                        <td>
                            <a href="#" class="btn btn-sm btn-primary balance-adjust" 
                               data-toggle="modal" 
                               data-target="#ideal-mini-modal" 
                               data-id="<?= Html::encode($client['id']) ?>">
                                <i class="fas fa-edit"></i> <?= Yii::t("app", "adjust_balance") ?>
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Пагинация -->
        <div class="mt-3">
            <?= LinkPager::widget([
                'pagination' => $pagination,
                'options' => ['class' => 'pagination justify-content-center'],
                'linkOptions' => ['class' => 'page-link'],
                'activePageCssClass' => 'active',
                'disabledPageCssClass' => 'disabled',
                'prevPageLabel' => '‹ ' . Yii::t('app', 'previous'),
                'nextPageLabel' => Yii::t('app', 'next') . ' ›',
                'firstPageLabel' => '« ' . Yii::t('app', 'first'),
                'lastPageLabel' => Yii::t('app', 'last') . ' »',
            ]) ?>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_clients_found') ?></p>
    <?php endif; ?>
</div>

<div id="balance-adjust-text" data-text="<?= Yii::t("app", "adjust_client_balance") ?>"></div>

<?php
// Подготавливаем все переменные для JavaScript
$updateUrl = Url::to(['/backend/client-balance-adjustment/update']);
$firstLabel = '« ' . Yii::t('app', 'first');
$lastLabel = Yii::t('app', 'last') . ' »';
$nextLabel = Yii::t('app', 'next') . ' ›';
$previousLabel = '‹ ' . Yii::t('app', 'previous');

$js = '
(function($) {
    var balanceAdjustText = $("#balance-adjust-text").attr("data-text");
    var searchLabel = ' . json_encode($searchLabel) . ';
    var lengthMenuLabel = ' . json_encode($lengthMenuLabel) . ';
    var zeroRecordsLabel = ' . json_encode($zeroRecordsLabel) . ';
    var infoLabel = ' . json_encode($infoLabel) . ';
    var infoEmptyLabel = ' . json_encode($infoEmptyLabel) . ';
    var infoFilteredLabel = ' . json_encode($infoFilteredLabel) . ';
    var updateUrl = ' . json_encode($updateUrl) . ';

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable("#client-balance-grid-view")) {
            $("#client-balance-grid-view").DataTable().destroy();
        }

        $("#client-balance-grid-view").DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel,
                "paginate": {
                    "first": ' . json_encode($firstLabel) . ',
                    "last": ' . json_encode($lastLabel) . ',
                    "next": ' . json_encode($nextLabel) . ',
                    "previous": ' . json_encode($previousLabel) . '
                }
            },
            "pageLength": 50,
            "order": [[0, "asc"]],
            "columnDefs": [
                {
                    "targets": [6],
                    "orderable": false
                }
            ],
            "stateSave": true,
            "stateDuration": 60 * 60 * 24,
            "responsive": true,
            "processing": true,
            "deferRender": true
        });
    }

    $(document).ready(function() {
        initializeDataTable();
        
        // Поиск по Enter
        $("#search").on("keypress", function(e) {
            if (e.which === 13) {
                $(this).closest("form").submit();
            }
        });
        
        // Автоматический поиск при изменении фильтра
        $("#balance_filter").on("change", function() {
            $(this).closest("form").submit();
        });
    });

    // Обработчик для корректировки баланса
    $(document).on("click", ".balance-adjust", function(e) {
        e.preventDefault();
        var clientId = $(this).data("id");
        
        $.ajax({
            url: updateUrl,
            type: "GET",
            data: { id: clientId },
            success: function(response) {
                if (response.status === "success") {
                    $("#ideal-mini-modal .modal-content").html(response.content);
                    $("#ideal-mini-modal .modal-title").text(balanceAdjustText);
                    $("#ideal-mini-modal").modal("show");
                } else {
                    alert(response.message || "Ошибка при загрузке формы");
                }
            },
            error: function() {
                alert("Ошибка при отправке запроса");
            }
        });
    });

    // Обработчик отправки формы корректировки
    $(document).on("submit", "#balance-adjustment-form", function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        
        $.ajax({
            url: updateUrl,
            type: "POST",
            data: formData,
            success: function(response) {
                if (response.status === "success") {
                    $("#ideal-mini-modal").modal("hide");
                    location.reload();
                } else {
                    var errorHtml = "<div class=\"alert alert-danger\"><ul>";
                    if (response.errors) {
                        $.each(response.errors, function(field, messages) {
                            if ($.isArray(messages)) {
                                $.each(messages, function(index, message) {
                                    errorHtml += "<li>" + message + "</li>";
                                });
                            } else {
                                errorHtml += "<li>" + messages + "</li>";
                            }
                        });
                    } else if (response.message) {
                        errorHtml += "<li>" + response.message + "</li>";
                    }
                    errorHtml += "</ul></div>";
                    
                    $("#balance-adjustment-form .form-errors").html(errorHtml);
                }
            },
            error: function() {
                $("#balance-adjustment-form .form-errors").html(
                    "<div class=\"alert alert-danger\">Ошибка при отправке запроса</div>"
                );
            }
        });
    });
})(jQuery);
';

$this->registerJs($js);
?> 