<?php

namespace app\modules\api\controllers;

use app\common\models\ApiResponse;
use app\common\models\FreeProductDetail;
use app\common\models\FreeProducts;
use app\common\models\Product;
use app\common\models\ProductStorage;
use app\common\models\SecurityRecord;
use app\modules\api\controllers\BaseController;
use app\modules\api\models\ClientIncomeForm;
use app\modules\api\models\SendToFreeProductsForm;
use app\common\models\ActionLogger;
use app\common\models\Tracking;
use app\modules\backend\models\Region;
use Yii;
use yii\web\ForbiddenHttpException;

class SecurityController extends BaseController
{
    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        try {
            if (!Yii::$app->user->can('security')) {
                throw new ForbiddenHttpException(Yii::t('app', "You don't have permission to access this resource"));
            }
        } catch (ForbiddenHttpException $e) {
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            Yii::$app->response->data = ApiResponse::response(
                $e->getMessage(),
                null,
                403
            );
            Yii::$app->response->statusCode = 403;
            return false;
        }

        return true;
    }

    public function actionIndex()
    {
        $date = Yii::$app->request->get('date') ?: date('Y-m-d');

        $incomeSql = "
            SELECT
                sr.id,
                r.name as region_name,
                sr.car_number,
                sr.driver_full_name,
                sr.description,
                sr.created_at as income_time,
                sr.accepted_at as accepted_time,
                CASE
                    WHEN sr.accepted_user_id IS NOT NULL AND sr.accepted_at IS NOT NULL
                    THEN true
                    WHEN sr.accepted_user_id IS NOT NULL AND sr.accepted_at IS NULL
                    THEN false
                    ELSE false
                END as is_accepted,
                CASE
                    WHEN tracking.accepted_at IS NOT NULL
                    AND tracking.status = :status_accepted
                    AND tracking.deleted_at IS NULL
                    THEN true
                    ELSE false
                END as status,
                r.id as region_id
            FROM security_records sr
            LEFT JOIN region r ON r.id = sr.region_id
            LEFT JOIN users u ON u.id = sr.accepted_user_id
            LEFT JOIN tracking ON tracking.process_id = sr.id
                AND tracking.progress_type = :progress_type
                AND tracking.deleted_at IS NULL
            WHERE DATE(sr.created_at) = :date
            AND sr.deleted_at IS NULL
            ORDER BY sr.created_at DESC
        ";

        $command = Yii::$app->db->createCommand($incomeSql);
        $command->bindValues([
            ':date' => $date,
            ':progress_type' => Tracking::TYPE_CLIENT_INCOME,
            ':status_accepted' => Tracking::STATUS_ACCEPTED
        ]);

        $records = $command->queryAll();

        return ApiResponse::response("", [
            'records' => $records
        ]);
    }

    public function actionClientIncome()
    {
        if(Yii::$app->request->isGet) {
            $regions = Region::find()
                ->select(['id', 'name'])
                ->where(['deleted_at' => null])
                ->asArray()
                ->all();

            return ApiResponse::response("", [
                'regions' => $regions
            ]);
        }

        if(Yii::$app->request->isPost){
            $model = new ClientIncomeForm();
            if ($model->load(Yii::$app->request->post(), '') && $model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $record = new SecurityRecord();
                    $record->region_id = $model->region_id;
                    $record->car_number = $model->car_number;
                    $record->driver_full_name = $model->driver_full_name;
                    $record->description = $model->description;
                    $record->add_user_id = Yii::$app->user->id;
                    $record->created_at = date('Y-m-d H:i:s');

                    if (!$record->save()) {
                        throw new \Exception('Security record save error: ' . json_encode($record->getErrors()));
                    }

                    $tracking = new Tracking();
                    $tracking->progress_type = Tracking::TYPE_CLIENT_INCOME;
                    $tracking->process_id = $record->id;
                    $tracking->created_at = date('Y-m-d H:i:s');
                    $tracking->status = Tracking::STATUS_NOT_ACCEPTED;

                    if (!$tracking->save()) {
                        throw new \Exception('Tracking save error');
                    }

                    ActionLogger::actionLog(
                        'client_income',
                        'security_records',
                        null,
                        [
                            'record_id' => $record->id,
                            'region_id' => $record->region_id,
                            'car_number' => $record->car_number,
                            'driver_full_name' => $record->driver_full_name,
                            'description' => $record->description
                        ]
                    );

                    $transaction->commit();
                    return ApiResponse::response(
                        'Record created successfully',
                    );

                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return ApiResponse::response(
                        'Failed to create record',
                        ['error' => $e->getMessage()],
                        ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                    );
                }
            } else {
                return ApiResponse::response(
                    'Validation failed',
                    $model->getErrors(),
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }
        }

    }

    public function actionAccept()
    {
        if(!Yii::$app->request->isPost) {
            return ApiResponse::response(
                'Method not allowed',
                null,
                ApiResponse::HTTP_METHOD_NOT_ALLOWED
            );
        }

        $id = Yii::$app->request->post('security_record_id');
        if(empty($id)) {
            return ApiResponse::response(
                'ID is required',
                null,
                ApiResponse::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        $tracking = Tracking::findOne([
            'process_id' => $id,
            'progress_type' => Tracking::TYPE_CLIENT_INCOME,
            'deleted_at' => null
        ]);

        if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
            return ApiResponse::response(
                'Запись уже подтверждена или не найдена',
                null,
                ApiResponse::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $record = SecurityRecord::findOne($id);
            if(!$record) {
                throw new \Exception('Record not found');
            }

            if($record->accepted_at !== null) {
                throw new \Exception('Record already accepted');
            }

            $record->accepted_user_id = Yii::$app->user->id;
            $record->accepted_at = date('Y-m-d H:i:s');

            if(!$record->save()) {
                throw new \Exception('Failed to save record: ' . json_encode($record->getErrors()));
            }



            ActionLogger::actionLog(
                'accept_security_record',
                'security_records',
                null,
                [
                    'record_id' => $record->id,
                    'region_id' => $record->region_id,
                    'car_number' => $record->car_number,
                    'accepted_by' => Yii::$app->user->id
                ]
            );

            $transaction->commit();
            return ApiResponse::response(
                'Record accepted successfully',
            );

        } catch (\Exception $e) {
            $transaction->rollBack();
            return ApiResponse::response(
                $e->getMessage(),
                null,
                ApiResponse::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function actionUpdateClientIncome()
    {
        if (Yii::$app->request->isGet) {
            $record_id = Yii::$app->request->get('record_id');

            if (!$record_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $tracking = Tracking::findOne([
                'process_id' => $record_id,
                'progress_type' => Tracking::TYPE_CLIENT_INCOME,
                'deleted_at' => null,
                'accepted_at' => null
            ]);

            if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
                return ApiResponse::response(
                    'Запись уже подтверждена или не найдена',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $record = SecurityRecord::find()
                ->select([
                    'security_records.*',
                    'region.name as region_name'
                ])
                ->leftJoin('region', 'region.id = security_records.region_id')
                ->where(['security_records.id' => $record_id])
                ->andWhere(['security_records.deleted_at' => null])
                ->andWhere(['security_records.accepted_at' => null])
                ->andWhere(['security_records.accepted_user_id' => null])
                ->asArray()
                ->one();

            if (!$record) {
                return ApiResponse::response(
                    'Запись не найдена',
                    null,
                    ApiResponse::HTTP_NOT_FOUND
                );
            }

            return ApiResponse::response('Данные записи', [
                'record' => $record
            ]);
        }

        if (Yii::$app->request->isPost) {
            $record_id = Yii::$app->request->post('record_id');
            if (!$record_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $tracking = Tracking::findOne([
                'process_id' => $record_id,
                'progress_type' => Tracking::TYPE_CLIENT_INCOME,
                'deleted_at' => null
            ]);

            if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
                return ApiResponse::response(
                    'Запись уже подтверждена или не найдена',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $model = new ClientIncomeForm();
            $model->load(Yii::$app->request->post(), '');

            if ($model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $record = SecurityRecord::findOne($record_id);
                    if (!$record) {
                        throw new \Exception('Запись не найдена');
                    }

                    $record->region_id = (int)$model->region_id;
                    $record->car_number = $model->car_number;
                    $record->driver_full_name = $model->driver_full_name;
                    $record->description = $model->description;

                    if (!$record->save()) {
                        throw new \Exception('Ошибка сохранения записи');
                    }

                    ActionLogger::actionLog(
                        'update_client_income',
                        'security_records',
                        $record->id,
                        [
                            'region_id' => $record->region_id,
                            'car_number' => $record->car_number,
                            'driver_full_name' => $record->driver_full_name,
                            'description' => $record->description
                        ]
                    );

                    $transaction->commit();
                    return ApiResponse::response('Запись успешно обновлена');

                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return ApiResponse::response(
                        'Ошибка при обновлении записи',
                        ['error' => $e->getMessage()],
                        ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                    );
                }
            } else {
                return ApiResponse::response(
                    'Ошибка валидации',
                    $model->getErrors(),
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }







    

    public function actionConfirmFreeProducts()
    {
        $data = Yii::$app->request->post();

        if (!isset($data['id'])) {
            return ApiResponse::response("ID is required", null, 400);
        }

        $freeProducts = FreeProducts::findOne($data['id']);
        if (!$freeProducts) {
            return ApiResponse::response(Yii::t('app', 'Record not found'), null, 404);
        }

        // Получаем все детали бесплатных продуктов
        $freeProductDetails = $freeProducts->freeProductDetails;
        if (empty($freeProductDetails)) {
            return ApiResponse::response(Yii::t('app', 'No products found in this record'), null, 400);
        }

        // Проверяем наличие всех продуктов на складе
        foreach ($freeProductDetails as $detail) {
            if ($detail->deleted_at !== null) {
                continue; // Пропускаем удаленные записи
            }

            // Выбираем только подтвержденные записи (accepted_at и accepted_user_id не NULL)
            $productStorage = ProductStorage::find()
                ->where([
                    'product_id' => $detail->product_id,
                    'deleted_at' => null
                ])
                ->andWhere(['IS NOT', 'accepted_at', null])
                ->andWhere(['IS NOT', 'accepted_user_id', null])
                ->andWhere(['>=', 'quantity', 0])
                ->orderBy(['enter_date' => SORT_ASC]) // Сначала используем старые записи
                ->all();

            $totalQuantity = array_sum(array_column($productStorage, 'quantity'));
            $product = $detail->product;
            $quantityBlock = $product && $product->size > 0 ? ($detail->quantity / $product->size) : 0;

            if ($totalQuantity < $detail->quantity) {
                $productName = $product ? $product->name : "ID: " . $detail->product_id;
                return ApiResponse::response(
                    Yii::t('app', 'Not enough product {name} on stock. Available: {available} pieces ({available_blocks} blocks), required: {required} pieces ({required_blocks} blocks)',
                        [
                            'name' => $productName,
                            'available' => $totalQuantity,
                            'available_blocks' => $product && $product->size > 0 ? number_format($totalQuantity / $product->size, 2) : 0,
                            'required' => $detail->quantity,
                            'required_blocks' => number_format($quantityBlock, 2)
                        ]
                    ),
                    null,
                    400
                );
            }
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $freeProducts->status = FreeProducts::STATUS_APPROVED;
            if (!$freeProducts->save()) {
                throw new \Exception("Error saving free products");
            }

            // Списываем продукты со склада
            foreach ($freeProductDetails as $detail) {
                if ($detail->deleted_at !== null) {
                    continue; // Пропускаем удаленные записи
                }

                $productStorage = ProductStorage::find()
                    ->where([
                        'product_id' => $detail->product_id,
                        'deleted_at' => null
                    ])
                    ->andWhere(['IS NOT', 'accepted_at', null])
                    ->andWhere(['IS NOT', 'accepted_user_id', null])
                    ->andWhere(['>=', 'quantity', 0])
                    ->orderBy(['enter_date' => SORT_ASC]) // Сначала используем старые записи
                    ->all();

                $product = $detail->product;
                $quantityBlock = $product && $product->size > 0 ? ($detail->quantity / $product->size) : 0;
                $remainingQuantity = $detail->quantity;

                foreach ($productStorage as $storage) {
                    if ($remainingQuantity <= 0) {
                        break;
                    }

                    $quantityToDeduct = min($storage->quantity, $remainingQuantity);
                    $storage->quantity -= $quantityToDeduct;
                    $remainingQuantity -= $quantityToDeduct;

                    if (!$storage->save()) {
                        throw new \Exception("Error updating product storage for product ID: {$detail->product_id}, quantity blocks: " . number_format($quantityBlock, 2));
                    }
                }
            }

            // Формируем ответ
            $responseData = $freeProducts->attributes;
            $responseData['details'] = [];

            foreach ($freeProductDetails as $detail) {
                if ($detail->deleted_at !== null) {
                    continue; // Пропускаем удаленные записи
                }

                $product = $detail->product;
                $responseData['details'][] = [
                    'id' => $detail->id,
                    'product_id' => $detail->product_id,
                    'product_name' => $product ? $product->name : null,
                    'quantity' => $detail->quantity,
                    'quantity_block' => $product && $product->size > 0 ? ($detail->quantity / $product->size) : null,
                    'product_size' => $product ? $product->size : null
                ];
            }

            $transaction->commit();
            return ApiResponse::response("Free products confirmed", $responseData);

        } catch (\Exception $e) {
            $transaction->rollBack();
            return ApiResponse::response("Error: " . $e->getMessage(), null, 500);
        }
    }


    public function actionGetFreeProducts()
    {
        $request = Yii::$app->request;
        $dateFilter = $request->get('date', date('Y-m-d'));

        $freeProductsQuery = FreeProducts::find()
            ->where(['deleted_at' => null]);

            if ($dateFilter) {
                $startDate = date('Y-m-d 00:00:00', strtotime($dateFilter));
                $endDate = date('Y-m-d 23:59:59', strtotime($dateFilter));

                $freeProductsQuery->andWhere(['between', 'created_at', $startDate, $endDate]);
            }

        $freeProducts = $freeProductsQuery->all();

        $responseData = [];
        foreach ($freeProducts as $freeProduct) {
            $freeProductData = $freeProduct->attributes;
            $freeProductData['details'] = [];

            // Получаем детали бесплатных продуктов
            foreach ($freeProduct->freeProductDetails as $detail) {
                if ($detail->deleted_at !== null) {
                    continue; // Пропускаем удаленные записи
                }

                $product = $detail->product;

                // Рассчитываем количество блоков
                $quantityBlock = null;
                if ($product && $product->size > 0) {
                    $quantityBlock = $detail->quantity / $product->size;
                }

                $freeProductData['details'][] = [
                    'id' => $detail->id,
                    'product_id' => $detail->product_id,
                    'product_name' => $product ? $product->name : null,
                    'quantity' => $detail->quantity,
                    'quantity_block' => $quantityBlock,
                    'product_size' => $product ? $product->size : null
                ];
            }

            $responseData[] = $freeProductData;
        }

        $data['free_products'] = $responseData;

        return ApiResponse::response("Free products retrieved", $data);
    }



    public function actionGetFreeProductDetail()
    {
        $id = Yii::$app->request->get('id');
        $freeProducts = FreeProducts::findOne($id);
        if (!$freeProducts) {
            return ApiResponse::response(Yii::t('app', 'Record not found'), null, 404);
        }

        $freeProductData = $freeProducts->attributes;
        $freeProductData['details'] = [];

        // Получаем детали бесплатных продуктов
        foreach ($freeProducts->freeProductDetails as $detail) {
            if ($detail->deleted_at !== null) {
                continue; // Пропускаем удаленные записи
            }

            $product = $detail->product;

            // Рассчитываем количество блоков
            $quantityBlock = null;
            if ($product && $product->size > 0) {
                $quantityBlock = $detail->quantity / $product->size;
            }

            $freeProductData['details'][] = [
                'id' => $detail->id,
                'product_id' => $detail->product_id,
                'product_name' => $product ? $product->name : null,
                'quantity' => $detail->quantity,
                'quantity_block' => $quantityBlock,
                'product_size' => $product ? $product->size : null
            ];
        }

        $responseData = [
            'free_products' => $freeProductData
        ];

        return ApiResponse::response("Free products", $responseData);
    }
}