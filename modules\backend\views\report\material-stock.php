<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $reportData array */
/* @var $materialId int */
/* @var $categoryId int */
/* @var $materials array */
/* @var $categories array */

$this->title = Yii::t('app', 'material_stock_report');

?>

<style>
    .material-status-good {
        color: #28a745;
        font-weight: bold;
    }
    .material-status-low {
        color: #ffc107;
        font-weight: bold;
    }
    .material-status-critical {
        color: #dc3545;
        font-weight: bold;
    }
    .text-right {
        text-align: right;
    }
</style>

    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h3 class="mb-0"><?= Yii::t('app', 'material_stock') ?></h3>
        </div>
    </div>   

 <?php if (!empty($reportData['items'])): ?>
        <div class="card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th><?= Yii::t('app', 'material') ?></th>
                                <th class="text-center"><?= Yii::t('app', 'quantity') ?></th>
                                <th class="text-center"><?= Yii::t('app', 'defect_quantity') ?></th>
                                <th class="text-center"><?= Yii::t('app', 'unit') ?></th>

                            </tr>
                        </thead>                        <tbody>
                            <?php foreach ($reportData['items'] as $item): ?>
                                <tr>
                                    <td><?= Html::encode($item['material_name']) ?></td>
                                    <td class="text-right">
                                        <?= number_format($item['quantity'], 0, '.', ' ') ?>
                                    </td>
                                    <td class="text-right"><?= number_format($item['defect_quantity'], 0, '.', ' ') ?></td>
                                    <td class="text-center"><?= Html::encode($item['unit'] ?? '-') ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="alert alert-info text-center">
            <i class="fas fa-info-circle mr-2"></i>
            <?= Yii::t('app', 'no_data_found') ?>
        </div>    <?php endif; ?>