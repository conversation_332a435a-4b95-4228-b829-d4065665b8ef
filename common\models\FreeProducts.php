<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "free_products".
 *
 * @property int $id
 * @property int|null $product_id
 * @property string|null $client
 * @property string|null $car_number
 * @property int|null $quantity
 * @property string|null $created_at
 * @property string|null $deleted_at
 * @property int|null $status
 *
 * @property Product $product
 * @property FreeProductDetail[] $freeProductDetails
 */
class FreeProducts extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'free_products';
    }

    const STATUS_PENDING = 0;
    const STATUS_APPROVED = 1;

    public function getStatusList()
    {
        return [
            self::STATUS_PENDING => Yii::t('app', 'Pending'),
            self::STATUS_APPROVED => Yii::t('app', 'Approved'),
        ];
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['client'], 'required'],
            [['product_id', 'client', 'quantity'], 'default', 'value' => null],
            [['product_id', 'quantity', 'status'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            ['client', 'string', 'max' => 250],
            [['car_number'], 'string', 'max' => 255],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'product_id' => Yii::t('app', 'product'),
            'client' => Yii::t('app', 'client'),
            'car_number' => Yii::t('app', 'car number'),
            'quantity' => Yii::t('app', 'quantity'),
            'created_at' => Yii::t('app', 'created at'),
            'deleted_at' => Yii::t('app', 'deleted at'),
            'status' => Yii::t('app', 'status'),
        ];
    }

    /**
     * Gets query for [[Product]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }

    /**
     * Gets query for [[FreeProductDetails]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getFreeProductDetails()
    {
        return $this->hasMany(FreeProductDetail::class, ['free_product_id' => 'id']);
    }
}
