<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "supplier_balance".
 *
 * @property int $id
 * @property int $supplier_id
 * @property float $amount
 * @property string|null $updated_at
 *
 * @property Supplier $supplier
 */
class SupplierBalance extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'supplier_balance';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['supplier_id', 'amount'], 'required'],
            [['supplier_id'], 'default', 'value' => null],
            [['supplier_id'], 'integer'],
            [['amount'], 'number'],
            [['updated_at'], 'safe'],
            [['supplier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Supplier::class, 'targetAttribute' => ['supplier_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'supplier_id' => 'Supplier ID',
            'amount' => 'Amount',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Supplier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSupplier()
    {
        return $this->hasOne(Supplier::class, ['id' => 'supplier_id']);
    }
}
