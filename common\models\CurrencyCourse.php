<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "currency_course".
 *
 * @property int $id
 * @property int|null $currency_id
 * @property float|null $course
 * @property string|null $start_date
 * @property string|null $end_date
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Currency $currency
 */
class CurrencyCourse extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'currency_course';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['currency_id'], 'default', 'value' => null],
            [['currency_id'], 'integer'],
            [['course'], 'number'],
            [['start_date', 'end_date', 'created_at', 'deleted_at'], 'safe'],
            [['currency_id'], 'exist', 'skipOnError' => true, 'targetClass' => Currency::class, 'targetAttribute' => ['currency_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'currency_id' => 'Currency ID',
            'course' => 'Course',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[Currency]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCurrency()
    {
        return $this->hasOne(Currency::class, ['id' => 'currency_id']);
    }

    /**
     * Получает актуальный курс валюты
     * @param int $currencyId ID валюты
     * @return float|null курс валюты или null если не найден
     */
    public static function getCurrentCourse($currencyId)
    {
        $course = self::find()
            ->where(['currency_id' => $currencyId])
            ->andWhere(['<=', 'start_date', date('Y-m-d')])
            ->andWhere([
                'or',
                ['>', 'end_date', date('Y-m-d')],
                ['end_date' => null]
            ])
            ->andWhere(['deleted_at' => null])
            ->orderBy(['start_date' => SORT_DESC])
            ->one();

        return $course ? $course->course : null;
    }

    /**
     * Конвертирует сумму из одной валюты в другую
     * @param float $amount сумма для конвертации
     * @param int $fromCurrencyId ID исходной валюты
     * @param int $toCurrencyId ID целевой валюты
     * @return array ['amount' => конвертированная_сумма, 'course' => курс_использованный, 'original_amount' => исходная_сумма]
     */
    public static function convertCurrency($amount, $fromCurrencyId, $toCurrencyId)
    {
        // Если валюты одинаковые, конвертация не нужна
        if ($fromCurrencyId == $toCurrencyId) {
            return [
                'amount' => $amount,
                'course' => 1,
                'original_amount' => $amount
            ];
        }

        // Доллары (ID=1) - базовая валюта
        $baseCurrencyId = 1;

        if ($fromCurrencyId == $baseCurrencyId) {
            // Конвертация из долларов в другую валюту
            $course = self::getCurrentCourse($toCurrencyId);
            if (!$course) {
                throw new \Exception("Курс валюты ID {$toCurrencyId} не найден");
            }
            return [
                'amount' => $amount * $course,
                'course' => $course,
                'original_amount' => $amount
            ];
        } elseif ($toCurrencyId == $baseCurrencyId) {
            // Конвертация из другой валюты в доллары
            $course = self::getCurrentCourse($fromCurrencyId);
            if (!$course) {
                throw new \Exception("Курс валюты ID {$fromCurrencyId} не найден");
            }
            return [
                'amount' => $amount / $course,
                'course' => 1 / $course,
                'original_amount' => $amount
            ];
        } else {
            // Конвертация между двумя не-долларовыми валютами (через доллары)
            $fromCourse = self::getCurrentCourse($fromCurrencyId);
            $toCourse = self::getCurrentCourse($toCurrencyId);
            
            if (!$fromCourse || !$toCourse) {
                throw new \Exception("Курсы валют не найдены");
            }
            
            // Сначала в доллары, потом в целевую валюту
            $amountInDollars = $amount / $fromCourse;
            $finalAmount = $amountInDollars * $toCourse;
            
            return [
                'amount' => $finalAmount,
                'course' => $toCourse / $fromCourse,
                'original_amount' => $amount
            ];
        }
    }

    public function beforeValidate()
    {
        $attributes = ['course'];

        foreach ($attributes as $attribute) {
            if (is_string($this->$attribute) && !empty($this->$attribute)) {
                $originalValue = $this->$attribute;
                
                $cleanValue = preg_replace('/[\s\x{A0}]+/u', '', $originalValue);
                $cleanValue = str_replace(',', '.', $cleanValue);
                
                if (is_numeric($cleanValue)) {
                    $this->$attribute = floatval($cleanValue);
                }
            }
        }
        
        return parent::beforeValidate();
    }
}
