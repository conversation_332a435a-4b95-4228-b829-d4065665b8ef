<?php

namespace app\modules\backend\controllers;

use app\common\models\ProductDefect;
use app\common\models\ProductStorage;
use app\common\models\Tracking;
use Yii;
use yii\db\Expression;
use yii\web\Controller;

/**
 * Контроллер для работы с переупаковкой продуктов
 */
class ProductRepackagingController extends BaseController
{
    /**
     * Отображение списка продуктов на переупаковку
     * 
     * @return string
     */
    public function actionIndex()
    {
        // Получение списка продуктов на переупаковке
        $query = ProductDefect::find()
            ->select([
                'product_defect.id',
                'product_defect.created_at',
                'u.username as user_name',
                'p.name as product_name',
                'product_defect.quantity',
                'product_defect.repackaging_reason',
                'product_defect.description',
                'au.username as accepted_by',
                new Expression('CASE 
                    WHEN product_defect.accepted_user_id IS NOT NULL 
                    THEN true 
                    ELSE false 
                END as is_accepted'),
                new Expression('CASE 
                    WHEN t.accepted_at IS NOT NULL 
                    AND t.status = ' . Tracking::STATUS_ACCEPTED . '
                    AND t.deleted_at IS NULL 
                    THEN true
                    ELSE false
                END as status')
            ])
            ->leftJoin('product p', 'p.id = product_defect.product_id')
            ->leftJoin('users u', 'u.id = product_defect.add_user_id')
            ->leftJoin('users au', 'au.id = product_defect.accepted_user_id')
            ->leftJoin('tracking t', 't.process_id = product_defect.id AND t.progress_type = ' . Tracking::TYPE_PRODUCT_REPACKAGING)
            ->where(['product_defect.deleted_at' => null])
            ->andWhere(['product_defect.is_repackaging' => true])
            ->orderBy(['product_defect.created_at' => SORT_DESC]);
        
        $repackagingProducts = $query->asArray()->all();

        // Получение списка доступных продуктов на складе
        $productStorages = ProductStorage::find()
            ->select([
                'product_storage.id',
                'p.name as product_name',
                'product_storage.quantity',
                'product_storage.enter_date'
            ])
            ->leftJoin('product p', 'p.id = product_storage.product_id')
            ->where(['product_storage.deleted_at' => null])
            ->andWhere(['>', 'product_storage.quantity', 0])
            ->orderBy(['p.name' => SORT_ASC])
            ->asArray()
            ->all();

        return $this->render('index', [
            'repackagingProducts' => $repackagingProducts,
            'productStorages' => $productStorages
        ]);
    }
}
