<?php

namespace app\modules\backend\controllers;

use Yii;
use app\common\models\Product;
use app\common\models\ProductPrice;
use app\modules\backend\models\ProductCreateForm;
use yii\web\Response;

/**
 * ProductController implements the CRUD actions for Product model.
 */
class ProductController extends BaseController
{
    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionIndex()
    {
        $sql = "
            SELECT
                p.id,
                p.name,
                p.size as block_quantity,
                p.created_at,
                p.deleted_at,
                pp.price,
                pp.start_date,
                pp.end_date,
                COALESCE(ps.total_stored, 0) as remainder_quantity,
                COALESCE(ps.total_stored, 0) * pp.price as total_sum,
                p.priority
            FROM
                product AS p
            LEFT JOIN product_price AS pp ON p.id = pp.product_id AND pp.end_date = '9999-12-31' and pp.deleted_at is null
            LEFT JOIN (
                SELECT product_id, SUM(quantity) as total_stored
                FROM product_storage
                WHERE deleted_at IS NULL
                    AND accepted_at IS NOT NULL
                    AND accepted_user_id IS NOT NULL
                GROUP BY product_id
            ) ps ON p.id = ps.product_id
            ORDER BY p.priority ASC
        ";
        $result = Yii::$app->db->createCommand($sql)->queryAll();

        return $this->render('index', [
            'result' => $result,
        ]);
    }

    /**
     * Creates a new Product model.
     * @return mixed
     */
    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new ProductCreateForm();
            $model->scenario = ProductCreateForm::SCENARIO_CREATE;
            $data = Yii::$app->request->post();
            $model->load($data, 'ProductCreateForm');

            if ($model->validate()) {

                $product = Product::findOne(['name' => $model->name]);
                if ($product) {
                    return [
                        'status' => 'error',
                        'errors' => ['name' => [Yii::t('app', 'Product already exists')]],
                    ];
                }

                $product = new Product();
                $product->name = $model->name;
                $product->size = $model->block_quantity;
                $product->type = $model->type == 1 ? Product::TYPE_FIZZY : Product::TYPE_SOFT;
                
                if (isset($model->priority) && $model->priority !== null) {
                    $product->priority = $model->priority;
                } else {
                    $lastPriority = Product::find()
                        ->select('priority')
                        ->where(['deleted_at' => null])
                        ->orderBy(['priority' => SORT_DESC])
                        ->scalar();
                
                    $product->priority = $lastPriority !== null ? $lastPriority + 1 : 1;
                }
                
                $product->created_at = date('Y-m-d H:i:s');

                if(!$product->save()){
                    return [
                        'status' => 'error',
                        'errors' => $product->getErrors(),
                    ];
                }
                $productPrice = new ProductPrice();
                $productPrice->product_id = $product->id;
                $productPrice->price = $model->price;
                $productPrice->start_date = date('Y-m-d H:i:s');
                $productPrice->end_date = '9999-12-31';
                $productPrice->created_at = date('Y-m-d H:i:s');
                
                if(!$productPrice->save()){
                    return [
                        'status' => 'error',
                        'errors' => $productPrice->getErrors(),
                    ];
                }

                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Product created successfully.')
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $model = new Product();
            return [
                "status" => 'success',
                "content" => $this->renderPartial('create', ['model' => $model])
            ];
        }
    }

    /**
     * Updates an existing Product model.
     * @return mixed
     */
    public function actionPriceChange()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post('ProductPrice');
            
            $product = Product::findOne($postData['product_id']);
            if (!$product) {
                return [
                    'status' => 'error',
                    'errors' => ['product' => [Yii::t('app', 'Product not found.')]]
                ];
            }

            if (strtotime($postData['start_date']) < strtotime(date('Y-m-d'))) {
                return [
                    'status' => 'error',
                    'errors' => ['start_date' => [Yii::t('app', 'Start date cannot be in the past')]]
                ];
            }

            $currentPrice = ProductPrice::find()
                ->where(['product_id' => $postData['product_id']])
                ->andWhere(['end_date' => '9999-12-31'])
                ->one();

            if ($currentPrice) {
                $currentPrice->end_date = date('Y-m-d', strtotime($postData['start_date'] . ' -1 day'));
                $currentPrice->deleted_at = date('Y-m-d H:i:s');
                if (!$currentPrice->save()) {
                    return [
                        'status' => 'error',
                        'errors' => $currentPrice->getErrors()
                    ];
                }
            }

            $newPrice = new ProductPrice();
            $newPrice->product_id = $postData['product_id'];
            $newPrice->price = $postData['price'];
            $newPrice->start_date = $postData['start_date'];
            $newPrice->end_date = '9999-12-31';
            $newPrice->created_at = date('Y-m-d H:i:s');

            if ($newPrice->save()) {
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'price_updated_successfully')
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $newPrice->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $product = Product::findOne($id);
            
            if (!$product) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Product not found.')
                ];
            }

            $currentPrice = ProductPrice::find()
                ->where(['product_id' => $id])
                ->andWhere(['end_date' => '9999-12-31'])
                ->one();

            $price = $currentPrice ? $currentPrice->price : 0;

            return [
                'status' => 'success',
                'content' => $this->renderPartial('_price_change', [
                    'model' => $product,
                    'currentPrice' => $price
                ])
            ];
        }
    }

   
    public function actionView()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $product = Product::findOne($id);
            if (!$product) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Product not found.')
                ];
            }

            // Получаем историю цен
            $priceHistory = ProductPrice::find()
                ->select(['price', 'start_date', 'end_date'])
                ->where(['product_id' => $id])
                ->orderBy(['id' => SORT_DESC])
                ->asArray()
                ->all();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('_view', [
                    'model' => $product,
                    'priceHistory' => $priceHistory
                ])
            ];
        }
    }

    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $model = new ProductCreateForm();
            $model->scenario = ProductCreateForm::SCENARIO_UPDATE;
            $model->load($postData, 'ProductCreateForm');
            $product = Product::findOne($postData['id']);
            if (!$product) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Product not found.')
                ];
            }

            $product->name = $model->name;
            $product->size = $model->block_quantity;
            $product->type = $model->type == 1 ? Product::TYPE_FIZZY : Product::TYPE_SOFT;

            if (isset($postData['ProductCreateForm']['priority']) && $postData['ProductCreateForm']['priority'] !== null && $postData['ProductCreateForm']['priority'] !== '') {
                $product->priority = $model->priority;
            } else {
                if ($product->priority === null) {
                    $lastPriority = Product::find()
                        ->select('priority')
                        ->where(['deleted_at' => null])
                        ->orderBy(['priority' => SORT_DESC])
                        ->scalar();
                    
                    $product->priority = $lastPriority !== null ? $lastPriority + 1 : 1;
                }
            }

            
            if (!$product->save()) {
                return [
                    'status' => 'error',
                    'errors' => $product->getErrors()
                ];
            }

            return [
                'status' => 'success',
            ];
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $product = Product::findOne($id);
            if (!$product) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Product not found.')
                ];
            }
            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', ['model' => $product])
            ];
        }
    }
}
