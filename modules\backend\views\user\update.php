<?php
use yii\bootstrap5\Html;
use app\modules\backend\models\Users;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<div class="users-form">
    <form id="user-update-form">
        <?= Html::hiddenInput(Yii::$app->request->csrfParam, Yii::$app->request->csrfToken) ?>
        <?= Html::hiddenInput('Users[id]', $model->id) ?>

        <div class="row">
            <div class="col-md-12">
                <div class="form-group mb-3">
                    <label for="full_name"><?= Yii::t('app', 'user_full_name') ?></label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                        </div>
                        <input type="text" id="full_name" name="Users[full_name]" class="form-control" value="<?= Html::encode($model->full_name) ?>">
                    </div>
                    <div class="error-container text-danger" id="full_name-error"></div>
                </div>
            </div>

            <div class="col-md-12">
                <div class="form-group mb-3">
                    <label for="role"><?= Yii::t('app', 'user_role') ?></label>
                    <div class="input-group">
                        
                        <select id="role" name="Users[role]" class="form-control select2">
                            <option value=""><?= Yii::t('app', 'select_role') ?></option>
                            <?php foreach (Users::getRoles() as $key => $value): ?>
                                <?php if ($key === 'admin') continue; ?>
                                <option value="<?= $key ?>" <?= $model->getAuthRole() == $key ? 'selected' : '' ?>><?= $value ?></option>
                            <?php endforeach; ?>
                        </select>

                    </div>
                    <div class="error-container text-danger" id="role-error"></div>
                </div>
            </div>

            <div class="col-md-12">
                <div class="form-group mb-3">
                    <label for="username"><?= Yii::t('app', 'username') ?></label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                        </div>
                        <input type="text" id="username" name="Users[username]" class="form-control" value="<?= Html::encode($model->username) ?>">
                    </div>
                    <div class="error-container text-danger" id="username-error"></div>
                </div>
            </div>

            <div class="col-md-12">
                <div class="form-group mb-3">
                    <label for="password"><?= Yii::t('app', 'password') ?></label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        </div>
                        <input type="password" 
                            id="password" 
                            name="Users[password]" 
                            class="form-control" 
                            placeholder="<?= Yii::t('app', 'leave_empty_if_not_changing') ?>">
                    </div>
                    <div class="error-container text-danger" id="password-error"></div>
                </div>
            </div>

        </div>
    </form>
</div>

<style>
    .is-invalid {
        border-color: red !important;
    }
    .error-text {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
</style>


