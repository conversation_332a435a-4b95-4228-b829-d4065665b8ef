<?php
use yii\helpers\Html;
use yii\helpers\ArrayHelper;
use app\common\models\Product;
use app\common\models\Material;

$products =Product::find()
    ->select(['id', 'name'])
    ->where(['deleted_at' => NULL])
    ->asArray()
    ->all();
$productsList = ArrayHelper::map($products, 'id', 'name');

$materials = Material::find()
    ->select(['id', 'name', 'category_id'])
    ->where(['deleted_at' => NULL])
    ->asArray()
    ->all();
$materialsList = ArrayHelper::map($materials, 'id', 'name');

// Получаем текущие основные ингредиенты
$currentMaterials = \app\common\models\ProductIngredients::find()
    ->select(['material_id'])
    ->where([
        'product_id' => $model->product_id,
        'end_date' => '9999-12-31',
        'is_alternative' => false
    ])
    ->column();

// Получаем текущие альтернативные ингредиенты
$currentAlternativeMaterials = \app\common\models\ProductIngredients::find()
    ->select(['material_id'])
    ->where([
        'product_id' => $model->product_id,
        'end_date' => '9999-12-31',
        'is_alternative' => true
    ])
    ->column();
?>

<form id="product-ingredients-update-form">
    <input type="hidden" name="ProductIngredients[id]" value="<?= Html::encode($model->id) ?>">

    <div class="form-group">
        <label for="product_id"><?= Yii::t('app', 'product') ?></label>
        <select id="product_id" name="ProductIngredients[product_id]" class="form-control select2" required>
            <option value=""><?= Yii::t('app', 'select_product') ?></option>
            <?php foreach ($productsList as $id => $name): ?>
                <option value="<?= $id ?>" <?= $model->product_id == $id ? 'selected' : '' ?>>
                    <?= Html::encode($name) ?>
                </option>
            <?php endforeach; ?>
        </select>
        <div class="error-container text-danger" id="product_id-error"></div>
    </div>

    <div class="form-group">
        <label for="material_ids"><?= Yii::t('app', 'Materials') ?></label>
        <select id="material_ids" name="ProductIngredients[material_ids][]" class="form-control select2" multiple required>
            <?php
            // Группируем материалы по категориям для удобства выбора
            $materialsByCategory = [];
            foreach ($materials as $material) {
                $categoryId = $material['category_id'] ?? 0;
                $categoryName = 'Без категории';

                if ($categoryId) {
                    $category = \app\common\models\MaterialCategory::findOne($categoryId);
                    if ($category) {
                        $categoryName = $category->name;
                    }
                }

                if (!isset($materialsByCategory[$categoryName])) {
                    $materialsByCategory[$categoryName] = [];
                }

                $materialsByCategory[$categoryName][] = $material;
            }

            // Выводим материалы, сгруппированные по категориям
            foreach ($materialsByCategory as $categoryName => $categoryMaterials):
            ?>
                <optgroup label="<?= Html::encode($categoryName) ?>">
                    <?php foreach ($categoryMaterials as $material): ?>
                        <option value="<?= $material['id'] ?>" <?= in_array($material['id'], $currentMaterials) ? 'selected' : '' ?>>
                            <?= Html::encode($material['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </optgroup>
            <?php endforeach; ?>
        </select>
        <div class="error-container text-danger" id="material_ids-error"></div>
    </div>

    <div class="form-group">
        <label for="alternative_material_ids"><?= Yii::t('app', 'Alternative Ingredients') ?></label>
        <select id="alternative_material_ids" name="alternative_material_ids[]" class="form-control select2" multiple>
            <?php
            // Группируем материалы по категориям для удобства выбора
            $materialsByCategory = [];
            foreach ($materials as $material) {
                $categoryId = $material['category_id'] ?? 0;
                $categoryName = 'Без категории';

                if ($categoryId) {
                    $category = \app\common\models\MaterialCategory::findOne($categoryId);
                    if ($category) {
                        $categoryName = $category->name;
                    }
                }

                if (!isset($materialsByCategory[$categoryName])) {
                    $materialsByCategory[$categoryName] = [];
                }

                $materialsByCategory[$categoryName][] = $material;
            }

            // Выводим материалы, сгруппированные по категориям
            foreach ($materialsByCategory as $categoryName => $categoryMaterials):
            ?>
                <optgroup label="<?= Html::encode($categoryName) ?>">
                    <?php foreach ($categoryMaterials as $material): ?>
                        <option value="<?= $material['id'] ?>" <?= in_array($material['id'], $currentAlternativeMaterials) ? 'selected' : '' ?>>
                            <?= Html::encode($material['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </optgroup>
            <?php endforeach; ?>
        </select>
        <div class="help-block">
            <?= Yii::t('app', 'Select alternative materials that can be used if main materials are not available') ?>
            <br>
            <strong><?= Yii::t('app', 'Note: Alternative materials should be from the same category as the main materials') ?></strong>
        </div>
        <div class="error-container text-danger" id="alternative_material_ids-error"></div>
    </div>
</form>
