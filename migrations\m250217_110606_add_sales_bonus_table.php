<?php

use yii\db\Migration;

class m250217_110606_add_sales_bonus_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%sales_bonus}}', [
            'id' => $this->primaryKey(),
            'sale_id' => $this->integer(),
            'quantity' => $this->integer()->null(),
            'product_id' => $this->integer()->null(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);
        $this->addForeignKey(
            'fk-sales_bonus-sale_id',
            '{{%sales_bonus}}',
            'sale_id',
            '{{%sales}}',
            'id',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk-sales_bonus-product_id',
            '{{%sales_bonus}}',
            'product_id',
            '{{%product}}',
            'id',
            'CASCADE'
        );

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%sales_bonus}}');
        $this->dropForeignKey('fk-sales_bonus-product_id', '{{%sales_bonus}}');
        $this->dropForeignKey('fk-sales_bonus-sale_id', '{{%sales_bonus}}');

    }

   
}
