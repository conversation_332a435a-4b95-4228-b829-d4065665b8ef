<?php

use app\assets\AppAsset;
use app\assets\Select2Asset;

AppAsset::register($this);
Select2Asset::register($this);
?>

<form id="special-price-form">
    <input type="hidden" name="ClientSpecialPrices[id]" value="<?= $model->id ?>">
    <div class="special-price">
        

        <div class="form-group mt-2">
            <label for="product_id"><?= Yii::t('app', 'product') ?></label>
            <select id="product_id" name="ClientSpecialPrices[product_id]" class="form-control select2">
                <option value=""><?= Yii::t('app', 'select_product') ?></option>
                <?php foreach ($products as $product): ?>
                    <option value="<?= $product['id'] ?>" <?= $model->product_id == $product['id'] ? 'selected' : '' ?>>
                        <?= $product['name'] ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <div class="error-container" id="product_id-error"></div>
        </div>

        <div class="form-group mt-3">
            <label for="special_price"><?= Yii::t('app', 'special_price') ?></label>
            <input type="text" id="special_price" name="ClientSpecialPrices[special_price]" class="form-control formatted-numeric-input" 
                   value="<?= $model->special_price ?>">
            <div class="error-container" id="special_price-error"></div>
        </div>

        <div class="form-group mt-3">
            <label for="sell_price"><?= Yii::t('app', 'sell_price') ?></label>
            <input type="text" id="sell_price" name="ClientSpecialPrices[sell_price]" class="form-control formatted-numeric-input" 
                   value="<?= $model->sell_price ?>">
            <div class="error-container" id="sell_price-error"></div>
        </div>
    </div>
</form>


<script>
    function cloneInput(input) {
        const sellPriceInput = document.getElementById('sell_price');
        sellPriceInput.value = input.value;
    }

    document.getElementById('special_price').addEventListener('input', function() {
        cloneInput(this);
    });
</script>
    