<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "supplier".
 *
 * @property int $id
 * @property string $full_name
 * @property string $phone_number
 * @property string|null $phone_number_2
 * @property string|null $account_number
 * @property string|null $address
 * @property string|null $created_at
 * @property string|null $deleted_at
 * @property int|null $currency_id
 *
 * @property Invoice[] $invoices
 * @property SupplierBalanceHistory[] $supplierBalanceHistories
 * @property SupplierBalance[] $supplierBalances
 * @property SupplierPayments[] $supplierPayments
 */
class Supplier extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'supplier';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['full_name', 'phone_number', 'currency_id'], 'required'],
            [['currency_id'], 'default', 'value' => null],
            [['currency_id'], 'integer'],
            [['address'], 'string'],
            [['created_at', 'deleted_at'], 'safe'],
            [['full_name'], 'string', 'max' => 255],
            [['phone_number', 'phone_number_2'], 'string', 'max' => 20],
            [['account_number'], 'string', 'max' => 30],
            [['phone_number', 'phone_number_2'], 'validatePhoneNumber'],
            [['currency_id'], 'exist', 'skipOnError' => true, 'targetClass' => Currency::class, 'targetAttribute' => ['currency_id' => 'id']],
        ];
    }

    public function validatePhoneNumber($attribute, $params, $validator)
    {
        $value = $this->$attribute;
        $numericValue = preg_replace('/\D/', '', $value);

        if (strlen($numericValue) !== 9) {
            $this->addError($attribute, Yii::t('app', 'phone_number_error_length'));
            return;
        }

        $companyCodes = ['90', '91', '93', '94', '88', '55', '87', '95', '97', '98', '99', '20', '33', '77', '50'];

        $operatorCode = substr($numericValue, 0, 2);

        if (!in_array($operatorCode, $companyCodes)) {
            $this->addError($attribute, Yii::t('app', "phone_number_error"));
        }
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'full_name' => Yii::t('app', 'supplier_full_name'),
            'phone_number' => Yii::t('app', 'phone_number'),
            'phone_number_2' => Yii::t('app', 'phone_number_2'),
            'account_number' => Yii::t('app', 'account_number'),
            'address' => Yii::t('app', 'address'),
            'created_at' => Yii::t('app', 'created_at'),
            'deleted_at' => Yii::t('app', 'deleted_at'),
            'currency_id' => Yii::t('app', 'currency'),
        ];
    }

    /**
     * Gets query for [[Invoices]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getInvoices()
    {
        return $this->hasMany(Invoice::class, ['supplier_id' => 'id']);
    }

    /**
     * Gets query for [[SupplierBalanceHistories]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSupplierBalanceHistories()
    {
        return $this->hasMany(SupplierBalanceHistory::class, ['supplier_id' => 'id']);
    }

    /**
     * Gets query for [[SupplierBalances]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSupplierBalances()
    {
        return $this->hasMany(SupplierBalance::class, ['supplier_id' => 'id']);
    }

    /**
     * Gets query for [[SupplierPayments]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSupplierPayments()
    {
        return $this->hasMany(SupplierPayments::class, ['supplier_id' => 'id']);
    }

    /**
     * Gets query for [[Currency]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCurrency()
    {
        return $this->hasOne(Currency::class, ['id' => 'currency_id']);
    }
}
