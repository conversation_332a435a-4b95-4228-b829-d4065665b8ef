<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\grid\GridView;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "region_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin')): ?>
                <a href="#" class="btn btn-primary region-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "add_region") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'region-grid-pjax']); ?>
    <?php if($dataProvider->models): ?>
        <div>
            <table id="region-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th><?= Yii::t("app", "region_name") ?></th>
                    <th><?= Yii::t("app", "created_at") ?></th>
                    <th><?= Yii::t("app", "status") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($dataProvider->models as $model): ?>
                    <tr>
                        <td><?= Html::encode($model->name) ?></td>
                        <td data-order="<?= !empty($model->created_at) ? strtotime($model->created_at) : '' ?>">
                            <?= !empty($model->created_at) ? Html::encode(date('d.m.Y H:i', strtotime($model->created_at))) : 'N/A' ?>
                        </td>
                        <td>
                            <span class="<?= $model['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?php
                                $statusText = $model['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                echo $statusText;
                                ?>
                            </span>
                        </td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <a href="#" class="dropdown-item region-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model->id) ?>">
                                        <?= Yii::t("app", "edit") ?>
                                    </a>
                                    <?php if ($model->deleted_at == NULL): ?>
                                        <a href="#" class="dropdown-item region-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model->id) ?>">
                                            <span class="red-text"><?= Yii::t("app", "delete") ?></span>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "add_region") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "edit_region") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "region_delete") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#region-grid-view')) {
            $('#region-grid-view').DataTable().destroy();
        }
        
        $('#region-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[1, 'desc']],
            "columnDefs": [
                {
                    "targets": [2, 3],
                    "orderable": false
                },
                {
                    "targets": 1,
                    "type": "date-eu",
                    "render": function(data, type, row) {
                        if (type === 'sort') {
                            var parts = data.split(' ');
                            var dateParts = parts[0].split('.');
                            var timePart = parts.length > 1 ? parts[1].replace(':', '') : '0000';
                            return dateParts[2] + dateParts[1] + dateParts[0] + timePart;
                        }
                        return data;
                    }
                }
            ]
        });
    }

    function initializeAll() {
        initializeDataTable();
        initializeDropdown();
        initializeSelect2();
        initializeRegionCreate();
        initializeRegionUpdate();
        initializeRegionDelete();
    }

    // Initialize everything on first load
    initializeAll();

    // Re-initialize after PJAX reloads
    $(document).on('pjax:success', function() {
        initializeAll();
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }
    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }
    
    function initializeRegionCreate() {
        $(document).off('click.region-create').on('click.region-create', '.region-create', function() {
            $.ajax({
                url: '/backend/region/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("region-create-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.region-create-button').on('click.region-create-button', '.region-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#region-create-form').serialize();
                $.ajax({
                    url: '/backend/region/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.success) {
                            button.prop('disabled', false);
                            $.pjax.reload({container: '#region-grid-pjax'});
                            $('.close').trigger('click');
                            initializeDataTable();
                        } else if (response && response.errors) {
                            $('.help-block').html('');
                            $.each(response.errors, function(field, errors) {
                                $('#' + field).next('.help-block').html(errors.join('<br>'));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeRegionUpdate() {
        $(document).off('click.region-update').on('click.region-update', '.region-update', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/region/update',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(two);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("region-update-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.region-update-button').on('click.region-update-button', '.region-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#region-update-form').serialize();
                $.ajax({
                    url: '/backend/region/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.success) {
                            button.prop('disabled', false);
                            $.pjax.reload({container: '#region-grid-pjax'});
                            $('.close').trigger('click');
                            initializeDataTable();
                        } else if (response && response.errors) {
                            $('.help-block').html('');
                            $.each(response.errors, function(field, errors) {
                                $('#' + field).next('.help-block').html(errors.join('<br>'));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeRegionDelete() {
        $(document).off('click.region-delete').on('click.region-delete', '.region-delete', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/region/delete',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal-delete .modal-title').html(three);
                    $('#ideal-mini-modal-delete .modal-body').html(response.content);
                    $('#ideal-mini-modal-delete .mini-button').addClass("region-delete-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.region-delete-button').on('click.region-delete-button', '.region-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#region-delete-form').serialize();
                $.ajax({
                    url: '/backend/region/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        button.prop('disabled', false);
                        if (response && response.success) {
                            $.pjax.reload({container: '#region-grid-pjax'});
                            $('#ideal-mini-modal-delete').modal('hide');
                            initializeDataTable();
                        } else {
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
