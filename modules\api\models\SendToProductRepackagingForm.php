<?php

namespace app\modules\api\models;

use yii\base\Model;

/**
 * Форма для отправки продукта на переупаковку
 */
class SendToProductRepackagingForm extends Model
{
    /**
     * @var int ID продукта для переупаковки
     */
    public $product_id;

    /**
     * @var float Количество продукта для переупаковки
     */
    public $quantity;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['product_id', 'quantity'], 'required'],
            [['product_id'], 'integer', 'min' => 1],
            [['quantity'], 'number', 'min' => 0.01],
            [['product_id'], 'validateProductStorage'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'product_id' => 'Продукт для переупаковки',
            'quantity' => 'Количество',
        ];
    }

    /**
     * Валидация записи на складе
     * 
     * @param string $attribute
     * @param array $params
     */
    public function validateProductStorage($attribute, $params)
    {
        $productStorages = \app\common\models\ProductStorage::find()
            ->where(['product_id' => $this->product_id])
            ->andWhere(['IS', 'deleted_at', null])
            ->andWhere(['>=', 'quantity', 0])
            ->orderBy(['enter_date' => SORT_ASC])
            ->all();

        
        if (empty($productStorages)) {
            $this->addError($attribute, 'Записи на складе не найдены для продукта ' . $this->product_id);
            return;
        }
        
        $totalQuantity = array_sum(array_column($productStorages, 'quantity'));
        
        if ($totalQuantity < $this->quantity) {
            $this->addError($attribute, 'Количество для переупаковки не может превышать доступное количество на складе для продукта ' . $this->product_id);
            return;
        }
    }
}
