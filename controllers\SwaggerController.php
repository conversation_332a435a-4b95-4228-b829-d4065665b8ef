<?php

namespace app\controllers;

use Yii;
use yii\web\Controller;
use yii2mod\swagger\SwaggerApiAction;

class SwaggerController extends Controller
{
    public $layout = false;

    public function actions()
    {
        return [
            'json' => [
                'class' => SwaggerApiAction::class,
                'scanDir' => [
                    Yii::getAlias('@app/modules/api/controllers'),
                ],
                'cache' => 'cache',
                'scanOptions' => [
                    'swagger' => [
                        'version' => '2.0'
                    ]
                ],
            ],
        ];
    }

    public function actionUi()
    {
        return $this->render('ui', [
            'restUrl' => '/swagger/json',
        ]);
    }

    public function actionIndex()
    {
        return $this->redirect(['ui']);
    }
}
