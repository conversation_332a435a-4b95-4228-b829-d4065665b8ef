<?php

namespace app\modules\api\models;

use Yii;
use yii\base\Model;

class IncomeRawMaterialForm extends Model
{
    public $supplier_id;
    public $materials;
    public $description;

   
    public function rules()
    {
        return [
            [['supplier_id', 'materials'], 'required'],
            ['supplier_id', 'integer'],
            ['description', 'string'],
            ['materials', 'validateMaterials'],
        ];
    }

    
    public function validateMaterials($attribute, $params)
    {
        if (!is_array($this->materials)) {
            $this->addError($attribute, 'Materials must be an array');
            return;
        }

        foreach ($this->materials as $index => $material) {
            if (!isset($material['material_id'], $material['quantity'])) {
                $this->addError($attribute, "Material at index {$index} is missing required fields");
                continue;
            }

            if (!is_numeric($material['material_id']) || !is_numeric($material['quantity'])) {
                $this->addError($attribute, "Invalid values in material at index {$index}");
                continue;
            }
            if ($material['quantity'] <= 0) {
                $this->addError($attribute, Yii::t('app', 'Quantity must be greater than zero'));
                continue;
            }

        }
    }

    /**
     * Определение полей
     */
    public function attributeLabels()
    {
        return [
            'supplier_id' => 'Supplier ID',
            'materials' => 'Materials',
        ];
    }
}
