<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "sales_detail".
 *
 * @property int $id
 * @property int $sale_id
    * @property int $product_id
    * @property int $quantity
 * @property float $special_price
 * @property float $total_price
 * @property float $sell_price
 * @property float $factory_price
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Product $product
 * @property Sales $sale
 */
class SalesDetail extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */

     public $box_count;
     
    public static function tableName()
    {
        return 'sales_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sale_id', 'product_id', 'quantity', 'special_price', 'total_price', 'sell_price'], 'required'],
            [['sale_id', 'product_id', 'quantity'], 'default', 'value' => null],
            [['sale_id', 'product_id', 'quantity'], 'integer'],
            [['special_price', 'total_price', 'sell_price', 'factory_price'], 'number'],
            [['created_at', 'deleted_at'], 'safe'],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
            [['sale_id'], 'exist', 'skipOnError' => true, 'targetClass' => Sales::class, 'targetAttribute' => ['sale_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sale_id' => 'Sale ID',
            'product_id' => 'Product ID',
            'quantity' => 'Quantity',
            'special_price' => 'Special Price',
            'sell_price' => 'Sell Price',
            'total_price' => 'Total Price',
            'created_at' => 'Created At',
        ];
    }

    /**
     * Gets query for [[Product]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }

    /**
     * Gets query for [[Sale]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSale()
    {
        return $this->hasOne(Sales::class, ['id' => 'sale_id']);
    }
}
