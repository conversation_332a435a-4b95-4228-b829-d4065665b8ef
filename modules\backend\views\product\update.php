<?php
use yii\helpers\Html;

/** @var yii\web\View $this */
/** @var app\common\models\Product $product */
?>

<div class="position-form">
    <form id="product-update-form">
        <?= Html::hiddenInput(Yii::$app->request->csrfParam, Yii::$app->request->csrfToken) ?>
        <?= Html::hiddenInput('id', $model->id) ?>
        <div class="form-group ">
            <label for="name"><?= Yii::t('app', 'product_name') ?> <span class="text-danger">*</span></label>
            <input type="text" id="name" name="ProductCreateForm[name]" maxlength="255" class="form-control" 
                   value="<?= Html::encode($model->name) ?>" required>
            <div class="error-container" id="name-error">
            </div>
        </div>

        <div class="form-group mt-2">
            <label for="block_quantity"><?= Yii::t('app', 'product_block_quantity') ?> <span class="text-danger">*</span></label>
            <input type="number" id="block_quantity" name="ProductCreateForm[block_quantity]" class="form-control" 
                   value="<?= Html::encode($model->size) ?>" required>
            <div class="error-container" id="block_quantity-error">
            </div>
        </div>

        <div class="form-group mt-2">
            <label for="priority"><?= Yii::t('app', 'product_priority') ?> <span class="text-danger">*</span></label>
            <input type="number" id="priority" name="ProductCreateForm[priority]" class="form-control" 
                   value="<?= Html::encode($model->priority) ?>" required>
            <div class="error-container" id="priority-error">
            </div>
        </div>

        <div class="form-group mt-3">
            <div class="checkbox">
                <label>
                    <input type="checkbox" id="type" name="ProductCreateForm[type]" value="1" 
                           <?= $model->type == 1 ? 'checked' : '' ?>>
                    <?= Yii::t('app', 'is_fizzy') ?>
                </label>
            </div>
            <div class="error-container" id="type-error">
            </div>
        </div>
    </form>
</div>

