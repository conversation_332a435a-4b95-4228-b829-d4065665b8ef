<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "supplier_payments".
 *
 * @property int $id
 * @property int $supplier_id
 * @property float $amount
 * @property int $type
 * @property int|null $expense_id
 * @property int|null $add_user_id
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Supplier $supplier
 */
class SupplierPayments extends \yii\db\ActiveRecord
{
 
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'supplier_payments';
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['supplier_id', 'amount', 'type'], 'required'],
            [['supplier_id', 'expense_id', 'add_user_id'], 'default', 'value' => null],
            [['supplier_id', 'expense_id', 'add_user_id'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            [['supplier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Supplier::class, 'targetAttribute' => ['supplier_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'supplier_id' => 'Supplier ID',
            'amount' => Yii::t('app', 'amount'),
            'type' => Yii::t('app', 'payment_type'),
            'expense_id' => 'Expense ID',
            'created_at' => 'Created At',
        ];
    }

    /**
     * Gets query for [[Supplier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSupplier()
    {
        return $this->hasOne(Supplier::class, ['id' => 'supplier_id']);
    }
}
