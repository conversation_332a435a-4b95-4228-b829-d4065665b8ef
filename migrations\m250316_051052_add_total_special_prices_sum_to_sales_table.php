<?php

use yii\db\Migration;

class m250316_051052_add_total_special_prices_sum_to_sales_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->dropColumn('sales_detail', 'unit_price');
        $this->addColumn('sales', 'total_special_prices_sum', $this->decimal(10, 2)->null());
        $this->addColumn('sales_detail', 'sell_price', $this->decimal(10, 2)->null());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('sales', 'total_special_prices_sum');
        $this->addColumn('sales_detail', 'unit_price', $this->decimal(10, 2)->defaultValue(0));
        $this->dropColumn('sales_detail', 'sell_price');
    }

}
