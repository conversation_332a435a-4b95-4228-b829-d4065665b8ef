<?php

use yii\helpers\Html;
use yii\helpers\Url;
use app\assets\DataTablesAsset;

/* @var $this yii\web\View */
/* @var $reportData array */
/* @var $type string */
/* @var $regionId int|null */
/* @var $regions array */

DataTablesAsset::register($this);

?>

<div class="card">
    <div style="margin-bottom: 3rem;">
        <h3 class="mb-0"><?= Yii::t('app', 'client_debts') ?></h3>
    </div>
    <div class="card-body">
        

        <!-- Таблица задолженностей -->
        <div class="table-responsive">
            <table id="debt-table" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'client') ?></th>
                        <th><?= Yii::t('app', 'phone') ?></th>
                        <th><?= Yii::t('app', 'region') ?></th>
                        <th><?= Yii::t('app', 'balance') ?></th>
                        <th><?= Yii::t('app', 'last_update') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($reportData['items'] as $item): ?>
                        <tr>
                            <td>
                                <?= Html::encode($item['full_name']) ?>
                                <?php if ($item['address']): ?>
                                    <br><small class="text-muted"><?= Html::encode($item['address']) ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?= Html::encode($item['phone_number']) ?>
                                <?php if ($item['phone_number_2']): ?>
                                    <br><?= Html::encode($item['phone_number_2']) ?>
                                <?php endif; ?>
                            </td>
                            <td><?= Html::encode($item['region_name'] ?? '-') ?></td>
                            <td class="text-right <?= $item['balance'] < 0 ? 'text-danger' : 'text-success' ?>">
                                <strong><?= Yii::$app->formatter->asDecimal($item['balance'], 0) ?></strong>
                            </td>
                        
                            <td>
                                <?= $item['last_update'] ? date('d.m.Y H:i', strtotime($item['last_update'])) : '-' ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="3" class="text-right"><?= Yii::t('app', 'total') ?>:</th>
                        <th class="text-right text-danger">
                            <strong><?= Yii::$app->formatter->asDecimal($reportData['totalDebtors'], 0) ?></strong>
                        </th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<style>
.card {
    margin-bottom: 1rem;
}

.badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.text-danger {
    color: #dc3545 !important;
}

.text-success {
    color: #28a745 !important;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.table-responsive {
    overflow-x: auto;
}

@media (max-width: 768px) {
    .card-body h4 {
        font-size: 1.25rem;
    }
}
</style>

<script>
$(document).ready(function() {
    // Инициализация DataTable
    if ($.fn.DataTable && !$.fn.DataTable.isDataTable('#debt-table')) {
        $('#debt-table').DataTable({
            "pageLength": 25,
            "responsive": true,
            "order": [[3, "asc"]], // Сортировка по балансу
            "language": {
                "search": "<?= Yii::t('app', 'search') ?>:",
                "lengthMenu": "<?= Yii::t('app', 'show') ?> _MENU_ <?= Yii::t('app', 'records') ?>",
                "zeroRecords": "<?= Yii::t('app', 'no_records_found') ?>",
                "info": "<?= Yii::t('app', 'showing') ?> _START_ <?= Yii::t('app', 'to') ?> _END_ <?= Yii::t('app', 'of') ?> _TOTAL_ <?= Yii::t('app', 'records') ?>",
                "infoEmpty": "<?= Yii::t('app', 'no_data') ?>",
                "infoFiltered": "(<?= Yii::t('app', 'filtered_from') ?> _MAX_ <?= Yii::t('app', 'total_records') ?>)",
                "paginate": {
                    "first": "<?= Yii::t('app', 'first') ?>",
                    "last": "<?= Yii::t('app', 'last') ?>",
                    "next": "<?= Yii::t('app', 'next') ?>",
                    "previous": "<?= Yii::t('app', 'previous') ?>"
                }
            }
        });
    }

    // Инициализация Select2
    if ($.fn.select2) {
        $('.select2').select2({
            width: '100%'
        });
    }
});
</script> 