<?php

use yii\widgets\ActiveForm;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<div class="material-income-update">
    <?php $form = ActiveForm::begin([
        'id' => 'income-material-update-form',
    ]); ?>

    <?= $form->field($model, 'id')->hiddenInput()->label(false) ?>
    <?= $form->field($model, 'invoice_id')->hiddenInput()->label(false) ?>

    <?= $form->field($model, 'material_id')->dropDownList($materials, [
        'class' => 'form-control select2',
        'prompt' => Yii::t('app', 'select_material')
    ])->label(Yii::t('app', 'material')) ?>

     <?= $form->field($model, 'quantity')->textInput([
        'type' => 'number',
        'step' => '0.01',
        'onkeydown' => 'restrictInput(event)',
        'oninput' => 'validateAmount(this)'
    ]) ?>

    <?= $form->field($model, 'price')->textInput([
        'type' => 'number',
        'step' => '0.01',
        'onkeydown' => 'restrictInput(event)',
        'oninput' => 'validateAmount(this)'
    ]) ?>

    <?= $form->field($model, 'description')->textarea(['rows' => 3]) ?>
    <?php ActiveForm::end(); ?>
</div>

<script>
function restrictInput(event) {
        if (event.key === 'e' || event.key === 'E' || event.key === '-' || event.key === '+') {
            event.preventDefault();
        }
    }

    function validateAmount(input) {
        input.value = input.value.replace(/[^0-9.]/g, ''); 
        if (input.value.startsWith('0')) {
            input.value = ''; 
        }
    }
</script>