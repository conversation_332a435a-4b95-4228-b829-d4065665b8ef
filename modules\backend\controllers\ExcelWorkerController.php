<?php

namespace app\modules\backend\controllers;

use app\modules\backend\models\Worker;
use app\modules\backend\models\WorkerSalary;
use app\modules\backend\models\Position;
use yii\web\UploadedFile;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Yii;

class ExcelWorkerController extends BaseController
{
    public function actionIndex()
    {
        $model = new \yii\base\DynamicModel(['file']);
        $model->addRule(['file'], 'file', ['extensions' => 'xlsx, xls']);

        if (Yii::$app->request->isPost) {
            $model->file = UploadedFile::getInstance($model, 'file');
            if ($model->file && $model->validate()) {
                $filePath = $model->file->tempName;

                try {
                    // Загружаем Excel файл
                    $spreadsheet = IOFactory::load($filePath);
                    $sheet = $spreadsheet->getActiveSheet();
                    $data = $sheet->toArray();
                    $workers = [];
                    $processedCount = 0;                    // Индексы колонок в Excel файле
                    $columnIndexes = [
                        'full_name' => 1,      // Имя работника (индекс 1)  
                        'position' => 2,       // Должность (индекс 2)
                        'salary' => 3,         // Зарплата (индекс 3)
                    ];

                    // Проверяем, что в данных есть хотя бы одна строка
                    if (count($data) < 2) {
                        throw new \Exception('Файл не содержит данных для импорта');
                    }

                    // Получаем все существующие должности
                    $positions = Position::find()->where(['deleted_at' => null])->all();
                    $positionsMap = [];
                    foreach ($positions as $position) {
                        $positionsMap[mb_strtolower(trim($position->name))] = $position->id;
                    }

                    // Обрабатываем данные из файла
                    foreach ($data as $index => $row) {
                        // Пропускаем заголовок
                        if ($index === 0) {
                            continue;
                        }

                        // Получаем данные работника
                        $fullName = trim($row[$columnIndexes['full_name']] ?? '');
                        if (empty($fullName)) {
                            continue; // Пропускаем строки с пустым именем
                        }                        $positionName = trim($row[$columnIndexes['position']] ?? '');
                        $salary = $row[$columnIndexes['salary']] ?? 0;                        // Преобразуем зарплату в число
                        if (is_string($salary)) {
                            // Удаляем пробелы и запятые (разделители тысяч)
                            $salary = str_replace([' ', ','], '', $salary);
                            if (empty($salary) || !is_numeric($salary)) {
                                $salary = 0;
                            } else {
                                $salary = (float)$salary;
                            }
                        }// Получаем номера телефонов - генерируем случайный номер
                        $phoneNumber = $this->generatePhoneNumber();
                        
                        // Ищем должность по схожести
                        $positionId = null;
                        if (!empty($positionName)) {
                            // Ищем похожую должность
                            $positionId = $this->findSimilarPosition($positionName, $positionsMap);
                            
                            // Если не найдена похожая должность, создаем новую
                            if (!$positionId) {
                                $newPosition = new Position();
                                $newPosition->name = $positionName;
                                if ($newPosition->save()) {
                                    $positionId = $newPosition->id;
                                    $positionsMap[mb_strtolower(trim($positionName))] = $positionId;
                                }
                            }
                        }                        // Собираем данные работника
                        $workerData = [
                            'full_name' => $fullName,
                            'position_id' => $positionId,
                            'salary' => $salary,
                            'phone_number' => $phoneNumber,
                        ];

                        $workers[] = $workerData;
                    }

                    // Начинаем транзакцию для сохранения всех изменений
                    $transaction = Yii::$app->db->beginTransaction();

                    try {
                        // Сохраняем работников
                        foreach ($workers as $workerData) {
                            // Ищем работника по имени с учетом схожести
                            $worker = $this->findSimilarWorker($workerData['full_name']);                            // Если работник не найден, создаем нового
                            if (!$worker) {
                                $worker = new Worker();
                                $worker->full_name = $workerData['full_name'];
                                $worker->position_id = $workerData['position_id'];
                                
                                if (!empty($workerData['phone_number'])) {
                                    // Сначала устанавливаем номер БЕЗ префикса для валидации
                                    $worker->phone_number = $workerData['phone_number'];
                                }

                                // Сохраняем нового работника (валидация пройдет с 9 цифрами)
                                if (!$worker->save()) {
                                    throw new \Exception('Ошибка сохранения работника: ' . json_encode($worker->errors));
                                }
                                
                                // ПОСЛЕ успешного сохранения форматируем номер как в WorkerController
                                if (!empty($workerData['phone_number'])) {
                                    $phoneNumber = str_replace(' ', '', $workerData['phone_number']);
                                    if (!str_starts_with($phoneNumber, '+998')) {
                                        $phoneNumber = '+998' . $phoneNumber;
                                    }
                                    $worker->phone_number = $phoneNumber;
                                    $worker->save(false); // Сохраняем без валидации
                                }
                            }                            // Создаем или обновляем зарплату работника
                            if ($workerData['salary'] > 0) {
                                // Проверяем есть ли уже активная зарплата
                                $existingSalary = WorkerSalary::find()
                                    ->where(['worker_id' => $worker->id])
                                    ->andWhere(['end_date' => '9999-12-31'])
                                    ->andWhere(['deleted_at' => null])
                                    ->one();

                                if ($existingSalary) {
                                    // Если есть существующая активная зарплата - обновляем её
                                    $existingSalary->amount = $workerData['salary'];
                                    $existingSalary->start_date = '2025-06-01';
                                    if (!$existingSalary->save()) {
                                        throw new \Exception('Ошибка обновления зарплаты работника: ' . json_encode($existingSalary->errors));
                                    }
                                } else {
                                    // Создаем новую запись зарплаты только если её нет
                                    $workerSalary = new WorkerSalary();
                                    $workerSalary->worker_id = $worker->id;
                                    $workerSalary->amount = $workerData['salary'];
                                    $workerSalary->start_date = '2025-06-01'; // Дата начала зарплаты 01.06.2025
                                    $workerSalary->end_date = '9999-12-31';

                                    if (!$workerSalary->save()) {
                                        throw new \Exception('Ошибка сохранения зарплаты работника: ' . json_encode($workerSalary->errors));
                                    }
                                }
                            }

                            $processedCount++;
                        }

                        // Если все прошло успешно, фиксируем транзакцию
                        $transaction->commit();
                        Yii::$app->session->setFlash('success', 'Работники успешно импортированы: ' . $processedCount);
                    } catch (\Exception $e) {
                        // В случае ошибки откатываем транзакцию
                        $transaction->rollBack();
                        Yii::$app->session->setFlash('error', 'Ошибка при импорте работников: ' . $e->getMessage());
                    }
                } catch (\Exception $e) {
                    Yii::$app->session->setFlash('error', 'Ошибка обработки файла: ' . $e->getMessage());
                }

                return $this->refresh();
            }
        }

        return $this->render('index', ['model' => $model]);
    }

    /**
     * Поиск работника по похожему имени (fuzzy match)
     *
     * @param string $fullName Имя работника для поиска
     * @return Worker|null Найденный работник или null
     */
    private function findSimilarWorker($fullName)
    {
        $fullName = mb_strtolower(trim($fullName));
        $workers = Worker::find()
            ->select(['id', 'full_name'])
            ->where(['deleted_at' => null])
            ->all();
            
        foreach ($workers as $worker) {
            $dbName = mb_strtolower(trim($worker->full_name));
            similar_text($fullName, $dbName, $percent);
            if ($percent > 90) { // Порог схожести
                return $worker;
            }
        }
        return null;
    }    /**
     * Генерация случайного номера телефона в формате 9 цифр (без префикса +998)
     *
     * @return string Сгенерированный номер телефона
     */
    private function generatePhoneNumber()
    {
        // Узбекские операторы
        $operators = ['90', '91', '93', '94', '88', '95', '97', '98', '99', '33', '20', '77', '50'];
        $operator = $operators[array_rand($operators)];
        
        // Генерируем 7 случайных цифр
        $randomDigits = '';
        for ($i = 0; $i < 7; $i++) {
            $randomDigits .= rand(0, 9);
        }
        
        return $operator . $randomDigits; // Возвращаем 9 цифр без префикса +998
    }

    /**
     * Поиск похожей должности с учетом схожести названий
     *
     * @param string $positionName Название должности для поиска
     * @param array $positionsMap Массив существующих должностей
     * @return int|null ID найденной должности или null
     */
    private function findSimilarPosition($positionName, $positionsMap)
    {
        $positionName = mb_strtolower(trim($positionName));
        $bestMatch = null;
        $bestPercent = 0;
        
        foreach ($positionsMap as $existingName => $positionId) {
            similar_text($positionName, $existingName, $percent);
            if ($percent > 85 && $percent > $bestPercent) { // Порог схожести 85%
                $bestMatch = $positionId;
                $bestPercent = $percent;
            }
        }
        
        return $bestMatch;
    }
}
