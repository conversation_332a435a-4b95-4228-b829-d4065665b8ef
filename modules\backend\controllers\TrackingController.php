<?php

namespace app\modules\backend\controllers;

use app\common\models\Invoice;
use app\common\models\InvoiceDetail;
use app\common\models\MaterialCurrency;
use app\common\models\MaterialStorage;
use app\common\models\MaterialStorageHistory;
use app\common\models\Product;
use app\common\models\ProductDefect;
use Yii;
use yii\web\Response;
use app\common\models\Tracking;
use app\common\models\Sales;

class TrackingController extends BaseController
{
    public function actionIndex()
    {
        $result = [
            'expenses' => $this->getExpensesTracking(),
            'client_payments' => $this->getClientPaymentsTracking(),
            'supplier_payments' => $this->getSupplierPaymentsTracking(),
            'worker_payments' => $this->getWorkerPaymentsTracking(),
            'security_records' => $this->getSecurityRecords(),
            'sales' => $this->getSales(),
            'material_returns' => $this->getMaterialReturnsTracking(),
            'product_release' => $this->getProductReleaseTracking(),
            'product_repackaging' => $this->getProductRepackagingTracking()
        ];


        $invoices = Invoice::find()
        ->with('invoiceDetails.material')
        ->where(['OR',
            ['accept_user_id' => null],
            ['>=', 'accepted_at', date('Y-m-d')]
        ])
        ->andWhere(['deleted_at' => null])
        ->all();


        return $this->render('index', [
            'result' => $result,
            'invoices' => $invoices
        ]);
    }

    private function getExpensesTracking()
    {
        $sql = "
            SELECT
                tracking.id AS tracking_id,
                expenses.summa AS amount,
                et.name AS related_name,
                users.full_name AS added_by,
                tracking.created_at,
                tracking.status
            FROM
                tracking
            LEFT JOIN
                expenses ON tracking.process_id = expenses.id
            LEFT JOIN
                expenses_type et ON expenses.expense_type_id = et.id
            LEFT JOIN
                users ON expenses.add_user_id = users.id
            WHERE
                tracking.progress_type = :progress_type
                AND (
                    tracking.status = :status_not_accepted
                    OR
                    (tracking.status = :status_accepted AND DATE(tracking.accepted_at) = CURRENT_DATE)
                )
                AND tracking.deleted_at IS NULL
            ORDER BY
                tracking.created_at DESC
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':progress_type', Tracking::TYPE_EXPENSES);
        $command->bindValue(':status_not_accepted', Tracking::STATUS_NOT_ACCEPTED);
        $command->bindValue(':status_accepted', Tracking::STATUS_ACCEPTED);

        return $command->queryAll();
    }

    private function getClientPaymentsTracking()
    {
        $sql = "
            SELECT
                tracking.id AS tracking_id,
                cp.summa AS amount,
                client.full_name AS related_name,
                users.full_name AS added_by,
                tracking.created_at,
                tracking.status
            FROM
                tracking
            LEFT JOIN
                client_payments cp ON tracking.process_id = cp.id
            LEFT JOIN
                client ON cp.client_id = client.id
            LEFT JOIN
                users ON cp.add_user_id = users.id
            WHERE
                tracking.progress_type = :progress_type
                AND (
                    tracking.status = :status_not_accepted
                    OR
                    (tracking.status = :status_accepted AND DATE(tracking.accepted_at) = CURRENT_DATE)
                )
                AND tracking.deleted_at IS NULL
            ORDER BY
                tracking.created_at DESC
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':progress_type', Tracking::PAY_FOR_CLIENT);
        $command->bindValue(':status_not_accepted', Tracking::STATUS_NOT_ACCEPTED);
        $command->bindValue(':status_accepted', Tracking::STATUS_ACCEPTED);

        return $command->queryAll();
    }

    private function getSupplierPaymentsTracking()
    {
        $sql = "
            SELECT
                tracking.id AS tracking_id,
                sp.amount AS amount,
                supplier.full_name AS related_name,
                users.full_name AS added_by,
                tracking.created_at,
                tracking.status
            FROM
                tracking
            LEFT JOIN
                supplier_payments sp ON tracking.process_id = sp.id
            LEFT JOIN
                supplier ON sp.supplier_id = supplier.id
            LEFT JOIN
                users ON sp.add_user_id = users.id
            WHERE
                tracking.progress_type = :progress_type
                AND (
                    tracking.status = :status_not_accepted
                    OR
                    (tracking.status = :status_accepted AND DATE(tracking.accepted_at) = CURRENT_DATE)
                )
                AND tracking.deleted_at IS NULL
            ORDER BY
                tracking.created_at DESC
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':progress_type', Tracking::PAY_FOR_SUPPLIER);
        $command->bindValue(':status_not_accepted', Tracking::STATUS_NOT_ACCEPTED);
        $command->bindValue(':status_accepted', Tracking::STATUS_ACCEPTED);

        return $command->queryAll();
    }

    private function getWorkerPaymentsTracking()
    {
        $sql = "
            SELECT
                tracking.id AS tracking_id,
                wf.amount AS amount,
                worker.full_name AS related_name,
                u.full_name AS added_by,
                tracking.created_at,
                tracking.status
            FROM
                tracking
            LEFT JOIN
                worker_finances wf ON tracking.process_id = wf.id
            LEFT JOIN
                worker ON wf.worker_id = worker.id
            LEFT JOIN
                expenses e ON e.worker_finance_id = wf.id
            LEFT JOIN
                users u ON e.add_user_id = u.id
            WHERE
                tracking.progress_type = :progress_type
                AND (
                    tracking.status = :status_not_accepted
                    OR
                    (tracking.status = :status_accepted AND DATE(tracking.accepted_at) = CURRENT_DATE)
                )
                AND tracking.deleted_at IS NULL
            ORDER BY
                tracking.created_at DESC
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':progress_type', Tracking::PAY_FOR_WORKER);
        $command->bindValue(':status_not_accepted', Tracking::STATUS_NOT_ACCEPTED);
        $command->bindValue(':status_accepted', Tracking::STATUS_ACCEPTED);

        return $command->queryAll();
    }


    private function getSecurityRecords(){
        $sql = "
            SELECT
                tracking.id AS tracking_id,
                security_records.car_number,
                security_records.driver_full_name,
                users.full_name AS added_by,
                users2.full_name AS accepted_by,
                security_records.created_at,
                security_records.accepted_at,
                rg.name AS region_name,
                tracking.status
            FROM
                tracking
            LEFT JOIN
                security_records ON tracking.process_id = security_records.id
            LEFT JOIN
                users ON security_records.add_user_id = users.id
            LEFT JOIN
                users as users2 ON security_records.accepted_user_id = users2.id
            left join
                 region as rg on rg.id = security_records.region_id
            WHERE
                tracking.progress_type = :progress_type
                AND (
                    tracking.status = :status_not_accepted
                    OR
                    (tracking.status = :status_accepted AND DATE(tracking.accepted_at) = CURRENT_DATE)
                )
                AND tracking.deleted_at IS NULL
            ORDER BY
                tracking.created_at DESC
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':progress_type', Tracking::TYPE_CLIENT_INCOME);
        $command->bindValue(':status_not_accepted', Tracking::STATUS_NOT_ACCEPTED);
        $command->bindValue(':status_accepted', Tracking::STATUS_ACCEPTED);

        return $command->queryAll();
    }

    private function getSales()
    {
        $sql = "
            SELECT
                tracking.id AS tracking_id,
                sales.id as sale_id,
                client.full_name AS client_name,
                sales.car_number,
                sales.driver as driver_full_name,
                u1.full_name AS added_by,
                u2.full_name AS accepted_by,
                sales.created_at,
                sales.completed_at as accepted_at,
                tracking.status,
                sales.total_sum as amount,
                CASE
                    WHEN EXISTS (
                        SELECT 1
                        FROM security_records sr
                        WHERE lower(sr.car_number) = lower(sales.car_number)
                        AND sr.deleted_at IS NULL
                    ) THEN true
                    ELSE false
                END as has_security_record

            FROM
                tracking
            LEFT JOIN
                sales ON tracking.process_id = sales.id
            LEFT JOIN
                client ON sales.client_id = client.id
            LEFT JOIN
                users u1 ON sales.sell_user_id = u1.id
            LEFT JOIN
                users u2 ON sales.confirm_user_id = u2.id
            WHERE
                tracking.progress_type = :progress_type
                 AND (
                    tracking.status = :status_not_accepted
                    OR
                    (tracking.status = :status_accepted AND tracking.accepted_at::date = CURRENT_DATE)
                )
                AND sales.status = :sales_status
                AND sales.confirm_user_id IS NOT NULL
                AND tracking.deleted_at IS NULL
            ORDER BY
                tracking.created_at DESC
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':progress_type', Tracking::TYPE_NEW_SALES);
        $command->bindValue(':status_not_accepted', Tracking::STATUS_NOT_ACCEPTED);
        $command->bindValue(':status_accepted', Tracking::STATUS_ACCEPTED);
        $command->bindValue(':sales_status', Sales::STATUS_CONFIRMED);

        $sales = $command->queryAll();


        // Получаем детали продаж для каждой записи
        foreach ($sales as &$sale) {
            $detailsSql = "
                SELECT
                    p.name as product_name,
                    sd.quantity,
                    sd.factory_price,
                    sd.total_price
                FROM
                    sales_detail sd
                LEFT JOIN
                    product p ON sd.product_id = p.id
                WHERE
                    sd.sale_id = :sale_id
                    AND sd.deleted_at IS NULL
            ";

            $detailsCommand = Yii::$app->db->createCommand($detailsSql);
            $detailsCommand->bindValue(':sale_id', $sale['sale_id']);

            $details = $detailsCommand->queryAll();
            $sale['products'] = $details;
            $sale['rowspan'] = count($details);
        }

        return $sales;
    }

    private function getMaterialReturnsTracking()
    {
        $sql = "
            SELECT
                tracking.id AS tracking_id,
                msg.id AS group_id,
                u1.full_name AS added_by,
                u2.full_name AS accepted_by,
                msg.created_at,
                msg.accepted_at,
                tracking.status
            FROM
                tracking
            LEFT JOIN
                material_status_group msg ON tracking.process_id = msg.id
            LEFT JOIN
                users u1 ON msg.add_user_id = u1.id
            LEFT JOIN
                users u2 ON msg.accepted_user_id = u2.id
            WHERE
                tracking.progress_type = :progress_type
                AND (
                    tracking.status = :status_not_accepted
                    OR
                    (tracking.status = :status_accepted AND DATE(tracking.accepted_at) = CURRENT_DATE)
                )
                AND tracking.deleted_at IS NULL
                AND msg.status = :return_status
                AND msg.deleted_at IS NULL
            ORDER BY
                tracking.created_at DESC
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':progress_type', Tracking::TYPE_MATERIAL_RETURN);
        $command->bindValue(':status_not_accepted', Tracking::STATUS_NOT_ACCEPTED);
        $command->bindValue(':status_accepted', Tracking::STATUS_ACCEPTED);
        $command->bindValue(':return_status', \app\common\models\MaterialStatusGroup::STATUS_RETURNED_TO_SUPPLIER);

        $returns = $command->queryAll();

        foreach ($returns as &$return) {
            $detailsSql = "
                SELECT
                    m.name as material_name,
                    ms.quantity,
                    COALESCE(id.price, 0) as unit_price,
                    COALESCE(id.price * ms.quantity, 0) as total_price
                FROM
                    material_status ms
                LEFT JOIN
                    material m ON ms.material_id = m.id
                LEFT JOIN
                    (SELECT material_id, price FROM invoice_detail
                     WHERE deleted_at IS NULL
                     AND id IN (SELECT MAX(id) FROM invoice_detail WHERE deleted_at IS NULL GROUP BY material_id)) id
                    ON id.material_id = ms.material_id
                WHERE
                    ms.status_group_id = :group_id
                    AND ms.deleted_at IS NULL
            ";

            $detailsCommand = Yii::$app->db->createCommand($detailsSql);
            $detailsCommand->bindValue(':group_id', $return['group_id']);

            $details = $detailsCommand->queryAll();
            $return['materials'] = $details;
            $return['rowspan'] = count($details);
            $return['total_sum'] = array_sum(array_column($details, 'total_price'));
        }

        return $returns;
    }

    private function getProductReleaseTracking()
    {
        $sql = "SELECT
                tracking.id AS tracking_id,
                product_storage.id AS product_storage_id,
                p.name AS product_name,
                product_storage.quantity,
                product_storage.enter_date,
                product_storage.accepted_at,
                tracking.status
            FROM
                tracking
            LEFT JOIN
                product_storage ON tracking.process_id = product_storage.id
            LEFT JOIN
                product as p ON product_storage.product_id = p.id
            WHERE
                tracking.progress_type = :progress_type
                AND tracking.deleted_at IS NULL
            ORDER BY
                tracking.created_at DESC
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':progress_type', Tracking::TYPE_PRODUCT_RELEASE);

        return $command->queryAll();
    }

    /**
     * Получение списка записей переупаковки продуктов для отслеживания
     *
     * @return array
     */
    private function getProductRepackagingTracking()
    {
        $sql = "SELECT
                tracking.id AS tracking_id,
                pd.id AS product_defect_id,
                p.name AS product_name,
                pd.quantity,
                pd.repackaging_reason,
                pd.created_at,
                u.username AS added_by,
                tracking.status
            FROM
                tracking
            LEFT JOIN
                product_defect pd ON tracking.process_id = pd.id
            LEFT JOIN
                product p ON pd.product_id = p.id
            LEFT JOIN
                users u ON pd.add_user_id = u.id
            WHERE
                tracking.progress_type = :progress_type
                AND tracking.deleted_at IS NULL
                AND pd.is_repackaging = true
            ORDER BY
                tracking.created_at DESC
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':progress_type', Tracking::TYPE_PRODUCT_REPACKAGING);

        return $command->queryAll();
    }


    public function actionConfirm()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $id = Yii::$app->request->post('id');
        $tracking = Tracking::findOne($id);

        if (!$tracking) {
            return [
                'success' => false,
                'message' => 'Запись не найдена'
            ];
        }

        $tracking->status = Tracking::STATUS_ACCEPTED;
        $tracking->accepted_at = date('Y-m-d H:i:s');

        if ($tracking->save()) {
            return [
                'success' => true,
                'message' => 'Операция успешно подтверждена'
            ];
        }

        return [
            'success' => false,
            'message' => 'Ошибка при сохранении: ' . implode(', ', $tracking->getErrorSummary(true))
        ];
    }




    public function actionSetPrice()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $post = Yii::$app->request->post();
            $transaction = Yii::$app->db->beginTransaction();

            try {
                $invoice = Invoice::findOne($post['invoice_id']);
                if (!$invoice) {
                    throw new \Exception('Накладная не найдена');
                }

                $supplier = $invoice->supplier;
                if (!$supplier || !$supplier->currency_id) {
                    throw new \Exception('Поставщик или валюта поставщика не найдены');
                }

                foreach ($post['prices'] as $materialId => $price) {
                    $detail = InvoiceDetail::find()
                        ->where(['material_id' => $materialId])
                        ->andWhere(['invoice_id' => $post['invoice_id']])
                        ->one();

                        $currency_id = $detail->invoice->supplier->currency_id;
                    if ($detail) {
                        $detail->price = (float)$price;
                        $detail->currency_id = $currency_id;
                        if (!$detail->save()) {
                            throw new \Exception('Ошибка сохранения цены для материала ID: ' . $materialId);
                        }

                        $materialCurrency = MaterialCurrency::find()
                            ->where(['material_id' => $materialId])
                            ->andWhere(['currency_id' => $supplier->currency_id])
                            ->andWhere(['deleted_at' => null])
                            ->one();

                        if (!$materialCurrency) {
                            $materialCurrency = new MaterialCurrency();
                            $materialCurrency->material_id = $materialId;
                            $materialCurrency->currency_id = $supplier->currency_id;
                            $materialCurrency->created_at = date('Y-m-d H:i:s');
                        }

                        $materialCurrency->price = (float)$price;

                        if (!$materialCurrency->save()) {
                            throw new \Exception('Ошибка сохранения валютной цены для материала ID: ' . $materialId);
                        }
                    }
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'prices_saved_successfully')
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }
        else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $invoice = Invoice::find()
                ->with(['invoiceDetails.material', 'supplier'])
                ->where(['id' => $id])
                ->one();

            if (!$invoice) {
                return [
                    'status' => 'error',
                    'message' => 'Запись не найдена'
                ];
            }

            return [
                'status' => 'success',
                'title' => Yii::t('app', 'set_price'),
                'content' => $this->renderPartial('_price', ['invoice' => $invoice])
            ];
        }
    }


    public function actionAcceptIncome()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $id = Yii::$app->request->post('id');
        $invoice = Invoice::findOne($id);
        if (!$invoice) {
            return ['status' => 'error', 'message' => 'Invoice not found'];
        }

        foreach ($invoice->invoiceDetails as $detail) {
            if ($detail->price === null || $detail->price <= 0) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'price_not_set', [
                        'material' => $detail->material->name ?? 'Unknown'
                    ])
                ];
            }
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $invoice->accepted_at = date('Y-m-d H:i:s');
            $invoice->accept_user_id = Yii::$app->user->id;
            if (!$invoice->save()) {
                throw new \Exception('Ошибка сохранения инвойса');
            }

            $tracking = Tracking::findOne(['process_id' => $invoice->id]);
            if ($tracking) {
                $tracking->status = Tracking::STATUS_ACCEPTED;
                $tracking->accepted_at = date('Y-m-d H:i:s');
                if (!$tracking->save()) {
                    throw new \Exception('Ошибка сохранения трекинга');
                }
            }

            // Получаем валюту поставщика
            $supplier = $invoice->supplier;
            if (!$supplier || !$supplier->currency_id) {
                throw new \Exception('Поставщик или валюта поставщика не найдены');
            }

            foreach ($invoice->invoiceDetails as $detail) {


                $materialCurrency = MaterialCurrency::find()
                    ->where(['material_id' => $detail->material_id])
                    ->andWhere(['currency_id' => $supplier->currency_id])
                    ->andWhere(['deleted_at' => null])
                    ->one();

                if (!$materialCurrency) {
                    $materialCurrency = new MaterialCurrency();
                    $materialCurrency->material_id = $detail->material_id;
                    $materialCurrency->currency_id = $supplier->currency_id;
                    $materialCurrency->created_at = date('Y-m-d H:i:s');
                }

                $materialCurrency->price = $detail->price;

                if (!$materialCurrency->save()) {
                    throw new \Exception('Ошибка сохранения валютной цены для материала ID: ' . $detail->material_id);
                }
            }

            $transaction->commit();
            return [
                'status' => 'success',
                'message' => Yii::t('app', 'invoice_accepted_success')
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }


}
