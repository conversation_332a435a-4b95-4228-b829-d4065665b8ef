<?php
use yii\helpers\Html;
use yii\helpers\ArrayHelper;

/* @var $reportData array */
/* @var $startDate string */
/* @var $endDate string */
/* @var $productId int|null */
/* @var $products array */

$this->title = Yii::t('app', 'free_products_report');
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="row align-items-center mb-3">
    <div class="col-md-6">
        <h3 class="mb-0"><?= Html::encode($this->title) ?></h3>
    </div>
    <div class="col-md-6 d-flex justify-content-end">
        <?= Html::beginForm(['free-products'], 'get', ['class' => 'd-inline-flex align-items-center']) ?>
            <?= Html::input('date', 'start_date', $startDate, ['class' => 'form-control mr-2', 'style' => 'width: 160px;', 'placeholder' => 'С']) ?>
            <?= Html::input('date', 'end_date', $endDate, ['class' => 'form-control mr-2', 'style' => 'width: 160px;', 'placeholder' => 'По']) ?>
            <?= Html::dropDownList('product_id', $productId, $products, ['prompt' => 'Все товары', 'class' => 'form-control mr-2']) ?>
            <?= Html::submitButton('Фильтровать', ['class' => 'btn btn-primary']) ?>
        <?= Html::endForm() ?>
    </div>
</div>

<?php if (empty($reportData['items'])): ?>
    <div class="alert alert-info text-center mt-3">
        <i class="fas fa-info-circle mr-2"></i> <?= Yii::t('app', 'no_data_found') ?>.
    </div>
<?php else: ?>
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered table-striped mb-0" style="min-width:100%;table-layout:fixed;">
                    <thead class="thead-light text-center">
                        <tr>
                            <th><?= Yii::t('app', 'client') ?></th>
                            <th><?= Yii::t('app', 'car_number') ?></th>
                            <th><?= Yii::t('app', 'product') ?></th>
                            <th><?= Yii::t('app', 'blocks') ?></th>
                            <th><?= Yii::t('app', 'quantity') ?></th>
                            <th><?= Yii::t('app', 'created_at') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($reportData['items'] as $item): ?>
                            <tr>
                                <td><?= Html::encode($item['client'] ?? '-') ?></td>
                                <td><?= Html::encode($item['car_number'] ?? '-') ?></td>
                                <td><?= Html::encode($item['product_name'] ?? '-') ?></td>
                                <td class="text-right"><?= number_format((int)($item['total_blocks'] ?? 0), 0, '.', ' ') ?></td>
                                <td class="text-right"><?= number_format((int)($item['total_quantity'] ?? 0), 0, '.', ' ') ?></td>
                                <td class="text-center"><?= Yii::$app->formatter->asDatetime($item['created_at'], 'php:d.m.Y H:i') ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="bg-light font-weight-bold">
                        <tr>
                            <td colspan="3" class="text-right"><?= Yii::t('app', 'total') ?>:</td>
                            <td class="text-right"><?= number_format((int)($reportData['summary']['totalBlocks'] ?? 0), 0, '.', ' ') ?></td>
                            <td class="text-right"><?= number_format((int)($reportData['summary']['totalQuantity'] ?? 0), 0, '.', ' ') ?></td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?> 