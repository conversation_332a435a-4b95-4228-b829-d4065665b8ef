<?php
use app\common\models\Cashbox;
use yii\helpers\Html;
?>

<div class="supplier-view">
    <div class="card-body">
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'Supplier Information') ?></h5>

        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <th><?= Yii::t('app', 'suplier_name') ?></th>
                    <th><?= Yii::t('app', 'phone_number') ?></th>
                    <?php if ($model['phone_number_2']): ?>
                        <th><?= Yii::t('app', 'Additional Phone') ?></th>
                    <?php endif; ?>
                    <th><?= Yii::t('app', 'address') ?></th>
                    <th><?= Yii::t('app', 'account_number') ?></th>
                    <th><?= Yii::t('app', 'currency') ?></th>
                    <th><?= Yii::t('app', 'balance') ?></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><?= Html::encode($model['full_name']) ?></td>
                    <td><?= Html::encode($model['phone_number']) ?></td>
                    <?php if ($model['phone_number_2']): ?>
                        <td><?= Html::encode($model['phone_number_2']) ?></td>
                    <?php endif; ?>
                    <td><?= Html::encode($model['address']) ?></td>
                    <td><?= Html::encode($model['account_number']) ?></td>
                    <td><?= Html::encode($model['currency_name']) ?></td>
                    <td class="total-value"><?= number_format($model['balance'], 0, '.', ' ') ?></td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- История накладных -->
    <?php if (!empty($invoices)): ?>
    <div class="card-body">
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'Invoice History') ?></h5>

        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'created_at') ?></th>
                        <th><?= Yii::t('app', 'Invoice Number') ?></th>
                        <th><?= Yii::t('app', 'Total Amount') ?></th>
                        <th><?= Yii::t('app', 'Description') ?></th>
                        <th><?= Yii::t('app', 'Accepted By') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($invoices as $invoice): ?>
                        <tr>
                            <td><?= date("d.m.Y",strtotime($invoice['created_at'])) ?></td>
                            <td><?= Html::encode($invoice['invoice_number']) ?></td>
                            <td><?= number_format($invoice['total_amount'], 0, '.', ' ') ?></td>
                            <td><?= Html::encode($invoice['description']) ?></td>
                            <td><?= Html::encode($invoice['accepted_by']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php endif; ?>

    <!-- История платежей -->
    <?php if (!empty($payments)): ?>
    <div class="card-body">
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'Payment History') ?></h5>

        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'payment_date') ?></th>
                        <th><?= Yii::t('app', 'accepted_by') ?></th>
                        <th><?= Yii::t('app', 'currency') ?></th>
                        <th><?= Yii::t('app', 'amount') ?></th>
                        <th><?= Yii::t('app', 'payment_type') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($payments as $payment): ?>
                        <tr>
                            <td><?= date("d.m.Y",strtotime($payment['created_at'])) ?></td>
                            <td><?= Html::encode($payment['user_name']) ?></td>
                            <td><?= Html::encode($payment['currency_name']) ?></td>
                            <td><?= number_format($payment['amount'], 0, '.', ' ') ?></td>
                            <td><?= Html::encode(Cashbox::getTypeDescription($payment['type'])) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php endif; ?>

    <!-- История баланса -->
    <?php if (!empty($balanceHistory)): ?>
    <div class="card-body">
        <h5 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'Balance History') ?></h5>

        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'Date') ?></th>
                        <th><?= Yii::t('app', 'old_amount') ?></th>
                        <th><?= Yii::t('app', 'currency') ?></th>
                        <th><?= Yii::t('app', 'amount') ?></th>
                        <th><?= Yii::t('app', 'payment_type') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($balanceHistory as $history): ?>
                        <tr>
                            <td><?= date("d.m.Y",strtotime($history['created_at'])) ?></td>
                            <td><?= number_format($history['old_amount'], 0, '.', ' ') ?></td>
                            <td><?= Html::encode($history['currency_name']) ?></td>
                            <td><?= number_format($history['amount'], 0, '.', ' ') ?></td>
                            <td><?= Html::encode(Cashbox::getTypeDescription($history['type'])) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php endif; ?>
</div>
