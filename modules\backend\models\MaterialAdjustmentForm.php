<?php

namespace app\modules\backend\models;

use app\common\models\Material;
use Yii;
use yii\base\Model;

/**
 * Форма для корректировки остатков материалов
 */
class MaterialAdjustmentForm extends Model
{
    /**
     * @var int ID материала
     */
    public $material_id;
    
    /**
     * @var float Количество материала
     */
    public $quantity;
    


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['material_id', 'quantity'], 'required'],
            [['material_id'], 'integer'],
            [['quantity'], 'number', 'min' => 0], // Новое общее количество не может быть отрицательным

            [['material_id'], 'exist', 'skipOnError' => true, 'targetClass' => Material::class, 'targetAttribute' => ['material_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'material_id' => 'Материал',
            'quantity' => 'Количество',

        ];
    }
}
