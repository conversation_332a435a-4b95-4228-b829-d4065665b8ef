<?php

namespace app\modules\api\controllers;

use app\common\models\ApiResponse;
use app\common\models\Notification;
use app\common\models\NotificationReadStatus;
use Yii;
use yii\web\ForbiddenHttpException;

/**
 * Контроллер для работы с уведомлениями
 */
class NotificationController extends BaseController
{
    /**
     * {@inheritdoc}
     */
    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        try {
            // Разрешаем доступ пользователям с ролями user, product_keeper и sales
            if (!Yii::$app->user->can('user') && !Yii::$app->user->can('product_keeper') && !Yii::$app->user->can('sales')) {
                throw new ForbiddenHttpException(Yii::t('app', "You don\'t have permission to access this resource"));
            }
        } catch (ForbiddenHttpException $e) {
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            Yii::$app->response->data = ApiResponse::response(
                $e->getMessage(),
                null,
                403
            );
            Yii::$app->response->statusCode = 403;
            return false;
        }

        return true;
    }

    /**
     * Получение списка уведомлений
     *
     * @return array
     */
    public function actionIndex()
    {
        $user_id = Yii::$app->user->id;
        $is_read = Yii::$app->request->get('is_read', null);

        $notifications = Notification::getAllForUser($user_id, $is_read);

        $result = [];
        foreach ($notifications as $item) {
            $notification = $item['notification'];
            $result[] = [
                'id' => $notification->id,
                'title' => $notification->title,
                'body' => $notification->body,
                'data' => $notification->data ? json_decode($notification->data, true) : null,
                'is_read' => (bool)$item['is_read'],
                'created_at' => $notification->created_at,
                'read_at' => $item['read_at']
            ];
        }

        return ApiResponse::response('Список уведомлений', [
            'notifications' => $result,
            'unread_count' => $this->getUnreadCount()
        ]);
    }

    /**
     * Маркировка уведомления как прочитанного
     *
     * @param int $id ID уведомления
     * @return array
     */
    public function actionMarkAsRead($id)
    {
        $user_id = Yii::$app->user->id;
        $notification = Notification::findOne($id);

        if (!$notification) {
            return ApiResponse::response('Уведомление не найдено', null, 404);
        }

        // Проверяем, что уведомление доступно пользователю
        $status = NotificationReadStatus::findOne([
            'notification_id' => $notification->id,
            'user_id' => $user_id
        ]);

        if (!$status) {
            return ApiResponse::response('Доступ запрещен', null, 403);
        }

        if ($notification->markAsReadForUser($user_id)) {
            return ApiResponse::response('Уведомление отмечено как прочитанное', [
                'unread_count' => $this->getUnreadCount()
            ]);
        }

        return ApiResponse::response('Ошибка при обновлении уведомления', null, 500);
    }

    /**
     * Маркировка всех уведомлений как прочитанных
     *
     * @return array
     */
    public function actionMarkAllAsRead()
    {
        $user_id = Yii::$app->user->id;
        $count = Notification::markAllAsReadForUser($user_id);

        return ApiResponse::response('Все уведомления отмечены как прочитанные', [
            'updated_count' => $count,
            'unread_count' => 0
        ]);
    }

    /**
     * Получение количества непрочитанных уведомлений
     *
     * @return array
     */
    public function actionUnreadCount()
    {
        return ApiResponse::response('Количество непрочитанных уведомлений', [
            'unread_count' => $this->getUnreadCount()
        ]);
    }

    /**
     * Получение ролей текущего пользователя
     *
     * @return array
     */
    private function getUserRoles()
    {
        $userRoles = Yii::$app->authManager->getRolesByUser(Yii::$app->user->id);
        return array_keys($userRoles);
    }

    /**
     * Получение количества непрочитанных уведомлений
     *
     * @return int
     */
    private function getUnreadCount()
    {
        $user_id = Yii::$app->user->id;

        return NotificationReadStatus::find()
            ->where(['user_id' => $user_id, 'is_read' => false])
            ->count();
    }
}
