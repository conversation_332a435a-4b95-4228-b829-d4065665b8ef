<?php

use yii\db\Migration;

class m250402_000001_update_quantity_fields_to_decimal extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Изменяем тип поля quantity в таблице material_status
        $this->alterColumn('material_status', 'quantity', $this->decimal(10, 3)->notNull());
        
        // Изменяем тип поля quantity в таблице material_storage
        $this->alterColumn('material_storage', 'quantity', $this->decimal(10, 3)->notNull());
        
        // Изменяем тип поля quantity в таблице material_storage_history
        $this->alterColumn('material_storage_history', 'quantity', $this->decimal(10, 3)->notNull());
        
        // Изменяем тип поля quantity в таблице material_defect
        $this->alterColumn('material_defect', 'quantity', $this->decimal(10, 3)->notNull());
        
        // Изменяем тип поля quantity в таблице material_production
        $this->alterColumn('material_production', 'quantity', $this->decimal(10, 3)->notNull());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Возвращаем тип поля quantity в таблице material_status
        $this->alterColumn('material_status', 'quantity', $this->integer()->notNull());
        
        // Возвращаем тип поля quantity в таблице material_storage
        $this->alterColumn('material_storage', 'quantity', $this->integer()->notNull());
        
        // Возвращаем тип поля quantity в таблице material_storage_history
        $this->alterColumn('material_storage_history', 'quantity', $this->integer()->notNull());
        
        // Возвращаем тип поля quantity в таблице material_defect
        $this->alterColumn('material_defect', 'quantity', $this->integer()->notNull());
        
        // Возвращаем тип поля quantity в таблице material_production
        $this->alterColumn('material_production', 'quantity', $this->integer()->notNull());
    }
}
