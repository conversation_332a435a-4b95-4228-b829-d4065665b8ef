{
    "php.version": "8.1",
    "zencoder.enableRepoIndexing": true,
    "github.copilot.advanced": {

    },

    "mcpServers": {
        "supabase": {
            "command": "npx",
            "env": {},
            "args": [
                "-y",
                "@modelcontextprotocol/server-postgres",
                "postgresql://postgres:postgres@127.0.0.1:5432/silver_test_1"
            ]
        }
    },
}