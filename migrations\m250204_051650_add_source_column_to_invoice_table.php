<?php

use yii\db\Migration;

/**
 * <PERSON>les adding columns to table `{{%invoice}}`.
 */
class m250204_051650_add_source_column_to_invoice_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('invoice', 'source', $this->integer()->null());

        $this->createTable('client_special_prices', [
            'id' => $this->bigPrimaryKey(),
            'client_id' => $this->bigInteger()->notNull(), 
            'product_id' => $this->bigInteger()->notNull(), 
            'special_price' => $this->decimal(10, 2)->null(), 
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey('fk-client_special_prices-client_id', 'client_special_prices', 'client_id', 'client', 'id');
        $this->addForeignKey('fk-client_special_prices-product_id', 'client_special_prices', 'product_id', 'product', 'id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('invoice', 'source');

        $this->dropForeignKey('fk-client_special_prices-client_id', 'client_special_prices');
        $this->dropForeignKey('fk-client_special_prices-product_id', 'client_special_prices');
        $this->dropTable('client_special_prices');
    }
}
