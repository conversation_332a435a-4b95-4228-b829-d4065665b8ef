<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;

/**
 * Модель для работы с токенами устройств пользователей
 *
 * @property int $id ID записи
 * @property int $user_id ID пользователя
 * @property string $device_token Токен устройства для Firebase
 * @property string $device_type Тип устройства (android, ios, web)
 * @property string|null $device_name Название устройства
 * @property bool $is_active Активен ли токен
 * @property string|null $last_used_at Дата последнего использования
 * @property string $created_at Дата создания
 * @property string|null $updated_at Дата обновления
 *
 * @property-read \app\modules\backend\models\Users $user
 */
class UserDeviceToken extends ActiveRecord
{
    const TYPE_ANDROID = 'android';
    const TYPE_IOS = 'ios';
    const TYPE_WEB = 'web';
    
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%user_device_tokens}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new Expression('NOW()'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'device_token', 'device_type'], 'required'],
            [['user_id'], 'integer'],
            [['is_active'], 'boolean'],
            [['last_used_at', 'created_at', 'updated_at'], 'safe'],
            [['device_token'], 'string', 'max' => 255],
            [['device_type'], 'string', 'max' => 20],
            [['device_name'], 'string', 'max' => 100],
            [['device_type'], 'in', 'range' => [self::TYPE_ANDROID, self::TYPE_IOS, self::TYPE_WEB]],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => \app\modules\backend\models\Users::class, 'targetAttribute' => ['user_id' => 'id']],
            [['device_token', 'user_id', 'device_type'], 'unique', 'targetAttribute' => ['device_token', 'user_id', 'device_type']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'Пользователь',
            'device_token' => 'Токен устройства',
            'device_type' => 'Тип устройства',
            'device_name' => 'Название устройства',
            'is_active' => 'Активен',
            'last_used_at' => 'Последнее использование',
            'created_at' => 'Дата создания',
            'updated_at' => 'Дата обновления',
        ];
    }

    /**
     * Получить связанного пользователя
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(\app\modules\backend\models\Users::class, ['id' => 'user_id']);
    }

    /**
     * Регистрация нового токена устройства
     *
     * @param int $user_id ID пользователя
     * @param string $device_token Токен устройства
     * @param string $device_type Тип устройства
     * @param string|null $device_name Название устройства
     * @return UserDeviceToken|null
     */
    public static function register($user_id, $device_token, $device_type, $device_name = null)
    {
        // Проверяем, существует ли уже такой токен
        $token = self::findOne([
            'user_id' => $user_id,
            'device_token' => $device_token,
            'device_type' => $device_type,
        ]);
        
        if ($token) {
            // Если токен существует, обновляем его
            $token->is_active = true;
            $token->last_used_at = new Expression('NOW()');
            $token->device_name = $device_name;
            
            if ($token->save()) {
                return $token;
            }
            
            return null;
        }
        
        // Если токен не существует, создаем новый
        $token = new self();
        $token->user_id = $user_id;
        $token->device_token = $device_token;
        $token->device_type = $device_type;
        $token->device_name = $device_name;
        $token->is_active = true;
        $token->last_used_at = new Expression('NOW()');
        
        if ($token->save()) {
            return $token;
        }
        
        Yii::error('Ошибка при регистрации токена устройства: ' . json_encode($token->getErrors()), 'firebase');
        return null;
    }

    /**
     * Деактивация токена устройства
     *
     * @param int $user_id ID пользователя
     * @param string $device_token Токен устройства
     * @return bool
     */
    public static function deactivate($user_id, $device_token)
    {
        $tokens = self::findAll([
            'user_id' => $user_id,
            'device_token' => $device_token,
            'is_active' => true,
        ]);
        
        foreach ($tokens as $token) {
            $token->is_active = false;
            $token->save(false);
        }
        
        return true;
    }

    /**
     * Получить активные токены устройств пользователя
     *
     * @param int $user_id ID пользователя
     * @return UserDeviceToken[]
     */
    public static function getActiveTokensByUserId($user_id)
    {
        return self::find()
            ->where(['user_id' => $user_id, 'is_active' => true])
            ->all();
    }

    /**
     * Получить активные токены устройств пользователей с определенной ролью
     *
     * @param string $role Название роли
     * @return array Массив токенов устройств
     */
    public static function getActiveTokensByRole($role)
    {
        $userIds = Yii::$app->authManager->getUserIdsByRole($role);
        
        if (empty($userIds)) {
            return [];
        }
        
        return self::find()
            ->where(['user_id' => $userIds, 'is_active' => true])
            ->all();
    }
}
