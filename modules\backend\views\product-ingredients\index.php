<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\grid\GridView;
use yii\grid\ActionColumn;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "ingredients");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin')): ?>
                <a href="#" class="btn btn-primary product-ingredients-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "add_product_ingredient") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'product-ingredients-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="product-ingredients-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "product") ?></th>
                        <th><?= Yii::t("app", "Materials") ?></th>
                        <th><?= Yii::t("app", "alternative_ingredients") ?></th>
                        <th><?= Yii::t("app", "created_at") ?></th>
                        <th><?= Yii::t("app", "status") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                        <td><?= Html::encode($model['product_name']) ?></td>
                        <td>
                            <?php
                            $materials = array_map(fn($material) => Html::encode($material['name']), $model['materials']);
                            echo implode(', ', $materials);
                            ?>
                        </td>
                        <td>
                            <?php
                            if (!empty($model['alternative_materials'])) {
                                $altMaterials = array_map(fn($material) => Html::encode($material['name']), $model['alternative_materials']);
                                echo implode(', ', $altMaterials);
                            } else {
                                echo '<span class="text-muted">'.Yii::t('app', 'No alternative materials').'</span>';
                            }
                            ?>
                        </td>
                        <td data-order="<?= !empty($model['start_date']) ? strtotime($model['start_date']) : '' ?>">
                            <?= !empty($model['start_date']) ? Html::encode(date('d.m.Y H:i', strtotime($model['start_date']))) : 'N/A' ?>
                        </td>
                        <td>
                            <span class="<?= $model['end_date'] !== '9999-12-31' ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?= $model['end_date'] !== '9999-12-31' ? Yii::t("app", "active") : Yii::t("app", "inactive") ?>
                            </span>
                        </td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?= Yii::t("app", "detail") ?>
                                </a>
                                <div class="dropdown-menu">
                                    <?php if($model['end_date'] !== '9999-12-31'): ?>
                                        <a href="#" class="dropdown-item product-ingredients-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>
                                        <a href="#" class="dropdown-item product-ingredients-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model['id']) ?>">
                                            <span class="red-text"><?= Yii::t("app", "delete") ?></span>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "add_product_ingredient") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "edit_product_ingredient") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "product_ingredient_delete") ?>"></div>




<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#product-ingredients-grid-view')) {
            $('#product-ingredients-grid-view').DataTable().destroy();
        }

        $('#product-ingredients-grid-view').DataTable({
            "language": {
                "search": "{$searchLabel}",
                "lengthMenu": "{$lengthMenuLabel}",
                "zeroRecords": "{$zeroRecordsLabel}",
                "info": "{$infoLabel}",
                "infoEmpty": "{$infoEmptyLabel}",
                "infoFiltered": "{$infoFilteredLabel}",
            },
            "pageLength": 25,
            "order": [[2, "desc"]],
            "columnDefs": [
                {"orderable": false, "targets": [3, 4]}
            ]
        });
    }

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }

    function initializeProductIngredientsCreate() {
        $(document).off('click.product-ingredients-create').on('click.product-ingredients-create', '.product-ingredients-create', function() {
            $.ajax({
                url: '/backend/product-ingredients/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("product-ingredients-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.product-ingredients-create-button').on('click.product-ingredients-create-button', '.product-ingredients-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#product-ingredients-create-form').serialize();
                $.ajax({
                    url: '/backend/product-ingredients/create',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#ideal-mini-modal').modal('hide');
                            $.pjax.reload({
                                container: '#product-ingredients-grid-pjax',
                                async: false
                            });
                            initializeDataTable();
                        } else {
                            if (response.errors) {
                                $.each(response.errors, function(field, errors) {
                                    $('#' + field + '-error').html(errors.join('<br>')).show();
                                });
                            }
                            if (response.message) {
                                console.log(response.message);
                            }
                        }
                    },
                    error: function() {
                        console.log('Произошла ошибка при сохранении');
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeProductIngredientsUpdate() {
        $(document).off('click.product-ingredients-update').on('click.product-ingredients-update', '.product-ingredients-update', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/product-ingredients/update',
                type: 'GET',
                data: { id: id },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal .modal-title').html(two);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').addClass("product-ingredients-update-button");
                        initializeSelect2();
                    } else {
                        console.log(response.message);
                    }
                }
            });
        });

        $(document).off('click.product-ingredients-update-button').on('click.product-ingredients-update-button', '.product-ingredients-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#product-ingredients-update-form').serialize();
                $.ajax({
                    url: '/backend/product-ingredients/update',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('#ideal-mini-modal').modal('hide');
                            $.pjax.reload({
                                container: '#product-ingredients-grid-pjax',
                                async: false
                            });
                            initializeDataTable();
                        } else {
                            if (response.errors) {
                                $.each(response.errors, function(field, errors) {
                                    $('#' + field + '-error').html(errors.join('<br>')).show();
                                });
                            }
                            if (response.message) {
                                console.log(response.message);
                            }
                        }
                    },
                    error: function() {
                        console.log('Произошла ошибка при обновлении');
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeProductIngredientsDelete() {
        $(document).off('click.product-ingredients-delete').on('click.product-ingredients-delete', '.product-ingredients-delete', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/product-ingredients/delete',
                type: 'GET',
                data: { id: id },
                dataType: 'json',
                success: function(response) {
                    $('#ideal-mini-modal-delete .modal-title').html(three);
                    $('#ideal-mini-modal-delete .modal-body').html(response.content);
                    $('#ideal-mini-modal-delete .mini-button').addClass("product-ingredients-delete-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.product-ingredients-delete-button').on('click.product-ingredients-delete-button', '.product-ingredients-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#product-ingredients-delete-form').serialize();
                $.ajax({
                    url: '/backend/product-ingredients/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        button.prop('disabled', false);
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('#ideal-mini-modal-delete').modal('hide');
                            $.pjax.reload({
                                container: '#product-ingredients-grid-pjax',
                                async: false
                            });
                            initializeDataTable();
                        } else {
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    initializeDataTable();
    initializeDropdown();
    initializeSelect2();
    initializeProductIngredientsCreate();
    initializeProductIngredientsUpdate();
    initializeProductIngredientsDelete();

    $(document).on('pjax:end', function() {
        initializeDataTable();
        initializeDropdown();
        initializeSelect2();
    });
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
