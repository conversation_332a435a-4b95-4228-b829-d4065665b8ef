<?php

namespace app\modules\backend\controllers;

use app\common\models\Client;
use app\common\models\ClientSpecialPrices;
use app\common\models\Product;
use app\modules\backend\controllers\BaseController;
use Yii;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class ClientSpecialPricesController extends BaseController
{
    public function actionIndex()
    {
        $sql = "
            SELECT 
                csp.id,
                c.full_name as client_name,
                p.name as product_name,
                csp.special_price,
                csp.sell_price,
                csp.created_at,
                csp.deleted_at
            FROM client_special_prices csp
            LEFT JOIN client c ON c.id = csp.client_id
            LEFT JOIN product p ON p.id = csp.product_id
            WHERE csp.deleted_at IS NULL
            ORDER BY c.full_name ASC
        ";

        $result = Yii::$app->db->createCommand($sql)->queryAll();

        if (Yii::$app->request->isAjax) {
            return $this->renderAjax('index', [
                'result' => $result
            ]);
        }

        return $this->render('index', [
            'result' => $result
        ]);
    }

    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
    
        if (Yii::$app->request->isPost) {
            $model = new ClientSpecialPrices();
            if ($model->load(Yii::$app->request->post())) {

                if (!$model->validate()) {
                    return [
                        'status' => 'fail',
                        'errors' => $model->getErrors()
                    ];
                }
    
                $existingRecord = ClientSpecialPrices::find()
                    ->where([
                        'client_id' => $model->client_id,
                        'product_id' => $model->product_id,
                        'deleted_at' => null, 
                    ])
                    ->one();
    
                if ($existingRecord) {
                    $existingRecord->deleted_at = date('Y-m-d H:i:s');
                    $existingRecord->save();
                }
    
               
    
                $model->save();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'created_successfully')
                ];
            }
            return [
                'status' => 'fail',
                'errors' => $model->getErrors()
            ];
        } else if (Yii::$app->request->isGet) {
    
            $model = new ClientSpecialPrices();
            $clients = Client::find()
                ->where(['deleted_at' => null])
                ->all();
            $products = Product::find()
                ->where(['deleted_at' => null])
                ->all();
    
            return [
                'status' => 'success',
                'content' => $this->renderAjax('_form', [
                    'model' => $model,
                    'clients' => $clients,
                    'products' => $products,
                ])
            ];
        }
    }

    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $model = $this->findModel($data['ClientSpecialPrices']['id']);

            if ($model->load(Yii::$app->request->post())) {
                if (!$model->validate()) {
                    return [
                        'status' => 'fail',
                        'errors' => $model->getErrors()
                    ];
                }

                $existingRecord = ClientSpecialPrices::find()
                    ->where([
                        'client_id' => $model->client_id,
                        'product_id' => $model->product_id,
                        'deleted_at' => null,
                    ])
                    ->one();

                if ($existingRecord && $existingRecord->id !== $model->id) {
                    $existingRecord->deleted_at = date('Y-m-d H:i:s');
                    $existingRecord->save();
                }

                $model->save();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'updated_successfully')
                ];
            }
            return [
                'status' => 'fail',
                'errors' => $model->getErrors()
            ];
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = $this->findModel($id);
            $clients = Client::find()
                ->where(['deleted_at' => null])
                ->all();
            $products = Product::find()
                ->where(['deleted_at' => null])
                ->all();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model,
                    'clients' => $clients,
                    'products' => $products,
                ])
            ];
      }
    }


    public function actionDetail()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = $this->findModel($id);
            return [
                'status' => 'success',
                'content' => $this->renderPartial('detail', [
                    'model' => $model
                ])
            ];
        }
        
        return [
            'status' => 'fail',
            'message' => Yii::t('app', 'invalid_request')
        ];
    }

    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
    
    
        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $model = $this->findModel($data['ClientSpecialPrices']['id']);
            $model->deleted_at = date('Y-m-d H:i:s');
    
            if ($model->save()) {
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'deleted_successfully')
                ];
            }
    
            return [
                'status' => 'fail',
                'errors' => $model->getErrors()
            ];
        } else if(Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = $this->findModel($id);
            return [
                'status' => 'success',
                'content' => $this->renderPartial('delete', [
                    'model' => $model
                ])
            ];
        }
    
    }
    

    protected function findModel($id)
    {
        $model = ClientSpecialPrices::findOne([
            'id' => $id,
            'deleted_at' => null
        ]);

        if ($model === null) {
            throw new NotFoundHttpException(Yii::t('app', 'page_not_found'));
        }

        return $model;
    }
}