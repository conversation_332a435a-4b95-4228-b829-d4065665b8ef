<?php

namespace app\components;

use Yii;
use yii\base\Behavior;
use yii\web\Application;

class LanguageBehavior extends Behavior
{
    public function events()
    {
        return [
            Application::EVENT_BEFORE_REQUEST => 'beforeRequest',
        ];
    }

    public function beforeRequest($event)
    {
        // Проверяем, есть ли параметр языка в URL
        $language = Yii::$app->request->get('language');
        if ($language && in_array($language, ['ru', 'uz'])) {
            Yii::$app->language = $language;
            Yii::$app->session->set('language', $language);
            return;
        }
        
        // Проверяем, есть ли язык в сессии
        if (Yii::$app->session->has('language')) {
            Yii::$app->language = Yii::$app->session->get('language');
            return;
        }
        
        // Проверяем, есть ли язык в куки
        $cookies = Yii::$app->request->cookies;
        if ($cookies->has('language')) {
            Yii::$app->language = $cookies->getValue('language');
            return;
        }
        
        // Если язык не установлен, используем язык по умолчанию (uz)
        Yii::$app->language = 'uz';
    }
}
