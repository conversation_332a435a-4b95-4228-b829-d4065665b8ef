<?php

namespace app\modules\backend\models;

use Yii;

/**
 * This is the model class for table "worker_salary".
 *
 * @property int $id
 * @property int $worker_id
 * @property float $amount
 * @property string $start_date
 * @property string|null $end_date
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Worker $worker
 */
class WorkerSalary extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'worker_salary';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['worker_id', 'amount', 'start_date'], 'required'],
            [['worker_id'], 'default', 'value' => null],
            [['worker_id'], 'integer'],
            [['amount'], 'number'],
            [['start_date', 'end_date', 'created_at', 'deleted_at'], 'safe'],
            [['worker_id'], 'exist', 'skipOnError' => true, 'targetClass' => Worker::class, 'targetAttribute' => ['worker_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'worker_id' => 'Worker ID',
            'amount' => 'Amount',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[Worker]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWorker()
    {
        return $this->hasOne(Worker::class, ['id' => 'worker_id']);
    }

    public function beforeValidate()
    {
        $attributes = ['amount'];

        foreach ($attributes as $attribute) {
            if (is_string($this->$attribute) && !empty($this->$attribute)) {
                $originalValue = $this->$attribute;
                
                $cleanValue = preg_replace('/[\s\x{A0}]+/u', '', $originalValue);
                $cleanValue = str_replace(',', '.', $cleanValue);
                
                if (is_numeric($cleanValue)) {
                    $this->$attribute = floatval($cleanValue);
                }
            }
        }
        
        return parent::beforeValidate();
    }
}
