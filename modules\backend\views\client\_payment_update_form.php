<?php

use app\common\models\Cashbox;
use app\assets\AppAsset;

AppAsset::register($this);

?>

<div class="client-form">
    <form id="client-payment-update-form">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="cashbox_id"><?= Yii::t('app', 'cashbox') ?></label>
                    <select id="cashbox_id" class="form-control select2" name="ClientPayments[cashbox_id]">
                        <option value=""><?= Yii::t('app', 'select') ?></option>
                        <?php
                        $cashboxes = Cashbox::find()->where(['deleted_at' => null])->all();
                        foreach ($cashboxes as $cashbox): ?>
                            <option value="<?= $cashbox->id ?>" <?= $model->cashbox_id == $cashbox->id ? 'selected' : '' ?>>
                                <?= $cashbox->title ?> (<?= $cashbox->getPaymentTypesString() ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="error-container" id="cashbox_id-error"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="type"><?= Yii::t('app', 'type') ?></label>
                    <select id="type" class="form-control select2" name="ClientPayments[type]">
                        <option value=""><?= Yii::t('app', 'select_type') ?></option>
                        <!-- Опции будут загружены динамически через JavaScript -->
                    </select>
                    <div class="error-container" id="type-error"></div>
                </div>
            </div>
        </div>

        <div class="form-group mt-3">
            <label for="summa"><?= Yii::t('app', 'amount') ?></label>
            <input type="text" class="form-control formatted-numeric-input" id="summa" name="ClientPayments[summa]" value="<?= $model->summa ?>">
            <div class="error-container" id="summa-error"></div>
        </div>

        <input type="hidden" name="ClientPayments[id]" value="<?= $model->id ?>">
        <input type="hidden" name="ClientPayments[client_id]" value="<?= $model->client_id ?>">
    </form>
</div>

<script>
$(document).ready(function() {
    // Функция для загрузки доступных типов платежей по выбранной кассе
    function loadPaymentTypesByCashbox(cashboxId, selectedPaymentType) {
        if (!cashboxId) {
            $('#type').empty().append('<option value=""><?= Yii::t("app", "select_type") ?></option>');
            return;
        }

        $.ajax({
            url: '<?= \yii\helpers\Url::to(['/backend/expenses/get-payment-types-by-cashbox']) ?>',
            type: 'GET',
            data: { cashbox_id: cashboxId },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    var paymentTypeSelect = $('#type');
                    paymentTypeSelect.empty();
                    paymentTypeSelect.append('<option value=""><?= Yii::t("app", "select_type") ?></option>');

                    $.each(response.payment_types, function(index, paymentType) {
                        var selected = (selectedPaymentType && selectedPaymentType == paymentType.type) ? 'selected' : '';
                        paymentTypeSelect.append(
                            '<option value="' + paymentType.type + '" ' + selected + '>' + paymentType.label + '</option>'
                        );
                    });

                    // Обновляем select2
                    paymentTypeSelect.trigger('change');
                } else {
                    console.error('Ошибка загрузки типов платежей:', response.message);
                    $('#type').empty().append('<option value=""><?= Yii::t("app", "select_type") ?></option>');
                }
            },
            error: function() {
                console.error('Ошибка AJAX запроса для загрузки типов платежей');
                $('#type').empty().append('<option value=""><?= Yii::t("app", "select_type") ?></option>');
            }
        });
    }

    // Обработчик изменения кассы
    $('#cashbox_id').on('change', function() {
        var cashboxId = $(this).val();
        loadPaymentTypesByCashbox(cashboxId);
    });

    // Загружаем типы платежей при инициализации с текущими значениями
    var initialCashboxId = $('#cashbox_id').val();
    var initialPaymentType = <?= $model->type ?: 'null' ?>;
    if (initialCashboxId) {
        loadPaymentTypesByCashbox(initialCashboxId, initialPaymentType);
    }
});
</script>
