<?php

namespace app\modules\backend\models;

use app\common\models\Product;
use Yii;
use yii\base\Model;

/**
 * Форма для корректировки остатков продуктов
 */
class ProductAdjustmentForm extends Model
{
    /**
     * @var int ID продукта
     */
    public $product_id;
    
    /**
     * @var float Количество продукта
     */
    public $quantity;
    


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['product_id', 'quantity'], 'required'],
            [['product_id'], 'integer'],
            [['quantity'], 'number', 'min' => 0],

            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'product_id' => 'Продукт',
            'quantity' => 'Количество',

        ];
    }
}
