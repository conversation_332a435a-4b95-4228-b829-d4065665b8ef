<?php

namespace app\components\services;

use app\common\models\Sales;
use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * Сервис для формирования отчетов по продажам
 */
class ReportService
{
    /**
     * Получить отчет по продажам с различными метриками
     * 
     * @param string|null $startDate Начальная дата в формате Y-m-d
     * @param string|null $endDate Конечная дата в формате Y-m-d
     * @param int|null $productId ID продукта для фильтрации
     * @param int|null $status Статус продажи для фильтрации
     * @return array Массив с данными отчета
     */
    public function getSalesReport($startDate = null, $endDate = null, $productId = null, $status = null)
    {
        $conditions = [];
        $salesConditions = [];
        $freeProductsConditions = [];
        
        if ($startDate) {
            $salesConditions[] = ['>=', 's.created_at', $startDate . ' 00:00:00'];
            $freeProductsConditions[] = ['>=', 'fp.created_at', $startDate . ' 00:00:00'];
        }
        
        if ($endDate) {
            $salesConditions[] = ['<=', 's.created_at', $endDate . ' 23:59:59'];
            $freeProductsConditions[] = ['<=', 'fp.created_at', $endDate . ' 23:59:59'];
        }
        
        if ($productId) {
            $salesConditions[] = ['sd.product_id' => $productId];
            $freeProductsConditions[] = ['fp.product_id' => $productId];
        }
        
        if ($status) {
            $salesConditions[] = ['s.status' => $status];
        }
        
        $salesData = $this->getSalesData($salesConditions);
        $bonusData = $this->getBonusData($salesConditions);
        $freeProductsData = $this->getFreeProductsData($freeProductsConditions);
        
        return [
            'sales' => $salesData,
            'bonus' => $bonusData,
            'freeProducts' => $freeProductsData,
            'summary' => [
                'totalSales' => $salesData['totalQuantity'] ?? 0,
                'totalBlocks' => $salesData['totalBlocks'] ?? 0,
                'totalBonus' => $bonusData['totalQuantity'] ?? 0,
                'totalFreeProducts' => $freeProductsData['totalQuantity'] ?? 0,
                'totalAmount' => $salesData['totalAmount'] ?? 0,
                'totalReturned' => $salesData['totalReturned'] ?? 0,
            ]
        ];
    }
    
    /**
     * Получить данные по продажам
     * 
     * @param array $conditions Условия фильтрации
     * @return array Данные по продажам
     */
    protected function getSalesData($conditions)
    {
        $query = new Query();
        $query->select([
                's.id',
                'p.id as product_id',
                'p.name as product_name',
                'p.size as size',
                'SUM(sd.quantity) as total_quantity',
                'CEIL(SUM(sd.quantity) / NULLIF(p.size,1)) as total_blocks',
                'SUM(sd.special_price * sd.quantity) as total_amount',
                's.created_at as created_at',
                's.status',
                'c.full_name as full_name',
                '(SELECT COALESCE(SUM(sr.quantity), 0) FROM sales_return sr 
                  WHERE sr.sale_id = s.id 
                  AND sr.product_id = p.id 
                  AND sr.status = :status) as returned_quantity'
            ])
            ->from(['sd' => 'sales_detail'])
            ->leftJoin(['s' => 'sales'], 'sd.sale_id = s.id')
            ->leftJoin(['p' => 'product'], 'sd.product_id = p.id')
            ->leftJoin(['c' => 'client'], 's.client_id = c.id')
            ->where(['IN', 's.status', [Sales::STATUS_NEW, Sales::STATUS_CONFIRMED]])
            ->andWhere(['IS', 'sd.deleted_at', null])
            ->andWhere(['IS', 's.deleted_at', null])
            ->params([':status' => \app\common\models\SalesReturn::STATUS_APPROVED])
            ->orderBy(['s.id' => SORT_DESC]);
        
        // Применение условий фильтрации
        foreach ($conditions as $condition) {
            $query->andWhere($condition);
        }
        
        // Группируем по продаже и продукту
        $query->groupBy(['s.id', 'p.id', 'p.name', 'p.size', 's.created_at', 's.status', 'c.full_name']);
        
        $result = $query->all();
        
        return [
            'items' => $result,
            'totalQuantity' => array_sum(ArrayHelper::getColumn($result, 'total_quantity')),
            'totalBlocks' => array_sum(ArrayHelper::getColumn($result, 'total_blocks')),
            'totalAmount' => array_sum(ArrayHelper::getColumn($result, 'total_amount')),
            'totalReturned' => array_sum(ArrayHelper::getColumn($result, 'returned_quantity'))
        ];
    }
    /**
     * Получить данные по бонусам
     * 
     * @param array $conditions Условия фильтрации
     * @return array Данные по бонусам
     */
    protected function getBonusData($conditions)
    {
        $query = new Query();
        $query->select([
                'p.id as product_id',
                'p.name as product_name',
                'SUM(sb.quantity) as total_quantity',
                'sb.sale_id as sale_id'
            ])
            ->from(['sb' => 'sales_bonus'])
            ->leftJoin(['p' => 'product'], 'sb.product_id = p.id')
            ->leftJoin(['s' => 'sales'], 'sb.sale_id = s.id')
            ->where(['IS', 'sb.deleted_at', null]);
        
        // Применение условий фильтрации
    foreach ($conditions as $condition) {
        if (is_array($condition)) {
                if (isset($condition[0]) && is_string($condition[0])) {
                    $condition[1] = str_replace('sd.', 'sb.', $condition[1]);
                } else {
                    $newCondition = [];
                    foreach ($condition as $key => $value) {
                        $newKey = str_replace('sd.', 'sb.', $key);
                        $newCondition[$newKey] = $value;
                    }
                    $condition = $newCondition;
                }
            }
            $query->andWhere($condition);
        }
        
        $query->groupBy(['p.id', 'p.name', 'sb.sale_id']);
        
        $result = $query->all();
        
        return [
            'items' => $result,
            'totalQuantity' => array_sum(ArrayHelper::getColumn($result, 'total_quantity'))
        ];
    }
    
    /**
     * Получить данные по бесплатным продуктам
     * 
     * @param array $conditions Условия фильтрации
     * @return array Данные по бесплатным продуктам
     */
    protected function getFreeProductsData($conditions)
    {
        $query = new Query();
        $query->select([
                'fp.id as free_product_id',
                'p.id as product_id',
                'p.name as product_name',
                'COALESCE(SUM(fpd.quantity), 0) as total_quantity',
                'COALESCE(CEIL(SUM(fpd.quantity) / NULLIF(p.size,1)), 0) as total_blocks',
                'fp.client as client',
                'fp.car_number as car_number',
                'fp.created_at as created_at'
            ])
            ->from(['fp' => 'free_products'])
            ->leftJoin(['fpd' => 'free_product_detail'], 'fp.id = fpd.free_product_id AND fpd.deleted_at IS NULL')
            ->leftJoin(['p' => 'product'], 'fpd.product_id = p.id')
            ->where(['IS', 'fp.deleted_at', null])
            ->orderBy(['fp.id' => SORT_DESC])
            ->groupBy(['fp.id', 'p.id', 'p.name', 'fp.client', 'fp.car_number', 'fp.created_at']);
            
        
        // Применение условий фильтрации
        foreach ($conditions as $condition) {
            if (is_array($condition) && isset($condition[0]) && is_string($condition[0])) {
                // Заменяем fp.product_id на fpd.product_id для новой структуры
                $condition[1] = str_replace('fp.product_id', 'fpd.product_id', $condition[1]);
                $condition[1] = str_replace('sd.', 'fp.', $condition[1]);
            } elseif (is_array($condition)) {
                // Обрабатываем условия типа ['fp.product_id' => $productId]
                $newCondition = [];
                foreach ($condition as $key => $value) {
                    $newKey = str_replace('fp.product_id', 'fpd.product_id', $key);
                    $newKey = str_replace('sd.', 'fp.', $newKey);
                    $newCondition[$newKey] = $value;
                }
                $condition = $newCondition;
            }
            $query->andWhere($condition);
        }
        
        $result = $query->all();
        
        // Подготовка итоговых данных
        $totalQuantity = array_sum(ArrayHelper::getColumn($result, 'total_quantity'));
        $totalBlocks = array_sum(ArrayHelper::getColumn($result, 'total_blocks'));

        return [
            'items' => $result,
            'totalQuantity' => $totalQuantity,
            'totalBlocks' => $totalBlocks
        ];
    }

    /**
     * Получить отчет только по бесплатным продуктам
     * 
     * @param string|null $startDate Начальная дата в формате Y-m-d
     * @param string|null $endDate Конечная дата в формате Y-m-d
     * @param int|null $productId ID продукта для фильтрации
     * @return array Массив с данными отчета по бесплатным продуктам
     */
    public function getFreeProductsReport($startDate = null, $endDate = null, $productId = null)
    {
        $conditions = [];
        
        if ($startDate) {
            $conditions[] = ['>=', 'fp.created_at', $startDate . ' 00:00:00'];
        }
        
        if ($endDate) {
            $conditions[] = ['<=', 'fp.created_at', $endDate . ' 23:59:59'];
        }
        
        if ($productId) {
            $conditions[] = ['fp.product_id' => $productId];
        }
        
        $freeProductsData = $this->getFreeProductsData($conditions);
        
        return [
            'items' => $freeProductsData['items'],
            'summary' => [
                'totalQuantity' => $freeProductsData['totalQuantity'],
                'totalBlocks' => $freeProductsData['totalBlocks']
            ]
        ];
    }
}