<?php
use yii\helpers\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);

?>

<div class="client-form">
    <form id="client-create-form">
        <div class="form-group">
            <label for="full_name"><?= Yii::t('app', 'client_name') ?></label>
            <input type="text" id="full_name" name="Client[full_name]" maxlength="255" class="form-control" required>
            <div class="error-container" id="full_name-error"></div>
        </div>

        <div class="form-group">
            <label for="region_id"><?= Yii::t('app', 'region') ?></label>
            <select id="region_id" name="Client[region_id]" class="form-control select2" data-minimum-results-for-search="Infinity">
                <option value=""><?= Yii::t('app', 'select_region') ?></option>
                <?php foreach ($regions as $region): ?>
                    <option value="<?= $region->id ?>"><?= Html::encode($region->name) ?></option>
                <?php endforeach; ?>
            </select>
            <div class="error-container" id="region_id-error"></div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="phone_number"><?= Yii::t('app', 'phone_number') ?></label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">+998</span>
                        </div>
                        <input type="text" id="phone_number" name="Client[phone_number]" class="form-control" required>
                    </div>
                    <div class="error-container text-danger" id="phone_number-error"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <label for="phone_number_2"><?= Yii::t('app', 'phone_number_2') ?></label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">+998</span>
                        </div>
                        <input type="text" id="phone_number_2" name="Client[phone_number_2]" class="form-control">
                    </div>
                    <div class="error-container text-danger" id="phone_number_2-error"></div>
                </div>
            </div>

        </div>

        <div class="form-group">
            <label for="account_number"><?= Yii::t('app', 'account_number') ?></label>
            <input type="text" id="account_number" name="Client[account_number]" class="form-control">
            <div class="error-container" id="account_number-error"></div>
        </div>

        <div class="form-group">
            <label for="organization"><?= Yii::t('app', 'organization') ?></label>
            <input type="text" id="organization" name="Client[organization]" class="form-control">
            <div class="error-container" id="organization-error"></div>
        </div>

        <div class="form-group">
            <label for="legal_address"><?= Yii::t('app', 'legal_address') ?></label>
            <input type="text" id="legal_address" name="Client[legal_address]" class="form-control">
            <div class="error-container" id="legal_address-error"></div>
        </div>

        <div class="form-group">
            <div class="form-check">
                <input type="checkbox" id="can_return" name="Client[can_return]" value="1" class="form-check-input">
                <label class="form-check-label" for="can_return"><?= Yii::t('app', 'can_return') ?></label>
            </div>
            <div class="error-container" id="can_return-error"></div>
        </div>

    </form>
</div>

<script>
$(document).ready(function () {
    $('.select2').select2({
        width: '100%',
        minimumResultsForSearch: Infinity,
        language: {
            noResults: function () {
                return "<?= Yii::t('app', 'nothing_found') ?>";
            }
        }
    });

    function initPhoneInput(inputId) {
        const input = $(`#${inputId}`);

        // Обработка ввода текста
        input.on('input', function () {
            let value = this.value.replace(/\D/g, ''); // Удаление всех нецифровых символов

            // Ограничение до 9 цифр
            if (value.length > 9) {
                value = value.slice(0, 9);
            }

            // Форматирование значения
            let formattedValue = '';
            if (value.length > 0) {
                formattedValue = value.substring(0, 2); // Первые 2 цифры
            }
            if (value.length > 2) {
                formattedValue += ' ' + value.substring(2, 5); // Следующие 3 цифры
            }
            if (value.length > 5) {
                formattedValue += ' ' + value.substring(5, 7); // Следующие 2 цифры
            }
            if (value.length > 7) {
                formattedValue += ' ' + value.substring(7, 9); // Последние 2 цифры
            }

            this.value = formattedValue; // Установка отформатированного значения
        });

        // Ограничение ввода только цифрами
        input.on('keypress', function (e) {
            if (!/^\d$/.test(e.key)) {
                e.preventDefault();
            }
        });

        // Обработка вставки текста
        input.on('paste', function (e) {
            e.preventDefault();
            let pastedText = (e.originalEvent || e).clipboardData.getData('text'); // Получение текста из буфера обмена
            let numericValue = pastedText.replace(/\D/g, '').slice(0, 9); // Удаление нецифровых символов и ограничение до 9 цифр
            this.value = numericValue;
            $(this).trigger('input'); // Запуск события input для форматирования
        });
    }

    // Инициализация телефонных полей
    initPhoneInput('phone_number');
    initPhoneInput('phone_number_2');
});

</script>
