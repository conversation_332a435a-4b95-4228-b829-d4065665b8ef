<?php
use yii\helpers\Html;
use app\common\models\Cashbox;
use app\common\models\CashboxDetail;
?>

<?php if($result): ?>
    <table id="cashbox-detail-grid-view" class="table table-bordered table-striped compact">
        <thead>
            <th><?= Yii::t("app", "cashbox_name") ?></th>
            <th><?= Yii::t("app", "added_user") ?></th>
            <th><?= Yii::t("app", "balance") ?></th>
            <th><?= Yii::t("app", "type_payment") ?></th>
            <th><?= Yii::t("app", "payment_created_at") ?></th>
        </thead>
        <tbody>
        <?php foreach ($result as $model): ?>
            <tr>
                <td><?= Html::encode($model['cashbox_name']) ?></td>
                <td><?= Html::encode($model['add_user']) ?></td>
                <td><?= Html::encode($model['amount']) ?></td>
                <td><?= Html::encode(CashboxDetail::getPaymentTypeDescription($model['payment_type'])) ?></td>
                <td><?= Html::encode(date('d.m.Y H:i', strtotime($model['created_at']))) ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
<?php else: ?>
    <p><?= Yii::t('app', 'no_data_available') ?></p>
<?php endif; ?>
