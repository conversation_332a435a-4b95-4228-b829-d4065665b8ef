<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "cashbox".
 *
 * @property int $id
 * @property string|null $title
 * @property float|null $balance
 * @property string|null $created_at
 * @property string|null $deleted_at
 * @property CashboxDetail[] $cashboxDetails
 * @property CashboxPaymentType[] $cashboxPaymentTypes
 * @property PaymentType[] $paymentTypes
 */
class Cashbox extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'cashbox';
    }


    public const IS_MAIN = 1;
    public const IS_SECONDARY = 2;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title'], 'required'],
            [['title'], 'string', 'max' => 255],
            [['balance'], 'number'],
            [['created_at', 'deleted_at'], 'safe'],
            [['title'], 'unique', 'targetAttribute' => ['title'], 
                'filter' => ['deleted_at' => null], 
                'message' => 'Касса с таким названием и валютой уже существует.'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'title' => 'Название кассы',
            'balance' => 'Баланс',
            'created_at' => 'Создана',
            'deleted_at' => 'Удалена',
        ];
    }


    /**
     * Gets query for [[CashboxDetails]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCashboxDetails()
    {
        return $this->hasMany(CashboxDetail::class, ['cashbox_id' => 'id']);
    }

    /**
     * Gets query for [[CashboxPaymentTypes]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCashboxPaymentTypes()
    {
        return $this->hasMany(CashboxPaymentType::class, ['cashbox_id' => 'id'])
            ->where(['cashbox_payment_type.deleted_at' => null]);
    }

    /**
     * Gets query for [[PaymentTypes]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPaymentTypes()
    {
        return $this->hasMany(PaymentType::class, ['id' => 'payment_type_id'])
            ->via('cashboxPaymentTypes')
            ->where(['payment_type.deleted_at' => null]);
    }

    /**
     * Находит кассу по типу платежа
     *
     * @param int $paymentTypeId
     * @return Cashbox|null
     */
    public static function findByPaymentType($paymentTypeId)
    {
        return self::find()
            ->joinWith(['cashboxPaymentTypes' => function($query) {
                $query->andWhere(['cashbox_payment_type.deleted_at' => null]);
            }])
            ->joinWith(['paymentTypes' => function($query) use ($paymentTypeId) {
                $query->andWhere([
                    'payment_type.type' => $paymentTypeId,
                    'payment_type.deleted_at' => null
                ]);
            }])
            ->where(['cashbox.deleted_at' => null])
            ->one();
    }

    /**
     * Возвращает строку с названиями всех связанных типов платежей
     *
     * @return string
     */
    public function getPaymentTypesString()
    {
        $paymentTypes = $this->paymentTypes;
        if (empty($paymentTypes)) {
            return 'Не связано ни с одним типом';
        }
        
        $names = [];
        foreach ($paymentTypes as $paymentType) {
            $names[] = PaymentType::getTypeDescription($paymentType->type);
        }
        
        return implode(', ', $names);
    }

    /**
     * Возвращает массив касс для select dropdown (новая структура)
     * @return array
     */
    public static function getNameLabels()
    {
        $cashboxes = self::find()
            ->select(['id', 'title'])
            ->where(['cashbox.deleted_at' => null])
            ->asArray()
            ->all();

        $result = [];
        foreach ($cashboxes as $cashbox) {
            $result[$cashbox['id']] = $cashbox['title'];
        }
        return $result;
    }

    /**
     * Возвращает название кассы (новая структура - просто возвращает переданное название)
     * @param string $name
     * @return string
     */
    public static function getNameDescription($name)
    {
        return $name; // В новой структуре у нас уже человекочитаемые названия
    }

    /**
     * Возвращает описание типа платежа (делегируем в PaymentType)
     * @param int $type
     * @return string
     */
    public static function getTypeDescription($type)
    {
        return PaymentType::getTypeDescription($type);
    }

}
