<?php

namespace app\modules\backend\controllers;

use app\common\models\MaterialDefect;
use app\common\models\ProductDefect;
use Yii;
use yii\data\ActiveDataProvider;
use yii\web\Controller;
use yii\web\Response;

/**
 * MaterialDefectController implements the defect reports for Material and Product models.
 */
class MaterialDefectController extends Controller
{
    /**
     * Главная страница отчетов по браку с табами
     * @return string
     */
    public function actionIndex()
    {
        return $this->render('index');
    }

    /**
     * Отчет по браку продукции со склада продукции
     * @return string
     */
    public function actionProductDefectFromWarehouse()
    {
        $query = ProductDefect::find()
            ->where(['is_repackaging' => false])
            ->joinWith(['product', 'addUser', 'acceptedUser']);

        $dateFrom = Yii::$app->request->get('date_from', date('Y-m-d', strtotime('-30 days')));
        $dateTo = Yii::$app->request->get('date_to', date('Y-m-d'));

        if ($dateFrom && $dateTo) {
            $query->andWhere(['between', 'product_defect.created_at', $dateFrom . ' 00:00:00', $dateTo . ' 23:59:59']);
        }

        $query->orderBy(['product_defect.created_at' => SORT_DESC]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 20,
            ],
        ]);

        return $this->renderAjax('_report', [
            'dataProvider' => $dataProvider,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'title' => Yii::t('app', 'product_defect_from_warehouse'),
            'type' => 'product_defect',
            'showAcceptedBy' => true,
        ]);
    }

    /**
     * Отчет по переупаковке продукции
     * @return string
     */
    public function actionProductRepackaging()
    {
        $query = ProductDefect::find()
            ->where(['is_repackaging' => true])
            ->joinWith(['product', 'addUser', 'acceptedUser']);

        $dateFrom = Yii::$app->request->get('date_from', date('Y-m-d', strtotime('-30 days')));
        $dateTo = Yii::$app->request->get('date_to', date('Y-m-d'));

        if ($dateFrom && $dateTo) {
            $query->andWhere(['between', 'product_defect.created_at', $dateFrom . ' 00:00:00', $dateTo . ' 23:59:59']);
        }

        $query->orderBy(['product_defect.created_at' => SORT_DESC]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 20,
            ],
        ]);

        return $this->renderAjax('_report', [
            'dataProvider' => $dataProvider,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'title' => Yii::t('app', 'product_repackaging'),
            'type' => 'product_repackaging',
            'showAcceptedBy' => false,
        ]);
    }

    /**
     * Отчет по браку сырья из производства
     * @return string
     */
    public function actionMaterialDefectFromProduction()
    {
        $query = MaterialDefect::find()
            ->where(['source' => MaterialDefect::SOURCE_MANUFACTURER])
            ->joinWith(['material', 'addUser', 'acceptedUser']);

        $dateFrom = Yii::$app->request->get('date_from', date('Y-m-d', strtotime('-30 days')));
        $dateTo = Yii::$app->request->get('date_to', date('Y-m-d'));

        if ($dateFrom && $dateTo) {
            $query->andWhere(['between', 'material_defect.created_at', $dateFrom . ' 00:00:00', $dateTo . ' 23:59:59']);
        }

        $query->orderBy(['material_defect.created_at' => SORT_DESC]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 20,
            ],
        ]);

        return $this->renderAjax('_report', [
            'dataProvider' => $dataProvider,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'title' => Yii::t('app', 'material_defect_from_production'),
            'type' => 'material_defect_production',
            'showAcceptedBy' => true,
        ]);
    }



    /**
     * Отчет по возврату материалов в производство
     * @return string
     */
    public function actionMaterialReturn()
    {
        // Для отчета по возврату материалов используем MaterialStatusGroup со статусом 5 (возврат)
        $query = \app\common\models\MaterialStatusGroup::find()
            ->where(['status' => \app\common\models\MaterialStatusGroup::STATUS_RETURNED_FROM_PRODUCTION])
            ->andWhere(['material_status_group.deleted_at' => null])
            ->joinWith(['addUser', 'acceptedUser']);

        $dateFrom = Yii::$app->request->get('date_from', date('Y-m-d', strtotime('-30 days')));
        $dateTo = Yii::$app->request->get('date_to', date('Y-m-d'));

        if ($dateFrom && $dateTo) {
            $query->andWhere(['between', 'material_status_group.created_at', $dateFrom . ' 00:00:00', $dateTo . ' 23:59:59']);
        }

        $query->orderBy(['material_status_group.created_at' => SORT_DESC]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 20,
            ],
        ]);

        return $this->renderAjax('_material_return_report', [
            'dataProvider' => $dataProvider,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'title' => Yii::t('app', 'material_returns'),
            'showAcceptedBy' => true,
        ]);
    }
}