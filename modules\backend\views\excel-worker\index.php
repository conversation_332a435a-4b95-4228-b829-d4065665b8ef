<?php
use yii\helpers\Html;
use yii\widgets\ActiveForm;

$this->title = 'Импорт работников из Excel';
?>

<h1><?= Html::encode($this->title) ?></h1>

<?php if (Yii::$app->session->hasFlash('success')): ?>
    <div class="alert alert-success">
        <?= Yii::$app->session->getFlash('success') ?>
    </div>
<?php endif; ?>

<?php if (Yii::$app->session->hasFlash('error')): ?>
    <div class="alert alert-danger">
        <?= Yii::$app->session->getFlash('error') ?>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5><?= Yii::t('app', 'excel_worker_import') ?></h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <?php $form = ActiveForm::begin(['options' => ['enctype' => 'multipart/form-data']]); ?>

                <?= $form->field($model, 'file')->fileInput([
                    'accept' => '.xlsx,.xls',
                    'class' => 'form-control-file'
                ])->label('Выберите Excel-файл с данными работников') ?>

                <div class="form-group">
                    <?= Html::submitButton('Загрузить и импортировать', ['class' => 'btn btn-primary']) ?>
                </div>

                <?php ActiveForm::end(); ?>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-header">
                        <h6>Формат Excel файла</h6>
                    </div>
                    <div class="card-body">                        <p><strong>Ожидаемые колонки (в порядке A, B, C, D):</strong></p>
                        <ul class="list-unstyled">
                            <li><strong>A:</strong> № (порядковый номер)</li>
                            <li><strong>B:</strong> Полное имя работника</li>
                            <li><strong>C:</strong> Должность</li>
                            <li><strong>D:</strong> Оклад (зарплата)</li>
                        </ul>
                        <p class="text-muted small">
                            <strong>Примечания:</strong><br>
                            • Первая строка должна содержать заголовки<br>
                            • Номер телефона будет сгенерирован автоматически<br>
                            • Дата начала зарплаты: 01.06.2025<br>
                            • Должности будут найдены по схожести (85%+) или созданы новые<br>
                            • Существующие работники будут найдены по схожести имен (90%+)
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
