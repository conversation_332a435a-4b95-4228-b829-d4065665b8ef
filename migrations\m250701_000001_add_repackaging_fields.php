<?php

use yii\db\Migration;

/**
 * Class m250701_000001_add_repackaging_fields
 * 
 * Добавляет поля для функционала переупаковки продукта
 */
class m250701_000001_add_repackaging_fields extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Добавление полей в таблицу product_defect
        $this->addColumn('product_defect', 'is_repackaging', $this->boolean()->defaultValue(false)->after('description'));
        $this->addColumn('product_defect', 'original_storage_id', $this->bigInteger()->null()->after('is_repackaging'));
        $this->addColumn('product_defect', 'repackaging_reason', $this->string(255)->null()->after('original_storage_id'));

        // Добавление внешнего ключа для original_storage_id
        $this->addForeignKey(
            'fk-product_defect-original_storage_id',
            'product_defect',
            'original_storage_id',
            'product_storage',
            'id',
            'SET NULL'
        );

        // Добавление поля в таблицу product_storage_history
        $this->addColumn('product_storage_history', 'original_storage_id', $this->bigInteger()->null()->after('add_user_id'));

        // Добавление внешнего ключа для original_storage_id в product_storage_history
        $this->addForeignKey(
            'fk-product_storage_history-original_storage_id',
            'product_storage_history',
            'original_storage_id',
            'product_storage',
            'id',
            'SET NULL'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаление внешнего ключа и поля original_storage_id из product_storage_history
        $this->dropForeignKey('fk-product_storage_history-original_storage_id', 'product_storage_history');
        $this->dropColumn('product_storage_history', 'original_storage_id');

        // Удаление внешнего ключа и полей из product_defect
        $this->dropForeignKey('fk-product_defect-original_storage_id', 'product_defect');
        $this->dropColumn('product_defect', 'repackaging_reason');
        $this->dropColumn('product_defect', 'original_storage_id');
        $this->dropColumn('product_defect', 'is_repackaging');
    }
}
