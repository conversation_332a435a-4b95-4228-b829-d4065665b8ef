<?php

use yii\db\Migration;

class m251028_063245_add_evro_currency_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->insert('{{%currency}}', [
            'name' => 'Евро',
            'is_main' => false,
            'created_at' => new \yii\db\Expression('CURRENT_TIMESTAMP'),
            'end_date' => '9999-12-31',
        ]);

    }

    
    public function safeDown()
    {
        $this->delete('{{%currency}}', ['name' => 'Евро']);
    }

   
}
