<?php

namespace app\modules\api\services\rawMaterial;

use Yii;
use app\common\models\MaterialDefect;
use app\common\models\MaterialStorage;
use app\common\models\MaterialStorageHistory;
use app\common\models\Material;
use app\common\models\Tracking;
use app\helpers\ActionLogger;
use yii\db\Exception;

/**
 * Сервис для работы с дефектными материалами
 */
class MaterialDefectService
{
    /**
     * Получить список дефектных материалов по дате
     * 
     * @param string $date Дата для фильтрации
     * @return array
     */
    public function getDefectMaterials($date = null)
    {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $query = MaterialDefect::find()
            ->select([
                'material_defect.id',
                'material_defect.created_at',
                'u.username as user_name',
                'm.name as material_name',
                new \yii\db\Expression('
                CASE
                    WHEN material_defect.quantity = FLOOR(material_defect.quantity) THEN CAST(CAST(material_defect.quantity AS INTEGER) AS TEXT)
                    ELSE TRIM(TRAILING \'0\' FROM CAST(material_defect.quantity AS TEXT))
                END as quantity
                '),
                'm.unit_type',
                'material_defect.source',
                'material_defect.description',
                'au.username as accepted_by',
                new \yii\db\Expression('CASE
                    WHEN material_defect.accepted_user_id IS NOT NULL
                    AND material_defect.accepted_at IS NOT NULL
                    THEN true
                    ELSE false
                END as is_accepted'),
                new \yii\db\Expression('CASE
                    WHEN t.accepted_at IS NOT NULL
                    AND t.status = ' . Tracking::STATUS_ACCEPTED . '
                    AND t.deleted_at IS NULL
                    THEN true
                    ELSE false
                END as status')
            ])
            ->leftJoin('material m', 'm.id = material_defect.material_id')
            ->leftJoin('users u', 'u.id = material_defect.add_user_id')
            ->leftJoin('users au', 'au.id = material_defect.accepted_user_id')
            ->leftJoin('tracking t', 't.process_id = material_defect.id AND t.progress_type = ' . Tracking::TYPE_MATERIAL_DEFECT)
            ->where(['material_defect.deleted_at' => null])
            ->andWhere('material_defect.accepted_at is null')
            ->andWhere('material_defect.accepted_user_id is null')
            ->andWhere(['DATE(material_defect.created_at)' => $date])
            ->orderBy(['material_defect.created_at' => SORT_DESC]);

        $defectMaterials = $query->asArray()->all();

        // Добавляем название единицы измерения для каждого материала
        foreach ($defectMaterials as &$material) {
            $material['unit_type_name'] = Material::getUnitTypeName($material['unit_type']);
        }

        return $defectMaterials;
    }

    /**
     * Отправить материалы в брак
     * 
     * @param array $materials Массив материалов с их количествами
     * @param string $description Описание
     * @param int $userId ID пользователя
     * @return array Результат операции
     * @throws Exception
     */
    public function sendToDefect($materials, $description, $userId)
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            $trackingIds = [];
            
            foreach ($materials as $material) {
                // Проверяем наличие материала на складе
                $materialStorages = MaterialStorage::find()
                    ->where(['material_id' => $material['material_id']])
                    ->andWhere(['>=', 'quantity', 0])
                    ->orderBy(['created_at' => SORT_ASC])
                    ->all();

                $totalAvailable = array_sum(array_column($materialStorages, 'quantity'));
                if ($totalAvailable < $material['quantity']) {
                    throw new Exception("Недостаточно материала #{$material['material_id']} на складе");
                }

                // Списываем материал со склада
                $this->deductMaterialFromStorage($materialStorages, $material, $userId);
                
                // Создаем запись о дефектном материале
                $materialDefect = $this->createMaterialDefect($material, $description, $userId);
                
                // Создаем запись трекинга
                $tracking = $this->createTracking($materialDefect->id);
                
                $trackingIds[] = $tracking->id;
            }

            // Логируем действие
            \app\common\models\ActionLogger::actionLog(
                'send_to_defect',
                'material_status',
                null,
                [
                    'tracking_ids' => $trackingIds,
                    'materials' => array_map(function($material) {
                        return [
                            'material_id' => $material['material_id'],
                            'quantity' => $material['quantity']
                        ];
                    }, $materials),
                    'description' => $description
                ]
            );

            $transaction->commit();
            
            return [
                'success' => true,
                'message' => 'Материалы успешно отмечены как брак'
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * Списать материал со склада
     * 
     * @param array $materialStorages
     * @param array $material
     * @param int $userId
     * @throws Exception
     */
    private function deductMaterialFromStorage($materialStorages, $material, $userId)
    {
        $remainingQuantity = $material['quantity'];
        
        foreach ($materialStorages as $storage) {
            if ($remainingQuantity <= 0) break;

            $quantityToDeduct = min($storage->quantity, $remainingQuantity);
            $storage->quantity -= $quantityToDeduct;
            $remainingQuantity -= $quantityToDeduct;

            if (!$storage->save()) {
                throw new Exception('Ошибка обновления склада');
            }

            // Создаем запись в истории склада
            $this->createStorageHistory($storage, $material['material_id'], -$quantityToDeduct, $userId);
        }
    }

    /**
     * Создать запись в истории склада
     * 
     * @param MaterialStorage $storage
     * @param int $materialId
     * @param float $quantity
     * @param int $userId
     * @throws Exception
     */
    private function createStorageHistory($storage, $materialId, $quantity, $userId)
    {
        $storageHistory = new MaterialStorageHistory();
        $storageHistory->material_storage_id = $storage->id;
        $storageHistory->material_id = $materialId;
        $storageHistory->quantity = $quantity;
        $storageHistory->created_at = date('Y-m-d H:i:s');
        $storageHistory->add_user_id = $userId;
        $storageHistory->type = MaterialStorageHistory::TYPE_OUTCOME;

        if (!$storageHistory->save()) {
            throw new Exception('Ошибка сохранения истории склада');
        }
    }

    /**
     * Создать запись о дефектном материале
     * 
     * @param array $material
     * @param string $description
     * @param int $userId
     * @return MaterialDefect
     * @throws Exception
     */
    private function createMaterialDefect($material, $description, $userId)
    {
        $materialDefect = new MaterialDefect();
        $materialDefect->material_id = $material['material_id'];
        $materialDefect->quantity = $material['quantity'];
        $materialDefect->add_user_id = $userId;
        $materialDefect->accepted_user_id = $userId;
        $materialDefect->description = $description;
        $materialDefect->source = MaterialDefect::SOURCE_KEEPER;
        $materialDefect->created_at = date('Y-m-d H:i:s');
        $materialDefect->accepted_at = date('Y-m-d H:i:s');

        if (!$materialDefect->save()) {
            throw new Exception('Ошибка сохранения дефектного материала');
        }

        return $materialDefect;
    }

    /**
     * Создать запись трекинга
     * 
     * @param int $processId
     * @return Tracking
     * @throws Exception
     */
    private function createTracking($processId)
    {
        $tracking = new Tracking();
        $tracking->progress_type = Tracking::TYPE_MATERIAL_DEFECT;
        $tracking->process_id = $processId;
        $tracking->created_at = date('Y-m-d H:i:s');
        $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
        $tracking->accepted_at = null;

        if (!$tracking->save()) {
            throw new Exception('Ошибка сохранения tracking');
        }

        return $tracking;
    }
}
