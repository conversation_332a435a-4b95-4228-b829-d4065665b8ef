<?php

/** @var yii\web\View $this */
/** @var yii\bootstrap5\ActiveForm $form */
/** @var app\models\LoginForm $model */

use yii\bootstrap5\ActiveForm;
use yii\bootstrap5\Html;

$this->title = 'Login';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="site-login">

    <div class="row">
        <div class="col-md-4 offset-md-4">
            <div class="card card-primary">
                <div class="card-header d-flex justify-content-center">
                    <h4><?php echo Yii::t('app','Kirish') ?></h4>
                </div>

                <div class="card-body pt-0">
                    <?php $form = ActiveForm::begin([
                        'id' => 'login-form',
                        'layout' => 'horizontal',
                        'fieldConfig' => [
                            'template' => "{label}\n{input}\n{error}",
                            'labelOptions' => ['class' => 'col-md-12 col-form-label p-0 mb-2'],
                            'inputOptions' => ['class' => 'form-control'],
                            'errorOptions' => ['class' => 'invalid-feedback'],
                        ],
                    ]); ?>
                    <?= $form->field($model, 'username')->textInput(['autofocus' => true])->label(Yii::t('app','Login')) ?>
                    <?= $form->field($model, 'password')->passwordInput()->label(Yii::t('app','Parol')) ?>
                    <div class="mb-3 row">
                        <?= Html::submitButton(Yii::t('app','Kirish'), ['class' => 'btn btn-block btn-primary p-2', 'name' => 'login-button']) ?>
                    </div>

                    <?php ActiveForm::end(); ?>
                </div>
            </div>
        </div>
    </div>
</div>
