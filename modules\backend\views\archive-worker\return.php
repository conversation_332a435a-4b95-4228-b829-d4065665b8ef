<?php
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\ArrayHelper;
use app\modules\backend\models\Position;

/* @var $worker app\modules\backend\models\Worker */
/* @var $salaryModel app\modules\backend\models\WorkerSalary */
?>

<?php $form = ActiveForm::begin([
    'id' => 'worker-return-form',
    'enableAjaxValidation' => false,
]); ?>

<?= Html::hiddenInput('worker_id', $worker->id) ?>

<div class="row">
    <div class="col-md-12">
        <?= $form->field($salaryModel, 'amount')->textInput(['type' => 'number'])->label(Yii::t('app', 'amount')) ?>
    </div>
    <div class="col-md-12">
        <?= $form->field($salaryModel, 'start_date')->input('date')->label(Yii::t('app', 'start_date')) ?>
    </div>
</div>

<?php ActiveForm::end(); ?> 