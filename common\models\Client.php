<?php

namespace app\common\models;

use Yii;
use app\modules\backend\models\Region;

/**
 * This is the model class for table "client".
 *
 * @property int $id
 * @property string $full_name
 * @property string|null $phone_number
 * @property string|null $phone_number_2
 * @property string|null $account_number
 * @property string|null $address
 * @property int|null $region_id
 * @property string|null $created_at
 * @property string|null $deleted_at
 * @property string|null $organization
 * @property string|null $legal_address
 *
 * @property ClientBalanceHistory[] $clientBalanceHistories
 * @property ClientBalance[] $clientBalances
 * @property ClientPayments[] $clientPayments
 * @property Region $region
 * @property Sales[] $sales
 */
class Client extends \yii\db\ActiveRecord
{
    /**
     * Виртуальное свойство для чекбокса возможности возврата
     */
    public $can_return;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'client';
    }


    public const SCENARIO_CREATE = 'create';
    public const SCENARIO_UPDATE = 'update';


    public function rules()
    {
        return [
            [['full_name', 'region_id'], 'required', 'on' => self::SCENARIO_CREATE],
            [['phone_number', 'phone_number_2'], 'validatePhoneNumber'],
            [['full_name'], 'required'],
            [['address', 'organization', 'legal_address'], 'string'],
            [['region_id'], 'default', 'value' => null],
            [['region_id'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            [['full_name', 'account_number', 'phone_number', 'phone_number_2'], 'string', 'max' => 255],
            [['region_id'], 'exist', 'skipOnError' => true, 'targetClass' => Region::class, 'targetAttribute' => ['region_id' => 'id']],
            [['can_return'], 'boolean'],
        ];
    }

        public function validatePhoneNumber($attribute, $params, $validator)
            {
                $value = $this->$attribute;
                $numericValue = preg_replace('/\D/', '', $value);

                if (strlen($numericValue) !== 9) {
                    $this->addError($attribute, Yii::t('app', 'phone_number_error_length'));
                    return;
                }

                $companyCodes = ['90', '91', '93', '94', '88', '55', '87', '95', '97', '98', '99', '20', '33', '77', '50'];

                $operatorCode = substr($numericValue, 0, 2);

                if (!in_array($operatorCode, $companyCodes)) {
                    $this->addError($attribute, Yii::t('app', "phone_number_error"));
                }
            }

            public function scenarios()
            {
                return [
                    self::SCENARIO_CREATE => ['full_name', 'phone_number_2', 'account_number', 'address', 'region_id', 'organization', 'legal_address', 'can_return'],
                    self::SCENARIO_UPDATE => ['full_name', 'phone_number', 'phone_number_2', 'account_number', 'address', 'region_id', 'organization', 'legal_address', 'can_return'],
                ];
            }

            /**
             * {@inheritdoc}
             */
            public function attributeLabels()
            {
                return [
                    'id' => 'ID',
                    'full_name' => Yii::t('app', 'client_name'),
                    'phone_number' => Yii::t('app', 'phone_number'),
                    'phone_number_2' => Yii::t('app', 'phone_number_2'),
                    'account_number' => Yii::t('app', 'account_number'),
                    'address' => Yii::t('app', 'address'),
                    'region_id' => Yii::t('app', 'client_region_id'),
                    'created_at' => Yii::t('app', 'created_at'),
                    'deleted_at' => Yii::t('app', 'deleted_at'),
                    'organization' => Yii::t('app', 'organization'),
                    'legal_address' => Yii::t('app', 'legal_address'),
                    'can_return' => Yii::t('app', 'can_return'),
                ];
            }

            /**
             * Gets query for [[ClientBalanceHistories]].
             *
             * @return \yii\db\ActiveQuery
             */
            public function getClientBalanceHistories()
            {
                return $this->hasMany(ClientBalanceHistory::class, ['client_id' => 'id']);
            }

            /**
             * Gets query for [[ClientBalances]].
             *
             * @return \yii\db\ActiveQuery
             */
            public function getClientBalances()
            {
                return $this->hasMany(ClientBalance::class, ['client_id' => 'id']);
            }

            /**
             * Gets query for [[ClientPayments]].
             *
             * @return \yii\db\ActiveQuery
             */
            public function getClientPayments()
            {
                return $this->hasMany(ClientPayments::class, ['client_id' => 'id']);
            }

            /**
             * Gets query for [[Region]].
             *
             * @return \yii\db\ActiveQuery
             */
            public function getRegion()
            {
                return $this->hasOne(Region::class, ['id' => 'region_id']);
            }

            /**
             * Gets query for [[Sales]].
             *
             * @return \yii\db\ActiveQuery
             */
            public function getSales()
            {
                return $this->hasMany(Sales::class, ['client_id' => 'id']);
            }

            public function getContract()
            {
                return $this->hasOne(ClientContract::class, ['client_id' => 'id']);
            }

            /**
             * Gets query for [[ClientReturn]].
             *
             * @return \yii\db\ActiveQuery
             */
            public function getClientReturn()
            {
                return $this->hasOne(ClientReturn::class, ['client_id' => 'id'])->andWhere(['deleted_at' => null]);
            }

            /**
             * Проверяет, может ли клиент делать возврат
             *
             * @return bool
             */
            public function getCanReturn()
            {
                return $this->getClientReturn()->exists();
            }

            /**
             * Устанавливает возможность возврата для клиента
             *
             * @param bool $value
             * @return bool
             */
            public function setCanReturn($value)
            {
                if ($value) {
                    // Если клиент должен иметь возможность возврата
                    $clientReturn = ClientReturn::findOne(['client_id' => $this->id, 'deleted_at' => null]);
                    if (!$clientReturn) {
                        // Если записи нет, создаем новую
                        $clientReturn = new ClientReturn();
                        $clientReturn->client_id = $this->id;
                        $clientReturn->created_at = date('Y-m-d H:i:s');
                        return $clientReturn->save();
                    }
                    return true;
                } else {
                    // Если клиент не должен иметь возможность возврата
                    $clientReturn = ClientReturn::findOne(['client_id' => $this->id, 'deleted_at' => null]);
                    if ($clientReturn) {
                        // Если запись есть, помечаем ее как удаленную
                        $clientReturn->deleted_at = date('Y-m-d H:i:s');
                        return $clientReturn->save();
                    }
                    return true;
                }
            }
        }
