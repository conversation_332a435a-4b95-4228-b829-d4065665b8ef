<?php

use yii\db\Migration;

class m250501_170745_add_heawer_role_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {

        $auth = Yii::$app->authManager;

        $adminRole = $auth->createRole('heawer');
        $adminRole->description = 'Heawer';
        $auth->add($adminRole);

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $auth = Yii::$app->authManager;
        $auth->remove($auth->getRole('heawer'));
    }
}
