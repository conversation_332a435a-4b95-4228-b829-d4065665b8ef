<?php

namespace app\common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;

/**
 * This is the model class for table "sales_return".
 *
 * @property int $id
 * @property int $sale_id
 * @property int $product_id
 * @property float $quantity
 * @property float $unit_price
 * @property float $total_price
 * @property int $status
 * @property string|null $comment
 * @property string $created_at
 * @property string $updated_at
 * @property string|null $deleted_at
 *
 * @property Product $product
 * @property Sales $sale
 */
class SalesReturn extends \yii\db\ActiveRecord
{
    const STATUS_PENDING = 0;
    const STATUS_APPROVED = 1;
    const STATUS_REJECTED = 2;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%sales_return}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => new Expression('NOW()'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sale_id', 'product_id', 'quantity', 'unit_price', 'total_price'], 'required'],
            [['sale_id', 'product_id', 'status'], 'integer'],
            [['quantity', 'unit_price', 'total_price'], 'number'],
            [['comment'], 'string'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['sale_id'], 'exist', 'skipOnError' => true, 'targetClass' => Sales::class, 'targetAttribute' => ['sale_id' => 'id']],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sale_id' => 'ID продажи',
            'product_id' => 'ID продукта',
            'quantity' => 'Количество',
            'unit_price' => 'Цена за единицу',
            'total_price' => 'Общая сумма',
            'status' => 'Статус',
            'comment' => 'Комментарий',
            'created_at' => 'Дата создания',
            'updated_at' => 'Дата обновления',
            'deleted_at' => 'Дата удаления',
        ];
    }

    /**
     * Gets query for [[Product]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }

    /**
     * Gets query for [[Sale]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSale()
    {
        return $this->hasOne(Sales::class, ['id' => 'sale_id']);
    }
}
