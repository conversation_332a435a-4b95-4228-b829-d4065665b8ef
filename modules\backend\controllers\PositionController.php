<?php

namespace app\modules\backend\controllers;

use Yii;
use app\modules\backend\models\Position;
use yii\data\ArrayDataProvider;
use yii\web\Response;

/**
 * PositionController implements the CRUD actions for Position model.
 */
class PositionController extends BaseController
{
    

    public function actionIndex()
    {
        $positions = Position::find()->all();

        $dataProvider = new ArrayDataProvider([
            'allModels' => $positions,
            'pagination' => [
                'pageSize' => 50,
            ],
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }



    public function actionCreate()
    {
        try {
            Yii::$app->response->format = Response::FORMAT_JSON;

            if (Yii::$app->request->isPost) {
                $model = new Position();
                $model->load(Yii::$app->request->post());
                if ($model->validate()) {
                    $model->save();

                    return [
                        'message' => 'Position created successfully.',
                        'data' => $model->attributes,
                    ];
                } else {
                    \Sentry\captureMessage('Position validation failed: ' . json_encode($model->getErrors()));
                    return [
                        'status' => 'fail',
                        'errors' => $model->getErrors(),
                    ];
                }
            } else if (Yii::$app->request->isGet) {
                $model = new Position();
                return [
                    "status" => true,
                    "content" => $this->renderPartial('create', ['model' => $model])
                ];
            }
        } catch (\Exception $e) {
            \Sentry\captureException($e);
            throw $e;
        }
    }

    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $model = Position::find()->where(['id' => $postData['Position']['id']])->one();
            $oldData = $model->attributes;
            if (!$model) {
                return ['status' => 'error', 'message' => 'Position not found.'];
            }

            $model->load($postData);

            if (isset($postData['Position']['status'])) {
                if ($postData['Position']['status'] == 1) {
                    $model->deleted_at = null;
                } else {
                    $model->deleted_at = date('Y-m-d H:i:s');
                }
            }

            if ($model->save()) {

                return [
                    'status' => 'success',
                    'message' => 'Position updated successfully.',
                ];
            } else {
                return [
                    'status' => 'fail',
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Position::find()->where(['id' => $id])->one();
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => 'Position not found.',
                ];
            } else {
                return [
                    "status" => true,
                    "content" => $this->renderPartial('update', ['model' => $model]),
                ];
            }
        }
    }

    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $model = Position::findOne($postData['Position']['id']);
            if (!$model) {
                return ['status' => 'error', 'message' => 'Position not found.'];
            }

            $model->deleted_at = date('Y-m-d H:i:s');
            if ($model->save()) {
                return [
                    'status' => 'success',
                    'message' => 'Position deleted successfully.',
                ];
            } else {
                return [
                    'status' => 'fail',
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $position_id = Yii::$app->request->get('id');
            $model = Position::findOne($position_id);
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => 'Position not found.',
                ];
            }

            return [
                "status" => 'success',
                "content" => $this->renderPartial('delete', ['model' => $model]),
            ];
        }

        return ['status' => 'error', 'message' => 'Invalid request method.'];
    }
}