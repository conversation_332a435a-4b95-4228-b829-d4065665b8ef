<?php

use app\modules\backend\models\Expenses;
use yii\bootstrap5\Html;
use app\common\models\Tracking;

if($payments): ?>
    <div class="table-responsive">
        <table id="supplier-payment-grid-view" class="table table-bordered table-striped compact">
            <thead>
                <tr>
                    <th><?= Yii::t("app", "supplier_name") ?></th>
                    <th><?= Yii::t("app", "created_at") ?></th>
                    <th><?= Yii::t("app", "who_entered") ?></th>
                    <th><?= Yii::t("app", "payment_type") ?></th>
                    <th><?= Yii::t("app", "amount") ?></th>
                    <th><?= Yii::t("app", "status") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($payments as $payment): ?>
                    <tr>
                        <td><?= Html::encode($payment['supplier_name']) ?></td>
                        <td><?= Html::encode(date('d.m.Y H:i', strtotime($payment['created_at']))) ?></td>
                        <td><?= Html::encode($payment['user_name']) ?></td>
                        <td><?= Html::encode(Expenses::getTypePayment($payment['type'])) ?></td>
                        <td><?= Html::encode($payment['amount']) ?></td>
                        <td>
                            <span class="<?= $payment['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?php
                                $statusText = $payment['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                echo $statusText;
                                ?>
                            </span>
                        </td>
                        <td>
                            <?php 
                            $tracking = Tracking::find()
                                ->where(['process_id' => $payment['expense_id']])
                                ->andWhere(['progress_type' => Tracking::PAY_FOR_SUPPLIER])
                                ->andWhere(['is', 'accepted_at', null])
                                ->andWhere(['is', 'deleted_at', null])
                                ->one();
                            
                            $showButton = $tracking !== null;
                            ?>
                            <?php if ($showButton): ?>
                                <?php if ($payment['deleted_at'] == NULL): ?>
                                    <div class="dropdown d-inline">
                                        <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                            <?php echo Yii::t("app", "detail"); ?>
                                        </a>
                                        <div class="dropdown-menu">
                                        
                                                <a href="#" class="dropdown-item supplier-payment-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($payment['id']) ?>">
                                                    <span class="red-text"><?= Yii::t("app", "delete") ?></span>
                                                </a>
                                        
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else: ?>
    <div>
        <p class="mb-0"><?= Yii::t('app', 'no_data_available') ?></p>
    </div>
<?php endif; ?>
