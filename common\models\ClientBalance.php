<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "client_balance".
 *
 * @property int $id
 * @property int $client_id
 * @property float $amount
 * @property string|null $updated_at
 *
 * @property Client $client
 */
class ClientBalance extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'client_balance';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['client_id', 'amount'], 'required'],
            [['client_id'], 'default', 'value' => null],
            [['client_id'], 'integer'],
            [['amount'], 'number'],
            [['updated_at'], 'safe'],
            [['client_id'], 'exist', 'skipOnError' => true, 'targetClass' => Client::class, 'targetAttribute' => ['client_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'client_id' => 'Client ID',
            'amount' => 'Amount',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Client]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClient()
    {
        return $this->hasOne(Client::class, ['id' => 'client_id']);
    }
}
