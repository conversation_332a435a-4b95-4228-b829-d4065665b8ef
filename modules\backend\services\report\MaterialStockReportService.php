<?php

namespace app\modules\backend\services\report;

use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * Сервис для формирования отчетов по остаткам сырья (материалов)
 */
class MaterialStockReportService
{    public function getMaterialStockReport($materialId = null)
    {
        $stockData = $this->getMaterialStockData($materialId);
        
        // Объединяем данные по материалам
        $combinedData = [];
        foreach ($stockData as $stock) {
            $combinedData[$stock['material_id']] = [
                'material_id' => $stock['material_id'],
                'material_name' => $stock['material_name'] ?: 'Неопределенный материал',
                'unit' => $this->getUnitName($stock['unit']),
                'quantity' => number_format((float)$stock['quantity'], 2, '.', ''),
                'defect_quantity' => 0
            ];
        }
        
        $result = [
            'items' => array_values($combinedData),
        ];
        
        return $result;
    }

    /**
     * Получить название единицы измерения
     * 
     * @param int $unitType
     * @return string
     */
    protected function getUnitName($unitType)
    {
        switch ($unitType) {
            case 1:
                return 'шт';
            case 2:
                return 'кг';
            case 3:
                return 'л';
            case 4:
                return 'м';
            default:
                return 'шт';
        }
    }/**
     * Получить данные по остаткам материалов
     * 
     * @param int|null $materialId
     * @return array
     */
    protected function getMaterialStockData($materialId = null)
    {
        $query = new Query();
        $query->select([
                'm.id as material_id',
                'm.name as material_name',
                'm.unit_type as unit',
                'SUM(ms.quantity) as quantity'
            ])
            ->from(['ms' => 'material_storage'])
            ->leftJoin(['m' => 'material'], 'ms.material_id = m.id')
            ->andWhere(['IS', 'ms.deleted_at', null])
            ->andWhere(['IS', 'm.deleted_at', null]);
            
        if ($materialId) {
            $query->andWhere(['ms.material_id' => $materialId]);
        }
        
        $query->groupBy(['m.id', 'm.name', 'm.unit_type'])
              ->orderBy(['m.name' => SORT_ASC]);
        
        $result = $query->all();

        return $result;
    }
    
   

    
   
}