<?php
/* @var $model app\models\Worker */
/* @var $currentSalary app\models\WorkerSalary */
/* @var $salaryHistory array */
use yii\helpers\Html;
?>

<div class="worker-view">
    <!-- Информация о работнике -->
    <div class="card-body">
        <h6 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'worker_info') ?></h6>

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th><?= Yii::t('app', 'full_name') ?></th>
                    <th><?= Yii::t('app', 'phone_number') ?></th>
                    <?php if ($model->phone_number_2): ?>
                        <th><?= Yii::t('app', 'phone_number_2') ?></th>
                    <?php endif; ?>
                    <th><?= Yii::t('app', 'address') ?></th>
                    <th><?= Yii::t('app', 'position') ?></th>
                    <th><?= Yii::t('app', 'created_at') ?></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><?= Html::encode($model->full_name) ?></td>
                    <td><?= Html::encode($model->phone_number) ?></td>
                    <?php if ($model->phone_number_2): ?>
                        <td><?= Html::encode($model->phone_number_2) ?></td>
                    <?php endif; ?>
                    <td><?= Html::encode($model->address) ?></td>
                    <td><?= Html::encode($model->position ? $model->position->name : '') ?></td>
                    <td><?= date("d.m.Y", strtotime($model->created_at)) ?></td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Текущая зарплата -->
    <div class="card-body">
        <h6 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'current_salary_info') ?></h6>

        <?php if ($currentSalary): ?>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th><?= Yii::t('app', 'amount') ?></th>
                        <th><?= Yii::t('app', 'start_date') ?></th>
                        <th><?= Yii::t('app', 'created_at') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><?= number_format($currentSalary->amount, 0, '.', ' ') ?></td>
                        <td><?= date("d.m.Y", strtotime($currentSalary->start_date)) ?></td>
                        <td><?= date("d.m.Y", strtotime($currentSalary->created_at)) ?></td>
                    </tr>
                </tbody>
            </table>
        <?php else: ?>
            <div class="alert alert-info">
                <?= Yii::t('app', 'no_salary_info') ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- История зарплаты -->
    <div class="card-body">
        <h6 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'salary_history') ?></h6>

        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th><?= Yii::t('app', 'salary') ?></th>
                        <th><?= Yii::t('app', 'start_date') ?></th>
                        <th><?= Yii::t('app', 'end_date') ?></th>
                        <th><?= Yii::t('app', 'created_at') ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($salaryHistory as $index => $salary): ?>
                    <tr>
                        <td><?= $index + 1 ?></td>
                        <td><?= number_format($salary->amount, 0, '.', ' ') ?></td>
                        <td><?= date("d.m.Y", strtotime($salary->start_date)) ?></td>
                        <td><?= $salary->end_date === '9999-12-31' ? Yii::t('app', 'current') : date("d.m.Y", strtotime($salary->end_date)) ?></td>
                        <td><?= date("d.m.Y", strtotime($salary->created_at)) ?></td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- История выплат зарплаты -->
    <div class="card-body">
        <h6 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'salary_payments_history') ?></h6>

        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th><?= Yii::t('app', 'month') ?></th>
                        <th><?= Yii::t('app', 'amount') ?></th>
                        <th><?= Yii::t('app', 'payment_date') ?></th>
                        <th><?= Yii::t('app', 'description') ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php if (!empty($salaryPayments)): ?>
                    <?php foreach ($salaryPayments as $index => $payment): ?>
                        <tr>
                            <td><?= $index + 1 ?></td>
                            <td><?= $payment->month ?></td>
                            <td><?= number_format($payment->amount, 0, '.', ' ') ?></td>
                            <td><?= date("d.m.Y", strtotime($payment->created_at)) ?></td>
                            <td><?= Html::encode($payment->description) ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="5" class="text-center"><?= Yii::t('app', 'no_salary_payments') ?></td>
                    </tr>
                <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>