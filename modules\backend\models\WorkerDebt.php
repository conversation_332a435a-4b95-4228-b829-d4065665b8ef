<?php

namespace app\modules\backend\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "worker_debt".
 *
 * @property int $id
 * @property int $worker_id
 * @property float $amount
 * @property string|null $reason
 * @property bool|null $status
 * @property string|null $due_date
 * @property string $created_at
 * @property string|null $paid_at
 * @property string|null $deleted_at
 *
 * @property Worker $worker
 */
class WorkerDebt extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'worker_debt';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['worker_id', 'amount'], 'required'],
            [['worker_id'], 'integer'],
            [['amount'], 'number'],
            [['reason'], 'string'],
            [['status'], 'boolean'],
            [['due_date', 'created_at', 'paid_at', 'deleted_at'], 'safe'],
            [['worker_id'], 'exist', 'skipOnError' => true, 'targetClass' => Worker::class, 'targetAttribute' => ['worker_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'worker_id' => 'Работник',
            'amount' => 'Сумма',
            'reason' => 'Причина',
            'status' => 'Статус',
            'due_date' => 'Срок погашения',
            'created_at' => 'Дата создания',
            'paid_at' => 'Дата погашения',
            'deleted_at' => 'Дата удаления',
        ];
    }

    /**
     * Gets query for [[Worker]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWorker()
    {
        return $this->hasOne(Worker::class, ['id' => 'worker_id']);
    }
}