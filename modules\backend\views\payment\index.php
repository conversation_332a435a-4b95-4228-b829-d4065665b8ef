<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\assets\Select2Asset;
use app\modules\backend\models\Expenses;
use app\common\models\Tracking;

DataTablesAsset::register($this);
Select2Asset::register($this);

$this->title = Yii::t("app", "payments_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$selectDatesMessage = Yii::t("app", "Please select both start and end dates");

// Get payment types translations
$paymentTypes = [
    Expenses::CASH => Html::encode(Expenses::getTypePayment(Expenses::CASH)),
    Expenses::CARD => Html::encode(Expenses::getTypePayment(Expenses::CARD)),
    Expenses::TRANSFER => Html::encode(Expenses::getTypePayment(Expenses::TRANSFER)),
];

// Prepare payment types for JavaScript
$paymentTypesJS = [];
foreach ($paymentTypes as $key => $value) {
    $paymentTypesJS[] = $key . ': ' . json_encode($value);
}
$paymentTypesString = implode(",\n        ", $paymentTypesJS);
$activeText = Yii::t("app", "active");
$inactiveText = Yii::t("app", "inactive");
?>


<style>
    /* Скрываем select до инициализации Select2 */
    select.select2-hidden-accessible {
        display: none;
    }

    /* Стили для конкретного select2 */
    #client_filter.select2 {
        min-width: 150px !important;
        width: 100% !important;
    }

    /* Стили для контейнера Select2 (если используется библиотека Select2) */
    #client_filter + .select2-container {
        width: 150px !important;
    }

    /* Стили для полей ввода даты */
    input[type="date"].form-control {
        width: 150px;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }
</style>


<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6">
            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('product_keeper')): ?>
                <div class="d-flex justify-content-end align-items-center gap-2">
                    <!-- Date inputs -->
                    <div class="d-flex gap-2" style="min-width: 200px;">
                        <input type="date" id="date_from" class="form-control" placeholder="<?= Yii::t('app', 'From Date') ?>">
                        <input type="date" id="date_to" class="form-control" placeholder="<?= Yii::t('app', 'To Date') ?>">
                    </div>

                    <!-- Worker filter dropdown -->
                    <select id="client_filter" class="form-control select2">
                        <option value=""><?= Yii::t('app', 'clients') ?></option>
                        <?php foreach($clients as $client): ?>
                            <option value="<?= $client->id ?>"><?= $client->full_name ?></option>
                        <?php endforeach; ?>
                    </select>

                    <!-- Filter button -->
                    <button type="button" class="btn btn-primary" id="search-button">
                        <?= Yii::t('app', 'search') ?>
                    </button>

                </div>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'payment-grid-pjax']); ?>
    <?= $this->render('_index', ['result' => $result]); ?>
    <?php Pjax::end(); ?>
</div>


<div id="one" data-text="<?= Yii::t("app", "payment_delete") ?>"></div>
<div id="update-title" data-text="<?= Yii::t("app", "payment_update") ?>"></div>


<?php
$js = <<<JS
(function($) {
    var one = $("#one").data("text");
    var updateTitle = $("#update-title").data("text");
    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";
    var selectDatesMessage = "{$selectDatesMessage}";
    var activeText = "{$activeText}";
    var inactiveText = "{$inactiveText}";

    var paymentTypes = {
        {$paymentTypesString}
    };

    // Инициализация Select2
    function initializeSelect2() {
        $('#client_filter').select2({
            width: '150px',
            minimumResultsForSearch: 5,
            dropdownAutoWidth: true
        }).on('select2:open', function() {
            setTimeout(function() {
                $('.select2-dropdown').css('min-width', '150px');
            }, 0);
        });
    }

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#payment-grid-view')) {
            $('#payment-grid-view').DataTable().destroy();
        }

        $('#payment-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[1, 'desc']],
            "columnDefs": [
                {
                    "targets": [5],
                    "orderable": false
                },
                {
                    "targets": 1,
                    "type": "date-eu",
                    "render": function(data, type, row) {
                        if (type === 'sort') {
                            var parts = data.split(' ');
                            var dateParts = parts[0].split('.');
                            return dateParts[2] + dateParts[1] + dateParts[0] + parts[1].replace(':', '');
                        }
                        return data;
                    }
                }
            ]
        });
    }

    $(document).off('pjax:end').on('pjax:end', function() {
        initializeSelect2();
        initializeDataTable();
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSearch() {
    $('#search-button').on('click', function () {
        var startDate = $('#date_from').val();
        var endDate = $('#date_to').val();
        var clientId = $('#client_filter').val();

        $.ajax({
            url: '/backend/payment/search',
            type: 'POST',
            data: {
                start_date: startDate || null,
                end_date: endDate || null,
                client_id: clientId || null
            },
            success: function (response) {
                $('#payment-grid-pjax').html(response);
                initializeSelect2();
                initializeDataTable();
                initializeDropdown();
            },
            error: function (xhr, status, error) {
                console.error('Search error:', error);
                iziToast.error({
                    timeout: 5000,
                    icon: 'fas fa-exclamation-triangle',
                    message: '<?= Yii::t("app", "An error occurred while searching") ?>',
                    position: 'topRight',
                    onOpening: function (instance, toast) {
                        toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                    }
                });
            }
        });
    });
}


    function initializePaymentDelete() {
        $(document).off('click.payment-delete').on('click.payment-delete', '.payment-delete', function() {
            var id = $(this).attr("data-id");
            $('#ideal-mini-modal-delete .mini-button').removeClass("btn-primary");
            $.ajax({
                url: '/backend/payment/delete',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal-delete .modal-title').html(one);
                        $('#ideal-mini-modal-delete .modal-body').html(response.content);
                        $('#ideal-mini-modal-delete .mini-button').addClass("btn-danger delete-payment-button");
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.delete-payment-button').on('click.delete-payment-button', '.delete-payment-button', function() {
            if (!$(this).prop('disabled')) {
                $('.delete-payment-button').prop('disabled', true);
                var formData = $('#payment-delete-form').serialize();
                $.ajax({
                    url: '/backend/payment/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.status === 'success') {
                            $.pjax.reload({ container: '#payment-grid-pjax' });
                            $('.delete-payment-button').prop('disabled', false);
                            $('.close').trigger('click');
                        } else if (response.status === 'error') {
                            $('#ideal-mini-modal-delete .modal-body').html(response.content);
                            iziToast.error({
                                timeout: 5000,
                                icon: 'fas fa-exclamation-triangle',
                                message: response.message,
                                position: 'topRight',
                                onOpening: function(instance, toast) {
                                    toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                                }
                            });
                            $('.delete-payment-button').prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        $('.delete-payment-button').prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    initializeSelect2();
    initializeDataTable();
    initializeDropdown();
    initializeSearch();
    initializePaymentDelete();
    initializePaymentUpdate();

    function initializePaymentUpdate() {
        $(document).off('click.payment-update').on('click.payment-update', '.payment-update', function() {
            var id = $(this).attr("data-id");
            $('#ideal-mini-modal .mini-button').removeClass("btn-danger");
            $.ajax({
                url: '/backend/client/update-payment',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal .modal-title').html(updateTitle);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').addClass("btn-primary update-payment-button");

                        // Инициализация Select2 для формы обновления
                        $('#ideal-mini-modal select.select2').select2({
                            dropdownParent: $('#ideal-mini-modal'),
                            width: '100%'
                        });

                        // Инициализация форматирования числовых полей
                        $('.formatted-numeric-input').on('input', function() {
                            var value = $(this).val().replace(/\s+/g, '');
                            var formattedValue = value.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
                            $(this).val(formattedValue);
                        });
                    } else if (response.status === 'error') {
                        iziToast.error({
                            timeout: 5000,
                            icon: 'fas fa-exclamation-triangle',
                            message: response.message,
                            position: 'topRight',
                            onOpening: function(instance, toast) {
                                toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                            }
                        });
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.update-payment-button').on('click.update-payment-button', '.update-payment-button', function() {
            if (!$(this).prop('disabled')) {
                $('.update-payment-button').prop('disabled', true);
                var formData = $('#client-payment-update-form').serialize();
                $.ajax({
                    url: '/backend/client/update-payment',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.status === 'success') {
                            $.pjax.reload({ container: '#payment-grid-pjax' });
                            $('.update-payment-button').prop('disabled', false);
                            $('.close').trigger('click');

                            iziToast.success({
                                timeout: 5000,
                                icon: 'fas fa-check-circle',
                                message: response.message,
                                position: 'topRight',
                                onOpening: function(instance, toast) {
                                    toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                                }
                            });
                        } else if (response.status === 'error') {
                            if (response.errors) {
                                // Отображаем ошибки валидации
                                $.each(response.errors, function(field, errors) {
                                    var errorContainer = $('#' + field.toLowerCase() + '-error');
                                    errorContainer.html(errors.join('<br>'));
                                });
                            } else {
                                iziToast.error({
                                    timeout: 5000,
                                    icon: 'fas fa-exclamation-triangle',
                                    message: response.message,
                                    position: 'topRight',
                                    onOpening: function(instance, toast) {
                                        toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                                    }
                                });
                            }
                            $('.update-payment-button').prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        $('.update-payment-button').prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>

