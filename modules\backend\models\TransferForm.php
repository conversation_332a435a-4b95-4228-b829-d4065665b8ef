<?php

namespace app\modules\backend\models;

use app\common\models\Cashbox;
use app\common\models\CashboxDetail;
use app\common\models\CurrencyCourse;
use app\common\models\Tracking;
use Yii;
use yii\base\Model;

class TransferForm extends Model
{
    public $fromType;
    public $toType;
    public $amount;

    public function rules()
    {
        return [
            [['fromType', 'toType', 'amount'], 'required'],
            ['amount', 'number', 'min' => 0.01],
            ['fromType', 'compare', 'compareAttribute' => 'toType', 'operator' => '!==', 'message' => Yii::t('app', 'fromType and toType should be different')],
        ];
    }

    public function attributeLabels()
    {
        return [
            'fromType' => Yii::t('app', 'from_type'),
            'toType' => Yii::t('app', 'to_type'),
            'amount' => Yii::t('app', 'amount'),
        ];
    }

    public function transfer()
    {
        $fromCashbox = Cashbox::find()
            ->where(['id' => $this->fromType, 'deleted_at' => null])
            ->one();
        
        $toCashbox = Cashbox::find()
            ->where(['id' => $this->toType, 'deleted_at' => null])
            ->one();

        if (!$fromCashbox || !$toCashbox) {
            $this->addError('amount', Yii::t('app', 'One of the cashboxes was not found.'));
            return false;
        }

        if ($fromCashbox->balance < $this->amount) {
            $this->addError('amount', Yii::t('app', 'Not enough funds on the source cashbox.'));
            return false;
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Определяем валюты касс
            $fromCurrency = $fromCashbox->currency_id;
            $toCurrency = $toCashbox->currency_id;
            
            $amountToAdd = $this->amount;
            $conversionInfo = null;
            
            // Если валюты разные, выполняем конвертацию
            if ($fromCurrency != $toCurrency) {
                try {
                    $conversionInfo = CurrencyCourse::convertCurrency($this->amount, $fromCurrency, $toCurrency);
                    $amountToAdd = $conversionInfo['amount'];
                } catch (\Exception $e) {
                    $this->addError('amount', 'Ошибка конвертации валют: ' . $e->getMessage());
                    return false;
                }
            }

            // Списываем из исходной кассы
            $fromCashbox->balance -= $this->amount;
            if (!$fromCashbox->save(false)) {
                throw new \Exception('Ошибка при сохранении исходной кассы: ' . json_encode($fromCashbox->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            // Добавляем в целевую кассу (с конвертацией если нужно)
            $toCashbox->balance += $amountToAdd;
            if (!$toCashbox->save(false)) {
                throw new \Exception('Ошибка при сохранении целевой кассы: ' . json_encode($toCashbox->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            // Создаем запись расхода (из исходной кассы)
            $cashboxDetailOut = new CashboxDetail();
            $cashboxDetailOut->cashbox_id = $fromCashbox->id;
            $cashboxDetailOut->add_user_id = Yii::$app->user->id;
            $cashboxDetailOut->amount = $this->amount;
            $cashboxDetailOut->created_at = date('Y-m-d H:i:s');
            $cashboxDetailOut->type = CashboxDetail::TYPE_OUT;

            if (!$cashboxDetailOut->save()) {
                throw new \Exception('Ошибка при сохранении деталей перевода (расход): ' . json_encode($cashboxDetailOut->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            // Создаем запись прихода (в целевую кассу)
            $cashboxDetailIn = new CashboxDetail();
            $cashboxDetailIn->cashbox_id = $toCashbox->id;
            $cashboxDetailIn->add_user_id = Yii::$app->user->id;
            $cashboxDetailIn->amount = $amountToAdd; // Сумма с учетом конвертации
            $cashboxDetailIn->created_at = date('Y-m-d H:i:s');
            $cashboxDetailIn->type = CashboxDetail::TYPE_IN;

            if (!$cashboxDetailIn->save()) {
                throw new \Exception('Ошибка при сохранении деталей перевода (приход): ' . json_encode($cashboxDetailIn->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            $tracking = new Tracking();
            $tracking->progress_type = Tracking::TYPE_TRANSFER_CASHBOX;
            $tracking->process_id = $cashboxDetailOut->id;
            $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
            $tracking->accepted_at = null;
            $tracking->created_at = date('Y-m-d H:i:s');

            if (!$tracking->save()) {
                throw new \Exception('Ошибка при сохранении отслеживания: ' . json_encode($tracking->getErrors(), JSON_UNESCAPED_UNICODE));
            }

            $transaction->commit();
            return true;
        } catch (\Exception $e) {
            $transaction->rollBack();
            $this->addError('amount', 'Ошибка при переводе: ' . $e->getMessage());
            return false;
        }
    }


    public function beforeValidate()
    {
        $attributes = ['amount'];

        foreach ($attributes as $attribute) {
            if (is_string($this->$attribute) && !empty($this->$attribute)) {
                $originalValue = $this->$attribute;
                
                $cleanValue = preg_replace('/[\s\x{A0}]+/u', '', $originalValue);
                $cleanValue = str_replace(',', '.', $cleanValue);
                
                if (is_numeric($cleanValue)) {
                    $this->$attribute = floatval($cleanValue);
                }
            }
        }
        
        return parent::beforeValidate();
    }
}