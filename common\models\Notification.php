<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;

/**
 * Модель для работы с уведомлениями
 *
 * @property int $id ID уведомления
 * @property string|null $group_id ID группы уведомлений для синхронизации статуса прочтения
 * @property string|null $role Роль пользователей, которым адресовано уведомление
 * @property string $title Заголовок уведомления
 * @property string $body Текст уведомления
 * @property string|null $data JSON с дополнительными данными
 * @property bool $is_sent Флаг отправки через Firebase
 * @property string $created_at Дата создания
 * @property string|null $updated_at Дата обновления
 *
 * @property-read NotificationReadStatus[] $readStatuses
 */
class Notification extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%system_notification}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new Expression('NOW()'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'body'], 'required'],
            [['body', 'data'], 'string'],
            [['is_sent'], 'boolean'],
            [['created_at', 'updated_at'], 'safe'],
            [['group_id', 'role'], 'string', 'max' => 50],
            [['title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'group_id' => 'Группа',
            'role' => 'Роль',
            'title' => 'Заголовок',
            'body' => 'Текст',
            'data' => 'Данные',
            'is_sent' => 'Отправлено',
            'created_at' => 'Дата создания',
            'updated_at' => 'Дата обновления',
        ];
    }

    /**
     * Получить связанные статусы прочтения
     *
     * @return \yii\db\ActiveQuery
     */
    public function getReadStatuses()
    {
        return $this->hasMany(NotificationReadStatus::class, ['notification_id' => 'id']);
    }

    /**
     * Создать новое уведомление для роли
     *
     * @param string $title Заголовок уведомления
     * @param string $body Текст уведомления
     * @param array|null $data Дополнительные данные
     * @param string|null $role Роль пользователей
     * @param string|null $group_id ID группы уведомлений (если null, будет сгенерирован автоматически)
     * @return Notification|null
     */
    public static function createForRole($title, $body, $data = null, $role = null, $group_id = null)
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            // Если group_id не указан, генерируем его
            if ($group_id === null) {
                $group_id = 'group_' . uniqid();
            }

            $notification = new self();
            $notification->title = $title;
            $notification->body = $body;
            $notification->data = $data ? json_encode($data) : null;
            $notification->role = $role;
            $notification->group_id = $group_id;
            $notification->is_sent = false;

            if (!$notification->save()) {
                throw new \Exception('Ошибка при создании уведомления: ' . json_encode($notification->getErrors()));
            }

            // Если указана роль, создаем записи статусов прочтения для всех пользователей с этой ролью
            if ($role !== null) {
                $userIds = self::getUserIdsByRole($role);

                foreach ($userIds as $userId) {
                    $status = new NotificationReadStatus();
                    $status->notification_id = $notification->id;
                    $status->user_id = $userId;
                    $status->is_read = false;

                    if (!$status->save()) {
                        throw new \Exception('Ошибка при создании статуса прочтения: ' . json_encode($status->getErrors()));
                    }
                }
            }

            $transaction->commit();
            return $notification;
        } catch (\Exception $e) {
            $transaction->rollBack();
            Yii::error('Ошибка при создании уведомления: ' . $e->getMessage(), 'notification');
            return null;
        }
    }

    /**
     * Создать новое уведомление для пользователя
     *
     * @param string $title Заголовок уведомления
     * @param string $body Текст уведомления
     * @param array|null $data Дополнительные данные
     * @param int $user_id ID пользователя
     * @param string|null $group_id ID группы уведомлений (если null, будет сгенерирован автоматически)
     * @return Notification|null
     */
    public static function createForUser($title, $body, $data = null, $user_id, $group_id = null)
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            // Если group_id не указан, генерируем его
            if ($group_id === null) {
                $group_id = 'group_' . uniqid();
            }

            $notification = new self();
            $notification->title = $title;
            $notification->body = $body;
            $notification->data = $data ? json_encode($data) : null;
            $notification->group_id = $group_id;
            $notification->is_sent = false;

            if (!$notification->save()) {
                throw new \Exception('Ошибка при создании уведомления: ' . json_encode($notification->getErrors()));
            }

            // Создаем запись статуса прочтения для пользователя
            $status = new NotificationReadStatus();
            $status->notification_id = $notification->id;
            $status->user_id = $user_id;
            $status->is_read = false;

            if (!$status->save()) {
                throw new \Exception('Ошибка при создании статуса прочтения: ' . json_encode($status->getErrors()));
            }

            $transaction->commit();
            return $notification;
        } catch (\Exception $e) {
            $transaction->rollBack();
            Yii::error('Ошибка при создании уведомления: ' . $e->getMessage(), 'notification');
            return null;
        }
    }

    /**
     * Пометить уведомление как прочитанное для пользователя
     *
     * @param int $user_id ID пользователя
     * @return bool
     */
    public function markAsReadForUser($user_id)
    {
        // Находим статус прочтения для данного пользователя
        $status = NotificationReadStatus::findOne([
            'notification_id' => $this->id,
            'user_id' => $user_id
        ]);

        if (!$status) {
            return false;
        }

        // Если уведомление входит в группу, помечаем все уведомления группы как прочитанные
        if ($this->group_id) {
            $groupNotifications = self::find()
                ->where(['group_id' => $this->group_id])
                ->all();

            foreach ($groupNotifications as $notification) {
                $groupStatus = NotificationReadStatus::findOne([
                    'notification_id' => $notification->id,
                    'user_id' => $user_id
                ]);

                if ($groupStatus) {
                    $groupStatus->markAsRead();
                }
            }

            return true;
        }

        // Если уведомление не входит в группу, помечаем только его
        return $status->markAsRead();
    }

    /**
     * Пометить уведомление как отправленное
     *
     * @return bool
     */
    public function markAsSent()
    {
        $this->is_sent = true;
        return $this->save(false);
    }

    /**
     * Получить непрочитанные уведомления для пользователя
     *
     * @param int $user_id ID пользователя
     * @return array Массив уведомлений с информацией о прочтении
     */
    public static function getUnreadForUser($user_id)
    {
        $query = self::find()
            ->alias('n')
            ->innerJoin(['rs' => NotificationReadStatus::tableName()], 'n.id = rs.notification_id')
            ->where(['rs.user_id' => $user_id, 'rs.is_read' => false])
            ->orderBy(['n.created_at' => SORT_DESC]);

        $notifications = $query->all();
        $result = [];

        foreach ($notifications as $notification) {
            $status = NotificationReadStatus::findOne([
                'notification_id' => $notification->id,
                'user_id' => $user_id
            ]);

            $result[] = [
                'notification' => $notification,
                'is_read' => $status ? $status->is_read : false,
                'read_at' => $status ? $status->read_at : null
            ];
        }

        return $result;
    }

    /**
     * Получить все уведомления для пользователя
     *
     * @param int $user_id ID пользователя
     * @param bool|null $is_read Фильтр по статусу прочтения (null - все)
     * @return array Массив уведомлений с информацией о прочтении
     */
    public static function getAllForUser($user_id, $is_read = null)
    {
        $query = self::find()
            ->alias('n')
            ->innerJoin(['rs' => NotificationReadStatus::tableName()], 'n.id = rs.notification_id')
            ->where(['rs.user_id' => $user_id]);

        if ($is_read !== null) {
            $query->andWhere(['rs.is_read' => (bool)$is_read]);
        }

        $query->orderBy(['n.created_at' => SORT_DESC]);

        $notifications = $query->all();
        $result = [];

        foreach ($notifications as $notification) {
            $status = NotificationReadStatus::findOne([
                'notification_id' => $notification->id,
                'user_id' => $user_id
            ]);

            $result[] = [
                'notification' => $notification,
                'is_read' => $status ? $status->is_read : false,
                'read_at' => $status ? $status->read_at : null
            ];
        }

        return $result;
    }

    /**
     * Пометить все уведомления пользователя как прочитанные
     *
     * @param int $user_id ID пользователя
     * @return int Количество обновленных записей
     */
    public static function markAllAsReadForUser($user_id)
    {
        return NotificationReadStatus::updateAll(
            [
                'is_read' => true,
                'read_at' => new Expression('NOW()')
            ],
            [
                'user_id' => $user_id,
                'is_read' => false
            ]
        );
    }

    /**
     * Получить ID пользователей по роли
     *
     * @param string $role Название роли
     * @return array Массив ID пользователей
     */
    private static function getUserIdsByRole($role)
    {
        $userIds = [];
        $auth = Yii::$app->authManager;

        // Получаем назначения для роли
        $userIdsWithRole = $auth->getUserIdsByRole($role);

        if (!empty($userIdsWithRole)) {
            $userIds = array_merge($userIds, $userIdsWithRole);
        }

        return array_unique($userIds);
    }
}
