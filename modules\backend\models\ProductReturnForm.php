<?php

namespace app\modules\backend\models;

use Yii;

class ProductReturnForm extends \yii\base\Model
{
    public $client_id;
    public $sale_id;
    public $products = [];

    public function rules()
    {
        return [
            [['client_id', 'sale_id'], 'required'],
            [['client_id', 'sale_id'], 'integer'],
            ['products', 'validateProducts'],
        ];
    }

    /**
     * Валидация продуктов для возврата
     */
    public function validateProducts($attribute, $params)
    {
        if (!is_array($this->products)) {
            $this->addError($attribute, Yii::t('app', 'products_must_be_array'));
            return;
        }

        $hasValidProduct = false;
        $productReturnService = new \app\common\services\ProductReturnService();

        foreach ($this->products as $index => $product) {
            if (!isset($product['product_id']) || !isset($product['quantity'])) {
                continue; // Пропускаем продукты без указанного количества
            }

            $productId = $product['product_id'];
            $quantity = $product['quantity'];

            if (empty($quantity) || $quantity <= 0) {
                continue; // Пропускаем продукты с нулевым или отрицательным количеством
            }

            // Проверяем, что количество не превышает доступное
            $salesDetail = $productReturnService->getInvoiceProductDetail($this->sale_id, $productId);
            if (!$salesDetail || $quantity > $salesDetail->quantity) {
                $this->addError($attribute, Yii::t('app', 'invalid_return_quantity_for_product') . ' ' . $productId);
                continue;
            }

            $hasValidProduct = true;
        }

        if (!$hasValidProduct) {
            $this->addError($attribute, Yii::t('app', 'no_valid_products_for_return'));
        }
    }

    public function attributeLabels()
    {
        return [
            'client_id' => Yii::t('app', 'client'),
            'sale_id' => Yii::t('app', 'sales'),
            'products' => Yii::t('app', 'products'),
        ];
    }
}