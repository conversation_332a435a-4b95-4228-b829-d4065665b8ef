<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "product_storage".
 *
 * @property int $id
 * @property int $product_id
 * @property int $quantity
 * @property string|null $enter_date
 * @property string|null $deleted_at
 * @property string|null $accepted_at
 * @property string|null $updated_date
 * @property int|null $add_user_id
 * @property int|null $accepted_user_id
 * @property int|null $quantity_block
 *
 * @property Product $product
 */
class ProductStorage extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product_storage';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['product_id', 'quantity', 'enter_date', 'add_user_id'], 'required'],
            [['product_id', 'quantity', 'accepted_user_id', 'add_user_id'], 'default', 'value' => null],
            [['product_id', 'quantity', 'accepted_user_id', 'add_user_id', 'quantity_block'], 'integer'],
            [['enter_date', 'accepted_at', 'updated_date', 'deleted_at'], 'safe'],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'product_id' => 'Product ID',
            'quantity' => 'Quantity',
            'enter_date' => 'Enter Date',
            'accepted_at' => 'Accepted Date',
            'updated_date' => 'Updated Date',
            'quantity_block' => 'Quantity Block',
        ];
    }

    /**
     * Gets query for [[Product]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }
}
