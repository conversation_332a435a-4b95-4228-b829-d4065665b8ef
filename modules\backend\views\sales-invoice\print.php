<?php
use yii\helpers\Html;

$totalSum1 = 0;
$totalSum2 = 0;
// Определяем, показывать ли цены
$showPrices = isset($withPrice) && $withPrice;
?>

<style>
    @media print {
        @page {
            size: A4 landscape;
            margin: 0.5cm;
        }

        /* Скрываем всё, кроме накладной */
        body * {
            visibility: hidden;
        }

        .invoice-container,
        .invoice-container * {
            visibility: visible;
        }

        /* Предотвращаем разрыв страницы внутри документа */
        .invoice-print {
            page-break-inside: avoid;
        }
    }

    .invoice-container {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        gap: 10px;
        margin-bottom: 0;
    }

    .invoice-column {
        width: 49%;
        border: 1px solid #000;
        padding: 10px;
        box-sizing: border-box;
        height: 95vh; /* Высота почти на весь экран */
        page-break-inside: avoid;
        page-break-before: avoid;
        page-break-after: avoid;
        font-size: 12px;
    }

    .document-header {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 15px;
        font-size: 12px;
        color: #000;
    }

    .grid-boxes {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 2px;
        width: 120px;
        float: right;
        margin-bottom: 20px;
    }

    .grid-box {
        border: 1px solid #000;
        height: 20px;
    }

    .document-title {
        text-align: center;
        margin: 15px 0 5px 0;
    }

    .document-title h2 {
        font-size: 16px;
        font-weight: bold;
        margin: 0;
        text-transform: uppercase;
    }

    .document-date {
        text-align: center;
        font-size: 12px;
        margin-bottom: 15px;
    }

    .info-sections-container {
        display: flex;
        justify-content: space-between;
        gap: 15px;
        margin-bottom: 15px;
    }

    .info-section {
        flex: 1;
        margin-bottom: 12px;
        font-size: 12px;
    }

    .info-row {
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .info-label {
        min-width: 120px;
        font-weight: normal;
    }

    .cargo-title {
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        margin: 15px 0;
        text-transform: uppercase;
    }

    .invoice-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 12px;
    }

    .invoice-table th, .invoice-table td {
        border: 1px solid #000;
        padding: 5px;
        text-align: center;
        <?php if (!$showPrices): ?>
        /* Стили для скрытия цен, если нужно */
        <?php endif; ?>
    }

    .invoice-table th {
        font-weight: normal;
    }

    .total-sum-text {
        margin: 12px 0;
        font-size: 12px;
        font-style: italic;
    }

    .signatures {
        margin-top: 20px;
        font-size: 12px;
        display: flex;
        justify-content: space-between;
    }

    .signature-block {
        display: flex;
        flex-direction: column;
    }

    .signature-line {
        border-bottom: 1px solid #000;
        width: 150px;
        margin: 15px 0 5px 0;
    }

    .stamp {
        margin-top: 15px;
        font-size: 12px;
    }

    .bonus-section {
        margin-top: 20px;
    }

    .bonus-title {
        font-size: 13px;
        font-weight: bold;
        text-align: center;
        margin: 10px 0;
        text-transform: uppercase;
    }

    .bonus-row {
        background-color: #f9f9f9;
        font-style: italic;
    }

    .bonus-row td {
        color: #666;
    }
</style>

<div class="invoice-print">
    <div class="invoice-container">
        <!-- Первая область (грузоотправителю) -->
        <div class="invoice-column">
            <div class="document-header">
                <div>1-й экз - грузоотправителю</div>
            </div>

            <div class="document-title">
                <h2>ТОВАРНО-ТРАНСПОРТНАЯ НАКЛАДНАЯ № <?= Html::encode($model->id) ?></h2>
            </div>
            <div class="document-date">
                <?= Html::encode(date('d F Y г.', strtotime($model->created_at))) ?>
            </div>

            <div class="info-sections-container">
                <div class="info-section">
                    <div class="info-row">
                        <span class="info-label">Договор</span>
                        <span>№ <?= Html::encode($model->client?->contract?->contract ?? ' ') ?> от <?= Html::encode($model->client?->contract?->start_date ?? ' ') ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Водитель</span>
                        <span><?= Html::encode($model->driver) ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Автомобиль</span>
                        <span><?= Html::encode($model->car_number) ?> к путевому листу №_______</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Прицеп</span>
                        <span>_______________ Марка, гос №</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Вид перевозки</span>
                        <span>____________________</span>
                    </div>
                </div>

                <div class="info-section">
                    <div class="info-row">
                        <span class="info-label">Грузоотправитель</span>
                        <span><?= "ООО 'SILVER VITA' " ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Пункт погрузки</span>
                        <span><?= "г. Ташкент Сергели район Южная промзона" ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Заказчик(плательщик)</span>
                        <span><?= Html::encode($model->client?->organization ?? ' ') ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Грузополучатель</span>
                        <span><?= Html::encode($model->client?->organization ?? ' ') ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Пункт разгрузки</span>
                        <span><?= Html::encode($model->client?->legal_address ?? ' ') ?></span>
                    </div>
                </div>
            </div>

            <div class="cargo-title">СВЕДЕНИЯ О ГРУЗЕ</div>

            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>Номенклатура</th>
                        <th>Единица измерения</th>
                        <th>Количество</th>
                        <?php if ($showPrices): ?>
                        <th>Цена с НДС</th>
                        <th>Сумма</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($model->salesDetails as $detail): ?>
                    <tr>
                        <td><?= Html::encode($detail->product->name) ?></td>
                        <td>шт.</td>
                        <td><?= Html::encode($detail->quantity) ?></td>
                        <?php if ($showPrices): ?>
                        <td><?= number_format($detail->special_price, 0, '.', ' ') ?></td>
                        <td><?= number_format($detail->quantity * $detail->special_price, 0, '.', ' ') ?></td>
                        <?php endif; ?>
                    </tr>
                    <?php
                        if ($showPrices) {
                            $totalSum1 += $detail->quantity * $detail->special_price;
                        }
                    ?>
                    <?php endforeach; ?>

                    <?php if ($model->bonus): ?>
                        <?php foreach ($model->bonus as $bonus): ?>
                        <tr class="bonus-row">
                            <td><?= Html::encode($bonus->product->name) ?> (Бонус)</td>
                            <td>шт.</td>
                            <td><?= Html::encode($bonus->quantity) ?></td>
                            <?php if ($showPrices): ?>
                            <td></td>
                            <td></td>
                            <?php endif; ?>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
                <?php if ($showPrices): ?>
                <tfoot>
                    <tr>
                        <td colspan="4" style="text-align: right;"><strong>ИТОГО:</strong></td>
                        <td><strong><?= number_format($totalSum1, 0, '.', ' ') ?></strong></td>
                    </tr>
                </tfoot>
                <?php endif; ?>
            </table>

            <?php if ($showPrices): ?>
            <div class="total-sum-text">
                Всего отпущено на сумму: <?= ucfirst($this->context->num2str($totalSum1)) ?>
            </div>
            <?php endif; ?>

            <div class="signatures">
                <div class="signature-block">
                    <div>Отпуск разрешил</div>
                    <div class="signature-line"></div>
                    <div style="font-size: 10px;">(должность, подпись)</div>
                </div>
                <div class="signature-block">
                    <div>Отпустил</div>
                    <div class="signature-line"></div>
                    <div style="font-size: 10px;">(ФИО, подпись)</div>
                </div>
                <div class="signature-block">
                    <div>Принял водитель-экспедитор</div>
                    <div class="signature-line"></div>
                </div>
            </div>

            <div class="stamp">
                М. П.
            </div>
        </div>

        <!-- Вторая область (грузополучателю) -->
        <div class="invoice-column">
            <div class="document-header">
                <div>1-й экз - грузополучателю</div>
            </div>

            <div class="document-title">
                <h2>ТОВАРНО-ТРАНСПОРТНАЯ НАКЛАДНАЯ № <?= Html::encode($model->id) ?></h2>
            </div>
            <div class="document-date">
                <?= Html::encode(date('d F Y г.', strtotime($model->created_at))) ?>
            </div>

            <div class="info-sections-container">
                <div class="info-section">
                    <div class="info-row">
                        <span class="info-label">Договор</span>
                        <span>№ <?= Html::encode($model->client?->contract?->contract ?? ' ') ?> от <?= Html::encode($model->client?->contract?->start_date ?? ' ') ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Водитель</span>
                        <span><?= Html::encode($model->driver) ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Автомобиль</span>
                        <span><?= Html::encode($model->car_number) ?> к путевому листу №_______</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Прицеп</span>
                        <span>_______________ Марка, гос №</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Вид перевозки</span>
                        <span>____________________</span>
                    </div>
                </div>

                <div class="info-section">
                    <div class="info-row">
                        <span class="info-label">Грузоотправитель</span>
                        <span><?= "ООО 'SILVER VITA' " ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Пункт погрузки</span>
                        <span><?= "г. Ташкент Сергели район Южная промзона" ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Заказчик(плательщик)</span>
                        <span><?= Html::encode($model->client?->organization ?? ' ') ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Грузополучатель</span>
                        <span><?= Html::encode($model->client?->organization ?? ' ') ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Пункт разгрузки</span>
                        <span><?= Html::encode($model->client?->legal_address ?? ' ') ?></span>
                    </div>
                </div>
            </div>

            <div class="cargo-title">СВЕДЕНИЯ О ГРУЗЕ</div>

            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>Номенклатура</th>
                        <th>Единица измерения</th>
                        <th>Количество</th>
                        <?php if ($showPrices): ?>
                        <th>Цена с НДС</th>
                        <th>Сумма</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($model->salesDetails as $detail): ?>
                    <tr>
                        <td><?= Html::encode($detail->product->name) ?></td>
                        <td>шт.</td>
                        <td><?= Html::encode($detail->quantity) ?></td>
                        <?php if ($showPrices): ?>
                        <td><?= number_format($detail->sell_price, 0, '.', ' ') ?></td>
                        <td><?= number_format($detail->quantity * $detail->sell_price, 0, '.', ' ') ?></td>
                        <?php endif; ?>
                    </tr>
                    <?php
                        if ($showPrices) {
                            $totalSum2 += $detail->quantity * $detail->sell_price;
                        }
                    ?>
                    <?php endforeach; ?>

                    <?php if ($model->bonus): ?>
                        <?php foreach ($model->bonus as $bonus): ?>
                        <tr class="bonus-row">
                            <td><?= Html::encode($bonus->product->name) ?> (Бонус)</td>
                            <td>шт.</td>
                            <td><?= Html::encode($bonus->quantity) ?></td>
                            <?php if ($showPrices): ?>
                            <td></td>
                            <td></td>
                            <?php endif; ?>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
                <?php if ($showPrices): ?>
                <tfoot>
                    <tr>
                        <td colspan="4" style="text-align: right;"><strong>ИТОГО:</strong></td>
                        <td><strong><?= number_format($totalSum2, 0, '.', ' ') ?></strong></td>
                    </tr>
                </tfoot>
                <?php endif; ?>
            </table>

            <?php if ($showPrices): ?>
            <div class="total-sum-text">
                Всего отпущено на сумму: <?= ucfirst($this->context->num2str(strval($totalSum2))) ?>
            </div>
            <?php endif; ?>

            <div class="signatures">
                <div class="signature-block">
                    <div>Отпуск разрешил</div>
                    <div class="signature-line"></div>
                    <div style="font-size: 10px;">(должность, подпись)</div>
                </div>
                <div class="signature-block">
                    <div>Отпустил</div>
                    <div class="signature-line"></div>
                    <div style="font-size: 10px;">(ФИО, подпись)</div>
                </div>
                <div class="signature-block">
                    <div>Принял водитель-экспедитор</div>
                    <div class="signature-line"></div>
                </div>
            </div>

            <div class="stamp">
                М. П.
            </div>
        </div>
    </div>
</div>