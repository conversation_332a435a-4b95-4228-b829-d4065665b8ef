<?php

namespace app\common\services;

use app\common\models\ActionLogger;
use app\common\models\ClientBalance;
use app\common\models\ClientBalanceHistory;
use app\common\models\ClientDriver;
use app\common\models\ClientSpecialPrices;
use app\common\models\Product;
use app\common\models\ProductPrice;
use app\common\models\ProductStorage;
use app\common\models\ProductStorageHistory;
use app\common\models\Sales;
use app\common\models\SalesBonus;
use app\common\models\SalesDetail;
use app\modules\backend\models\InvoiceCreateForm;
use Exception;
use Yii;

class SalesService
{
    /**
     * Проверяет возможность редактирования продажи
     * @param Sales $sales
     * @return bool
     */
    public function canUpdateSales(Sales $sales): bool
    {
        if ($sales->status !== Sales::STATUS_CONFIRMED) {
            return true;
        }

        $completedAt = strtotime($sales->completed_at);
        $currentTime = time();
        $sevenDaysInSeconds = 7 * 24 * 60 * 60;

        return ($currentTime - $completedAt) <= $sevenDaysInSeconds;
    }

    /**
     * Обновляет продажу с учетом всех изменений
     * @param InvoiceCreateForm $model
     * @return array
     */
    public function updateSales(InvoiceCreateForm $model): array
    {
        $sales = Sales::findOne($model->id);
        if (!$sales) {
            return [
                'status' => 'error',
                'message' => 'Запись продажи не найдена'
            ];
        }

        if (!$this->canUpdateSales($sales)) {
            return [
                'status' => 'error',
                'message' => 'Невозможно редактировать продажу после 7 дней с момента подтверждения'
            ];
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Сохраняем старые данные для сравнения
            $oldTotalSum = $sales->total_sum;
            $oldDetails = SalesDetail::findAll(['sale_id' => $sales->id]);
            $oldBonuses = SalesBonus::findAll(['sale_id' => $sales->id]);

            // Обновляем основные данные продажи
            $sales = $this->updateSalesData($sales, $model);

            // Обновляем детали продажи и склад
            $result = $this->updateSalesDetails($sales, $model, $oldDetails);
            if ($result !== null) {
                $transaction->rollBack();
                return $result;
            }

            // Обновляем бонусы и склад
            $bonusResult = $this->updateBonuses($sales, $model, $oldBonuses);
            if ($bonusResult !== null) {
                $transaction->rollBack();
                return $bonusResult;
            }

            // Обновляем баланс клиента если продажа подтверждена
            if ($sales->status === Sales::STATUS_CONFIRMED) {
                $this->updateClientBalance($sales, $oldTotalSum);
            }

            // Логируем действие
            $this->logUpdateAction($sales, $model->products);

            $transaction->commit();
            return [
                'status' => 'success',
                'message' => Yii::t('app', 'record_successfully_updated')
            ];
        } catch (Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Обновляет основные данные продажи
     * @param Sales $sales
     * @param InvoiceCreateForm $model
     * @return Sales
     * @throws Exception
     */
    private function updateSalesData(Sales $sales, InvoiceCreateForm $model): Sales
    {
        $driver = ClientDriver::findOne($model->driver);
        if (!$driver) {
            throw new Exception('Водитель не найден');
        }

        $sales->client_id = $model->client_id;
        $sales->driver = $driver->driver;
        $sales->car_number = $driver->car_number;
        $sales->total_sum = $model->total_price;

        if (!$sales->save()) {
            throw new Exception('Ошибка при обновлении продажи: ' . json_encode($sales->getErrors()));
        }

        return $sales;
    }

    /**
     * Обновляет детали продажи и склад
     * @param Sales $sales
     * @param InvoiceCreateForm $model
     * @param array $oldDetails
     * @return array|null
     * @throws Exception
     */
    private function updateSalesDetails(Sales $sales, InvoiceCreateForm $model, array $oldDetails): ?array
    {
        // Возвращаем товары на склад из старых деталей
        foreach ($oldDetails as $oldDetail) {
            $this->returnProductToStock($oldDetail->product_id, $oldDetail->quantity);
        }

        // Удаляем старые детали
        SalesDetail::deleteAll(['sale_id' => $sales->id]);
        $sales->total_special_prices_sum = 0;

        // Создаем новые детали и обновляем склад
        foreach ($model->products as $product) {
            $result = $this->checkProductAvailability($product);
            if ($result !== null) {
                return $result;
            }
            $this->saveSalesDetail($sales, $product);
            $this->updateSpecialPrices($sales, $product);
            $this->deductProductFromStock($product['product_id'], $product['quantity']);
        }

        if ($model->bonus && $model->has_bonus) {
            foreach ($model->bonus as $bonus) {
                if (!empty($bonus['product_id']) && !empty($bonus['quantity'])) {
                    $result = $this->checkBonusAvailability($bonus);
                    if ($result !== null) {
                        return $result;
                    }
                    $this->saveBonus($sales, $bonus);
                    $this->deductProductFromStock($bonus['product_id'], $bonus['quantity']);
                }
            }
        }

        return null;
    }

    /**
     * Обновляет бонусы и склад
     * @param Sales $sales
     * @param InvoiceCreateForm $model
     * @param array $oldBonuses
     * @return array|null
     * @throws Exception
     */
    private function updateBonuses(Sales $sales, InvoiceCreateForm $model, array $oldBonuses): ?array
    {
        // Возвращаем бонусные товары на склад
        foreach ($oldBonuses as $oldBonus) {
            $this->returnProductToStock($oldBonus->product_id, $oldBonus->quantity);
        }

        // Удаляем старые бонусы
        SalesBonus::deleteAll(['sale_id' => $sales->id]);

        if (!isset(Yii::$app->request->post('InvoiceCreateForm')['has_bonus']) || 
            !Yii::$app->request->post('InvoiceCreateForm')['has_bonus'] || 
            empty($model->bonus)) {
            return null;
        }

        foreach ($model->bonus as $bonus) {
            if (!empty($bonus['product_id']) && !empty($bonus['quantity'])) {
                $result = $this->checkBonusAvailability($bonus);
                if ($result !== null) {
                    return $result;
                }
                $this->saveBonus($sales, $bonus);
                $this->deductProductFromStock($bonus['product_id'], $bonus['quantity']);
            }
        }

        return null;
    }

    /**
     * Обновляет баланс клиента
     * @param Sales $sales
     * @param float $oldTotalSum
     * @throws Exception
     */
    private function updateClientBalance(Sales $sales, float $oldTotalSum): void
    {
        $clientBalance = ClientBalance::findOne(['client_id' => $sales->client_id]);
        if (!$clientBalance) {
            $clientBalance = new ClientBalance();
            $clientBalance->client_id = $sales->client_id;
            $clientBalance->amount = 0;
        }

        // Возвращаем старую сумму
        $clientBalance->amount += $oldTotalSum;

        // Вычитаем новую сумму
        $clientBalance->amount -= $sales->total_special_prices_sum;

        if (!$clientBalance->save()) {
            throw new Exception("Ошибка при сохранении баланса клиента");
        }

        // Логируем изменение баланса
        $clientBalanceHistory = new ClientBalanceHistory();
        $clientBalanceHistory->client_id = $sales->client_id;
        $clientBalanceHistory->amount = $sales->total_special_prices_sum;
        $clientBalanceHistory->old_amount = $clientBalance->amount + $sales->total_special_prices_sum;
        $clientBalanceHistory->type = ClientBalanceHistory::TYPE_DECREASE;
        $clientBalanceHistory->created_at = date('Y-m-d H:i:s');
        
        if (!$clientBalanceHistory->save()) {
            throw new Exception("Ошибка при сохранении истории баланса");
        }
    }

    /**
     * Возвращает товар на склад
     * @param int $productId
     * @param float $quantity
     * @throws Exception
     */
    private function returnProductToStock(int $productId, float $quantity): void
    {
        $productStorages = ProductStorage::find()
            ->where(['product_id' => $productId, 'deleted_at' => null])
            ->orderBy(['enter_date' => SORT_DESC])
            ->all();

        if (empty($productStorages)) {
            throw new Exception("Не найдены записи склада для продукта #{$productId}");
        }

        $remainingQuantity = $quantity;
        foreach ($productStorages as $storage) {
            if ($remainingQuantity <= 0) {
                break;
            }

            $storage->quantity += $remainingQuantity;
            if (!$storage->save()) {
                throw new Exception("Ошибка при возврате товара на склад");
            }

            // Записываем в историю склада
            $history = new ProductStorageHistory();
            $history->product_storage_id = $storage->id;
            $history->product_id = $productId;
            $history->quantity = $remainingQuantity;
            $history->type = ProductStorageHistory::TYPE_INCOME;
            $history->created_at = date('Y-m-d H:i:s');
            
            if (!$history->save()) {
                throw new Exception("Ошибка при сохранении истории склада");
            }

            $remainingQuantity = 0;
        }
    }

    /**
     * Вычитает товар со склада
     * @param int $productId
     * @param float $quantity
     * @throws Exception
     */
    private function deductProductFromStock(int $productId, float $quantity): void
    {
        $productStorages = ProductStorage::find()
            ->where(['product_id' => $productId, 'deleted_at' => null])
            ->orderBy(['enter_date' => SORT_ASC])
            ->all();

        if (empty($productStorages)) {
            throw new Exception("Не найдены записи склада для продукта #{$productId}");
        }

        $remainingQuantity = $quantity;
        foreach ($productStorages as $storage) {
            if ($remainingQuantity <= 0) {
                break;
            }

            $deductFromThisStorage = min($remainingQuantity, $storage->quantity);
            $storage->quantity -= $deductFromThisStorage;
            $remainingQuantity -= $deductFromThisStorage;

            if (!$storage->save()) {
                throw new Exception("Ошибка при обновлении количества на складе");
            }

            // Записываем в историю склада
            $history = new ProductStorageHistory();
            $history->product_storage_id = $storage->id;
            $history->product_id = $productId;
            $history->quantity = $deductFromThisStorage;
            $history->type = ProductStorageHistory::TYPE_OUTCOME;
            $history->created_at = date('Y-m-d H:i:s');
            
            if (!$history->save()) {
                throw new Exception("Ошибка при сохранении истории склада");
            }
        }
    }

    /**
     * Сохраняет деталь продажи
     * @param Sales $sales
     * @param array $product
     * @throws Exception
     */
    private function saveSalesDetail(Sales $sales, array $product): void
    {
        $factory_price = ProductPrice::findOne(['product_id' => $product['product_id'], 'deleted_at' => null]);
        $salesDetail = new SalesDetail();
        $salesDetail->sale_id = $sales->id;
        $salesDetail->product_id = $product['product_id'];
        $salesDetail->special_price = $product['special_price'];
        $salesDetail->sell_price = $product['sell_price'];
        $salesDetail->total_price = $product['sell_price'] * $product['quantity'];
        $salesDetail->factory_price = $factory_price->price ?? 0;
        $salesDetail->quantity = $product['quantity'];
        
        if (!$salesDetail->save()) {
            throw new \Exception('Ошибка при сохранении деталей продажи');
        }

        $sales->total_special_prices_sum += $product['special_price'] * $product['quantity'];
        if (!$sales->save()) {
            throw new \Exception('Ошибка при сохранении продажи');
        }
    }

    /**
     * Обновляет специальные цены
     * @param Sales $sales
     * @param array $product
     * @throws Exception
     */
    private function updateSpecialPrices(Sales $sales, array $product): void
    {
        $existingSpecialPrice = ClientSpecialPrices::findOne([
            'client_id' => $sales->client_id,
            'product_id' => $product['product_id'],
            'deleted_at' => null
        ]);

        $newPrice = round((float)$product['special_price'], 2);
        $newSellPrice = round((float)$product['sell_price'], 2);
        $currentTime = date('Y-m-d H:i:s');

        // Допустимая погрешность для сравнения цен - 5 сум
        $priceThreshold = 5.0;

        if ($existingSpecialPrice) {
            $existingSpecialPriceRounded = round($existingSpecialPrice->special_price, 2);
            $existingSellPriceRounded = round($existingSpecialPrice->sell_price, 2);

            // Проверяем разницу с допустимой погрешностью
            $specialPriceDiff = abs($existingSpecialPriceRounded - $newPrice);
            $sellPriceDiff = abs($existingSellPriceRounded - $newSellPrice);
            $pricesChanged = ($specialPriceDiff > $priceThreshold) || ($sellPriceDiff > $priceThreshold);

            // Временное логирование для отладки
            Yii::info("SalesService - Сравнение цен для клиента {$sales->client_id}, продукт {$product['product_id']}: " .
                "существующая_спец={$existingSpecialPriceRounded}, новая_спец={$newPrice}, разница_спец={$specialPriceDiff}, " .
                "существующая_продажа={$existingSellPriceRounded}, новая_продажа={$newSellPrice}, разница_продажа={$sellPriceDiff}, " .
                "порог={$priceThreshold}, изменились=" . ($pricesChanged ? 'ДА' : 'НЕТ'), 'special_prices');

            if ($pricesChanged) {
                Yii::info("SalesService - Обновление специальной цены для клиента {$sales->client_id}, продукт {$product['product_id']}", 'special_prices');
                $this->updateExistingSpecialPrice($existingSpecialPrice, $sales, $product, $currentTime);
            } else {
                Yii::info("SalesService - Цены не изменились для клиента {$sales->client_id}, продукт {$product['product_id']}: пропускаем обновление", 'special_prices');
            }
        } else {
            Yii::info("SalesService - Создание новой специальной цены для клиента {$sales->client_id}, продукт {$product['product_id']}: " .
                "спец_цена={$newPrice}, цена_продажи={$newSellPrice}", 'special_prices');
            $this->createNewSpecialPrice($sales, $product, $currentTime);
        }
    }

    /**
     * Обновляет существующую специальную цену
     * @param ClientSpecialPrices $existingSpecialPrice
     * @param Sales $sales
     * @param array $product
     * @param string $currentTime
     * @throws Exception
     */
    private function updateExistingSpecialPrice(ClientSpecialPrices $existingSpecialPrice, Sales $sales, array $product, string $currentTime): void
    {
        $existingSpecialPrice->deleted_at = $currentTime;
        if (!$existingSpecialPrice->save()) {
            throw new Exception('Ошибка при удалении старой специальной цены: ' . json_encode($existingSpecialPrice->getErrors()));
        }

        $this->createNewSpecialPrice($sales, $product, $currentTime);
    }

    /**
     * Создает новую специальную цену
     * @param Sales $sales
     * @param array $product
     * @param string $currentTime
     * @throws Exception
     */
    private function createNewSpecialPrice(Sales $sales, array $product, string $currentTime): void
    {
        $specialPrice = new ClientSpecialPrices();
        $specialPrice->client_id = $sales->client_id;
        $specialPrice->product_id = $product['product_id'];
        $specialPrice->special_price = (float)$product['special_price'];
        $specialPrice->sell_price = (float)$product['sell_price'];
        $specialPrice->created_at = $currentTime;
        
        if (!$specialPrice->save()) {
            throw new Exception('Ошибка при сохранении новой специальной цены: ' . json_encode($specialPrice->getErrors()));
        }
    }

    /**
     * Проверяет наличие товара на складе
     * @param array $product
     * @return array|null
     */
    private function checkProductAvailability(array $product): ?array
    {
        $productStorage = ProductStorage::findOne(['product_id' => $product['product_id'], 'deleted_at' => null]);
        $productName = Product::findOne(['id' => $product['product_id'], 'deleted_at' => null])->name;
        
        if (!$productStorage) {
            return [
                'status' => 'error',
                'message' => [
                    'products' => Yii::t('app', 'Product "{product_name}" not found on the warehouse', [
                        'product_name' => $productName
                    ])
                ],
                'code' => 404
            ];
        }

        $productStorages = ProductStorage::find()
            ->where(['product_id' => $product['product_id'], 'deleted_at' => null])
            ->all();
        
        $totalAvailable = array_sum(array_column($productStorages, 'quantity'));
        if ($totalAvailable < $product['quantity']) {
            return [
                'status' => 'error',
                'message' => [
                    'products' => Yii::t('app', 'Not enough product "{product_name}" on the warehouse. Available: {available}, Required: {required}', [
                        'product_name' => $productName,
                        'available' => $totalAvailable,
                        'required' => $product['quantity']
                    ])
                ],
                'code' => 422
            ];
        }

        return null;
    }

    /**
     * Сохраняет бонус
     * @param Sales $sales
     * @param array $bonus
     * @throws Exception
     */
    private function saveBonus(Sales $sales, array $bonus): void
    {
        $product = Product::findOne(['id' => $bonus['product_id'], 'deleted_at' => null]);
        if (!$product) {
            throw new Exception(Yii::t('app', 'Product with ID #{product_id} not found', [
                'product_id' => $bonus['product_id']
            ]));
        }

        $salesBonus = new SalesBonus();
        $salesBonus->sale_id = $sales->id;
        $salesBonus->product_id = $bonus['product_id'];
        $salesBonus->quantity = $bonus['quantity'];
        $salesBonus->created_at = date('Y-m-d H:i:s');
        
        if (!$salesBonus->save()) {
            throw new Exception(Yii::t('app', 'Error saving bonus for product "{product_name}"', [
                'product_name' => $product->name
            ]));
        }
    }

    /**
     * Проверяет наличие бонусного товара на складе
     * @param array $bonus
     * @return array|null
     */
    private function checkBonusAvailability(array $bonus): ?array
    {
        $productStorages = ProductStorage::find()
            ->where(['product_id' => $bonus['product_id'], 'deleted_at' => null])
            ->all();

        $product = Product::findOne($bonus['product_id']);
        if (empty($productStorages)) {
            return [
                'status' => 'error',
                'message' => [
                    'bonus' => Yii::t('app', 'Product "{product_name}" not found on the warehouse', [
                        'product_name' => $product->name
                    ])
                ],
                'code' => 404
            ];
        }

        $totalAvailable = array_sum(array_column($productStorages, 'quantity'));
        if ($totalAvailable < $bonus['quantity']) {
            return [
                'status' => 'error',
                'message' => [
                    'bonus' => Yii::t('app', 'Not enough product "{product_name}" on the warehouse. Available: {available}, Required: {required}', [
                        'product_name' => $product->name,
                        'available' => $totalAvailable,
                        'required' => $bonus['quantity']
                    ])
                ],
                'code' => 422
            ];
        }

        return null;
    }

    /**
     * Логирует действие обновления
     * @param Sales $sales
     * @param array $products
     */
    private function logUpdateAction(Sales $sales, array $products): void
    {
        ActionLogger::actionLog(
            'update_invoice',
            'sales',
            $sales->id,
            [
                'client_id' => $sales->client_id,
                'driver' => $sales->driver,
                'car_number' => $sales->car_number,
                'total_sum' => $sales->total_sum,
                'products' => array_map(function($product) {
                    return [
                        'product_id' => $product['product_id'],
                        'quantity' => $product['quantity'],
                        'price' => $product['special_price'],
                        'sell_price' => $product['sell_price']
                    ];
                }, $products),
            ]
        );
    }

    /**
     * @param InvoiceCreateForm $model
     * @return array|bool
     * @throws Exception
     */
    public function create(InvoiceCreateForm $model)
    {
        // ... existing code ...
    }
} 