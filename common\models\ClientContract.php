<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "client_contract".
 *
 * @property int $id
 * @property int $client_id
 * @property string $contract
 * @property int|null $type
 * @property string|null $start_date
 * @property string|null $end_date
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Client $client
 */
class ClientContract extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'client_contract';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['client_id', 'contract'], 'required'],
            [['client_id', 'type'], 'default', 'value' => null],
            [['client_id', 'type'], 'integer'],
            [['start_date', 'end_date', 'created_at', 'deleted_at'], 'safe'],
            [['contract'], 'string', 'max' => 255],
            [['client_id'], 'exist', 'skipOnError' => true, 'targetClass' => Client::class, 'targetAttribute' => ['client_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'client_id' => 'Client ID',
            'contract' => 'Contract',
            'type' => 'Type',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[Client]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClient()
    {
        return $this->hasOne(Client::class, ['id' => 'client_id']);
    }
}
