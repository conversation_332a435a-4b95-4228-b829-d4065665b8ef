<?php

use yii\helpers\Html;
use app\modules\backend\models\Expenses;

?>

<?php if($result): ?>
    <div>
        <table id="expenses-grid-view" class="table table-bordered table-striped compact">
            <thead>
                <tr>
                    <th><?= Yii::t("app", "expense_type") ?></th>
                    <th><?= Yii::t("app", "added_by") ?></th>
                    <th><?= Yii::t("app", "payment_type") ?></th>
                    <th><?= Yii::t("app", "amount") ?></th>
                    <th><?= Yii::t("app", "created_at") ?></th>
                    <th><?= Yii::t("app", "description") ?></th>
                    <th><?= Yii::t("app", "status") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </tr>
            </thead>
            <tbody>
            <?php foreach ($result as $model): ?>
                    <tr>

                    <td>
                        <?php
                            $expenseMapping = [
                                'Oylik' => 'salary',
                                'Avans' => 'advance',
                                'Bonus' => 'bonus',
                                'Debt' => 'debt',
                                'One time payment' => 'one_time_payment',
                            ];

                            $expenseType = $model['expense_type_name'] ?? null;

                            echo Yii::t('app', match ($expenseType) {
                                'Oylik' => 'salary',
                                'Avans' => 'advance',
                                'Bonus' => 'bonus',
                                'Debt' => 'debt',
                                'One time payment' => 'one_time_payment',
                                default => $expenseType,
                            });
                        ?>
                    </td>


                    <td><?= Html::encode($model['added_by_user']) ?></td>
                    <td>
                        <?= Html::encode(Expenses::getTypePayment($model['payment_type'])) ?>
                    </td>
                    <td><?= number_format($model['summa'], 0, '.', ',') ?></td>
                    <td><?= Yii::$app->formatter->asDate($model['created_at']) ?></td>
                    <td><?= Html::encode($model['description']) ?></td>
                    <td>
                            <?php if ($model['status'] == Expenses::TYPE_NOT_ACCEPTED): ?>
                                <span class="badge badge-danger">
                                    <?= Yii::t("app", "not_accepted") ?>
                                </span>
                            <?php else: ?>
                                <span class="badge badge-success">
                                    <?= Yii::t("app", "accepted") ?>
                                </span>
                            <?php endif; ?>
                        </td>
                    <td>
                        <?php
                            // Используем метод canBeEdited для определения, показывать ли кнопки
                            $showButton = Expenses::canBeEdited($model['id']);
                        ?>

                        <?php if ($showButton): ?>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <a href="#" class="dropdown-item expenses-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                        <?= Yii::t("app", "update") ?>
                                    </a>
                                    <a href="#" class="dropdown-item expenses-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model['id']) ?>">
                                        <?= Yii::t("app", "delete") ?>
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else: ?>
    <p><?= Yii::t('app', 'no_data_available') ?></p>
<?php endif; ?>
