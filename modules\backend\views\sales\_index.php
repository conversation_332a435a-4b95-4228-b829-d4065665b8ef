<?php
use yii\helpers\Html;
use yii\widgets\Pjax;

?>

<?php Pjax::begin(['id' => 'sales-grid-pjax']); ?>
<?php if($result): ?>
    <div>
        <table id="sales-grid-view" class="table table-bordered table-striped compact">
            <thead>
                <tr>
                    <th><?= Yii::t("app", "client_name") ?></th>
                    <th><?= Yii::t("app", "sell_user") ?></th>
                    <th><?= Yii::t("app", "total_quantity") ?></th>
                    <th><?= Yii::t("app", "total_sum") ?></th>
                    <th><?= Yii::t("app", "sales_created_at") ?></th>
                    <th><?= Yii::t("app", "status") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($result as $row): ?>
                    <tr>
                        <td><?= Html::encode($row['client_name']) ?></td>
                        <td><?= Html::encode($row['sell_user_name']) ?></td>
                        <td><?= Html::encode($row['total_quantity']) ?></td>
                        <td><?= Html::encode($row['total_sum']) ?></td>
                        <td><?= Html::encode(date('d.m.Y',strtotime($row['created_at']))) ?></td>
                        <td>
                            <span class="<?= $row['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?php
                                $statusText = $row['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                echo $statusText;
                                ?>
                            </span>
                        </td>
                        <td>
                            <a href="#" class="badge badge-info" data-toggle="modal" data-target="#ideal-large-modal-without-save" data-id="<?= Html::encode($row['id']) ?>">
                                <span><?= Yii::t("app", "detail") ?></span>
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else: ?>
    <p><?= Yii::t('app', 'no_data_available') ?></p>
<?php endif; ?>
<?php Pjax::end(); ?>