<?php

namespace app\modules\backend\controllers;

use app\common\models\Material;
use app\common\models\MaterialProduction;
use app\common\models\MaterialStorage;
use app\common\models\MaterialStorageHistory;
use app\common\models\Product;
use app\common\models\ProductStorage;
use app\common\models\ProductStorageHistory;
use app\modules\backend\models\MaterialAdjustmentForm;
use app\modules\backend\models\ProductAdjustmentForm;
use Yii;
use yii\web\Response;

/**
 * Контроллер для корректировки остатков материалов и готовых продуктов
 */
class InventoryAdjustmentController extends BaseController
{
    /**
     * Отображает страницу корректировки остатков
     *
     * @return string
     */
    public function actionIndex()
    {
        // Получаем список материалов в производстве с группировкой по материалам
        $materialProductionsQuery = "
            SELECT
                mp.material_id,
                m.name as material_name,
                SUM(mp.quantity) as quantity,
                MAX(mp.created_at) as created_at
            FROM material_production mp
            LEFT JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            GROUP BY mp.material_id, m.name
            HAVING SUM(mp.quantity) > 0
            ORDER BY m.name ASC
        ";
        $materialProductions = Yii::$app->db->createCommand($materialProductionsQuery)->queryAll();

        // Получаем список материалов на складе с группировкой по материалам
        $materialStoragesQuery = "
            SELECT
                ms.material_id,
                m.name as material_name,
                SUM(ms.quantity) as quantity,
                MAX(ms.created_at) as created_at
            FROM material_storage ms
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE ms.deleted_at IS NULL
            GROUP BY ms.material_id, m.name
            HAVING SUM(ms.quantity) > 0
            ORDER BY m.name ASC
        ";
        $materialStorages = Yii::$app->db->createCommand($materialStoragesQuery)->queryAll();

        // Получаем список продуктов на складе с группировкой по продуктам
        $productStoragesQuery = "
            SELECT
                ps.product_id,
                p.name as product_name,
                SUM(ps.quantity) as quantity,
                CASE 
                    WHEN p.size > 0 THEN FLOOR(SUM(ps.quantity) / p.size)
                    ELSE 0
                END as quantity_block,
                MAX(ps.enter_date) as enter_date
            FROM product_storage ps
            LEFT JOIN product p ON p.id = ps.product_id
            WHERE ps.deleted_at IS NULL
                AND ps.accepted_at IS NOT NULL
                AND ps.accepted_user_id IS NOT NULL
                AND p.size > 0
            GROUP BY ps.product_id, p.name, p.size
            HAVING SUM(ps.quantity) > 0
            ORDER BY p.name ASC
        ";
        $productStorages = Yii::$app->db->createCommand($productStoragesQuery)->queryAll();

        // Получаем список всех материалов
        $materials = Material::find()
            ->select(['id', 'name'])
            ->where(['deleted_at' => null])
            ->orderBy(['name' => SORT_ASC])
            ->asArray()
            ->all();

        // Получаем список всех продуктов
        $products = Product::find()
            ->select(['id', 'name', 'size'])
            ->where(['deleted_at' => null])
            ->orderBy(['name' => SORT_ASC])
            ->asArray()
            ->all();

        return $this->render('index', [
            'materialProductions' => $materialProductions,
            'materialStorages' => $materialStorages,
            'productStorages' => $productStorages,
            'materials' => $materials,
            'products' => $products,
        ]);
    }

    /**
     * Корректировка остатков материалов в производстве
     *
     * @return array
     */
    public function actionAdjustMaterialProduction()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new MaterialAdjustmentForm();
            $model->load(Yii::$app->request->post());

            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Получаем общую сумму всех записей для данного материала
                $totalQuantity = MaterialProduction::find()
                    ->where(['material_id' => $model->material_id])
                    ->andWhere(['deleted_at' => null])
                    ->sum('quantity');

                // Если общая сумма не найдена, устанавливаем её в 0
                if ($totalQuantity === null) {
                    $totalQuantity = 0;
                }

                // Ищем последнюю запись для этого материала
                $materialProduction = MaterialProduction::find()
                    ->where(['material_id' => $model->material_id])
                    ->andWhere(['deleted_at' => null])
                    ->orderBy(['id' => SORT_DESC])
                    ->one();

                if (!$materialProduction) {
                    // Создаем новую запись
                    $materialProduction = new MaterialProduction();
                    $materialProduction->material_id = $model->material_id;
                    $materialProduction->quantity = 0;
                    $materialProduction->created_at = date('Y-m-d H:i:s');
                }

                // Сохраняем старое общее количество для логирования
                $oldTotalQuantity = $totalQuantity;

                // Вычисляем разницу между новым общим количеством и текущим общим количеством
                $difference = floatval($model->quantity) - floatval($totalQuantity);

                // Обновляем количество и дату
                $materialProduction->quantity += $difference; // Добавляем разницу к текущему количеству
                $materialProduction->updated_at = date('Y-m-d H:i:s');

                if (!$materialProduction->save()) {
                    throw new \Exception('Ошибка при сохранении записи: ' . json_encode($materialProduction->getErrors()));
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => 'Остатки материала в производстве успешно скорректированы'
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return [
            'status' => 'error',
            'message' => 'Метод не поддерживается'
        ];
    }

    /**
     * Корректировка остатков материалов на складе
     *
     * @return array
     */
    public function actionAdjustMaterialStorage()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new MaterialAdjustmentForm();
            $model->load(Yii::$app->request->post());

            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Получаем общую сумму всех записей для данного материала
                $totalQuantity = MaterialStorage::find()
                    ->where(['material_id' => $model->material_id])
                    ->andWhere(['deleted_at' => null])
                    ->sum('quantity');

                // Если общая сумма не найдена, устанавливаем её в 0
                if ($totalQuantity === null) {
                    $totalQuantity = 0;
                }

                // Ищем последнюю запись для этого материала
                $materialStorage = MaterialStorage::find()
                    ->where(['material_id' => $model->material_id])
                    ->andWhere(['deleted_at' => null])
                    ->orderBy(['id' => SORT_DESC])
                    ->one();

                if (!$materialStorage) {
                    // Создаем новую запись
                    $materialStorage = new MaterialStorage();
                    $materialStorage->material_id = $model->material_id;
                    $materialStorage->quantity = 0;
                    $materialStorage->created_at = date('Y-m-d H:i:s');
                }

                // Сохраняем старое общее количество для истории
                $oldTotalQuantity = $totalQuantity;

                // Вычисляем разницу между новым общим количеством и текущим общим количеством
                $difference = floatval($model->quantity) - floatval($totalQuantity);

                // Обновляем количество и дату
                $materialStorage->quantity += $difference; // Добавляем разницу к текущему количеству
                $materialStorage->updated_at = date('Y-m-d H:i:s');

                if (!$materialStorage->save()) {
                    throw new \Exception('Ошибка при сохранении записи: ' . json_encode($materialStorage->getErrors()));
                }

                // Создаем запись в истории
                $history = new MaterialStorageHistory();
                $history->material_storage_id = $materialStorage->id;
                $history->material_id = $model->material_id;
                $history->quantity = abs($difference); // Используем абсолютное значение разницы
                $history->type = ($difference > 0) ? MaterialStorageHistory::TYPE_INCOME : MaterialStorageHistory::TYPE_OUTCOME;
                $history->add_user_id = Yii::$app->user->getId();
                $history->created_at = date('Y-m-d H:i:s');

                if (!$history->save()) {
                    throw new \Exception('Ошибка при сохранении истории: ' . json_encode($history->getErrors()));
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => 'Остатки материала на складе успешно скорректированы'
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return [
            'status' => 'error',
            'message' => 'Метод не поддерживается'
        ];
    }

    /**
     * Корректировка остатков готовых продуктов
     *
     * @return array
     */
    public function actionAdjustProductStorage()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new ProductAdjustmentForm();
            $model->load(Yii::$app->request->post());

            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Получаем общую сумму всех записей для данного продукта
                $totalQuantity = ProductStorage::find()
                    ->where(['product_id' => $model->product_id])
                    ->andWhere(['deleted_at' => null])
                    ->andWhere(['IS NOT', 'accepted_at', null])
                    ->andWhere(['IS NOT', 'accepted_user_id', null])
                    ->sum('quantity');

                // Если общая сумма не найдена, устанавливаем её в 0
                if ($totalQuantity === null) {
                    $totalQuantity = 0;
                }

                // Ищем последнюю запись для этого продукта
                $productStorage = ProductStorage::find()
                    ->where(['product_id' => $model->product_id])
                    ->andWhere(['deleted_at' => null])
                    ->andWhere(['IS NOT', 'accepted_at', null])
                    ->andWhere(['IS NOT', 'accepted_user_id', null])
                    ->orderBy(['id' => SORT_DESC])
                    ->one();

                if (!$productStorage) {
                    // Создаем новую запись
                    $productStorage = new ProductStorage();
                    $productStorage->product_id = $model->product_id;
                    $productStorage->quantity = 0;
                    $productStorage->enter_date = date('Y-m-d H:i:s');
                    $productStorage->add_user_id = Yii::$app->user->getId();
                    $productStorage->accepted_at = date('Y-m-d H:i:s');
                    $productStorage->accepted_user_id = Yii::$app->user->getId();
                }

                // Сохраняем старое общее количество для истории
                $oldTotalQuantity = $totalQuantity;

                // Вычисляем разницу между новым общим количеством и текущим общим количеством
                $difference = floatval($model->quantity) - floatval($totalQuantity);

                // Обновляем количество и дату
                $productStorage->quantity += $difference; // Добавляем разницу к текущему количеству
                $productStorage->updated_date = date('Y-m-d H:i:s');

                // Обновляем количество блоков
                $product = Product::findOne($model->product_id);
                if ($product && $product->size > 0) {
                    $productStorage->quantity_block = floor($model->quantity / $product->size);
                }

                if (!$productStorage->save()) {
                    throw new \Exception('Ошибка при сохранении записи: ' . json_encode($productStorage->getErrors()));
                }

                // Создаем запись в истории
                $history = new ProductStorageHistory();
                $history->product_storage_id = $productStorage->id;
                $history->product_id = $model->product_id;
                $history->quantity = abs($difference); // Используем абсолютное значение разницы
                // ProductStorageHistory использует строковые константы, а не числовые
                $history->type = ($difference > 0) ? ProductStorageHistory::TYPE_INCOME : ProductStorageHistory::TYPE_OUTCOME;
                $history->add_user_id = Yii::$app->user->getId();
                $history->created_at = date('Y-m-d H:i:s');

                if (!$history->save()) {
                    throw new \Exception('Ошибка при сохранении истории: ' . json_encode($history->getErrors()));
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => 'Остатки продукта успешно скорректированы'
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return [
            'status' => 'error',
            'message' => 'Метод не поддерживается'
        ];
    }
}
