<?php

namespace app\services;

use app\common\models\ClientDriver;
use Exception;
use Yii;

/**
 * Сервис для работы с водителями клиентов
 */
class ClientDriverService
{
    /**
     * Создание нового водителя
     * 
     * @param int $clientId ID клиента
     * @param string $driverName Имя водителя
     * @param string|null $carNumber Номер машины
     * @return array Результат операции
     */
    public function createDriver($clientId, $driverName, $carNumber = null)
    {
        if (empty($clientId) || empty($driverName)) {
            return [
                'success' => false,
                'message' => 'Не указаны обязательные параметры'
            ];
        }

        $model = new ClientDriver();
        $model->client_id = $clientId;
        $model->driver = $driverName;
        $model->car_number = $carNumber;
        
        if ($model->save()) {
            return [
                'success' => true,
                'id' => $model->id,
                'message' => 'Водитель успешно добавлен'
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Ошибка при сохранении: ' . json_encode($model->errors)
        ];
    }

    /**
     * Обновление данных водителя
     * 
     * @param int $id ID водителя
     * @param string $driverName Имя водителя
     * @param string|null $carNumber Номер машины
     * @return array Результат операции
     */
    public function updateDriver($id, $driverName, $carNumber = null)
    {
        if (!is_numeric($id) || empty($driverName)) {
            return [
                'success' => false,
                'message' => 'Не указаны обязательные параметры'
            ];
        }
        
        $model = ClientDriver::findOne(['id' => (int)$id, 'deleted_at' => null]);
        if (!$model) {
            return [
                'success' => false,
                'message' => 'Водитель не найден'
            ];
        }

        $model->driver = $driverName;
        $model->car_number = $carNumber;
        
        if ($model->save()) {
            return [
                'success' => true,
                'id' => $model->id,
                'message' => 'Водитель успешно обновлен'
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Ошибка при обновлении: ' . json_encode($model->errors)
        ];
    }

    /**
     * Удаление водителя (мягкое удаление)
     * 
     * @param int $id ID водителя
     * @return array Результат операции
     */
    public function deleteDriver($id)
    {
        if (!is_numeric($id)) {
            return [
                'success' => false,
                'message' => 'Неверный ID водителя'
            ];
        }
        
        $model = ClientDriver::findOne(['id' => (int)$id, 'deleted_at' => null]);
        
        if (!$model) {
            return [
                'success' => false,
                'message' => 'Водитель не найден'
            ];
        }
        
        $model->deleted_at = date('Y-m-d H:i:s');
        
        if ($model->save()) {
            return [
                'success' => true,
                'message' => 'Водитель успешно удален'
            ];
        }
        
        return [
            'success' => false,
            'message' => 'Ошибка при удалении: ' . json_encode($model->errors)
        ];
    }

    /**
     * Получение списка водителей клиента
     * 
     * @param int $clientId ID клиента
     * @return array Массив водителей
     */
    public function getDriversByClientId($clientId)
    {
        $drivers = ClientDriver::find()
            ->where([
                'client_id' => $clientId,
                'deleted_at' => null
            ])
            ->all();

        $result = [];
        foreach ($drivers as $driver) {
            $result[] = [
                'id' => $driver->id,
                'name' => $driver->driver,
                'car_number' => $driver->car_number
            ];
        }

        return $result;
    }

    /**
     * Получение деталей водителя по ID
     * 
     * @param int $id ID водителя
     * @return array|null Информация о водителе или null, если водитель не найден
     */
    public function getDriverById($id)
    {
        $driver = ClientDriver::findOne(['id' => (int)$id, 'deleted_at' => null]);
        
        if (!$driver) {
            return null;
        }
        
        return [
            'id' => $driver->id,
            'client_id' => $driver->client_id,
            'name' => $driver->driver,
            'car_number' => $driver->car_number
        ];
    }
}
