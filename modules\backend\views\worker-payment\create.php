<?php
use app\common\models\PaymentType;
use app\modules\backend\models\WorkerFinances;
use yii\helpers\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<style>
    .payment-methods-container {
        padding: 8px;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .payment-methods-container .form-check {
        margin-bottom: 0;
        padding: 8px 12px;
        background: white;
        border-radius: 6px;
        border: 1px solid #dee2e6;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        min-width: 120px;
        position: relative;
    }

    .payment-methods-container .form-check:hover {
        border-color: #007bff;
        box-shadow: 0 1px 3px rgba(0,123,255,0.1);
    }

    .payment-methods-container .form-check-input {
        margin: 0;
        transform: scale(1.1);
        flex-shrink: 0;
        position: absolute;
        right: 10px;
    }

    .payment-methods-container .form-check-label {
        font-weight: 500;
        font-size: 14px;
        color: #495057;
        cursor: pointer;
        margin: 0;
        white-space: nowrap;
        text-align: left;
        padding-right: 24px;
    }

    .payment-methods-container .form-check-input:disabled + .form-check-label {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .payment-methods-container .form-check-input:checked + .form-check-label {
        color: #007bff;
        font-weight: 600;
    }

    /* Стили для основных чекбоксов типов платежей */
    .payment-type-row .form-check {
        padding: 12px 15px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        min-width: 150px;
        position: relative;
    }

    .payment-type-row .form-check:hover {
        background: #e3f2fd;
        border-color: #2196f3;
    }

    .payment-type-row .form-check-input {
        margin: 0;
        transform: scale(1.2);
        flex-shrink: 0;
        position: absolute;
        right: 10px;
    }

    .payment-type-row .form-check-label {
        font-weight: 600;
        font-size: 15px;
        color: #343a40;
        margin: 0;
        white-space: nowrap;
        text-align: left;
        padding-right: 24px;
    }

    .payment-type-row .form-check-input:checked + .form-check-label {
        color:hsl(207, 89.70%, 54.10%);
    }

    /* Выравнивание контента в ячейках таблицы */
    .payment-type-row td {
        vertical-align: middle;
    }

    .amount-column {
        vertical-align: middle;
    }

    /* Стили для полей ввода чисел */
    input[inputmode="numeric"] {
        text-align: right;
    }

    /* Стили для секции погашения долга */
    #debt-payment-section .card {
        border: 1px solid #dee2e6;
        border-radius: 6px;
        background: white;
    }

    #debt-payment-section .card-header {
        background:rgb(160, 190, 239);
        border-bottom: 1px solid #dee2e6;
        padding: 12px 15px;
    }

    #debt-payment-section .form-check {
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        position: relative;
        cursor: pointer;
    }

    #debt-payment-section .form-check:hover {
        background: #f8f9fa;
        border-radius: 4px;
        padding: 8px 12px;
        margin: -8px -12px;
    }

    #debt-payment-section .form-check-input {
        margin: 0;
        transform: scale(1.2);
        flex-shrink: 0;
        position: absolute;
        right: 10px;
    }

    #debt-payment-section .form-check-label {
        font-weight: 350;
        font-size: 15px;
        color:rgb(2, 11, 20);
        margin: 0;
        cursor: pointer;
        padding-right: 30px;
        width: 100%;
    }

    #debt-payment-section .form-check-input:checked + .form-check-label {
        color: #dc3545;
    }

    #debt-payment-section .card-body {
        background: white;
        padding: 20px;
    }

    /* Стили для валидации */
    .is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }

    .error-container {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .validation-success {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }

    .validation-warning {
        border-color: #ffc107 !important;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
    }

    /* Стили для панели итогов */
    #payment-summary .card-header {
        background: linear-gradient(45deg, #17a2b8, #138496) !important;
    }

    #payment-summary .card-body {
        background: #f8f9fa;
    }

    /* Анимация для полей с ошибками */
    .is-invalid {
        animation: shake 0.5s ease-in-out;
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    /* Стили для кнопок */
    .btn-lg {
        padding: 12px 30px;
        font-size: 16px;
        border-radius: 8px;
    }

    .btn-primary {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(45deg, #0056b3, #004085);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    .btn-secondary {
        background: linear-gradient(45deg, #6c757d, #545b62);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: linear-gradient(45deg, #545b62, #3d4142);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
    }

    /* Улучшенные стили для алертов */
    .alert {
        border-radius: 8px;
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .alert-success {
        background: linear-gradient(45deg, #d4edda, #c3e6cb);
        color: #155724;
    }

    .alert-danger {
        background: linear-gradient(45deg, #f8d7da, #f5c6cb);
        color: #721c24;
    }

    .alert-warning {
        background: linear-gradient(45deg, #fff3cd, #ffeaa7);
        color: #856404;
    }
</style>

<div class="worker-payment-form">
    <form id="worker-payment-create-form" novalidate autocomplete="off">
        
        <div class="row mb-3">
            <div class="col-md-8">
                <label for="worker_id"><?= Yii::t('app', 'worker') ?></label>
                <select id="worker_id" name="worker_id" class="form-control select2" required>
                    <option value=""><?= Yii::t('app', 'select_worker') ?></option>
                    <?php foreach ($workers as $worker): ?>
                        <option value="<?= $worker['id'] ?>"><?= Html::encode($worker['full_name']) ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="error-container" id="worker_id-error"></div>
            </div>
            <div class="col-md-4">
                <label for="month"><?= Yii::t('app', 'month') ?></label>
                <input type="month" id="month" name="month" class="form-control" value="<?= date('Y-m') ?>" required>
                <div class="error-container" id="month-error"></div>
            </div>
        </div>

        <!-- Worker Info Panel -->
        <div id="worker-info" class="alert alert-info mt-3" style="display: none;">
            <div class="row">
                <div class="col-md-4">
                    <strong><?= Yii::t('app', 'salary') ?>:</strong> <span id="worker-salary">0</span>
                </div>
                <div class="col-md-4">
                    <strong><?= Yii::t('app', 'total_paid') ?>:</strong> <span id="worker-total-paid">0</span>
                </div>
                <div class="col-md-4" id="remaining-salary-info" style="display: none;">
                    <strong><?= Yii::t('app', 'remaining_salary') ?>:</strong> <span id="worker-remaining-salary">0</span>
                </div>
            </div>
        </div>

        <!-- Payment Types Selection -->
        <div class="form-group">
            <label><?= Yii::t('app', 'payment_types') ?></label>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th width="40%"><?= Yii::t('app', 'payment_type') ?></th>
                            <th width="30%"><?= Yii::t('app', 'payment_method') ?></th>
                            <th width="30%"><?= Yii::t('app', 'amount') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($paymentTypes as $typeId => $typeName): ?>
                            <?php if ($typeId != WorkerFinances::TYPE_DEBT_PAYMENT): // Исключаем долг из основной таблицы ?>
                                <tr class="payment-type-row" data-type="<?= $typeId ?>">
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input payment-type-checkbox" type="checkbox" 
                                                   id="payment_type_<?= $typeId ?>" value="<?= $typeId ?>">
                                            <label class="form-check-label" for="payment_type_<?= $typeId ?>">
                                                <?= $typeName ?>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="payment-methods-container">
                                            <div class="form-check">
                                                <input class="form-check-input payment-method-checkbox" type="checkbox"
                                                       id="cash_<?= $typeId ?>" value="<?= PaymentType::CASH ?>"
                                                       name="payment_types[<?= $typeId ?>][methods][]" disabled>
                                                <label class="form-check-label" for="cash_<?= $typeId ?>">
                                                    <?= Yii::t('app', 'cash') ?>
                                                </label>
                                            </div>
                                            <?php if ($typeId != WorkerFinances::TYPE_DEBT): // Для долга скрываем карточный платеж ?>
                                                <div class="form-check">
                                                    <input class="form-check-input payment-method-checkbox" type="checkbox"
                                                           id="card_<?= $typeId ?>" value="<?= PaymentType::PAYMENT_CARD ?>"
                                                           name="payment_types[<?= $typeId ?>][methods][]" disabled>
                                                    <label class="form-check-label" for="card_<?= $typeId ?>">
                                                        <?= Yii::t('app', 'payment_card') ?>
                                                    </label>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="amount-column">
                                        <input type="text"
                                               class="form-control amount-input"
                                               name="payment_types[<?= $typeId ?>][amount]"
                                               placeholder="<?= Yii::t('app', 'amount') ?>"
                                               inputmode="numeric"
                                               pattern="[0-9\s]*"
                                               disabled>
                                        <div class="dynamic-amounts" style="display: none;">
                                            <!-- Dynamic inputs will be added here -->
                                        </div>
                                        <div class="error-container"></div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Debt Payment Section -->
        <div id="debt-payment-section" class="form-group" style="display: none;">
                <div class="card-header">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="debt-payment-checkbox">
                        <label class="form-check-label" for="debt-payment-checkbox">
                            <strong><?= $paymentTypes[WorkerFinances::TYPE_DEBT_PAYMENT] ?? 'Погашение долга' ?></strong>
                        </label>
                    </div>
                </div>
                <div class="card-body" id="debt-payment-details" style="display: none;">
                    <div class="row">
                        <div class="col-md-12">
                            <label><?= Yii::t('app', 'amount') ?></label>
                            <input type="text" class="form-control" id="debt-payment-amount"
                                   name="payment_types[<?= WorkerFinances::TYPE_DEBT_PAYMENT ?>][amount]"
                                   inputmode="numeric" pattern="[0-9\s]*" disabled>
                            <small class="text-muted"><?= Yii::t('app', 'debt_amount') ?>: <span id="worker-debt-amount">0</span></small>
                            <div class="error-container"></div>
                        </div>
                    </div>
                </div>
        </div>

    </form>
</div>

<script>
// ===== ПОЛНЫЙ JAVASCRIPT КОД ДЛЯ ФОРМЫ СОЗДАНИЯ ПЛАТЕЖЕЙ =====
// Все обработчики событий и логика в одном месте для решения проблемы с Backspace

$(document).ready(function() {

    // ===== КОНСТАНТЫ =====
    const WORKER_FINANCES_TYPE_SALARY = 1;
    const WORKER_FINANCES_TYPE_ADVANCE = 2;
    const WORKER_FINANCES_TYPE_DEBT = 4;
    const WORKER_FINANCES_TYPE_DEBT_PAYMENT = 6;
    const WORKER_FINANCES_TYPE_CASH_SALARY = 8;
    const PAYMENT_TYPE_CASH = 1;
    const PAYMENT_TYPE_TRANSFER = 2;
    const PAYMENT_TYPE_TERMINAL = 3;
    const PAYMENT_TYPE_PAYMENT_CARD = 4;

    // Переменная для отслеживания операций удаления
    var isDeleteOperation = false;

    // ===== ФУНКЦИИ ФОРМАТИРОВАНИЯ =====

    // Форматирование чисел с разделителями тысяч
    function formatNumberInput(value) {
        if (value === null || value === undefined || value === '') return '';
        try {
            // Если это уже число, используем его напрямую
            if (typeof value === 'number') {
                return Math.floor(value).toLocaleString('ru-RU');
            }

            // Если это строка, сначала парсим как число
            var numericValue = parseFloat(value.toString());
            if (isNaN(numericValue)) {
                // Если не удалось распарсить, пробуем удалить все кроме цифр
                var cleanValue = value.toString().replace(/[^\d]/g, '');
                if (cleanValue === '') return '';
                numericValue = parseInt(cleanValue, 10);
            }

            // Округляем до целого и форматируем
            return Math.floor(numericValue).toLocaleString('ru-RU');
        } catch (e) {
            console.warn('Error formatting number input:', value, e);
            return '';
        }
    }

    // Получение числового значения из строки
    function getNumericValue(value) {
        if (!value) return 0;
        return parseInt(value.toString().replace(/[^\d]/g, ''), 10) || 0;
    }

    // ===== ОБРАБОТЧИКИ КЛАВИАТУРЫ =====

    // Обработчик keydown для отслеживания операций удаления
    $(document).on('keydown', '#worker-payment-create-form input', function (e) {
        var keyCode = e.which || e.keyCode;
        var $currentElement = $(this);

        if (keyCode === 8 || keyCode === 46) { // Backspace или Delete
            isDeleteOperation = true;
        }

    // Предотвращаем закрытие модального окна при нажатии Enter
        if (keyCode === 13) { // Enter
            e.preventDefault();
            e.stopPropagation();

            // Переходим к следующему полю ввода
            var inputs = $('#worker-payment-create-form input:visible:enabled');
            var currentIndex = inputs.index($currentElement);
            if (currentIndex >= 0 && currentIndex < inputs.length - 1) {
                inputs.eq(currentIndex + 1).focus();
            }
            return false;
        }
    });

    // Обработчик keyup для сброса флага операции удаления
    $(document).on('keyup', '#worker-payment-create-form input', function (e) {
        var keyCode = e.which || e.keyCode;

        if (keyCode === 8 || keyCode === 46) { // Backspace или Delete
            setTimeout(function () {
                isDeleteOperation = false;
            }, 50);
        }
    });

    // ===== ФУНКЦИИ ВАЛИДАЦИИ =====

    // Валидация максимальной суммы платежа
    function validateMaxPaymentAmount() {
        var errors = [];
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
        var maxAllowedAmount = Math.max(0, originalSalary - originalTotalPaid);

        // Проверяем каждую строку с типом платежа
        $('.payment-type-checkbox:checked').each(function () {
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            var totalAmount = 0;

            // Получаем общую сумму для этого типа платежа
            var dynamicInputs = row.find('.dynamic-amounts input[data-method]:visible');
            var mainInput = row.find('.amount-input').not('[data-method]');

            if (dynamicInputs.length > 0) {
                // Если есть динамические поля, суммируем их
                dynamicInputs.each(function() {
                    totalAmount += getNumericValue($(this).val());
                });
            } else if (mainInput.is(':visible')) {
                // Если виден основной инпут, берем его значение
                totalAmount = getNumericValue(mainInput.val());
            }

            // Для зарплаты и аванса проверяем лимит
            if ((typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) && totalAmount > maxAllowedAmount) {
                var targetField = dynamicInputs.length > 0 ? dynamicInputs.first() : mainInput;
                errors.push({
                    field: targetField,
                    message: 'Сумма не может превышать оставшуюся зарплату: ' + formatNumberInput(maxAllowedAmount.toString())
                });
            }

            // Проверяем минимальную сумму
            if (totalAmount <= 0) {
                var targetField = dynamicInputs.length > 0 ? dynamicInputs.first() : mainInput;
                errors.push({
                    field: targetField,
                    message: 'Сумма должна быть больше 0'
                });
            }
        });

        return errors;
    }

    // Валидация выбора типов платежей
    function validatePaymentTypesSelection() {
        var errors = [];
        var hasSelectedPaymentType = false;

        $('.payment-type-checkbox:checked').each(function () {
            hasSelectedPaymentType = true;
            var row = $(this).closest('tr');
            var selectedMethods = row.find('.payment-method-checkbox:checked');
            var amountInput = row.find('.amount-input').not('[data-method]');
            var dynamicAmounts = row.find('.dynamic-amounts input[data-method]:visible');

            // Проверяем, что выбран хотя бы один метод оплаты
            if (selectedMethods.length === 0) {
                errors.push({
                    field: row.find('.payment-method-checkbox').first(),
                    message: 'Выберите метод оплаты'
                });
                return; // Переходим к следующему типу платежа
            }

            // Проверяем суммы
            var hasValidAmount = false;
            
            if (dynamicAmounts.length > 0) {
                // Несколько методов - проверяем динамические поля
                dynamicAmounts.each(function() {
                    var amount = getNumericValue($(this).val());
                    if (amount > 0) {
                        hasValidAmount = true;
                    }
                });
                
                if (!hasValidAmount) {
                    errors.push({
                        field: dynamicAmounts.first(),
                        message: 'Укажите сумму'
                    });
                }
            } else if (amountInput.is(':visible')) {
                // Один метод - проверяем основное поле
                var mainAmount = getNumericValue(amountInput.val());
                if (mainAmount > 0) {
                    hasValidAmount = true;
                }
                
                if (!hasValidAmount) {
                    errors.push({
                        field: amountInput,
                        message: 'Укажите сумму'
                    });
                }
            }
        });

        // Проверяем погашение долга
        if ($('#debt-payment-checkbox').is(':checked')) {
            hasSelectedPaymentType = true;
            var debtAmount = getNumericValue($('#debt-payment-amount').val());
            if (debtAmount <= 0) {
                errors.push({
                    field: $('#debt-payment-amount'),
                    message: 'Укажите сумму погашения долга'
                });
            }
        }

        if (!hasSelectedPaymentType) {
            errors.push({
                field: $('.payment-type-checkbox').first(),
                message: 'Выберите хотя бы один тип платежа'
            });
        }

        return errors;
    }

    // Показ ошибок валидации
    function showValidationErrors(errors) {
        // Очищаем предыдущие ошибки
        $('.error-container').empty();
        $('.is-invalid').removeClass('is-invalid');

        // Показываем новые ошибки
        errors.forEach(function (error) {
            var errorContainer = error.field.closest('td, .col-md-12, .form-group').find('.error-container');
            if (errorContainer.length === 0) {
                errorContainer = error.field.parent().find('.error-container');
            }
            if (errorContainer.length === 0) {
                errorContainer = $('<div class="error-container"></div>');
                error.field.after(errorContainer);
            }

            errorContainer.html('<div class="text-danger">' + error.message + '</div>');
            error.field.addClass('is-invalid');
        });
    }

    // Основная функция валидации формы
    function validateForm() {
        var allErrors = [];

        // Запускаем все проверки
        allErrors = allErrors.concat(validateMaxPaymentAmount());
        allErrors = allErrors.concat(validatePaymentTypesSelection());

        // Показываем ошибки
        showValidationErrors(allErrors);

        return allErrors.length === 0;
    }

    // ===== ФУНКЦИИ РАСЧЕТОВ =====

    // Основная функция расчета общей суммы
    function calculateTotal() {
        var total = 0;
        var hasSalaryOrAdvance = false;

        // Calculate total from table rows (все выплаты)
        $('.amount-input:not(:disabled)').each(function () {
            var amount = getNumericValue($(this).val());
            total += amount;

            // Проверяем, есть ли выплата зарплаты или аванса
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            if (typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) {
                hasSalaryOrAdvance = true;
            }
        });

        // Добавляем сумму погашения долга, если чекбокс отмечен
        if ($('#debt-payment-checkbox').is(':checked')) {
            var debtAmount = getNumericValue($('#debt-payment-amount').val());
            total += debtAmount;
        }

        // Пересчитываем оставшуюся зарплату
        updateRemainingSalary();
    }

    // Обновление оставшейся зарплаты
    function updateRemainingSalary() {
        // Получаем изначальную зарплату
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;

        // Считаем сумму выплат по зарплате и авансу (только текущие выплаты в форме)
        var currentSalaryAndAdvancePayments = 0;
        
        $('.payment-type-checkbox:checked').each(function () {
            var row = $(this).closest('tr');
            var typeId = row.data('type');

            // Учитываем только зарплату и аванс
            if (typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) {
                var totalAmount = 0;
                
                // Проверяем, есть ли динамические поля
                var dynamicInputs = row.find('.dynamic-amounts input[data-method]:visible');
                var mainInput = row.find('.amount-input').not('[data-method]');

                if (dynamicInputs.length > 0) {
                    // Суммируем динамические поля
                    dynamicInputs.each(function() {
                        totalAmount += getNumericValue($(this).val());
                    });
                } else if (mainInput.is(':visible')) {
                    // Берем значение основного поля
                    totalAmount = getNumericValue(mainInput.val());
                }
                
                currentSalaryAndAdvancePayments += totalAmount;
            }
        });

        // Получаем уже выплаченную сумму (только зарплата и аванс, без текущих выплат)
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;

        // Общая выплаченная сумма = уже выплаченное + текущие выплаты зарплаты/аванса
        var totalPaidIncludingCurrent = originalTotalPaid + currentSalaryAndAdvancePayments;

        // Оставшаяся зарплата = изначальная зарплата - общая выплаченная сумма
        var remainingSalary = originalSalary - totalPaidIncludingCurrent;

        // Обновляем отображение только если есть информация о зарплате
        if ($('#remaining-salary-info').is(':visible')) {
            $('#worker-remaining-salary').text(formatNumberInput(Math.max(0, remainingSalary)));
        }
    }

    // Обновление основного поля суммы на основе динамических полей
    function updateMainAmountFromDynamicInputs(paymentRow) {
        var dynamicInputs = paymentRow.find('.dynamic-amounts input[data-method]');
        var total = 0;

        dynamicInputs.each(function() {
            var value = getNumericValue($(this).val());
            total += value;
        });

        var mainInput = paymentRow.find('.amount-input').not('[data-method]');
        if (mainInput.length) {
            mainInput.val(total > 0 ? formatNumberInput(total.toString()) : '');
        }
    }

    // Валидация сумм с автоматическим ограничением для зарплаты и аванса
    function validatePaymentAmounts(changedInput) {
        var paymentRow = changedInput.closest('tr');
        var paymentTypeId = paymentRow.data('type');

        // Для всех типов платежей просто обновляем основное поле суммы
        updateMainAmountFromDynamicInputs(paymentRow);

        // Для зарплаты и аванса применяем связанную валидацию
        if (paymentTypeId == WORKER_FINANCES_TYPE_SALARY || paymentTypeId == WORKER_FINANCES_TYPE_ADVANCE) {
            var dynamicInputs = paymentRow.find('.dynamic-amounts input[data-method]');

            if (dynamicInputs.length === 2) {
                // Есть два поля: наличные и карта
                var cashInput = dynamicInputs.filter('[data-method="' + PAYMENT_TYPE_CASH + '"]');
                var cardInput = dynamicInputs.filter('[data-method="' + PAYMENT_TYPE_PAYMENT_CARD + '"]');

                if (cashInput.length && cardInput.length) {
                    var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
                    var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
                    var maxAllowedAmount = Math.max(0, originalSalary - originalTotalPaid);

                    var cashAmount = getNumericValue(cashInput.val());
                    var cardAmount = getNumericValue(cardInput.val());

                    // Определяем, какое поле было изменено
                    var isChangedInputCash = changedInput.is(cashInput);
                    var isChangedInputCard = changedInput.is(cardInput);

                    if (isChangedInputCash) {
                        // Изменено поле наличных - ограничиваем его если превышает лимит
                        var maxCashAmount = Math.max(0, maxAllowedAmount - cardAmount);
                        if (cashAmount > maxCashAmount) {
                            cashInput.val(maxCashAmount > 0 ? formatNumberInput(maxCashAmount.toString()) : '');
                            cashAmount = maxCashAmount;
                        }
                    } else if (isChangedInputCard) {
                        // Изменено поле карты - ограничиваем его если превышает лимит
                        var maxCardAmount = Math.max(0, maxAllowedAmount - cashAmount);
                        if (cardAmount > maxCardAmount) {
                            cardInput.val(maxCardAmount > 0 ? formatNumberInput(maxCardAmount.toString()) : '');
                            cardAmount = maxCardAmount;
                        }
                    }

                    var totalAmount = cashAmount + cardAmount;

                    // Очищаем ошибки (автоматическое ограничение уже применено выше)
                    paymentRow.find('.error-container').empty();
                    dynamicInputs.removeClass('is-invalid');
                    return true;
                }
            } else {
                // Одно поле - автоматическое ограничение без показа ошибок
                var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
                var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
                var maxAllowedAmount = Math.max(0, originalSalary - originalTotalPaid);

                // Ограничиваем значение изменяемого поля
                var currentAmount = getNumericValue(changedInput.val());
                if (currentAmount > maxAllowedAmount) {
                    changedInput.val(maxAllowedAmount > 0 ? formatNumberInput(maxAllowedAmount.toString()) : '');
                }

                // Очищаем ошибки
                paymentRow.find('.error-container').empty();
                dynamicInputs.removeClass('is-invalid');
                return true;
            }
        }

        return true;
    }

    // ===== ОБРАБОТЧИКИ INPUT СОБЫТИЙ =====

    // Обработчик input событий для всех числовых полей
    $(document).on('input', '#worker-payment-create-form input[inputmode="numeric"], #worker-payment-create-form .amount-input', function () {
        var $this = $(this);
        var value = $this.val();

        // Очищаем все символы, кроме цифр, для последующего форматирования
        value = value.replace(/[^\d]/g, '');

        // Форматируем число с разделителями тысяч
        var formattedValue = formatNumberInput(value);
        $this.val(formattedValue);

        // Сохраняем значение в data-атрибуте строки для динамических полей
        if ($this.attr('data-method')) {
            var row = $this.closest('tr');
            var methodId = $this.attr('data-method');
            var amounts = row.data('amounts') || {};
            amounts[methodId] = getNumericValue(formattedValue);
            row.data('amounts', amounts);
            console.log('Updated amounts for method', methodId, ':', amounts); // Для отладки
        }

        // Логика обработки в зависимости от типа поля
        if ($this.hasClass('amount-input') && !$this.attr('data-dynamic')) {
            // Основное поле суммы типа платежа
            var row = $this.closest('tr');
            if (row.length) {
                var paymentTypeId = row.data('type');

                // Для зарплаты и аванса применяем автоматическое ограничение
                if (paymentTypeId == WORKER_FINANCES_TYPE_SALARY || paymentTypeId == WORKER_FINANCES_TYPE_ADVANCE) {
                    var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
                    var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
                    var maxAllowedAmount = Math.max(0, originalSalary - originalTotalPaid);

                    var currentAmount = getNumericValue(formattedValue);
                    if (currentAmount > maxAllowedAmount) {
                        formattedValue = maxAllowedAmount > 0 ? formatNumberInput(maxAllowedAmount.toString()) : '';
                        $this.val(formattedValue);
                    }
                }

                // Сохраняем значение для выбранных методов
                var amounts = row.data('amounts') || {};
                row.find('.payment-method-checkbox:checked').each(function() {
                    amounts[$(this).val()] = getNumericValue(formattedValue);
                });
                row.data('amounts', amounts);
            }

        } else if ($this.attr('data-dynamic') === '1' || $this.closest('.dynamic-amounts').length > 0) {
            // Динамическое поле в .dynamic-amounts

            // Всегда выполняем валидацию без автоперераспределения
            validatePaymentAmounts($this);

        } else if ($this.attr('id') === 'debt-payment-amount') {
            // Поле погашения долга
            console.log('🔍 Обработка поля погашения долга');
        }

        // Пересчитываем общую сумму
        calculateTotal();
    });

    // ===== ОБРАБОТЧИКИ ЧЕКБОКСОВ =====

    // Обработчик изменения чекбоксов типов платежей
    $(document).on('change', '.payment-type-checkbox', function () {
        var $this = $(this);
        var row = $this.closest('tr');
        var paymentMethods = row.find('.payment-method-checkbox');
        var amountInput = row.find('.amount-input');
        var dynamicAmounts = row.find('.dynamic-amounts');

        if ($this.prop('checked')) {
            // Включаем методы оплаты и поле суммы
            paymentMethods.prop('disabled', false);
            amountInput.prop('disabled', false);

            // Автоматически выбираем наличный метод оплаты, если ни один метод ещё не выбран
            if (!row.find('.payment-method-checkbox:checked').length) {
                var cashCheckbox = row.find('.payment-method-checkbox[value="' + PAYMENT_TYPE_CASH + '"]');
                if (cashCheckbox.length) {
                    cashCheckbox.prop('checked', true).trigger('change');
                }
            }
        } else {
            // Отключаем методы оплаты и очищаем поля
            paymentMethods.prop('disabled', true).prop('checked', false);
            amountInput.prop('disabled', true).val('');
            dynamicAmounts.hide().empty();
        }

        calculateTotal();
    });

    // === ИСПРАВЛЕННЫЙ ОБРАБОТЧИК ИЗМЕНЕНИЯ ЧЕКБОКСОВ МЕТОДОВ ОПЛАТЫ ===
    $(document).on('change', '.payment-method-checkbox', function () {
        var $this = $(this);
        var row = $this.closest('tr');
        var checkedMethods = row.find('.payment-method-checkbox:checked');
        var mainAmountInput = row.find('.amount-input').not('[data-method]');
        var dynamicAmounts = row.find('.dynamic-amounts');
        var paymentTypeId = row.data('type');

        // Сохраняем значения между переключениями
        // Используем data-атрибуты для хранения значений по каждому методу
        if (!row.data('amounts')) row.data('amounts', {});
        var amounts = row.data('amounts');

        // ВАЖНО: Сохраняем текущие значения ПЕРЕД изменением интерфейса
        if (dynamicAmounts.find('input[data-method]').length > 0) {
            // Если сейчас показаны динамические поля, сохраняем их значения
            dynamicAmounts.find('input[data-method]').each(function() {
                var methodId = $(this).attr('data-method');
                var value = getNumericValue($(this).val());
                if (value > 0) {
                    amounts[methodId] = value;
                }
            });
        } else if (mainAmountInput.is(':visible') && mainAmountInput.val()) {
            // Если показан основной инпут, сохраняем его значение только для уже выбранного метода
            var currentValue = getNumericValue(mainAmountInput.val());
            if (currentValue > 0) {
                // Находим ранее выбранные методы (до текущего изменения)
                var previouslyCheckedMethods = row.find('.payment-method-checkbox').filter(function() {
                    return $(this).prop('checked') && !$(this).is($this);
                });

                // Сохраняем значение только для ранее выбранных методов
                previouslyCheckedMethods.each(function() {
                    amounts[$(this).val()] = currentValue;
                });
            }
        }

        // Обновляем сохраненные данные
        row.data('amounts', amounts);

        console.log('Saved amounts:', amounts); // Для отладки

        if (checkedMethods.length > 1) {
            // Несколько методов - показываем динамические поля
            mainAmountInput.hide();
            dynamicAmounts.show();
            dynamicAmounts.empty();
            
            checkedMethods.each(function() {
                var methodId = $(this).val();
                var methodName = methodId == PAYMENT_TYPE_CASH ? 'Наличные' : 'Платежная карта';
                var prevValue = amounts[methodId] ? formatNumberInput(amounts[methodId].toString()) : '';
                
                var inputHtml = '<div class="form-group mb-2">' +
                    '<label class="form-label">' + methodName + '</label>' +
                    '<input type="text" class="form-control amount-input" ' +
                    'data-dynamic="1" data-method="' + methodId + '" ' +
                    'inputmode="numeric" pattern="[0-9\\s]*" value="' + prevValue + '">' +
                    '</div>';
                dynamicAmounts.append(inputHtml);
            });
        } else if (checkedMethods.length === 1) {
            // Один метод - показываем основное поле
            var methodId = checkedMethods.first().val();
            var prevValue = amounts[methodId] ? formatNumberInput(amounts[methodId].toString()) : '';
            
            mainAmountInput.val(prevValue);
            mainAmountInput.show();
            dynamicAmounts.hide().empty();
        } else {
            // Нет выбранных методов - скрываем все
            mainAmountInput.show().val('');
            dynamicAmounts.hide().empty();
        }

        // Обновляем основное поле суммы
        updateMainAmountFromDynamicInputs(row);
        
        // Пересчитываем общую сумму
        calculateTotal();
    });

    // Обработчик чекбокса погашения долга
    $(document).on('change', '#debt-payment-checkbox', function () {
        var $this = $(this);
        var details = $('#debt-payment-details');
        var amountInput = $('#debt-payment-amount');

        if ($this.prop('checked')) {
            details.show();
            amountInput.prop('disabled', false);
        } else {
            details.hide();
            amountInput.prop('disabled', true).val('');
        }

        calculateTotal();
    });

    // ===== ОБРАБОТЧИКИ КЛИКОВ =====

    // Делаем весь контейнер кликабельным для чекбоксов
    $(document).on('click', '.form-check', function (e) {
        if (!$(e.target).is('input[type="checkbox"], label')) {
            var checkbox = $(this).find('input[type="checkbox"]');
            if (!checkbox.prop('disabled')) {
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        }
    });

    // ===== AJAX ФУНКЦИИ =====

    // Загрузка информации о работнике
    function loadWorkerInfo(workerId, month) {
        $.ajax({
            url: '/backend/worker-payment/get-worker-info',
            type: 'GET',
            data: {
                worker_id: workerId,
                month: month
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {

                    // Обновляем информацию о работнике
                    $('#worker-salary').text(formatNumberInput(response.salary));
                    $('#worker-total-paid').text(formatNumberInput(response.total_paid));
                    $('#worker-debt-amount').text(formatNumberInput(response.debt));

                    // Показываем панель информации
                    $('#worker-info').show();

                    // Показываем оставшуюся зарплату если есть
                    if (response.salary > 0) {
                        var remaining = Math.max(0, response.salary - response.total_paid);
                        $('#worker-remaining-salary').text(formatNumberInput(remaining));
                        $('#remaining-salary-info').show();
                    }

                    // Показываем секцию погашения долга если есть долг
                    if (response.debt > 0) {
                        $('#debt-payment-section').show();
                    } else {
                        $('#debt-payment-section').hide();
                    }

                    // Пересчитываем суммы
                    calculateTotal();
                } else {
                    console.error('Error loading worker info:', response.message || 'Unknown error');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error loading worker info:', error);
            }
        });
    }

    // Подготовка данных формы для отправки
    function prepareFormData() {
        var data = {
            worker_id: $('#worker_id').val(),
            month: $('#month').val(),
            payment_types: {}
        };

        // Собираем данные по типам платежей
        $('.payment-type-checkbox:checked').each(function () {
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            var selectedMethods = row.find('.payment-method-checkbox:checked');
            var dynamicAmounts = row.find('.dynamic-amounts input[data-method]:visible');
            var mainInput = row.find('.amount-input').not('[data-method]');

            data.payment_types[typeId] = {
                amounts: {}
            };

            if (dynamicAmounts.length > 0) {
                // Несколько методов оплаты - собираем из динамических полей
                dynamicAmounts.each(function() {
                    var methodId = $(this).attr('data-method');
                    var amount = getNumericValue($(this).val());
                    if (amount > 0) {
                        data.payment_types[typeId].amounts[methodId] = amount;
                    }
                });
            } else if (mainInput.is(':visible')) {
                // Один метод оплаты - берем из основного поля
                var mainAmount = getNumericValue(mainInput.val());
                if (mainAmount > 0 && selectedMethods.length > 0) {
                    var methodId = selectedMethods.first().val();
                    data.payment_types[typeId].amounts[methodId] = mainAmount;
                }
            } else {
                // Резервный способ - берем из сохраненных данных
                var amounts = row.data('amounts') || {};
                selectedMethods.each(function() {
                    var methodId = $(this).val();
                    if (amounts[methodId] && amounts[methodId] > 0) {
                        data.payment_types[typeId].amounts[methodId] = amounts[methodId];
                    }
                });
            }
        });

        // Добавляем данные о погашении долга
        if ($('#debt-payment-checkbox').is(':checked')) {
            var debtAmount = getNumericValue($('#debt-payment-amount').val());
            if (debtAmount > 0) {
                data.payment_types[WORKER_FINANCES_TYPE_DEBT_PAYMENT] = {
                    amounts: {}
                };
                data.payment_types[WORKER_FINANCES_TYPE_DEBT_PAYMENT].amounts[PAYMENT_TYPE_CASH] = debtAmount;
            }
        }

        console.log('Prepared form data:', data); // Для отладки
        return data;
    }

    // Отправка платежа на сервер
    function submitPayment(formData) {
        $.ajax({
            url: '/backend/worker-payment/store',
            type: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('.worker-payment-submit-button').prop('disabled', true);
            },
            success: function(response) {
                console.log('Server response:', response); // Для отладки
                
                if (response.status === 'success' || response.success === true) {
                    // Показываем уведомление об успехе
                    if (typeof iziToast !== 'undefined') {
                        iziToast.success({
                            title: 'Успех',
                            message: response.message || 'Платеж успешно создан!',
                            position: 'topRight',
                            timeout: 3000
                        });
                    }

                    // Очищаем форму
                    $('#worker-payment-create-form')[0].reset();
                    $('.payment-type-checkbox').prop('checked', false).trigger('change');
                    $('#worker-info').hide();
                    $('#debt-payment-section').hide();
                    
                    // Небольшая задержка перед закрытием модального окна
                    setTimeout(function() {
                        // Триггерим событие для index (для обновления таблицы и закрытия модального окна)
                        $(document).trigger('paymentCreated');
                    }, 500);

                    // Обновляем таблицу если есть
                    if (typeof $.pjax !== 'undefined') {
                        $.pjax.reload({
                            container: '#worker-payment-grid-pjax',
                            timeout: false
                        });
                    }

                } else {
                    // Показываем ошибку
                    var errorMessage = response.message || 'Неизвестная ошибка при создании платежа';

                    if (typeof iziToast !== 'undefined') {
                        iziToast.error({
                            title: 'Ошибка',
                            message: errorMessage,
                            position: 'topRight',
                            timeout: 5000
                        });
                    } else {
                        alert('Ошибка: ' + errorMessage);
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error creating payment:', error);

                var errorMessage = 'Ошибка сервера: ' + (xhr.responseText || error);

                if (typeof iziToast !== 'undefined') {
                    iziToast.error({
                        title: 'Ошибка сервера',
                        message: errorMessage,
                        position: 'topRight',
                        timeout: 5000
                    });
                } else {
                    alert(errorMessage);
                }
            },
            complete: function() {
                $('.worker-payment-submit-button').prop('disabled', false);
            }
        });
    }

    // ===== ОБРАБОТЧИКИ СОБЫТИЙ ФОРМЫ =====

    // Обработчик выбора работника
    $(document).on('change', '#worker_id', function () {
        var workerId = $(this).val();
        var month = $('#month').val();

        if (workerId && month) {
            loadWorkerInfo(workerId, month);
        } else {
            $('#worker-info').hide();
            $('#debt-payment-section').hide();
        }
    });

    // Обработчик изменения месяца
    $(document).on('change', '#month', function () {
        var workerId = $('#worker_id').val();
        var month = $(this).val();

        if (workerId && month) {
            loadWorkerInfo(workerId, month);
        }
    });

    // Флаг, предотвращающий повторную отправку, пока запрос не завершён
    var requestInProgress = false;

    // Обработчик кнопки отправки
    $(document).on('click', '.worker-payment-submit-button', function (e) {
        e.preventDefault();
        e.stopPropagation();

        if (requestInProgress) {
            return; // защита от двойного клика
        }

        if (!validateForm()) {
            return;
        }

        requestInProgress = true;

        var formData = prepareFormData();
        submitPayment(formData);
    });

    // Сброс флага после любого завершения AJAX-запроса формы
    $(document).ajaxComplete(function () {
        requestInProgress = false;
        $('.worker-payment-submit-button').prop('disabled', false);
    });

    // ===== ДОПОЛНИТЕЛЬНЫЕ ОБРАБОТЧИКИ =====

    // Валидация ввода - разрешаем только цифры и пробелы
    $(document).on('keypress', 'input[inputmode="numeric"]', function (e) {
        var char = String.fromCharCode(e.which);
        if (!/[\d\s]/.test(char) && e.which !== 8 && e.which !== 0) {
            e.preventDefault();
        }
    });

    // Предотвращаем вставку недопустимых символов
    $(document).on('paste', 'input[inputmode="numeric"]', function (e) {
        var paste = (e.originalEvent.clipboardData || window.clipboardData).getData('text');
        if (!/^[\d\s]*$/.test(paste)) {
            e.preventDefault();
        }
    });

    // Инициализация Select2
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({
            placeholder: 'Выберите работника',
            allowClear: true,
            width: '100%',
            language: {
                noResults: function() {
                    return "Работник не найден";
                },
                searching: function() {
                    return "Поиск...";
                }
            },
            // Включаем поиск для всех элементов
            minimumResultsForSearch: 0,
            // Настройки поиска
            matcher: function(params, data) {
                // Если нет поискового запроса, показываем все результаты
                if ($.trim(params.term) === '') {
                    return data;
                }

                // Если нет текста для поиска в элементе, пропускаем его
                if (typeof data.text === 'undefined') {
                    return null;
                }

                // Поиск по тексту (нечувствительный к регистру)
                var searchTerm = params.term.toLowerCase();
                var optionText = data.text.toLowerCase();

                if (optionText.indexOf(searchTerm) > -1) {
                    return data;
                }

                // Возвращаем null если совпадений нет
                return null;
            },
            dropdownParent: $('#ideal-mini-modal').length ? $('#ideal-mini-modal') : $(document.body)
        });
    }

    // === Защита от случайного submit/click вне формы ===
    $(document).on('click', '#worker-payment-create-form button, #worker-payment-create-form input[type="button"]', function (e) {
        e.stopPropagation();
    });
    
    $(document).on('click', '#worker-payment-create-form', function (e) {
        e.stopPropagation();
    });
    
    $(document).on('click', '#ideal-mini-modal .modal-dialog', function (e) {
        e.stopPropagation();
    });

    // Дополнительная защита от случайных событий submit
    $(document).on('keydown', '#worker-payment-create-form', function (e) {
        // Блокируем отправку формы по Enter, кроме случаев когда фокус на кнопке submit
        if (e.keyCode === 13 && !$(e.target).hasClass('worker-payment-submit-button')) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    });

    // Блокируем автоматическую отправку формы браузером
    $(document).on('keypress', '#worker-payment-create-form', function (e) {
        if (e.keyCode === 13 && !$(e.target).hasClass('worker-payment-submit-button')) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    });

    console.log('Worker Payment Create Form initialized successfully');

});
</script>


