<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "product".
 *
 * @property int $id
 * @property string $name
 * @property int $size
 * @property string|null $description
 * @property string|null $created_at
 * @property string|null $deleted_at
 * @property int|null $type
 * @property int|null $priority
 * @property ProductPrice[] $productPrices
 * @property ProductStorage[] $productStorages
 * @property SalesDetail[] $salesDetails
 */
class Product extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product';
    }

    public const TYPE_FIZZY = 1;
    public const TYPE_SOFT = 0;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'size'], 'required'],
            [['size'], 'default', 'value' => null],
            [['size', 'type', 'priority'], 'integer'],
            [['description'], 'string'],
            [['created_at', 'deleted_at'], 'safe'],
            [['name'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'size' => 'Size',
            'description' => 'Description',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
            'type' => 'Type',
            'priority' => 'Priority',
        ];
    }

    /**
     * Gets query for [[ProductPrices]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProductPrices()
    {
        return $this->hasMany(ProductPrice::class, ['product_id' => 'id']);
    }

    /**
     * Gets query for [[ProductStorages]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProductStorages()
    {
        return $this->hasMany(ProductStorage::class, ['product_id' => 'id']);
    }

    /**
     * Gets query for [[SalesDetails]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSalesDetails()
    {
        return $this->hasMany(SalesDetail::class, ['product_id' => 'id']);
    }
}
