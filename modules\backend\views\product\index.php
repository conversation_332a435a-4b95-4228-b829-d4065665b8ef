<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "product_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('product_keeper')): ?>
                <a href="#" class="btn btn-primary product-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "add_product") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'product-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="product-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th><?= Yii::t("app", "product_name") ?></th>
                    <th><?= Yii::t("app", "product_price") ?></th>
                    <th><?= Yii::t("app", "product_block_quantity") ?></th>
                    <th><?= Yii::t("app", "remainder_quantity") ?></th>
                    <th><?= Yii::t("app", "total_sum") ?></th>
                    <th><?= Yii::t("app", "product_priority") ?></th>
                    <th><?= Yii::t("app", "created_at") ?></th>
                    <th><?= Yii::t("app", "status") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                        <td><?= Html::encode($model['name']) ?></td>
                        <td><?= Html::encode($model['price']) ?></td>
                        <td><?= Html::encode($model['block_quantity']) ?></td>
                        <td><?= !empty($model['remainder_quantity']) ? Html::encode($model['remainder_quantity']) : 0 ?></td>
                        <td><?= !empty($model['total_sum']) ? Html::encode($model['total_sum']) : 0 ?></td>
                        <td data-order="<?= !empty($model['priority']) ? strtotime($model['priority']) : '' ?>">
                            <?= !empty($model['priority']) ? Html::encode($model['priority']) : '' ?>
                        </td>
                        <td data-order="<?= !empty($model['created_at']) ? strtotime($model['created_at']) : '' ?>">
                            <?= !empty($model['created_at']) ? Html::encode(date('d-m-Y H:i', strtotime($model['created_at']))) : 'N/A' ?>
                        </td>
                        <td>
                            <span class="<?= $model['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?php
                                $statusText = $model['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                echo $statusText;
                                ?>
                            </span>
                        </td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <a href="#" class="dropdown-item product-view" data-toggle="modal" data-target="#ideal-large-modal-without-save" data-id="<?= Html::encode($model['id']) ?>">
                                        <?= Yii::t("app", "view") ?>
                                    </a>
                                    <?php if ($model['deleted_at'] == NULL): ?>
                                        <a href="#" class="dropdown-item product-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>
                                        <a href="#" class="dropdown-item product-price-change" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "change_price") ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "add_product") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "change_price") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "view") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "edit_product") ?>"></div>
<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');
    var four = $('#four').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#product-grid-view')) {
            $('#product-grid-view').DataTable().destroy();
        }
        
        $('#product-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[5, 'desc']],
            "columnDefs": [
                {
                    "targets": [7, 8],
                    "orderable": false
                }
            ]
        });
    }

    function initializeAll() {
        initializeDataTable();
        initializeDropdown();
        initializeProductCreate();
        initializeProductPriceChange();
        initializeProductView();
        initializeProductUpdate();
    }

    // Initialize everything on first load
    initializeAll();

    // Re-initialize after PJAX reloads
    $(document).on('pjax:success', function() {
        initializeAll();
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }
    
    function initializeProductCreate() {
        $(document).off('click.product-create').on('click.product-create', '.product-create', function() {
            $.ajax({
                url: '/backend/product/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("product-create-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.product-create-button').on('click.product-create-button', '.product-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#product-create-form').serialize();
                $.ajax({
                    url: '/backend/product/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#product-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'error') {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }






    function initializeProductUpdate() {
        $(document).off('click.product-update').on('click.product-update', '.product-update', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/product/update',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(four);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("product-update-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.product-update-button').on('click.product-update-button', '.product-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#product-update-form').serialize();
                $.ajax({
                    url: '/backend/product/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#product-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'error') {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }




  






    function initializeProductPriceChange() {
        $(document).off('click.product-price-change').on('click.product-price-change', '.product-price-change', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/product/price-change',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(two);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("product-price-change-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.product-price-change-button').on('click.product-price-change-button', '.product-price-change-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#product-price-change-form').serialize();
                $.ajax({
                    url: '/backend/product/price-change',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#product-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeProductView() {
        $(document).off('click.product-view').on('click.product-view', '.product-view', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/product/view',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-large-modal-without-save .modal-body').html(response.content);
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });
    }

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
