<?php

use yii\helpers\Html;
use yii\web\View;
use yii\data\ActiveDataProvider;
use app\assets\DataTablesAsset;

/* @var $this yii\web\View */
/* @var $dataProvider ActiveDataProvider */
/* @var $dateFrom string */
/* @var $dateTo string */
/* @var $title string */
/* @var $showAcceptedBy bool */

DataTablesAsset::register($this);

?>

<div class="material-return-report">
    <!-- Фильтр по дате -->
    <div class="row mb-3">
        <div class="col-md-6">
            <h5 class="my-0"><?= Html::encode($title) ?></h5>
        </div>
        <div class="col-md-6">
            <form class="d-flex justify-content-end align-items-center">
                <div class="input-group mr-2" style="width: 300px;">
                    <input type="date" name="date_from" class="form-control" 
                        value="<?= Html::encode($dateFrom) ?>" placeholder="<?= Yii::t('app', 'From Date') ?>">
                    <span style="width: 10px; display: inline-block"></span>
                    <input type="date" name="date_to" class="form-control" 
                        value="<?= Html::encode($dateTo) ?>" placeholder="<?= Yii::t('app', 'To Date') ?>">
                </div>
                <button type="submit" class="btn btn-primary">
                    <?= Yii::t('app', 'search') ?>
                </button>
            </form>
        </div>
    </div>

    <!-- Таблица с данными -->
    <?php if($dataProvider->getModels()): ?>
        <div>
            <table id="material-return-table" class="table table-bordered table-striped compact" data-report-type="material-return">
                <thead>
                    <tr>
                        <th>№</th>
                        <th><?= Yii::t('app', 'created_at') ?></th>
                        <th><?= Yii::t('app', 'materials') ?></th>
                        <th><?= Yii::t('app', 'who_entered') ?></th>
                        <?php if ($showAcceptedBy): ?>
                            <th><?= Yii::t('app', 'accepted_by') ?></th>
                        <?php endif; ?>
                        <th><?= Yii::t('app', 'status') ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php 
                $models = $dataProvider->getModels();
                $i = 1;
                foreach ($models as $model): ?>
                    <tr>
                        <td><?= $i++ ?></td>
                        <td data-order="<?= strtotime($model->created_at) ?>">
                            <?= Html::encode(date('d.m.Y H:i', strtotime($model->created_at))) ?>
                        </td>
                        <td>
                            <?php
                            // Получаем материалы для этой группы возврата
                            $materials = \app\common\models\MaterialStatus::find()
                                ->where(['status_group_id' => $model->id])
                                ->andWhere(['material_status.deleted_at' => null])
                                ->joinWith(['material'])
                                ->all();
                            
                            $materialsList = [];
                            foreach ($materials as $material) {
                                $materialsList[] = $material->material->name . ' (' . $material->quantity . ')';
                            }
                            echo Html::encode(implode(', ', $materialsList));
                            ?>
                        </td>
                        <td><?= Html::encode($model->addUser->full_name ?? $model->addUser->username ?? '') ?></td>
                        <?php if ($showAcceptedBy): ?>
                            <td><?= Html::encode($model->acceptedUser->full_name ?? $model->acceptedUser->username ?? '') ?></td>
                        <?php endif; ?>
                        <td>
                            <?php if ($model->accepted_at): ?>
                                <span class="badge badge-success"><?= Yii::t('app', 'accepted') ?></span>
                            <?php else: ?>
                                <span class="badge badge-warning"><?= Yii::t('app', 'not_accepted') ?></span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle mr-2"></i>
            <?= Yii::t('app', 'no_data_available') ?>
        </div>
    <?php endif; ?>
</div>

<?php
$js = <<<JS
(function($) {
    // Инициализируем DataTable для возврата материалов
    var tableElement = $('table[data-report-type="material-return"]');
    if (tableElement.length > 0) {
        tableElement.DataTable({
        "language": {
            "search": "Поиск:",
            "lengthMenu": "Показать _MENU_ записей",
            "zeroRecords": "Ничего не найдено",
            "info": "Показано _START_ до _END_ из _TOTAL_ записей",
            "infoEmpty": "Нет данных",
            "infoFiltered": "(отфильтровано из _MAX_ записей)",
            "paginate": {
                "first": "Первая",
                "last": "Последняя",
                "next": "Следующая",
                "previous": "Предыдущая"
            }
        },
        "pageLength": 25,
        "order": [[1, "desc"]], // сортировка по дате по умолчанию
        "columnDefs": [
            {"orderable": false, "targets": [0]}
        ],
        "responsive": true
        });
    }
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?> 