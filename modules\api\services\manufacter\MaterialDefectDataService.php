<?php

namespace app\modules\api\services\manufacter;

use Yii;
use yii\base\Component;
use PDO;

/**
 * Сервис для получения данных о материалах для брака
 */
class MaterialDefectDataService extends Component
{
    /**
     * Получить данные о материалах для отправки в брак
     *
     * @param string|null $date Дата для фильтрации
     * @return array
     */
    public function getMaterialDefectData($date = null)
    {
        return [
            'available_materials' => $this->getAvailableMaterials($date),
            'defect_materials' => $this->getDefectMaterials($date),
            'return_materials' => $this->getReturnMaterials($date)
        ];
    }

    /**
     * Получить доступные материалы из производства
     *
     * @param string|null $date
     * @return array
     */
    private function getAvailableMaterials($date = null)
    {
        $sql = "
            SELECT
                m.id as material_id,
                m.name as material_name,
                CASE
                    WHEN m.unit_type = 1 THEN to_char(SUM(mp.quantity), 'FM999999990')
                    WHEN SUM(mp.quantity) = floor(SUM(mp.quantity)) THEN to_char(SUM(mp.quantity), 'FM999999990')
                    ELSE to_char(SUM(mp.quantity), 'FM999999990.000')
                END as quantity,
                m.unit_type,
                " . $this->getUnitTypeCaseExpression() . " as unit_type_name
            FROM material_production mp
            JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            AND mp.quantity > 0
            " . $this->getDateFilter('mp.created_at', $date) . "
            GROUP BY m.id, m.name, m.unit_type
            ORDER BY m.name
        ";


        return $this->executeQuery($sql, $date);
    }

    /**
     * Получить материалы в браке
     *
     * @param string|null $date
     * @return array
     */
    private function getDefectMaterials($date = null)
    {
        $defectDate = !empty($date) ? $date : date('Y-m-d');

        $sql = "
            SELECT
                md.id as defect_id,
                m.name as material_name,
               CASE
                    WHEN m.unit_type = 1 THEN to_char(md.quantity, 'FM999999990')
                    WHEN md.quantity = floor(md.quantity) THEN to_char(md.quantity, 'FM999999990')
                    ELSE to_char(md.quantity, 'FM999999990.000')
                END as quantity,
                md.description,
                md.created_at,
                m.unit_type,
                " . $this->getUnitTypeCaseExpression() . " as unit_type_name,
                CASE
                    WHEN md.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                md.is_processed,
                u.full_name as added_by,
                au.full_name as accepted_by
            FROM material_defect md
            JOIN material m ON m.id = md.material_id
            LEFT JOIN users u ON u.id = md.add_user_id
            LEFT JOIN users au ON au.id = md.accepted_user_id
            WHERE md.deleted_at IS NULL 
            AND md.source = :source
            AND md.add_user_id = :user_id
            " . $this->getDateFilter('md.created_at', $date, $defectDate) . "
            ORDER BY md.created_at DESC
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':user_id', Yii::$app->user->id);
        $command->bindValue(':source', \app\common\models\MaterialDefect::SOURCE_MANUFACTURER);

        $this->bindDateParameters($command, $date, $defectDate);

        return $command->queryAll();
    }

    /**
     * Получить материалы для возврата
     *
     * @param string|null $date
     * @return array
     */
    private function getReturnMaterials($date = null)
    {
        $defectDate = !empty($date) ? $date : date('Y-m-d');

        // Получаем группы возврата с материалами одним запросом
        $sql = "
            SELECT
                msg.id as group_id,
                msg.created_at,
                msg.accepted_at,
                CASE
                    WHEN msg.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                u.full_name as added_by,
                au.full_name as accepted_by,
                ms.material_id,
                m.name as material_name,
                CASE
                    WHEN m.unit_type = 1 THEN to_char(ms.quantity, 'FM999999990')
                    WHEN ms.quantity = floor(ms.quantity) THEN to_char(ms.quantity, 'FM999999990')
                    ELSE to_char(ms.quantity, 'FM999999990.000')
                END as quantity,
                m.unit_type,
                " . $this->getUnitTypeCaseExpression() . " as unit_type_name
            FROM material_status_group msg
            LEFT JOIN users u ON u.id = msg.add_user_id
            LEFT JOIN users au ON au.id = msg.accepted_user_id
            LEFT JOIN material_status ms ON ms.status_group_id = msg.id AND ms.deleted_at IS NULL
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE msg.deleted_at IS NULL
            AND msg.status = 5
            AND msg.add_user_id = :user_id
            " . $this->getDateFilter('msg.created_at', $date, $defectDate) . "
            ORDER BY msg.created_at DESC, m.name
        ";

        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':user_id', Yii::$app->user->id);
        $this->bindDateParameters($command, $date, $defectDate);

        $results = $command->queryAll();

        // Группируем результаты по group_id
        return $this->groupReturnMaterials($results);
    }

    /**
     * Получить CASE выражение для типа единицы измерения
     *
     * @return string
     */
    private function getUnitTypeCaseExpression()
    {
        return "CASE
            WHEN m.unit_type = 1 THEN '" . Yii::t('app', 'unit_piece') . "'
            WHEN m.unit_type = 2 THEN '" . Yii::t('app', 'unit_kg') . "'
            WHEN m.unit_type = 3 THEN '" . Yii::t('app', 'unit_liter') . "'
            ELSE '" . Yii::t('app', 'unknown') . "'
        END";
    }

    /**
     * Получить фильтр по дате
     *
     * @param string $dateField
     * @param string|null $date
     * @param string|null $defectDate
     * @return string
     */
    private function getDateFilter($dateField, $date = null, $defectDate = null)
    {
        if (!empty($date)) {
            return "AND {$dateField} >= :date_start AND {$dateField} < :date_end";
        } elseif ($defectDate) {
            return "AND DATE({$dateField}) = :defect_date";
        }
        return "";
    }

    /**
     * Выполнить запрос с параметрами даты
     *
     * @param string $sql
     * @param string|null $date
     * @return array
     */
    private function executeQuery($sql, $date = null)
    {
        $command = Yii::$app->db->createCommand($sql);
        $this->bindDateParameters($command, $date);
        return $command->queryAll();
    }

    /**
     * Привязать параметры даты к команде
     *
     * @param \yii\db\Command $command
     * @param string|null $date
     * @param string|null $defectDate
     */
    private function bindDateParameters($command, $date = null, $defectDate = null)
    {
        if (!empty($date)) {
            $dateStart = $date . ' 00:00:00';
            $dateEnd = $date . ' 23:59:59';
            $command->bindValue(':date_start', $dateStart, PDO::PARAM_STR);
            $command->bindValue(':date_end', $dateEnd, PDO::PARAM_STR);
        } elseif ($defectDate) {
            $command->bindValue(':defect_date', $defectDate, PDO::PARAM_STR);
        }
    }

    /**
     * Группировать материалы возврата по группам
     *
     * @param array $results
     * @return array
     */
    private function groupReturnMaterials($results)
    {
        $grouped = [];

        foreach ($results as $row) {
            $groupId = $row['group_id'];

            if (!isset($grouped[$groupId])) {
                $grouped[$groupId] = [
                    'group_id' => $groupId,
                    'created_at' => $row['created_at'],
                    'accepted_at' => $row['accepted_at'],
                    'is_accepted' => $row['is_accepted'],
                    'added_by' => $row['added_by'],
                    'accepted_by' => $row['accepted_by'],
                    'materials' => []
                ];
            }

            if ($row['material_id']) {
                $grouped[$groupId]['materials'][] = [
                    'material_id' => $row['material_id'],
                    'material_name' => $row['material_name'],
                    'quantity' => $row['quantity'],
                    'unit_type' => $row['unit_type'],
                    'unit_type_name' => $row['unit_type_name']
                ];
            }
        }

        return array_values($grouped);
    }
}
