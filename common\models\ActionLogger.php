<?php

namespace app\common\models;

use Yii;
use yii\base\Component;

class ActionLogger 
{
    
    public static function actionLog($actionType, $tableName, $oldData = null, $newData = null)
    {
        $userId = Yii::$app->user->id ?? null;

        Yii::$app->db->createCommand()->insert('action_logs', [
            'user_id' => $userId,
            'action_type' => $actionType,
            'table_name' => $tableName,
            'old_data' => $oldData ? json_encode($oldData) : null,
            'new_data' => $newData ? json_encode($newData) : null,
            'action_time' => date('Y-m-d H:i:s'),
        ])->execute();
    }
}
