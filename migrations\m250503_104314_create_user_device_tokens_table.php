<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%user_device_tokens}}`.
 */
class m250503_104314_create_user_device_tokens_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Создаем таблицу для хранения токенов устройств пользователей
        $this->createTable('{{%user_device_tokens}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull()->comment('ID пользователя'),
            'device_token' => $this->string(255)->notNull()->comment('Токен устройства для Firebase'),
            'device_type' => $this->string(20)->notNull()->comment('Тип устройства (android, ios, web)'),
            'device_name' => $this->string(100)->null()->comment('Название устройства'),
            'is_active' => $this->boolean()->defaultValue(true)->comment('Активен ли токен'),
            'last_used_at' => $this->dateTime()->null()->comment('Дата последнего использования'),
            'created_at' => $this->dateTime()->notNull()->comment('Дата создания'),
            'updated_at' => $this->dateTime()->null()->comment('Дата обновления'),
        ]);

        // Создаем индексы для быстрого поиска
        $this->createIndex('idx-user_device_tokens-user_id', '{{%user_device_tokens}}', 'user_id');
        $this->createIndex('idx-user_device_tokens-device_token', '{{%user_device_tokens}}', 'device_token');
        $this->createIndex('idx-user_device_tokens-is_active', '{{%user_device_tokens}}', 'is_active');

        // Добавляем внешний ключ
        $this->addForeignKey(
            'fk-user_device_tokens-user_id',
            '{{%user_device_tokens}}',
            'user_id',
            '{{%users}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%user_device_tokens}}');
    }
}
