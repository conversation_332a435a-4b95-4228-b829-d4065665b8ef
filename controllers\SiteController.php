<?php

namespace app\controllers;

use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\Response;
use yii\filters\VerbFilter;
use app\models\LoginForm;
use app\models\ContactForm;

class SiteController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'only' => ['logout', 'index', 'change-language'],
                'rules' => [
                    [
                        'actions' => ['login', 'change-language'],
                        'allow' => true,
                        'roles' => ['?'],
                    ],
                    [
                        'actions' => ['logout', 'index', 'change-language'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'logout' => ['post'],
                ],
            ],
        ];
    }

    public function actionIndex()
    {
        return $this->redirect(['backend/position/index']);
    }

    /**
     * Login action.
     *
     * @return Response|string
     */
    public function actionLogin()
    {
        $this->layout = 'login';
        $model = new LoginForm();

        if ($model->load(Yii::$app->request->post()))
            if($model->login())
            {
                return $this->redirect(['/backend/position/index']);
            }

        return $this->render('login', [
            'model' => $model,
        ]);
    }

    /**
     * Logout action.
     *
     * @return Response
     */
    public function actionLogout()
    {
        Yii::$app->user->logout();

        return $this->redirect(['site/login']);
    }

    public function actionError()
    {
        $this->layout = 'error';
        return $this->render('error');
    }
    
    /**
     * Смена языка
     * 
     * @param string $language Код языка (ru, uz)
     * @return Response
     */
    public function actionChangeLanguage($language)
    {
        // Логирование для отладки
        
        // Проверяем, что язык допустимый
        if (in_array($language, ['ru', 'uz'])) {
            // Устанавливаем язык в сессию
            Yii::$app->session->set('language', $language);
            // Устанавливаем куки для сохранения выбора языка
            Yii::$app->response->cookies->add(new \yii\web\Cookie([
                'name' => 'language',
                'value' => $language,
                'expire' => time() + 86400 * 30, // 30 дней
            ]));
            
            // Устанавливаем язык для текущего запроса
            Yii::$app->language = $language;
            
        } else {
            Yii::warning("Попытка установить недопустимый язык: $language", 'language');
        }
        
        // Возвращаемся на предыдущую страницу или на главную
        $referrer = Yii::$app->request->referrer;
        return $this->redirect($referrer ?: Yii::$app->homeUrl);
    }
}
