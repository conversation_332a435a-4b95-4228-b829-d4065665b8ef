<?php

namespace app\common\models;
use app\modules\backend\models\Users;
use Yii;

/**
 * This is the model class for table "action_logs".
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $action_type
 * @property string|null $table_name
 * @property string|null $old_data
 * @property string|null $new_data
 * @property string|null $action_time
 *
 * @property Users $user
 */
class ActionLogs extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'action_logs';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id'], 'required'],
            [['user_id'], 'default', 'value' => null],
            [['user_id'], 'integer'],
            [['old_data', 'new_data', 'action_time'], 'safe'],
            [['action_type'], 'string', 'max' => 50],
            [['table_name'], 'string', 'max' => 255],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'action_type' => 'Action Type',
            'table_name' => 'Table Name',
            'old_data' => 'Old Data',
            'new_data' => 'New Data',
            'action_time' => 'Action Time',
        ];
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(Users::class, ['id' => 'user_id']);
    }
}
