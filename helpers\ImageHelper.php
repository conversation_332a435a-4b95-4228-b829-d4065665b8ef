<?php

namespace app\helpers;

use Yii;

class ImageHelper
{
    /**
     * Создает миниатюру изображения
     * @param string $sourcePath Путь к исходному изображению
     * @param string $thumbPath Путь для сохранения миниатюры
     * @param int $maxWidth Максимальная ширина
     * @param int $maxHeight Максимальная высота
     * @param int $quality Качество сжатия (1-100)
     * @return bool
     */
    public static function createThumbnail($sourcePath, $thumbPath, $maxWidth = 300, $maxHeight = 300, $quality = 80)
    {
        if (!file_exists($sourcePath)) {
            return false;
        }

        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) {
            return false;
        }

        $originalWidth = $imageInfo[0];
        $originalHeight = $imageInfo[1];
        $imageType = $imageInfo[2];

        // Вычисляем новые размеры с сохранением пропорций
        $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
        $newWidth = intval($originalWidth * $ratio);
        $newHeight = intval($originalHeight * $ratio);

        // Создаем новое изображение
        $newImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Загружаем исходное изображение
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($sourcePath);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($sourcePath);
                // Сохраняем прозрачность для PNG
                imagealphablending($newImage, false);
                imagesavealpha($newImage, true);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($sourcePath);
                break;
            default:
                return false;
        }

        // Изменяем размер
        imagecopyresampled($newImage, $source, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);

        // Сохраняем новое изображение
        $result = false;
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($newImage, $thumbPath, $quality);
                break;
            case IMAGETYPE_PNG:
                $result = imagepng($newImage, $thumbPath, intval(9 - ($quality / 10)));
                break;
            case IMAGETYPE_GIF:
                $result = imagegif($newImage, $thumbPath);
                break;
        }

        // Освобождаем память
        imagedestroy($newImage);
        imagedestroy($source);

        return $result;
    }

    /**
     * Получает URL миниатюры, создавая её при необходимости
     * @param string $imagePath Путь к изображению относительно web
     * @param int $maxWidth Максимальная ширина
     * @param int $maxHeight Максимальная высота
     * @return string
     */
    public static function getThumbnailUrl($imagePath, $maxWidth = 300, $maxHeight = 300)
    {
        if (empty($imagePath)) {
            return '/images/no-image.svg';
        }

        $fullPath = Yii::getAlias('@webroot') . $imagePath;
        if (!file_exists($fullPath)) {
            return '/images/no-image.svg';
        }

        // Создаем имя для миниатюры
        $pathInfo = pathinfo($imagePath);
        $thumbPath = $pathInfo['dirname'] . '/thumbs/' . $pathInfo['filename'] . '_' . $maxWidth . 'x' . $maxHeight . '.' . $pathInfo['extension'];
        $fullThumbPath = Yii::getAlias('@webroot') . $thumbPath;

        // Создаем папку для миниатюр если не существует
        $thumbDir = dirname($fullThumbPath);
        if (!is_dir($thumbDir)) {
            mkdir($thumbDir, 0755, true);
        }

        // Если миниатюра не существует или исходное изображение новее
        if (!file_exists($fullThumbPath) || filemtime($fullPath) > filemtime($fullThumbPath)) {
            if (!self::createThumbnail($fullPath, $fullThumbPath, $maxWidth, $maxHeight)) {
                return $imagePath; // Возвращаем оригинал если не удалось создать миниатюру
            }
        }

        return $thumbPath;
    }
}
