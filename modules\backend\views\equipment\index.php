<?php

use app\common\models\Equipment;
use app\helpers\ImageHelper;
use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "Equipments");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");
$active = Yii::t("app", "active");
$inactive = Yii::t("app", "inactive");
$repair = Yii::t("app", "repair");
$equipment = [];
$equipment = Equipment::getStatusLabels();
?>

<style>
    #equipment_filter.select2 {
        min-width: 145px !important;
        width: 100% !important;
    }

    /* Стили для контейнера Select2 (если используется библиотека Select2) */
    #equipment_filter + .select2-container {
        width: 145px !important;
    }

    /* Стили для полей ввода даты */
    input[type="date"].form-control {
        width: 150px;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }
    
    /* Оптимизация для изображений */
    .equipment-photo {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .equipment-photo:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }
    
    /* Стили для плейсхолдера загрузки изображений */
    .image-placeholder {
        transition: opacity 0.3s ease;
    }
    
    /* Стили для карусели фотографий */
    .carousel-control-prev,
    .carousel-control-next {
        opacity: 0.7;
        transition: opacity 0.2s ease;
    }
    
    .carousel-control-prev:hover,
    .carousel-control-next:hover {
        opacity: 1;
    }
    
    /* Улучшенный спиннер загрузки */
    .spinner-border {
        animation: spinner-border 0.75s linear infinite;
    }
    
    @keyframes spinner-border {
        to {
            transform: rotate(360deg);
        }
    }
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <div class="d-flex justify-content-end align-items-center gap-2">

                    <select id="equipment_filter" class="form-control select2">
                        <option value=""><?= Yii::t('app', 'select_type_status') ?></option>
                        <?php foreach($equipment as $index => $value): ?>
                           <option value="<?= $index ?>" style="text-align: left;"><?= $value ?></option>
                        <?php endforeach; ?>
                    </select>
                        <!-- Filter button -->
                        <button type="button" class="btn btn-primary" id="search-button">
                            <?= Yii::t('app', 'search') ?>
                        </button>

                <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('technical_staff')): ?>
                    <a href="#" class="btn btn-primary equipment-create" data-toggle="modal" data-target="#ideal-mini-modal">
                        <?= Yii::t("app", "Create Equipment") ?>
                    </a>
                <?php endif ?>
            </div>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'equipment-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="equipment-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th><?= Yii::t("app", "Photo") ?></th>
                    <th><?= Yii::t("app", "Name") ?></th>
                    <th><?= Yii::t("app", "Description") ?></th>
                    <th><?= Yii::t("app", "Status") ?></th>
                    <th><?= Yii::t("app", "Purchase Date") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>                <?php foreach ($result as $model): ?>
                    <?php 
                    // Получаем модель оборудования с фотографиями
                    $equipment = Equipment::findOne($model['id']);
                    $mainPhoto = $equipment ? $equipment->mainPhoto : null;
                    ?>
                    <tr class="equipment-row" data-equipment-id="<?= $model['id'] ?>" style="cursor: pointer;">                        <td>
                            <?php if ($mainPhoto): ?>
                                <?php 
                                $originalPath = '/uploads/equipment/' . Html::encode($mainPhoto->photo_path);
                                $smallThumbPath = \app\helpers\ImageHelper::getThumbnailUrl($originalPath, 80, 80);
                                ?>
                                <img src="<?= $smallThumbPath ?>"
                                     alt="Equipment"
                                     style="max-width: 50px; max-height: 50px; object-fit: cover; border-radius: 4px; cursor: pointer;"
                                     class="equipment-photo"
                                     data-id="<?= $model['id'] ?>"
                                     loading="lazy">
                            <?php elseif ($model['photo']): ?>
                                <!-- Fallback для старых записей -->
                                <?php 
                                $oldPhotoPath = '/uploads/equipment/' . Html::encode($model['photo']);
                                $oldSmallThumbPath = \app\helpers\ImageHelper::getThumbnailUrl($oldPhotoPath, 80, 80);
                                ?>
                                <img src="<?= $oldSmallThumbPath ?>"
                                     alt="Equipment"
                                     style="max-width: 50px; max-height: 50px; object-fit: cover; border-radius: 4px; cursor: pointer;"
                                     class="equipment-photo"
                                     data-id="<?= $model['id'] ?>"
                                     loading="lazy">                            <?php else: ?>
                                <img src="/images/no-image.svg" 
                                     alt="No image" 
                                     style="max-width: 50px; max-height: 50px; opacity: 0.5; border-radius: 4px;">
                            <?php endif; ?>
                        </td>
                        <td><?= Html::encode($model['name']) ?></td>
                        <td><?= Html::encode($model['description']) ?></td>
                        <td>
                            <span class="badge badge-<?= $model['status'] == 0 ? 'danger' : ($model['status'] == 1 ? 'success' : 'warning') ?>">
                                <?= $model['status'] == 0 ? Yii::t("app", "inactive") : ($model['status'] == 1 ? Yii::t("app", "active") : Yii::t("app", "repair")) ?>
                            </span>
                        </td>
                        <td data-order="<?= strtotime($model['purchase_date']) ?>">
                            <?= Yii::$app->formatter->asDate($model['purchase_date']) ?>
                        </td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <?php if ($model['deleted_at'] == NULL): ?>

                                        <a href="#" class="dropdown-item equipment-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>

                                        <a href="#" class="dropdown-item equipment-change-status" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "change_status") ?>
                                        </a>


                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>


<div id="one" data-text="<?= Yii::t("app", "Create Equipment") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "change_status") ?>"></div>



<style>
.equipment-row:hover {
    background-color: #f8f9fa !important;
    transition: background-color 0.2s ease;
}

.equipment-row:hover td {
    background-color: transparent !important;
}

.equipment-row {
    transition: background-color 0.2s ease;
}
</style>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var all = "{$all}";
    var active = "{$active}";
    var inactive = "{$inactive}";
    var repair = "{$repair}";
    var detailText = "<?= Yii::t('app', 'detail') ?>";
    var editText = "<?= Yii::t('app', 'edit') ?>";
    var changeStatusText = "<?= Yii::t('app', 'change_status') ?>";

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#equipment-grid-view')) {
            $('#equipment-grid-view').DataTable().destroy();
        }

        $('#equipment-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "columnDefs": [
                {
                    "targets": [0, 5],
                    "orderable": false
                },
                {
                    "targets": 4,
                    "render": function(data, type, row) {
                        if (type === 'display' && data) {
                            return moment(data).format('DD.MM.YYYY');
                        }
                        return data;
                    }
                }
            ]
        });
    }


    function initializeSearch() {
        $('#search-button').on('click', function() {
            var status = $('#equipment_filter').val();

            $.ajax({
                url: '/backend/equipment/search',
                type: 'POST',
                data: {
                    status: status
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#equipment-grid-pjax').html(response.content);
                        initializeDataTable();
                    } else {
                        alert(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Search error:', error);
                    alert("<?= Yii::t('app', 'Error occurred while searching') ?>");
                }
            });
        });
    }

    function initializeSelect2() {
        $('.select2:not([multiple])').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "<?= Yii::t('app', 'No results found') ?>";
                },
                searching: function() {
                    return "<?= Yii::t('app', 'Searching...') ?>";
                }
            }
        });

        // Инициализация мультиселекта
        $('.select2[multiple]').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "<?= Yii::t('app', 'No results found') ?>";
                },
                searching: function() {
                    return "<?= Yii::t('app', 'Searching...') ?>";
                }
            },
            placeholder: "<?= Yii::t('app', 'Select parts') ?>",
            allowClear: true
        });
    }

    function initializeEquipmentDecommission() {
        $(document).off('click.equipment-decommission').on('click.equipment-decommission', '.equipment-decommission', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment/decommission',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html("<?= Yii::t('app', 'decommission') ?>");
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('equipment-decommission-save');
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.equipment-decommission-save').on('click.equipment-decommission-save', '.equipment-decommission-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var id = $('.equipment-decommission').data('id');
                var formData = new FormData($('#equipment-decommission-form')[0]);

                $.ajax({
                    url: '/backend/equipment/decommission?id=' + id,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#equipment-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                            alert(response.message);
                        } else {
                            button.prop('disabled', false);
                            alert(response.message);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializeActionButtons() {
        // Кнопка "Архивировать"
        $('#btn-archive').on('click', function(e) {
            e.preventDefault();
            alert('<?= Yii::t("app", "Archive functionality will be implemented in the future") ?>');
        });

        // Кнопка "Поделиться"
        $('#btn-share').on('click', function(e) {
            e.preventDefault();
            alert('<?= Yii::t("app", "Share functionality will be implemented in the future") ?>');
        });
    }

    function initializeEquipmentPartsTab() {
        $(document).off('click.equipment-parts-tab').on('click.equipment-parts-tab', '.equipment-parts-tab', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment/parts-tab',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-large-modal-without-save .modal-title').html("<?= Yii::t('app', 'Equipment Parts') ?>");
                    $('#ideal-large-modal-without-save .modal-body').html(response.content);
                    $('#ideal-large-modal-without-save').modal('show');

                    // Инициализируем обработчики для запчастей внутри вкладки
                    initializePartUpdate();
                    initializePartPhoto();
                    initializePartStatus();
                    initializePartHistory();
                    initializePartDetach();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });
    }

    function initializeAll() {
        initializeDataTable();
        initializeSelect2();
        initializeEquipmentCreate();
        initializeEquipmentUpdate();
        initializeEquipmentPhoto();
        initializeEquipmentStatus();
        initializeEquipmentDecommission();
        initializeEquipmentPartsTab();
        initializeActionButtons();
        initializeSearch();
        initializeDropdown();
    }

    // Initialize everything on first load
    initializeAll();

    // Re-initialize after PJAX reloads
    $(document).on('pjax:success', function() {
        initializeAll();
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item:not(.spare-parts-link)', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        // Специальный обработчик для кнопки запчастей
        $(document).off('click.spare-parts-link').on('click.spare-parts-link', '.spare-parts-link', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var url = $(this).data('url');
            console.log('Navigating to URL:', url);
            window.location.href = url;
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeEquipmentCreate() {
        $(document).off('click.equipment-create').on('click.equipment-create', '.equipment-create', function() {
            $.ajax({
                url: '/backend/equipment/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("equipment-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.equipment-create-button').on('click.equipment-create-button', '.equipment-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = new FormData($('#equipment-create-form')[0]);
                $.ajax({
                    url: '/backend/equipment/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#equipment-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            // Очистка предыдущих ошибок
                            $('.form-control').removeClass('is-invalid');
                            $('.invalid-feedback').text('').hide();

                            // Отображение новых ошибок
                            $.each(response.errors, function(field, errors) {
                                var input = $('[name="' + field + '"]');
                                var errorDiv = $('#' + field + '-error');

                                input.addClass('is-invalid');
                                errorDiv.text(errors.join(', ')).show();
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }



    function initializeEquipmentUpdate() {
        $(document).off('click.equipment-update').on('click.equipment-update', '.equipment-update', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment/update',
                dataType: 'json',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("equipment-update-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.equipment-update-button').on('click.equipment-update-button', '.equipment-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = new FormData($('#equipment-update-form')[0]);
                $.ajax({
                    url: '/backend/equipment/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#equipment-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            // Очистка предыдущих ошибок
                            $('.form-control').removeClass('is-invalid');
                            $('.invalid-feedback').text('').hide();

                            // Отображение новых ошибок
                            $.each(response.errors, function(field, errors) {
                                var input = $('[name="' + field + '"]');
                                var errorDiv = $('#' + field + '-error');

                                input.addClass('is-invalid');
                                errorDiv.text(errors.join(', ')).show();
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }






    function initializeEquipmentPhoto() {
        $(document).off('click.equipment-photo');

        $(document).on('click.equipment-photo', '.equipment-photo', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment/get-photo',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-large-modal-without-save .modal-title').text(response.name);
                        var content = '<div class="text-center"><img src="' + response.photo + '" alt="" style="max-width: 100%;"></div>';
                        $('#ideal-large-modal-without-save .modal-body').html(content);
                        $('#ideal-large-modal-without-save').modal('show');
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert("<?= Yii::t('app', 'Error loading photo') ?>");
                }
            });
        });

        // Очистка содержимого при закрытии
        $('#ideal-large-modal-without-save').on('hidden.bs.modal', function () {
            $('#ideal-large-modal-without-save .modal-body').html('');
            $('#ideal-large-modal-without-save .modal-title').text('');
        });
    }

    function initializeEquipmentStatus() {
        $(document).off('click.equipment-change-status').on('click.equipment-change-status', '.equipment-change-status', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/equipment/change-status',
                type: 'GET',
                data: {id: id},
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(two);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('equipment-status-save');
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.equipment-status-save').on('click.equipment-status-save', '.equipment-status-save', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var id = $('.equipment-change-status').data('id');
                var formData = new FormData($('#equipment-status-form')[0]);

                // Проверяем, выбран ли статус "Списать" (STATUS_INACTIVE = 0)
                var selectedStatus = $('#equipment-status').val();

                // Если выбран статус "Списать", собираем выбранные запчасти
                if (selectedStatus == '0') {
                    // Получаем выбранные запчасти через чекбоксы
                    var selectedParts = [];
                    $('#parts-selection-block input[type="checkbox"]:checked').each(function() {
                        selectedParts.push($(this).val());
                    });

                    // Добавляем выбранные запчасти в formData
                    selectedParts.forEach(function(partId) {
                        formData.append('parts[]', partId);
                    });

                }

                $.ajax({
                    url: '/backend/equipment/change-status?id=' + id,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#equipment-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            alert(response.message);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });

        // Обработчик клика по строкам таблицы для перехода к запчастям
        $(document).off('click.equipment-row').on('click.equipment-row', '.equipment-row', function(e) {
            // Проверяем, что клик не был по dropdown или его элементам
            if (!$(e.target).closest('.dropdown').length &&
                !$(e.target).hasClass('dropdown-toggle') &&
                !$(e.target).hasClass('dropdown-item') &&
                !$(e.target).closest('.equipment-photo').length) {

                var equipmentId = $(this).data('equipment-id');
                if (equipmentId) {
                    window.location.href = '/backend/equipment/' + equipmentId + '/parts';
                }            }
        });

        // Предотвращаем переход при клике на dropdown
        $(document).off('click.dropdown-prevent').on('click.dropdown-prevent', '.dropdown, .dropdown-toggle, .dropdown-item', function(e) {
            e.stopPropagation();
        });

        // Предотвращаем переход при клике на фото (для увеличения)
        $(document).off('click.photo-prevent').on('click.photo-prevent', '.equipment-photo', function(e) {
            e.stopPropagation();
            
            // Показываем модальное окно с фотографиями
            const equipmentId = $(this).data('id');
            showEquipmentPhotos(equipmentId);
        });
    }
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);

// Отдельный JavaScript для фотографий
$photoJs = <<<JS
// Вспомогательные функции для избежания проблем с кавычками
function hideImagePlaceholder(img) {
    var placeholder = img.parentNode.querySelector('.image-placeholder');
    if (placeholder) {
        placeholder.style.display = 'none';
    }
}

function handleImageError(img) {
    img.src = '/images/no-image.svg';
    hideImagePlaceholder(img);
}

function openOriginalImage(url) {
    window.open(url, '_blank');
}

// Функция для показа фотографий оборудования
function showEquipmentPhotos(equipmentId) {
    // Показываем загрузчик сразу с улучшенным дизайном
    $('#ideal-large-modal-without-save .modal-title').html('<i class="fa fa-spinner fa-spin"></i> Загрузка фотографий...');
    $('#ideal-large-modal-without-save .modal-body').html(
        '<div class="text-center p-4">' +
            '<div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">' +
                '<span class="sr-only">Загрузка...</span>' +
            '</div>' +
            '<br><br><p class="text-muted">Подготовка фотографий...</p>' +
        '</div>'
    );
    $('#ideal-large-modal-without-save').modal('show');
    
    $.ajax({
        url: '/backend/equipment/get-photo',
        type: 'GET',
        data: { id: equipmentId },
        timeout: 15000, // Увеличиваем таймаут до 15 секунд
        success: function(response) {
            if (response.status === 'success' && response.photos && Array.isArray(response.photos) && response.photos.length > 0) {
                // Сохраняем фотографии глобально для предзагрузки
                window.currentPhotos = response.photos;
                  // Обновляем заголовок
                $('#ideal-large-modal-without-save .modal-title').html('Фотографии: ' + response.name + ' (' + response.photos.length + ')');
                
                // Создаем HTML более эффективно с улучшенным дизайном
                let photosArray = [];
                let indicatorsArray = [];
                
                if (response.photos && Array.isArray(response.photos)) {
                    response.photos.forEach((photo, index) => {
                        if (!photo || !photo.path) {
                            return; // Пропускаем некорректные фотографии
                        }
                        
                        const activeClass = index === 0 ? 'active' : '';
                        const mainBadge = photo.is_main ? 
                            '<span class="badge badge-primary position-absolute" style="top: 15px; right: 15px; z-index: 10; font-size: 0.8em;">' +
                                '<i class="fa fa-star"></i> Главная' +
                            '</span>' : '';
                    
                    // Используем прогрессивную загрузку с плейсхолдером
                    photosArray.push(
                        '<div class="carousel-item ' + activeClass + '">' +
                            '<div style="position: relative; background: #f8f9fa; min-height: 400px; display: flex; align-items: center; justify-content: center;">' +
                                // Плейсхолдер пока грузится изображение
                                '<div class="image-placeholder" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center; background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%); background-size: 20px 20px; background-position: 0 0, 0 10px, 10px -10px, -10px 0px;">' +
                                    '<i class="fa fa-image fa-3x text-muted"></i>' +
                                '</div>' +                                '<img src="' + photo.path + '" ' +
                                'class="d-block w-100 equipment-photo-img" ' +
                                'style="max-height: 70vh; object-fit: contain; position: relative; z-index: 5;" ' +
                                'loading="lazy" ' +
                                'onload="hideImagePlaceholder(this)" ' +
                                'onerror="handleImageError(this)">' +
                                mainBadge +
                                // Кнопка для просмотра оригинала
                                (photo.original_path ? 
                                    '<button class="btn btn-sm btn-secondary position-absolute" style="bottom: 15px; left: 15px; z-index: 10;" onclick="openOriginalImage(' + "'" + photo.original_path + "'" + ')">' +
                                        '<i class="fa fa-expand"></i> Оригинал' +
                                    '</button>' : '') +
                            '</div>' +                        '</div>'
                    );
                    
                    indicatorsArray.push(
                        '<li data-target="#equipmentPhotoCarousel" data-slide-to="' + index + '" class="' + activeClass + '" style="background-color: #007bff; border-radius: 50%; width: 12px; height: 12px; margin: 0 4px;"></li>'
                    );
                });
                } // Закрывающая скобка для if (response.photos && Array.isArray(response.photos))
                
                // Собираем весь HTML за один раз с улучшенным дизайном
                const modalContent = 
                    '<div id="equipmentPhotoCarousel" class="carousel slide" data-ride="carousel" data-interval="false">' +
                        '<ol class="carousel-indicators" style="bottom: -50px;">' +
                            indicatorsArray.join('') +
                        '</ol>' +
                        '<div class="carousel-inner" style="border-radius: 8px; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">' +
                            photosArray.join('') +
                        '</div>' +
                        '<a class="carousel-control-prev" href="#equipmentPhotoCarousel" role="button" data-slide="prev" style="background: linear-gradient(to right, rgba(0,0,0,0.5), transparent); border-radius: 8px 0 0 8px;">' +
                            '<span class="carousel-control-prev-icon" style="background-image: url(\'data:image/svg+xml;charset=utf8,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 fill=%22%23fff%22 viewBox=%220 0 8 8%22%3E%3Cpath d=%22M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z%22/%3E%3C/svg%3E\');"></span>' +
                        '</a>' +
                        '<a class="carousel-control-next" href="#equipmentPhotoCarousel" role="button" data-slide="next" style="background: linear-gradient(to left, rgba(0,0,0,0.5), transparent); border-radius: 0 8px 8px 0;">' +
                            '<span class="carousel-control-next-icon" style="background-image: url(\'data:image/svg+xml;charset=utf8,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 fill=%22%23fff%22 viewBox=%220 0 8 8%22%3E%3Cpath d=%22M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z%22/%3E%3C/svg%3E\');"></span>' +
                        '</a>' +
                    '</div>' +
                    '<div class="text-center mt-4">' +
                        '<small class="text-muted">Всего фотографий: ' + response.photos.length + '</small>' +
                    '</div>';
                  // Вставляем HTML одной операцией
                $('#ideal-large-modal-without-save .modal-body').html(modalContent);
                
                // Предзагружаем следующие 2-3 изображения для плавной навигации
                if (response.photos && response.photos.length > 0) {
                    preloadNextImages(response.photos, 0, 3);
                }
                
            } else {
                $('#ideal-large-modal-without-save .modal-title').html('Фотографии не найдены');
                $('#ideal-large-modal-without-save .modal-body').html(
                    '<div class="text-center p-4">' +
                        '<div style="padding: 40px; background: #f8f9fa; border-radius: 8px; border: 2px dashed #ddd;">' +
                            '<i class="fa fa-image fa-3x text-muted mb-3"></i><br>' +
                            '<h5 class="text-muted">Фотографии отсутствуют</h5>' +
                            '<p class="text-muted">У этого оборудования пока нет фотографий</p>' +
                        '</div>' +
                    '</div>'
                );
            }
        },
        error: function(xhr, textStatus, errorThrown) {
            $('#ideal-large-modal-without-save .modal-title').html('Ошибка загрузки');
            let errorMessage = 'Неизвестная ошибка';
            if (textStatus === 'timeout') {
                errorMessage = 'Время ожидания истекло. Попробуйте еще раз.';
            } else if (xhr.status === 404) {
                errorMessage = 'Фотографии не найдены';
            } else if (xhr.status === 500) {
                errorMessage = 'Ошибка сервера. Обратитесь к администратору.';
            }
            
            $('#ideal-large-modal-without-save .modal-body').html(
                '<div class="text-center p-4">' +
                    '<div style="padding: 40px; background: #fff5f5; border-radius: 8px; border: 2px dashed #f56565;">' +
                        '<i class="fa fa-exclamation-triangle fa-3x text-danger mb-3"></i><br>' +
                        '<h5 class="text-danger">Ошибка загрузки</h5>' +
                        '<p class="text-muted">' + errorMessage + '</p>' +
                        '<button class="btn btn-primary btn-sm" onclick="showEquipmentPhotos(' + equipmentId + ')">' +
                            '<i class="fa fa-refresh"></i> Попробовать снова' +
                        '</button>' +
                    '</div>' +
                '</div>'
            );
        }
    });
}

// Функция предзагрузки следующих изображений для плавной навигации
function preloadNextImages(photos, currentIndex, count) {
    if (!photos || !Array.isArray(photos) || photos.length === 0) {
        return;
    }
    
    const startIndex = currentIndex + 1;
    const endIndex = Math.min(startIndex + count, photos.length);
    
    for (let i = startIndex; i < endIndex; i++) {
        if (photos[i] && photos[i].path) {
            const img = new Image();
            img.src = photos[i].path;
        }
    }
}

// Добавляем обработчик смены слайдов для предзагрузки
$(document).on('slide.bs.carousel', '#equipmentPhotoCarousel', function(e) {
    const photos = window.currentPhotos; // Сохраняем фотографии глобально
    if (photos && Array.isArray(photos) && photos.length > 0 && typeof e.to === 'number') {
        preloadNextImages(photos, e.to, 2);
    }
});
JS;
$this->registerJs($photoJs, View::POS_END);
?>
