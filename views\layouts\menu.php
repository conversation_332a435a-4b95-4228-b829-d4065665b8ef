<?php

use yii\helpers\Html;
use yii\helpers\Url;
?>

<style>
/* Отключаем стрелку после пункта меню с выпадающим списком */
.nav-link.has-dropdown::after {
    display: none !important;
}

/* Увеличиваем специфичность селектора для выпадающего меню */
.navbar .nav-item .dropdown-menu {
    min-width: 250px !important; /* Увеличиваем минимальную ширину */
    max-width: 350px !important; /* Ограничиваем максимальную ширину */
    width: auto !important; /* Позволяем ширине адаптироваться к содержимому */
    white-space: normal !important; /* Разрешаем перенос текста */
    overflow-wrap: break-word !important; /* Перенос длинных слов */
    padding: 0.5rem 0 !important; /* Отступы для читаемости */
}

/* Стили для элементов внутри выпадающего меню */
.navbar .nav-item .dropdown-menu .dropdown-item {
    padding: 0.5rem 1rem !important; /* Увеличиваем отступы */
    white-space: normal !important; /* Убеждаемся, что текст переносится */
    overflow-wrap: break-word !important; /* Перенос слов */
    line-height: 1.5 !important; /* Улучшаем читаемость */
}

/* Убеждаемся, что родительский контейнер не обрезает меню */
.navbar-nav, .nav-item, .nav-collapse {
    overflow: visible !important; /* Предотвращаем обрезку */
}


</style>


<div class="navbar-bg" style="height: 70px;"></div>
    <nav class="navbar navbar-expand-lg main-navbar">
        <a href="<?= Url::home() ?>" class="navbar-brand sidebar-gone-hide"><?php echo Yii::$app->params['projectName']; ?></a>
        <a href="#" class="nav-link sidebar-gone-show" data-toggle="sidebar"><i class="fas fa-bars"></i></a>
        <div class="nav-collapse">
            <a class="sidebar-gone-show nav-collapse-toggle nav-link" href="#">
                <i class="fas fa-ellipsis-v"></i>
            </a>

            <ul class="navbar-nav">
                <?php
                // Определяем, какое меню показать в зависимости от роли пользователя
                if (Yii::$app->user->can('admin')) {
                    // Меню для администратора - полный доступ
                    echo $this->render('partials/menu-admin');
                    
                } elseif (Yii::$app->user->can('accountant')) {
                    // Меню для бухгалтера - финансы и работники
                    echo $this->render('partials/menu-accountant');
                    
                } elseif (Yii::$app->user->can('technical_staff')) {
                    // Меню для технического сотрудника - оборудование
                    echo $this->render('partials/menu-technical');
                    
                } elseif (Yii::$app->user->can('sales')) {
                    // Меню для продаж - только инвойсы
                    echo $this->render('partials/menu-sales');
                    
                } elseif (Yii::$app->user->can('product_keeper')) {
                    // Меню для кладовщика продукции
                    echo $this->render('partials/menu-product-keeper');
                    
                } else {
                    // Для остальных ролей показываем базовое меню или перенаправляем
                    echo '<li class="nav-item"><a href="' . Url::to(['/site/logout']) . '" class="nav-link text-warning">Нет доступа</a></li>';
                }
                ?>
            </ul>
        </div>

        <ul class="navbar-nav navbar-right ml-auto">
            <!-- Переключатель языка -->
            <li class="dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg">
                    <i class="fas fa-language"></i>
                    <div class="d-sm-none d-lg-inline-block">
                        <?php echo strtoupper(Yii::$app->language); ?>
                    </div>
                </a>
                <div class="dropdown-menu dropdown-menu-right">
                    <?= Html::a('Русский', ['/site/change-language', 'language' => 'ru'], [
                        'class' => 'dropdown-item' . (Yii::$app->language == 'ru' ? ' active' : ''),
                        'data-method' => 'post',
                    ]) ?>
                    <?= Html::a('Ўзбекча', ['/site/change-language', 'language' => 'uz'], [
                        'class' => 'dropdown-item' . (Yii::$app->language == 'uz' ? ' active' : ''),
                        'data-method' => 'post',
                    ]) ?>
                </div>
            </li>

            <li class="dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg nav-link-user">
                    <div class="d-sm-none d-lg-inline-block">
                        <?php echo ((isset(Yii::$app->user->identity)) ? Yii::$app->user->identity->username : '') ?>
                    </div>
                </a>

                <div class="dropdown-menu dropdown-menu-right">
                    <?php if (Yii::$app->user->isGuest): ?>
                        <?= Html::a('<i class="fas fa-sign-in-alt"></i> Login', ['/site/login'], ['class' => 'dropdown-item has-icon']) ?>
                    <?php else: ?>
                        <?= Html::a(
                            '<i class="fas fa-sign-out-alt"></i> ' . Yii::t('app', 'Chiqish'),
                            ['/site/logout'],
                            [
                                'class' => 'dropdown-item has-icon text-danger',
                                'data-method' => 'post',
                            ]
                        ) ?>
                    <?php endif; ?>
                </div>
            </li>
        </ul>
    </nav>
