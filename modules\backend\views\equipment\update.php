<?php
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<div class="equipment-form">
    <?php $form = ActiveForm::begin(['id' => 'equipment-update-form', 'options' => ['enctype' => 'multipart/form-data']]); ?>

    <input type="hidden" name="id" value="<?= $model->id ?>">

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Name') ?></label>
                <input type="text" class="form-control" name="name" value="<?= Html::encode($model->name) ?>" required>
                <div class="invalid-feedback" id="name-error"></div>
            </div>
        </div>        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Photos') ?></label>
                
                <!-- Existing photos -->
                <div id="existing-photos" class="mb-3">
                    <?php if (!empty($model->photos)): ?>
                        <div class="row">
                            <?php foreach ($model->photos as $photo): ?>
                                <div class="col-md-3 mb-2" id="photo-<?= $photo->id ?>">
                                    <div class="card">
                                        <img src="/uploads/equipment/<?= Html::encode($photo->photo_path) ?>" 
                                             class="card-img-top" 
                                             style="height: 150px; object-fit: cover;" 
                                             alt="Equipment photo">
                                        <div class="card-body p-2">
                                            <div class="btn-group w-100">
                                                <?php if (!$photo->is_main): ?>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-primary set-main-photo" 
                                                            data-photo-id="<?= $photo->id ?>">
                                                        <?= Yii::t('app', 'Set as main') ?>
                                                    </button>
                                                <?php else: ?>
                                                    <span class="badge bg-primary"><?= Yii::t('app', 'Main') ?></span>
                                                <?php endif; ?>
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-danger delete-photo" 
                                                        data-photo-id="<?= $photo->id ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Add new photos -->
                <input type="file" class="form-control" name="photos[]" accept="image/*" multiple>
                <small class="form-text text-muted"><?= Yii::t('app', 'You can add more photos') ?></small>
                <div class="invalid-feedback" id="photos-error"></div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Purchase Date') ?></label>
                <input type="date" class="form-control" name="purchase_date" value="<?= Html::encode($model->purchase_date) ?>" required
                       onkeydown="restrictInput(event)" oninput="validateAmount(this)">
                <div class="invalid-feedback" id="purchase_date-error"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label"><?= Yii::t('app', 'Status') ?></label>
                <select class="form-control select2" name="status" required>
                    <option value="1" <?= $model->status == '1' ? 'selected' : '' ?>><?= Yii::t('app', 'active') ?></option>
                    <option value="0" <?= $model->status == '0' ? 'selected' : '' ?>><?= Yii::t('app', 'inactive') ?></option>
                    <option value="2" <?= $model->status == '2' ? 'selected' : '' ?>><?= Yii::t('app', 'repair') ?></option>
                </select>
                <div class="invalid-feedback" id="status-error"></div>
            </div>
        </div>
    </div>



    <div class="form-group mt-3">
        <label class="form-label"><?= Yii::t('app', 'Description') ?></label>
        <textarea class="form-control" name="description" rows="3"><?= Html::encode($model->description) ?></textarea>
        <div class="invalid-feedback" id="description-error"></div>
    </div>


    <?php ActiveForm::end(); ?>
</div>

<style>
.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.is-invalid ~ .invalid-feedback {
    display: block;
}

.form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
</style>

<script>
    function restrictInput(event) {
        if (event.key === 'e' || event.key === 'E' || event.key === '-' || event.key === '+') {
            event.preventDefault();
        }
    }

    function validateAmount(input) {
        input.value = input.value.replace(/[^0-9.]/g, '');
        if (input.value.startsWith('0')) {
            input.value = '';
        }
    }

    // Handle photo deletion
    $(document).on('click', '.delete-photo', function() {
        const photoId = $(this).data('photo-id');
        
        if (confirm('<?= Yii::t('app', 'Are you sure you want to delete this photo?') ?>')) {
            $.ajax({
                url: '/backend/equipment/delete-photo',
                type: 'POST',
                data: {
                    photo_id: photoId,
                    '<?= Yii::$app->request->csrfParam ?>': '<?= Yii::$app->request->csrfToken ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#photo-' + photoId).fadeOut(300, function() {
                            $(this).remove();
                        });
                        toastr.success(response.message);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('<?= Yii::t('app', 'An error occurred') ?>');
                }
            });
        }
    });

    // Handle setting main photo
    $(document).on('click', '.set-main-photo', function() {
        const photoId = $(this).data('photo-id');
        
        $.ajax({
            url: '/backend/equipment/set-main-photo',
            type: 'POST',
            data: {
                photo_id: photoId,
                '<?= Yii::$app->request->csrfParam ?>': '<?= Yii::$app->request->csrfToken ?>'
            },
            success: function(response) {
                if (response.status === 'success') {
                    // Remove all main badges and set-main buttons
                    $('.badge.bg-primary, .set-main-photo').remove();
                    
                    // Add main badge to current photo
                    $('#photo-' + photoId + ' .btn-group').html('<span class="badge bg-primary"><?= Yii::t('app', 'Main') ?></span><button type="button" class="btn btn-sm btn-outline-danger delete-photo" data-photo-id="' + photoId + '"><i class="fas fa-trash"></i></button>');
                    
                    // Add set-main buttons to other photos
                    $('.card .btn-group').each(function() {
                        if (!$(this).find('.badge').length) {
                            const currentPhotoId = $(this).closest('[id^="photo-"]').attr('id').replace('photo-', '');
                            $(this).prepend('<button type="button" class="btn btn-sm btn-outline-primary set-main-photo" data-photo-id="' + currentPhotoId + '"><?= Yii::t('app', 'Set as main') ?></button>');
                        }
                    });
                    
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('<?= Yii::t('app', 'An error occurred') ?>');
            }
        });
    });
</script>