<?php
use yii\helpers\Html;
use app\assets\Select2Asset;
use yii\helpers\ArrayHelper;

Select2Asset::register($this);

$products = \app\common\models\Product::find()
    ->select(['id', 'name'])
    ->where(['deleted_at' => null])
    ->asArray()
    ->all();

// Материалы и их список теперь передаются из контроллера
$productsList = ArrayHelper::map($products, 'id', 'name');
?>

<div class="product-ingredients-form">
    <form id="product-ingredients-create-form">
        <div class="form-group">
            <label for="product_id"><?= Yii::t('app', 'product') ?></label>
            <select id="product_id" name="ProductIngredients[product_id]" class="form-control select2" required>
                <option value=""><?= Yii::t('app', 'select_product') ?></option>
                <?php foreach ($productsList as $id => $name): ?>
                    <option value="<?= $id ?>"><?= Html::encode($name) ?></option>
                <?php endforeach; ?>
            </select>
            <div class="error-container text-danger" id="product_id-error"></div>
        </div>

        <div class="form-group">
            <label for="material_ids"><?= Yii::t('app', 'Materials') ?></label>
            <select id="material_ids" name="ProductIngredients[material_ids][]" class="form-control select2" multiple required>
                <?php
                // Группируем материалы по категориям для удобства выбора
                $materialsByCategory = [];
                foreach ($materials as $material) {
                    $categoryId = $material['category_id'] ?? 0;
                    $categoryName = 'Без категории';

                    if ($categoryId) {
                        $category = \app\common\models\MaterialCategory::findOne($categoryId);
                        if ($category) {
                            $categoryName = $category->name;
                        }
                    }

                    if (!isset($materialsByCategory[$categoryName])) {
                        $materialsByCategory[$categoryName] = [];
                    }

                    $materialsByCategory[$categoryName][] = $material;
                }

                // Выводим материалы, сгруппированные по категориям
                foreach ($materialsByCategory as $categoryName => $categoryMaterials):
                ?>
                    <optgroup label="<?= Html::encode($categoryName) ?>">
                        <?php foreach ($categoryMaterials as $material): ?>
                            <option value="<?= $material['id'] ?>"><?= Html::encode($material['name']) ?></option>
                        <?php endforeach; ?>
                    </optgroup>
                <?php endforeach; ?>
            </select>
            <div class="error-container text-danger" id="material_ids-error"></div>
        </div>

        <div class="form-group">
            <label for="alternative_material_ids"><?= Yii::t('app', 'alternative_ingredients') ?></label>
            <select id="alternative_material_ids" name="alternative_material_ids[]" class="form-control select2" multiple>
                <?php
                // Группируем материалы по категориям для удобства выбора
                $materialsByCategory = [];
                foreach ($materials as $material) {
                    $categoryId = $material['category_id'] ?? 0;
                    $categoryName = 'Без категории';

                    if ($categoryId) {
                        $category = \app\common\models\MaterialCategory::findOne($categoryId);
                        if ($category) {
                            $categoryName = $category->name;
                        }
                    }

                    if (!isset($materialsByCategory[$categoryName])) {
                        $materialsByCategory[$categoryName] = [];
                    }

                    $materialsByCategory[$categoryName][] = $material;
                }

                // Выводим материалы, сгруппированные по категориям
                foreach ($materialsByCategory as $categoryName => $categoryMaterials):
                ?>
                    <optgroup label="<?= Html::encode($categoryName) ?>">
                        <?php foreach ($categoryMaterials as $material): ?>
                            <option value="<?= $material['id'] ?>"><?= Html::encode($material['name']) ?></option>
                        <?php endforeach; ?>
                    </optgroup>
                <?php endforeach; ?>
            </select>
            <div class="help-block">
                <?= Yii::t('app', 'Select alternative materials that can be used if main materials are not available') ?>
                <br>
                <strong><?= Yii::t('app', 'Note: Alternative materials should be from the same category as the main materials') ?></strong>
            </div>
            <div class="error-container text-danger" id="alternative_material_ids-error"></div>
        </div>

    </form>
</div>

<?php
$js = <<<JS
$(document).ready(function() {
    $('.select2').select2({
        width: '100%',
        language: {
            noResults: function() {
                return "Натижа топилмади";
            }
        }
    });
});
JS;
Yii::$app->view->registerJs($js);
?>