<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "tracking".
 *
 * @property int $id
 * @property int $progress_type
 * @property int $process_id
 * @property string|null $created_at
 * @property int|null $status
 * @property string|null $accepted_at
 * @property string|null $deleted_at
 */
class Tracking extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'tracking';
    }

    const TYPE_EXPENSES = 1;
    const TYPE_SALES = 2;
    const TYPE_CLIENT_PAY = 3;
    const MATERIAL_INCOME = 4;
    const PAY_FOR_SUPPLIER = 5;
    const PAY_FOR_CLIENT = 6;
    const PAY_FOR_WORKER = 7;
    const TYPE_MATERIAL_INCOME = 8;
    const TYPE_MATERIAL_OUTCOME = 9;
    const TYPE_MATERIAL_DEFECT = 10;
    const TYPE_ACCEPT_PRODUCTION_MATERIAL = 11;
    const TYPE_PRODUCT_RELEASE = 12;
    const TYPE_PRODUCT_DEFECT = 13;
    const TYPE_CLIENT_INCOME = 14;

    const TYPE_NEW_SALES = 15;
    const TYPE_COMPLETED_SALES = 16;
    const TYPE_TRANSFER_CASHBOX  = 17;
    const TYPE_FREE_PRODUCTS = 18;
    const TYPE_RETURN_PRODUCT = 19;
    const TYPE_MATERIAL_RETURN = 20;
    const TYPE_PRODUCT_REPACKAGING = 21;

    CONST STATUS_ACCEPTED = 1;
    CONST STATUS_NOT_ACCEPTED = 0;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['progress_type', 'process_id'], 'required'],
            [['progress_type', 'process_id', 'status'], 'default', 'value' => null],
            [['progress_type', 'process_id', 'status'], 'integer'],
            [['created_at', 'accepted_at', 'deleted_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'progress_type' => 'Progress Type',
            'process_id' => 'Process ID',
            'created_at' => 'Created At',
            'status' => 'Status',
            'accepted_at' => 'Accepted At',
        ];
    }
}
