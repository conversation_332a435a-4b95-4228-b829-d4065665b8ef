<?php

namespace app\modules\api\models;

use Yii;
use yii\base\Model;

/**
 * Модель формы для обновления инвойса через API
 */
class InvoiceUpdateForm extends Model
{
    public $invoice_id;
    public $client_id;
    public $driver_id;
    public $products = [];
    public $car_number;
    public $bonus = [];
    public $has_bonus;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['invoice_id', 'client_id', 'products'], 'required'],
            [['invoice_id', 'client_id', 'driver_id'], 'integer'],
            [['car_number'], 'string', 'max' => 20],
            ['products', 'validateProducts'],
            ['bonus', 'validateBonus', 'skipOnEmpty' => false],
            ['has_bonus', 'boolean'],
            ['bonus', 'validateBonus', 'when' => function ($model) {
                return $model->has_bonus;
            }],
        ];
    }

    /**
     * Валидация продуктов
     *
     * @param string $attribute Атрибут
     * @param array $params Параметры
     */
    public function validateProducts($attribute, $params)
    {
        if (!is_array($this->products) || empty($this->products)) {
            $this->addError($attribute, Yii::t('app', 'fill_all_fields'));
            return;
        }

        foreach ($this->products as $product) {
            if (empty($product['product_id'])) {
                $this->addError($attribute, Yii::t('app', 'fill_all_fields'));
                return;
            }

            // Проверяем quantity_block, если оно указано
            if (!empty($product['quantity_block']) && $product['quantity_block'] <= 0) {
                $this->addError($attribute, Yii::t('app', 'quantity_block_error'));
                return;
            }

            // Цены будут установлены автоматически на бэкенде
        }
    }

    /**
     * Валидация бонусных продуктов
     *
     * @param string $attribute Атрибут
     * @param array $params Параметры
     */
    public function validateBonus($attribute, $params)
    {
        // Если has_bonus = false, то бонусные продукты не нужны
        if (!$this->has_bonus) {
            // Если при этом указаны бонусные продукты, выдаем предупреждение
            if (is_array($this->bonus) && !empty($this->bonus)) {
                $this->addError($attribute, Yii::t('app', 'bonus_products_ignored_when_has_bonus_false'));
            }
            return;
        }

        // Если has_bonus = true, но бонусные продукты не указаны, это ошибка
        if (!is_array($this->bonus) || empty($this->bonus)) {
            $this->addError($attribute, Yii::t('app', 'bonus_products_required_when_has_bonus_true'));
            return;
        }

        // Валидация каждого бонусного продукта
        foreach ($this->bonus as $index => $bonus) {
            if (empty($bonus['product_id'])) {
                $this->addError("bonus[$index][product_id]", Yii::t('app', 'fill_all_fields'));
                return;
            }

            // Проверяем, что указано хотя бы одно из полей: quantity или quantity_block
            if (empty($bonus['quantity']) && empty($bonus['quantity_block'])) {
                $this->addError("bonus[$index][product_id]", Yii::t('app', 'fill_all_fields'));
                return;
            }

            // Проверяем quantity, если оно указано
            if (!empty($bonus['quantity']) && $bonus['quantity'] < 1) {
                $this->addError("bonus[$index][quantity]", Yii::t('app', 'quantity_error'));
                return;
            }

            // Проверяем quantity_block, если оно указано
            if (!empty($bonus['quantity_block']) && $bonus['quantity_block'] <= 0) {
                $this->addError("bonus[$index][quantity_block]", Yii::t('app', 'quantity_block_error'));
                return;
            }
        }
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'invoice_id' => Yii::t('app', 'invoice'),
            'client_id' => Yii::t('app', 'client'),
            'driver_id' => Yii::t('app', 'driver'),
            'products' => Yii::t('app', 'products'),
            'car_number' => Yii::t('app', 'car number'),
            'bonus' => Yii::t('app', 'bonus'),
            'has_bonus' => Yii::t('app', 'has_bonus')
        ];
    }
}
