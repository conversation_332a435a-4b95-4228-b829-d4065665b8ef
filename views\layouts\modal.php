<!-- mini modal -->
<div class="modal fade" tabindex="-1" role="dialog" id="ideal-mini-modal" aria-hidden="true" style="display: none;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">

            </div>
            <div class="modal-footer bg-whitesmoke br">
                <button type="button" data-dismiss="modal" class="btn btn-secondary"><?php echo Yii::t("app","close"); ?></button>
                <button type="button" class="btn btn-primary mini-button"><?php echo Yii::t("app","save"); ?></button>
            </div>
        </div>
    </div>
</div>


<!-- for delete modal -->
<div class="modal fade" tabindex="-1" role="dialog" id="ideal-mini-modal-delete" aria-hidden="true" style="display: none;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">

            </div>
            <div class="modal-footer bg-whitesmoke br">
                <button type="button" data-dismiss="modal" class="btn btn-secondary"><?php echo Yii::t("app","close"); ?></button>
                <button type="button" class="btn btn-primary mini-button"><?php echo Yii::t("app","delete"); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- mini modal2 -->
<div class="modal fade" tabindex="-1" role="dialog" id="ideal-mini-modal2" aria-hidden="true" style="display: none;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="btn btn-sm btn-light close_modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">

            </div>
            <div class="modal-footer bg-whitesmoke br">
                <button type="button" class="btn btn-secondary close_modal"><?php echo Yii::t("app","close"); ?></button>
                <button type="button" class="btn btn-primary mini-button"><?php echo Yii::t("app","save"); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- large modal -->
<div class="modal fade bd-example-modal-lg" id="ideal-large-modal" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">

            </div>
            <div class="modal-footer bg-whitesmoke br">
                <button type="button" data-dismiss="modal" class="btn btn-secondary"><?php echo Yii::t("app","close"); ?></button>
                <button type="button" class="btn btn-primary large-button"><?php echo Yii::t("app","save"); ?></button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade bd-example-modal-lg" id="ideal-large-modal-without-save" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true" style="z-index: 1045;">
  <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">

            </div>
            <div class="modal-footer bg-whitesmoke br">
                <button type="button" data-dismiss="modal" class="btn btn-secondary"><?php echo Yii::t("app","close"); ?></button>
            </div>
        </div>
    </div>
</div>
<!-- mini modal -->
<div class="modal fade" tabindex="-1" role="dialog" id="ideal-location-modal" aria-hidden="true" style="display: none;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">

            </div>
            <div class="modal-footer bg-whitesmoke br">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo Yii::t("app","close"); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- excel uchun -->
<div class="modal fade" tabindex="-1" role="dialog" id="ideal-mini-modal-excel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">

            </div>
            <div class="modal-footer bg-whitesmoke br">
                <button type="button" class="btn btn-primary mini-button"><?php echo Yii::t("app","export_excel_to_download"); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Print Invoice Modal -->
<div class="modal fade" tabindex="-1" role="dialog" id="print-invoice-modal" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-xl" role="document" style="max-width: 90%;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo Yii::t("app", "print_invoice"); ?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Содержимое будет загружено через AJAX -->
            </div>
            <div class="modal-footer bg-whitesmoke br">
                <button type="button" data-dismiss="modal" class="btn btn-secondary"><?php echo Yii::t("app","close"); ?></button>
            </div>
        </div>
    </div>
</div>

<?php
$jss = <<<JS


$('#ideal-mini-modal-delete').on('hide.bs.modal', function (e) {
        $('#ideal-mini-modal-delete .modal-title').html('');
        $('#ideal-mini-modal-delete .modal-body').html('');
        $("#ideal-mini-modal-delete .mini-button").attr('class', 'btn btn-primary mini-button');
    });

    $('#ideal-mini-modal-excel').on('hide.bs.modal', function (e) {
        $('#ideal-mini-modal-excel .modal-title').html('');
        $('#ideal-mini-modal-excel .modal-body').html('');
        $("#ideal-mini-modal-excel .mini-button").attr('class', 'btn btn-primary mini-button');
    });


    $('#ideal-mini-modal').on('hide.bs.modal', function (e) {
        $('#ideal-mini-modal .modal-title').html('');
        $('#ideal-mini-modal .modal-body').html('');
        $("#ideal-mini-modal .mini-button").attr('class', 'btn btn-primary mini-button');
    });

    $('#ideal-location-modal').on('hide.bs.modal', function (e) {
        $('#ideal-location-modal .modal-title').html('');
        $('#ideal-location-modal .modal-body').html('');
    });

    $('#ideal-large-modal').on('hide.bs.modal', function (e) {
        $('#ideal-large-modal .modal-title').html('');
        $('#ideal-large-modal .modal-body').html('');
        $("#ideal-large-modal .large-button").attr('class', 'btn btn-primary large-button');
    });

    $('#ideal-large-modal-without-save').on('hide.bs.modal', function (e) {
        $('#ideal-large-modal-without-save .modal-title').html('');
        $('#ideal-large-modal-without-save .modal-body').html('');
        $("#ideal-large-modal-without-save .large-button").attr('class', 'btn btn-primary large-button');
    });

    $('#print-invoice-modal').on('hide.bs.modal', function (e) {
        $('#print-invoice-modal .modal-body').html('');
    });

    $(document).on('click','.close_modal',function(){
        $('.modal-backdrop').removeClass('show')
        $('#ideal-mini-modal').removeClass('show')
        $('#ideal-mini-modal2').removeClass('show')
        $('body.layout-3').removeClass('modal-open')
    })

    $(document).on('click','.close2',function(){
        $('.modal-backdrop').removeClass('show')
        $('#ideal-mini-modal').removeClass('show')
        $('#ideal-mini-modal2').removeClass('show')
        $('body.layout-3').removeClass('modal-open')
    })

JS;
$this->registerJs($jss, \yii\web\View::POS_END);

?>