<?php 

namespace app\modules\api\models;

use yii\base\Model;

class ClientIncomeForm extends Model
{
    public $region_id;
    public $car_number;
    public $driver_full_name;
    public $description;

    public function rules()
    {
        return [
            [['region_id', 'car_number'], 'required'],
            [['region_id'], 'integer'],
            [['car_number'], 'string', 'max' => 20],
            [['driver_full_name'], 'string', 'max' => 255],
            [['description'], 'string'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'region_id' => 'Регион',
            'car_number' => 'Номер машины',
            'driver_full_name' => 'ФИО водителя',
            'description' => 'Описание',
        ];
    }
}