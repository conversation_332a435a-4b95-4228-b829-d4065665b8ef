<?php

namespace app\modules\backend\models;

use Yii;
use yii\base\Model;
use app\common\models\Product;

class ProductCreateForm extends Model
{
    public $id;
    public $name;
    public $price;
    public $block_quantity;
    public $type;
    public $priority;

    // Константы для сценариев
    const SCENARIO_CREATE = 'create';
    const SCENARIO_UPDATE = 'update';

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            // Общие правила для всех сценариев
            [['name', 'block_quantity'], 'required'],
            [['name'], 'string', 'max' => 255],
            [['price'], 'number', 'min' => 0], 
            [['type'], 'integer'],
            [['priority'], 'integer'],
            [['block_quantity'], 'integer', 'min' => 1],
            [['price'], 'required', 'on' => self::SCENARIO_CREATE],

            [['id'], 'required', 'on' => self::SCENARIO_UPDATE],
            [['id'], 'integer', 'min' => 1, 'on' => self::SCENARIO_UPDATE],
            [['id'], 'exist', 'targetClass' => Product::class, 'targetAttribute' => 'id', 'on' => self::SCENARIO_UPDATE],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        return [
            self::SCENARIO_CREATE => ['name', 'price', 'block_quantity', 'type', 'priority'],
            self::SCENARIO_UPDATE => ['id', 'name', 'price', 'block_quantity', 'type', 'priority'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'name' => Yii::t('app', 'product_name'),
            'price' => Yii::t('app', 'product_price'),
            'block_quantity' => Yii::t('app', 'product_block_quantity'),
            'type' => Yii::t('app', 'product_type'),
            'priority' => Yii::t('app', 'product_priority'),
        ];
    }
    

    public function beforeValidate()
    {
        $attributes = ['price'];

        foreach ($attributes as $attribute) {
            if (is_string($this->$attribute) && !empty($this->$attribute)) {
                $originalValue = $this->$attribute;
                
                $cleanValue = preg_replace('/[\s\x{A0}]+/u', '', $originalValue);
                $cleanValue = str_replace(',', '.', $cleanValue);
                
                if (is_numeric($cleanValue)) {
                    $this->$attribute = floatval($cleanValue);
                }
            }
        }
        
        return parent::beforeValidate();
    }
 

  
}