<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\Pjax;
use app\assets\DataTablesAsset;
use yii\web\View;

DataTablesAsset::register($this);

$this->title = Yii::t('app', 'archived_workers');
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
?>

<div class="card-body">
    <h4 class="mb-3"><?= Html::encode($this->title) ?></h4>

    <?php Pjax::begin(['id' => 'archive-worker-grid-pjax']); ?>
    <?php if ($result): ?>
        <table id="archive-worker-grid-view" class="table table-bordered table-striped compact">
            <thead>
                <th><?= Yii::t('app', 'full_name') ?></th>
                <th><?= Yii::t('app', 'position') ?></th>
                <th><?= Yii::t('app', 'phone_number') ?></th>
                <th><?= Yii::t('app', 'salary') ?></th>
                <th><?= Yii::t('app', 'deleted_at') ?></th>
                <th><?= Yii::t('app', 'actions') ?></th>
            </thead>
            <tbody>
            <?php foreach ($result as $model): ?>
                <tr>
                    <td><?= Html::encode($model['full_name']) ?></td>
                    <td><?= Html::encode($model['position_name']) ?></td>
                    <td><?= Html::encode($model['phone_number']) ?></td>
                    <td><?= !empty($model['salary']) ? number_format($model['salary'], 0, '.', ',') : 0 ?></td>
                    <td data-order="<?= strtotime($model['deleted_at']) ?>">
                        <?= Html::encode(date('d.m.Y H:i', strtotime($model['deleted_at']))) ?>
                    </td>
                    <td>
                        <a href="#" class="btn btn-sm btn-success worker-return" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                            <?= Yii::t('app', 'return_to_work') ?>
                        </a>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>


<div id="one" data-text="<?= Yii::t("app", "return_to_work") ?>"></div>



<?php
$js = <<<JS
(function($) {
    var returnTitle = $('#one').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initDataTable() {
        if ($.fn.DataTable.isDataTable('#archive-worker-grid-view')) {
            $('#archive-worker-grid-view').DataTable().destroy();
        }
        $('#archive-worker-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[4, 'desc']],
            "columnDefs": [{ "targets": [5], "orderable": false }]
        });
    }

    function initReturn() {
        $(document).off('click.worker-return').on('click.worker-return', '.worker-return', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/archive-worker/return',
                type: 'GET',
                data: { id: id },
                dataType: 'json',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(returnTitle);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('worker-return-button');
                },
                error: function(xhr) {
                    console.error(xhr);
                }
            });
        });

        $(document).off('click.worker-return-button').on('click.worker-return-button', '.worker-return-button', function() {
            var button = $(this);
            if (button.prop('disabled')) return;
            button.prop('disabled', true);
            var formData = $('#worker-return-form').serialize();
            $.ajax({
                url: '/backend/archive-worker/return',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $.pjax.reload({container: '#archive-worker-grid-pjax'});
                        $('#ideal-mini-modal').modal('hide');
                    } else if (response.errors) {
                        $.each(response.errors, function(field, errors) {
                            $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                        });
                    }
                    button.prop('disabled', false);
                },
                error: function(xhr) {
                    console.error(xhr);
                    button.prop('disabled', false);
                }
            });
        });
    }

    function initAll() {
        initDataTable();
        initReturn();
    }

    initAll();
    $(document).on('pjax:complete', function() { initAll(); });
})(jQuery);
JS;
$this->registerJs($js, View::POS_END); 