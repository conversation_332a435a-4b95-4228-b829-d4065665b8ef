<?php

namespace app\modules\backend\controllers;

use app\common\models\ActionLogger;
use app\common\models\Client;
use app\common\models\ClientDriver;
use app\common\models\ClientSpecialPrices;
use app\common\models\Product;
use app\common\models\ProductPrice;
use app\common\models\ProductStorage;
use app\common\models\ProductStorageHistory;
use app\common\models\Sales;
use app\common\models\SalesBonus;
use app\common\models\SalesDetail;
use app\common\models\SalesReturn;
use app\common\models\Tracking;
use app\common\services\ProductReturnService;
use app\modules\backend\models\InvoiceCreateForm;
use app\modules\backend\models\ProductReturnForm;
use Exception;
use Yii;

class SalesInvoiceController extends BaseController
{
    private $productReturnService;

    public function __construct($id, $module, $config = [])
    {
        parent::__construct($id, $module, $config);
        $this->productReturnService = new ProductReturnService();
    }

    public function actionIndex()
    {
        // Оптимизация запроса - убираем лишние LEFT JOIN и используем более эффективный запрос
        $sql = "SELECT
          s.id,
          s.created_at,
          COALESCE(c.full_name, 'Неизвестный клиент') as client_name,
          COALESCE(SUM(sd.quantity), 0) as total_quantity,
          COALESCE(SUM(sd.total_price), 0) as total_sum,
          s.status,
          s.car_number,
          s.driver,
          COALESCE(u.username, 'Неизвестный') as sell_user,
          COALESCE(u2.username, '') as confirm_user,
          s.print_number,
          s.deleted_at
        FROM sales as s
        LEFT JOIN client as c ON s.client_id = c.id
        LEFT JOIN sales_detail as sd ON s.id = sd.sale_id AND sd.deleted_at IS NULL
        LEFT JOIN users as u ON s.sell_user_id = u.id
        LEFT JOIN users as u2 ON s.confirm_user_id = u2.id
        WHERE s.deleted_at IS NULL
        GROUP BY s.id, s.created_at, c.full_name, s.status, s.car_number, s.driver, u.username, u2.username, s.print_number, s.deleted_at
        ORDER BY s.id DESC";

        $command = Yii::$app->db->createCommand($sql);
        $result = $command->queryAll();

        return $this->render('index', [
            'result' => $result
        ]);
    }


    public function actionGetDrivers($client_id)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $drivers = ClientDriver::find()
            ->where(['client_id' => $client_id, 'deleted_at' => null])
            ->all();
        $driverList = [];
        foreach ($drivers as $driver) {
            $driverList[$driver->id] = [
                'name' => $driver->driver,
                'car_number' => $driver->car_number
            ];
        }
        return ['drivers' => $driverList];
    }

    public function actionGetDriverDetails($driver_id)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $driver = ClientDriver::findOne(['id' => $driver_id, 'deleted_at' => null]);
        return [
            'car_number' => $driver ? $driver->car_number : null
        ];
    }

    public function actionCreate()
    {
        // Устанавливаем быстрый JSON формат ответа
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        
        // Оптимизация памяти
        ini_set('memory_limit', '256M');
        
        if (Yii::$app->request->isPost) {
            $model = new InvoiceCreateForm();
            $model->load(Yii::$app->request->post());

            // Быстрая предварительная проверка на пустые данные
            if (empty($model->products) && empty($model->client_id)) {
                return [
                    'status' => 'error',
                    'message' => [
                        'products' => [Yii::t('app', 'please_fill_at_least_one_product')]
                    ]
                ];
            }

            foreach ($model->products as $key => $product) {
                $quantity = $product['quantity'] ?? '';
                if (empty($quantity) || $quantity == 0) {
                    unset($model->products[$key]);
                }
            }
            $model->products = array_values($model->products);

            // Проверка на наличие водителя
            if (empty($model->driver)) {
                return [
                    'status' => 'error',
                    'message' => [
                        'driver' => [Yii::t('app', 'driver_is_required')]
                    ]
                ];
            }

            if ($model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $sales = new Sales();
                    $sales->client_id = $model->client_id;
                    $sales->sell_user_id = Yii::$app->user->getId();
                    $sales->total_sum = $model->total_price;
                    $sales->status = Sales::STATUS_NEW;
                    $sales->created_at = date('Y-m-d H:i:s');

                    $driver = ClientDriver::findOne(['id' => $model->driver, 'deleted_at' => null]);
                    $sales->driver = $driver->driver ?? null;
                    $sales->car_number = $driver->car_number ?? null;
                    if (!$sales->save()) {
                        throw new Exception('Ошибка при сохранении продажи' . json_encode($sales->getErrors()));
                    }



                    $salesDetails = [];
                    foreach ($model->products as $product) {
                        $factory_price = ProductPrice::findOne(['product_id' => $product['product_id'], 'deleted_at' => null]);
                        $salesDetail = new SalesDetail();
                        $salesDetail->sale_id = $sales->id;
                        $salesDetail->product_id = $product['product_id'];
                        $salesDetail->special_price = $product['special_price'];
                        $salesDetail->sell_price = $product['sell_price'];
                        $salesDetail->total_price = $product['sell_price'] * $product['quantity'];
                        $salesDetail->factory_price = $factory_price->price ?? 0;
                        $salesDetail->quantity = $product['quantity'];

                        if (!$salesDetail->save()) {
                            throw new \Exception('Ошибка при сохранении деталей продажи');
                        }

                        $sales->total_special_prices_sum += $product['special_price'] * $product['quantity'];
                        if (!$sales->save()) {
                            throw new \Exception('Ошибка при сохранении продажи');
                        }


                        $existingSpecialPrice = ClientSpecialPrices::findOne([
                            'client_id' => $sales->client_id,
                            'product_id' => $product['product_id'],
                            'deleted_at' => null
                        ]);

                        $newPrice = round((float)$product['special_price'], 2);
                        $newSellPrice = round((float)$product['sell_price'], 2);
                        $currentTime = date('Y-m-d H:i:s');

                        // Допустимая погрешность для сравнения цен - 5 сум
                        $priceThreshold = 5.0;

                        if ($existingSpecialPrice) {
                            $existingSpecialPriceRounded = round($existingSpecialPrice->special_price, 2);
                            $existingSellPriceRounded = round($existingSpecialPrice->sell_price, 2);

                            // Проверяем разницу с допустимой погрешностью
                            $specialPriceDiff = abs($existingSpecialPriceRounded - $newPrice);
                            $sellPriceDiff = abs($existingSellPriceRounded - $newSellPrice);
                            $pricesChanged = ($specialPriceDiff > $priceThreshold) || ($sellPriceDiff > $priceThreshold);

                            // Временное логирование для отладки
                            Yii::info("SalesInvoice - Сравнение цен для клиента {$sales->client_id}, продукт {$product['product_id']}: " .
                                "существующая_спец={$existingSpecialPriceRounded}, новая_спец={$newPrice}, разница_спец={$specialPriceDiff}, " .
                                "существующая_продажа={$existingSellPriceRounded}, новая_продажа={$newSellPrice}, разница_продажа={$sellPriceDiff}, " .
                                "порог={$priceThreshold}, изменились=" . ($pricesChanged ? 'ДА' : 'НЕТ'), 'special_prices');

                            if ($pricesChanged) {
                                Yii::info("SalesInvoice - Обновление специальной цены для клиента {$sales->client_id}, продукт {$product['product_id']}: " .
                                    "помечаем старую запись как удаленную (ID={$existingSpecialPrice->id})", 'special_prices');

                                $existingSpecialPrice->deleted_at = $currentTime;
                                if (!$existingSpecialPrice->save()) {
                                    $errorMsg = 'Ошибка при удалении старой специальной цены: ' . json_encode($existingSpecialPrice->getErrors());
                                    Yii::error("SalesInvoice - " . $errorMsg, 'special_prices');
                                    throw new Exception($errorMsg);
                                }

                                $specialPrice = new ClientSpecialPrices();
                                $specialPrice->client_id = $sales->client_id;
                                $specialPrice->product_id = $product['product_id'];
                                $specialPrice->special_price = $newPrice;
                                $specialPrice->sell_price = $newSellPrice;
                                $specialPrice->created_at = $currentTime;

                                if (!$specialPrice->save()) {
                                    $errorMsg = 'Ошибка при сохранении новой специальной цены: ' . json_encode($specialPrice->getErrors());
                                    Yii::error("SalesInvoice - " . $errorMsg, 'special_prices');
                                    throw new Exception($errorMsg);
                                }

                                Yii::info("SalesInvoice - Создана новая специальная цена (ID={$specialPrice->id}) для клиента {$sales->client_id}, продукт {$product['product_id']}", 'special_prices');
                            } else {
                                Yii::info("SalesInvoice - Цены не изменились для клиента {$sales->client_id}, продукт {$product['product_id']}: пропускаем обновление", 'special_prices');
                            }
                        } else {
                            Yii::info("SalesInvoice - Создание новой специальной цены для клиента {$sales->client_id}, продукт {$product['product_id']}: " .
                                "спец_цена={$newPrice}, цена_продажи={$newSellPrice}", 'special_prices');

                            $specialPrice = new ClientSpecialPrices();
                            $specialPrice->client_id = $sales->client_id;
                            $specialPrice->product_id = $product['product_id'];
                            $specialPrice->special_price = $newPrice;
                            $specialPrice->sell_price = $newSellPrice;
                            $specialPrice->created_at = $currentTime;

                            if (!$specialPrice->save()) {
                                $errorMsg = 'Ошибка при создании новой специальной цены: ' . json_encode($specialPrice->getErrors());
                                Yii::error("SalesInvoice - " . $errorMsg, 'special_prices');
                                throw new Exception($errorMsg);
                            }

                            Yii::info("SalesInvoice - Создана новая специальная цена (ID={$specialPrice->id}) для клиента {$sales->client_id}, продукт {$product['product_id']}", 'special_prices');
                        }



                        $productStorage = ProductStorage::findOne(['product_id' => $product['product_id'], 'deleted_at' => null]);
                        $productName = Product::findOne(['id' => $product['product_id'], 'deleted_at' => null])->name;
                        if (!$productStorage) {
                            return [
                                'status' => 'error',
                                'message' => [
                                    'products' => Yii::t('app', 'Product "{product_name}" not found on the warehouse', [
                                        'product_name' => $productName
                                    ])
                                ],
                                'code' => 404
                            ];
                        }

                        // Проверяем наличие на складе
                        $productStorages = ProductStorage::find()
                            ->where(['product_id' => $product['product_id'], 'deleted_at' => null])
                            ->all();
                        $productName = Product::findOne(['id' => $product['product_id'], 'deleted_at' => null])->name;

                        $totalAvailable = array_sum(array_column($productStorages, 'quantity'));
                        if ($totalAvailable < $product['quantity']) {
                            return [
                                'status' => 'error',
                                'message' => [
                                    'products' => Yii::t('app', 'Not enough product "{product_name}" on the warehouse. Available: {available}, Required: {required}', [
                                        'product_name' => $productName,
                                        'available' => $totalAvailable,
                                        'required' => $product['quantity']
                                    ])
                                ]
                            ];
                        }

                        $salesDetails[] = [
                            'product_id' => $product['product_id'],
                            'quantity' => $product['quantity'],
                            'price' => $product['special_price'],
                            'sell_price' => $product['sell_price']
                        ];
                    }

                    if ($model->bonus && isset(Yii::$app->request->post('InvoiceCreateForm', [])['has_bonus']) && Yii::$app->request->post('InvoiceCreateForm', [])['has_bonus']) {
                        foreach ($model->bonus as $bonus) {
                            if (!empty($bonus['product_id']) && !empty($bonus['quantity'])) {
                                $product = Product::findOne(['id' => $bonus['product_id'], 'deleted_at' => null]);
                                if (!$product) {
                                    return [
                                        'status' => 'error',
                                        'message' => [
                                            'bonus' => Yii::t('app', 'Product with ID #{product_id} not found', [
                                                'product_id' => $bonus['product_id']
                                            ])
                                        ],
                                        'code' => 404 // HTTP_NOT_FOUND
                                    ];
                                }
                                $productName = $product->name;

                                $salesBonus = new SalesBonus();
                                $salesBonus->sale_id = $sales->id;
                                $salesBonus->product_id = $bonus['product_id'];
                                $salesBonus->quantity = $bonus['quantity'];
                                $salesBonus->created_at = date('Y-m-d H:i:s');
                                if (!$salesBonus->save()) {
                                    throw new \Exception(Yii::t('app', 'Error saving bonus for product "{product_name}"', [
                                        'product_name' => $productName
                                    ]));
                                }

                                $productStorages = ProductStorage::find()
                                    ->where(['product_id' => $bonus['product_id'], 'deleted_at' => null])
                                    ->all();

                                if (empty($productStorages)) {
                                    return [
                                        'status' => 'error',
                                        'message' => [
                                            'bonus' => Yii::t('app', 'Product "{product_name}" not found on the warehouse', [
                                                'product_name' => $productName
                                            ])
                                        ],
                                        'code' => 422 // HTTP_UNPROCESSABLE_ENTITY
                                    ];
                                }

                                $totalAvailable = array_sum(array_column($productStorages, 'quantity'));
                                if ($totalAvailable < $bonus['quantity']) {
                                    return [
                                        'status' => 'error',
                                        'message' => [
                                            'bonus' => Yii::t('app', 'Not enough product "{product_name}" on the warehouse. Available: {available}, Required: {required}', [
                                                'product_name' => $productName,
                                                'available' => $totalAvailable,
                                                'required' => $bonus['quantity']
                                            ])
                                        ],
                                        'code' => 422 // HTTP_UNPROCESSABLE_ENTITY
                                    ];
                                }
                            }
                        }
                    }

                    $tracking = new Tracking();
                    $tracking->progress_type = Tracking::TYPE_NEW_SALES;
                    $tracking->process_id = $sales->id;
                    $tracking->created_at = date('Y-m-d H:i:s');
                    $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                    $tracking->accepted_at = null;

                    if (!$tracking->save()) {
                        throw new \Exception('Ошибка при сохранении tracking');
                    }

                    ActionLogger::actionLog(
                        'create_invoice',
                        'sales',
                        $sales->id,
                        [
                            'client_id' => $sales->client_id,
                            'driver' => $sales->driver,
                            'car_number' => $sales->car_number,
                            'total_sum' => $sales->total_sum,
                            'products' => $salesDetails,
                            'tracking_id' => $tracking->id
                        ]
                    );

                    $transaction->commit();
                    
                    // Принудительная очистка памяти для ускорения
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }
                    
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'record_successfully_created')
                    ];

                } catch (Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ];
                }
            } else {
                return [
                    'status' => 'error',
                    'message' => $model->getErrors()
                ];
            }
        } else if(Yii::$app->request->isGet) {
            $model = new InvoiceCreateForm();
            return [
                'status' => 'success',
                'content' => $this->renderPartial('create', [
                    'model' => $model
                ])
            ];
        }
    }


    public function actionUpdate()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new InvoiceCreateForm();
            $model->load(Yii::$app->request->post(), 'InvoiceCreateForm');

            foreach ($model->products as $key => $product) {
                $quantity = $model->products[$key]['quantity'] ?? '';
                if (empty($quantity) || $quantity == 0) {
                    unset($model->products[$key]);
                }
            }
            $model->products = array_values($model->products);

            if ($model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $sales = Sales::findOne($model->id);
                    if (!$sales) {
                        throw new Exception('Запись продажи не найдена');
                    }

                    // Получаем данные водителя
                    $driver = ClientDriver::findOne($model->driver);
                    if (!$driver) {
                        throw new Exception('Водитель не найден');
                    }

                    // Обновляем данные продажи
                    $sales->client_id = $model->client_id;
                    $sales->driver = $driver->driver;
                    $sales->car_number = $driver->car_number;
                    $sales->total_sum = $model->total_price;

                    if (!$sales->save()) {
                        throw new Exception('Ошибка при обновлении продажи: ' . json_encode($sales->getErrors()));
                    }

                    // Удаляем старые детали продаж
                    SalesDetail::deleteAll(['sale_id' => $sales->id]);

                    $salesDetails = [];
                    $sales->total_special_prices_sum = 0;
                    foreach ($model->products as $product) {
                        $factory_price = ProductPrice::findOne(['product_id' => $product['product_id'], 'deleted_at' => null]);
                        $salesDetail = new SalesDetail();
                        $salesDetail->sale_id = $sales->id;
                        $salesDetail->product_id = $product['product_id'];
                        $salesDetail->special_price = $product['special_price'];
                        $salesDetail->sell_price = $product['sell_price'];
                        $salesDetail->total_price = $product['sell_price'] * $product['quantity'];
                        $salesDetail->factory_price = $factory_price->price ?? 0;
                        $salesDetail->quantity = $product['quantity'];

                        if (!$salesDetail->save()) {
                            throw new \Exception('Ошибка при сохранении деталей продажи');
                        }


                        $sales->total_special_prices_sum += $product['special_price'] * $product['quantity'];
                        if (!$sales->save()) {
                            throw new \Exception('Ошибка при сохранении продажи');
                        }

                        $existingSpecialPrice = ClientSpecialPrices::findOne([
                            'client_id' => $sales->client_id,
                            'product_id' => $product['product_id'],
                            'deleted_at' => null
                        ]);

                        $newPrice = round((float)$product['special_price'], 2);
                        $newSellPrice = round((float)$product['sell_price'], 2);
                        $currentTime = date('Y-m-d H:i:s');

                        // Допустимая погрешность для сравнения цен - 5 сум
                        $priceThreshold = 5.0;

                        if ($existingSpecialPrice) {
                            $existingSpecialPriceRounded = round($existingSpecialPrice->special_price, 2);
                            $existingSellPriceRounded = round($existingSpecialPrice->sell_price, 2);

                            // Проверяем разницу с допустимой погрешностью
                            $specialPriceDiff = abs($existingSpecialPriceRounded - $newPrice);
                            $sellPriceDiff = abs($existingSellPriceRounded - $newSellPrice);
                            $pricesChanged = ($specialPriceDiff > $priceThreshold) || ($sellPriceDiff > $priceThreshold);

                            // Временное логирование для отладки (actionUpdate)
                            Yii::info("SalesInvoice UPDATE - Сравнение цен для клиента {$sales->client_id}, продукт {$product['product_id']}: " .
                                "существующая_спец={$existingSpecialPriceRounded}, новая_спец={$newPrice}, разница_спец={$specialPriceDiff}, " .
                                "существующая_продажа={$existingSellPriceRounded}, новая_продажа={$newSellPrice}, разница_продажа={$sellPriceDiff}, " .
                                "порог={$priceThreshold}, изменились=" . ($pricesChanged ? 'ДА' : 'НЕТ'), 'special_prices');

                            if ($pricesChanged) {
                                Yii::info("SalesInvoice UPDATE - Обновление специальной цены для клиента {$sales->client_id}, продукт {$product['product_id']}: " .
                                    "помечаем старую запись как удаленную (ID={$existingSpecialPrice->id})", 'special_prices');

                                $existingSpecialPrice->deleted_at = $currentTime;
                                if (!$existingSpecialPrice->save()) {
                                    $errorMsg = 'Ошибка при удалении старой специальной цены: ' . json_encode($existingSpecialPrice->getErrors());
                                    Yii::error("SalesInvoice UPDATE - " . $errorMsg, 'special_prices');
                                    throw new Exception($errorMsg);
                                }

                                $specialPrice = new ClientSpecialPrices();
                                $specialPrice->client_id = $sales->client_id;
                                $specialPrice->product_id = $product['product_id'];
                                $specialPrice->special_price = $newPrice;
                                $specialPrice->sell_price = $newSellPrice;
                                $specialPrice->created_at = $currentTime;

                                if (!$specialPrice->save()) {
                                    $errorMsg = 'Ошибка при сохранении новой специальной цены: ' . json_encode($specialPrice->getErrors());
                                    Yii::error("SalesInvoice UPDATE - " . $errorMsg, 'special_prices');
                                    throw new Exception($errorMsg);
                                }

                                Yii::info("SalesInvoice UPDATE - Создана новая специальная цена (ID={$specialPrice->id}) для клиента {$sales->client_id}, продукт {$product['product_id']}", 'special_prices');
                            } else {
                                Yii::info("SalesInvoice UPDATE - Цены не изменились для клиента {$sales->client_id}, продукт {$product['product_id']}: пропускаем обновление", 'special_prices');
                            }
                        } else {
                            Yii::info("SalesInvoice UPDATE - Создание новой специальной цены для клиента {$sales->client_id}, продукт {$product['product_id']}: " .
                                "спец_цена={$newPrice}, цена_продажи={$newSellPrice}", 'special_prices');

                            $specialPrice = new ClientSpecialPrices();
                            $specialPrice->client_id = $sales->client_id;
                            $specialPrice->product_id = $product['product_id'];
                            $specialPrice->special_price = $newPrice;
                            $specialPrice->sell_price = $newSellPrice;
                            $specialPrice->created_at = $currentTime;

                            if (!$specialPrice->save()) {
                                $errorMsg = 'Ошибка при создании новой специальной цены: ' . json_encode($specialPrice->getErrors());
                                Yii::error("SalesInvoice UPDATE - " . $errorMsg, 'special_prices');
                                throw new Exception($errorMsg);
                            }

                            Yii::info("SalesInvoice UPDATE - Создана новая специальная цена (ID={$specialPrice->id}) для клиента {$sales->client_id}, продукт {$product['product_id']}", 'special_prices');
                        }

                        $productStorage = ProductStorage::findOne(['product_id' => $product['product_id'], 'deleted_at' => null]);
                        $productName = Product::findOne(['id' => $product['product_id'], 'deleted_at' => null])->name;
                        if (!$productStorage) {
                            return [
                                'status' => 'error',
                                'message' => [
                                    'products' => Yii::t('app', 'Product "{product_name}" not found on the warehouse', [
                                        'product_name' => $productName
                                    ])
                                ],
                                'code' => 404
                            ];
                        }

                        // Проверяем наличие на складе
                        $productStorages = ProductStorage::find()
                            ->where(['product_id' => $product['product_id'], 'deleted_at' => null])
                            ->all();

                        $totalAvailable = array_sum(array_column($productStorages, 'quantity'));
                        $newQuantity = $product['quantity'];

                        if ($totalAvailable < $newQuantity) {
                            return [
                                'status' => 'error',
                                'message' => [
                                    'products' => Yii::t('app', 'Not enough product "{product_name}" on the warehouse. Available: {available}, Required: {required}', [
                                        'product_name' => $productName,
                                        'available' => $totalAvailable,
                                        'required' => $newQuantity
                                    ])
                                ]
                            ];
                        }

                        $salesDetails[] = [
                            'product_id' => $product['product_id'],
                            'quantity' => $product['quantity'],
                            'price' => $product['special_price'],
                            'sell_price' => $product['sell_price']
                        ];
                    }

                    // Обработка бонусов
                    SalesBonus::deleteAll(['sale_id' => $sales->id]); // Удаляем старые бонусы
                    if (isset(Yii::$app->request->post('InvoiceCreateForm')['has_bonus']) &&
                        Yii::$app->request->post('InvoiceCreateForm')['has_bonus'] &&
                        !empty($model->bonus)) {
                        foreach ($model->bonus as $bonus) {
                            if (!empty($bonus['product_id']) && !empty($bonus['quantity'])) {
                                $product = Product::findOne(['id' => $bonus['product_id'], 'deleted_at' => null]);
                                if (!$product) {
                                    return [
                                        'status' => 'error',
                                        'message' => [
                                            'bonus' => Yii::t('app', 'Product with ID #{product_id} not found', [
                                                'product_id' => $bonus['product_id']
                                            ])
                                        ],
                                        'code' => 404
                                    ];
                                }
                                $productName = $product->name;

                                $salesBonus = new SalesBonus();
                                $salesBonus->sale_id = $sales->id;
                                $salesBonus->product_id = $bonus['product_id'];
                                $salesBonus->quantity = $bonus['quantity'];
                                $salesBonus->created_at = date('Y-m-d H:i:s');
                                if (!$salesBonus->save()) {
                                    throw new \Exception(Yii::t('app', 'Error saving bonus for product "{product_name}"', [
                                        'product_name' => $productName
                                    ]));
                                }

                                $productStorages = ProductStorage::find()
                                    ->where(['product_id' => $bonus['product_id'], 'deleted_at' => null])
                                    ->all();

                                if (empty($productStorages)) {
                                    return [
                                        'status' => 'error',
                                        'message' => [
                                            'bonus' => Yii::t('app', 'Product "{product_name}" not found on the warehouse', [
                                                'product_name' => $productName
                                            ])
                                        ],
                                        'code' => 422
                                    ];
                                }

                                $totalAvailable = array_sum(array_column($productStorages, 'quantity'));
                                if ($totalAvailable < $bonus['quantity']) {
                                    return [
                                        'status' => 'error',
                                        'message' => [
                                            'bonus' => Yii::t('app', 'Not enough product "{product_name}" on the warehouse. Available: {available}, Required: {required}', [
                                                'product_name' => $productName,
                                                'available' => $totalAvailable,
                                                'required' => $bonus['quantity']
                                            ])
                                        ],
                                        'code' => 422
                                    ];
                                }
                            }
                        }
                    }


                    ActionLogger::actionLog(
                        'update_invoice',
                        'sales',
                        $sales->id,
                        [
                            'client_id' => $sales->client_id,
                            'driver' => $sales->driver,
                            'car_number' => $sales->car_number,
                            'total_sum' => $sales->total_sum,
                            'products' => $salesDetails,
                        ]
                    );

                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'record_successfully_updated')
                    ];

                } catch (Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ];
                }
            } else {
                return [
                    'status' => 'error',
                    'message' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $sales = Sales::findOne($id);
            if (!$sales) {
                return [
                    'status' => 'error',
                    'message' => 'Запись продажи не найдена'
                ];
            }

            $driver = ClientDriver::findOne([
                'client_id' => $sales->client_id,
                'driver' => $sales->driver,
                'car_number' => $sales->car_number,
                'deleted_at' => null
            ]);

            $model = new InvoiceCreateForm();
            $model->id = $sales->id;
            $model->client_id = $sales->client_id;
            // Устанавливаем ID водителя
            $model->driver = $driver ? $driver->id : null;
            // Устанавливаем номер машины из sales
            $model->car_number = $sales->car_number;
            $model->total_price = $sales->total_sum;

            // Загружаем существующие продукты
            $existingDetails = SalesDetail::findAll(['sale_id' => $sales->id]);
            foreach ($existingDetails as $detail) {
                $product = Product::findOne($detail->product_id);
                if ($product && $product->size > 0) {
                    $quantity_block = $detail->quantity / $product->size; // Вычисляем quantity_block
                } else {
                    $quantity_block = ''; // Если size неизвестен, оставляем пустым
                }
                $model->products[] = [
                    'product_id' => $detail->product_id,
                    'quantity_block' => $quantity_block,
                    'quantity' => $detail->quantity,
                    'price' => $detail->special_price,
                    'total_price' => $detail->total_price,
                    'sell_price' => $detail->sell_price
                ];
            }

            // Загружаем существующие бонусы
            $existingBonuses = SalesBonus::findAll(['sale_id' => $sales->id]);
            foreach ($existingBonuses as $bonus) {
                $product = Product::findOne($bonus->product_id);
                if ($product && $product->size > 0) {
                    $quantity_block = $bonus->quantity / $product->size; // Вычисляем quantity_block
                } else {
                    $quantity_block = ''; // Если size неизвестен, оставляем пустым
                }
                $model->bonus[] = [
                    'product_id' => $bonus->product_id,
                    'quantity_block' => $quantity_block,
                    'quantity' => $bonus->quantity
                ];
            }

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model,
                    'sales' => $sales
                ])
            ];
        }
    }


    public function actionDelete()
{
    Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

    if (Yii::$app->request->isGet) {
        $id = Yii::$app->request->get('id');
        $sales = Sales::findOne($id);
        if (!$sales) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Sale not found')
            ];
        }

        return [
            'status' => 'success',
            'content' => $this->renderPartial('delete', [
                'model' => $sales
            ])
        ];
    }
    else if (Yii::$app->request->isPost) {
        $data = Yii::$app->request->post();
        $sales = Sales::findOne($data['Sales']['id']);
        if (!$sales) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Sale not found')
            ];
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Получаем связанные записи
            $salesDetails = SalesDetail::findAll(['sale_id' => $sales->id]);
            $salesBonuses = SalesBonus::findAll(['sale_id' => $sales->id]);
            $tracking = Tracking::findOne([
                'process_id' => $sales->id,
                'progress_type' => Tracking::TYPE_NEW_SALES
            ]);

            // Восстанавливаем количество продуктов на складе
            foreach ($salesDetails as $detail) {
                $productStorage = ProductStorage::findOne(['product_id' => $detail->product_id, 'deleted_at' => null]);
                if ($productStorage) {
                    $productStorage->quantity += $detail->quantity;
                    if (!$productStorage->save()) {
                        throw new Exception('Ошибка при обновлении количества на складе');
                    }

                    $history = new ProductStorageHistory();
                    $history->product_storage_id = $productStorage->id;
                    $history->product_id = $detail->product_id;
                    $history->quantity = $detail->quantity;
                    $history->type = ProductStorageHistory::TYPE_INCOME;
                    $history->created_at = date('Y-m-d H:i:s');
                    if (!$history->save()) {
                        throw new Exception('Ошибка при сохранении истории склада');
                    }
                }
            }

            // Восстанавливаем количество бонусных продуктов
            foreach ($salesBonuses as $bonus) {
                $productStorage = ProductStorage::findOne(['product_id' => $bonus->product_id, 'deleted_at' => null]);
                if ($productStorage) {
                    $productStorage->quantity += $bonus->quantity;
                    if (!$productStorage->save()) {
                        throw new Exception('Ошибка при обновлении количества бонусного товара на складе');
                    }

                    $history = new ProductStorageHistory();
                    $history->product_storage_id = $productStorage->id;
                    $history->product_id = $bonus->product_id;
                    $history->quantity = $bonus->quantity;
                    $history->type = ProductStorageHistory::TYPE_INCOME;
                    $history->created_at = date('Y-m-d H:i:s');
                    if (!$history->save()) {
                        throw new Exception('Ошибка при сохранении истории склада для бонуса');
                    }
                }
            }

            // Удаляем специальные цены
            $specialPrices = ClientSpecialPrices::find()
                ->where(['client_id' => $sales->client_id])
                ->andWhere(['in', 'product_id', array_column($salesDetails, 'product_id')])
                ->andWhere(['deleted_at' => null])
                ->all();

            $currentTime = date('Y-m-d H:i:s');
            foreach ($specialPrices as $specialPrice) {
                $specialPrice->deleted_at = $currentTime;
                if (!$specialPrice->save()) {
                    throw new Exception('Ошибка при удалении специальной цены');
                }
            }

            // Логируем действие
            ActionLogger::actionLog(
                'delete_invoice',
                'sales',
                $sales->id,
                [
                    'client_id' => $sales->client_id,
                    'driver' => $sales->driver,
                    'car_number' => $sales->car_number,
                    'total_sum' => $sales->total_sum,
                    'products' => array_map(function($detail) {
                        return [
                            'product_id' => $detail->product_id,
                            'quantity' => $detail->quantity,
                            'price' => $detail->special_price,
                            'sell_price' => $detail->sell_price
                        ];
                    }, $salesDetails),
                    'tracking_id' => $tracking ? $tracking->id : null
                ]
            );

            // Удаляем связанные записи
            SalesDetail::deleteAll(['sale_id' => $sales->id]);
            SalesBonus::deleteAll(['sale_id' => $sales->id]);

            // Явно удаляем tracking, если он существует
            if ($tracking && !$tracking->delete()) {
                throw new Exception('Ошибка при удалении tracking');
            }

            // Удаляем продажу
            if (!$sales->delete()) {
                throw new Exception('Ошибка при удалении продажи');
            }

            $transaction->commit();
            return [
                'status' => 'success',
                'message' => Yii::t('app', 'record_successfully_deleted')
            ];

        } catch (Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    return [
        'status' => 'error',
        'message' => Yii::t('app', 'Method not allowed')
    ];
}



    public function actionView($id)
    {
        $model = Sales::find()
            ->with([
                'client',
                'sellUser',
                'confirmUser',
                'salesDetails' => function($query) {
                    $query->with('product')
                    ->andWhere(['deleted_at' => null]);
                }
            ])
            ->where(['id' => $id])
            ->one();

        if (!$model) {
            throw new Exception('Счет не найден');
        }

        // Рассчитываем box_count для каждого salesDetail
        foreach ($model->salesDetails as $detail) {
            $detail['box_count'] = $detail->product->size > 0 ? round($detail->quantity / $detail->product->size, 2) : 0;
        }


        if (Yii::$app->request->isAjax) {
            return $this->renderAjax('view', [
                'model' => $model
            ]);
        }

        return $this->render('view', [
            'model' => $model
        ]);
    }


    public function actionGetBlockQuantity()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $productId = Yii::$app->request->get('id');
        $blocks = (int)Yii::$app->request->get('blocks');

        $product = Product::findOne($productId);

        if ($product === null) {
            return ['error' => 'Продукт не найден', 'quantity' => 0];
        }

        $itemsInBlock = $product->size ?? 1;

        $quantity = $blocks * $itemsInBlock;

        return ['quantity' => $quantity];
    }

    /**
     * Обрабатывает пакетные запросы для получения количества продуктов по блокам
     *
     * @return array Массив результатов для каждого запроса
     */
    public function actionGetBatchQuantities()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $requestsJson = Yii::$app->request->post('requests');

        if (!$requestsJson) {
            return ['error' => 'Не переданы данные запроса', 'results' => []];
        }

        try {
            $requests = json_decode($requestsJson, true);

            if (!is_array($requests)) {
                return ['error' => 'Неверный формат данных', 'results' => []];
            }

            $results = [];
            $productCache = []; // Кэш для хранения загруженных продуктов

            foreach ($requests as $request) {
                $productId = $request['id'] ?? null;
                $blocks = isset($request['blocks']) ? (int)$request['blocks'] : 0;

                if (!$productId || $blocks <= 0) {
                    $results[] = [
                        'id' => $productId,
                        'blocks' => $blocks,
                        'quantity' => 0,
                        'error' => 'Неверные параметры'
                    ];
                    continue;
                }

                // Используем кэш для продуктов, чтобы не загружать их повторно
                if (!isset($productCache[$productId])) {
                    $product = Product::findOne($productId);
                    $productCache[$productId] = $product;
                } else {
                    $product = $productCache[$productId];
                }

                if ($product === null) {
                    $results[] = [
                        'id' => $productId,
                        'blocks' => $blocks,
                        'quantity' => 0,
                        'error' => 'Продукт не найден'
                    ];
                    continue;
                }

                $itemsInBlock = $product->size ?? 1;
                $quantity = $blocks * $itemsInBlock;

                $results[] = [
                    'id' => $productId,
                    'blocks' => $blocks,
                    'quantity' => $quantity
                ];
            }

            return ['results' => $results];

        } catch (\Exception $e) {
            Yii::error('Error in actionGetBatchQuantities: ' . $e->getMessage());
            return ['error' => 'Ошибка обработки запроса', 'results' => []];
        }
    }

    public function actionGetProductSize()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $productId = Yii::$app->request->get('product_id');

        $product = Product::findOne($productId);

        if ($product === null) {
            return [
                'status' => 'error',
                'message' => 'Продукт не найден',
                'size' => 0
            ];
        }

        return [
            'status' => 'success',
            'size' => $product->size ?? 1
        ];
    }

    public function actionPrint($id)
    {
        $model = Sales::find()
            ->with([
                'client',
                'sellUser',
                'confirmUser',
                'salesDetails' => function($query) {
                    $query->with('product');
                },
                'salesBonus' => function($query) {
                    $query->with('product');
                }
            ])
            ->where(['id' => $id])
            ->one();

        if (!$model) {
            throw new Exception('Счет не найден');
        }

        // Получаем параметр withPrice из запроса (по умолчанию true)
        $withPrice = Yii::$app->request->get('withPrice', 'true') === 'true';

        $viewParams = [
            'model' => $model,
            'withPrice' => $withPrice // Передаем параметр в представление
        ];

        if (Yii::$app->request->isAjax) {
            return $this->renderAjax('print', $viewParams);
        }

        return $this->render('print', $viewParams);
    }


    public function actionGetPrices($client_id)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        // Получаем все специальные цены для клиента одним запросом
        $specialPrices = ClientSpecialPrices::find()
            ->where([
                'client_id' => $client_id,
                'deleted_at' => null
            ])
            ->indexBy('product_id')
            ->all();

        // Получаем все стандартные цены одним запросом
        $productPrices = ProductPrice::find()
            ->where(['end_date' => '9999-12-31'])
            ->indexBy('product_id')
            ->all();

        // Получаем все продукты одним запросом
        $products = Product::find()
            ->where(['deleted_at' => null])
            ->indexBy('id')
            ->all();

        $prices = [];

        foreach ($products as $product) {
            if (isset($specialPrices[$product->id])) {
                // Если есть специальная цена для клиента
                $prices[$product->id] = [
                    'price' => (int)$specialPrices[$product->id]->special_price,
                    'sell_price' => (int)($specialPrices[$product->id]->sell_price ?? $specialPrices[$product->id]->special_price)
                ];
            } elseif (isset($productPrices[$product->id])) {
                // Если есть стандартная цена
                $prices[$product->id] = [
                    'price' => (int)$productPrices[$product->id]->price,
                    'sell_price' => (int)($productPrices[$product->id]->sell_price ?? $product->sell_price ?? $productPrices[$product->id]->price)
                ];
            } else {
                // Базовая цена из продукта
                $prices[$product->id] = [
                    'price' => (int)$product->price,
                    'sell_price' => (int)($product->sell_price ?? $product->price)
                ];
            }
        }

        return $prices;
    }

    public function num2str($num) {
        $nul = 'ноль';
        $ten = array(
            array('', 'один', 'два', 'три', 'четыре', 'пять', 'шесть', 'семь', 'восемь', 'девять'),
            array('', 'одна', 'две', 'три', 'четыре', 'пять', 'шесть', 'семь', 'восемь', 'девять')
        );
        $a20 = array('десять', 'одиннадцать', 'двенадцать', 'тринадцать', 'четырнадцать', 'пятнадцать', 'шестнадцать', 'семнадцать', 'восемнадцать', 'девятнадцать');
        $tens = array(2 => 'двадцать', 'тридцать', 'сорок', 'пятьдесят', 'шестьдесят', 'семьдесят', 'восемьдесят', 'девяносто');
        $hundred = array('', 'сто', 'двести', 'триста', 'четыреста', 'пятьсот', 'шестьсот', 'семьсот', 'восемьсот', 'девятьсот');
        $unit = array(
            array('тысяча', 'тысячи', 'тысяч', 1), // женский род
            array('миллион', 'миллиона', 'миллионов', 0), // мужской род
            array('миллиард', 'миллиарда', 'миллиардов', 0) // мужской род
        );


        $numStr = (string)$num; // Преобразуем в строку для str_replace
        $numStr = str_replace([' ', ','], ['', '.'], $numStr);

        if (!is_numeric($numStr)) {
            return "Ошибка: нечисловое значение"; // Или сообщение об ошибке
        }

        $numFormatted = number_format((float)$numStr, 2, '.', '');
        list($rub, $kop) = explode('.', $numFormatted);



        $rub = strval(intval($rub));

        $out = array();
        if (intval($rub) > 0) {
            $length = strlen($rub);
            $groups = [];
            for ($i = $length - 1; $i >= 0; $i -= 3) {
                $start = max(0, $i - 2);
                $group = substr($rub, $start, $i - $start + 1);
                $groups[] = ltrim($group, '0') ?: '0';
            }
            $groups = array_reverse($groups);
            $n = count($groups); // общее количество групп

            foreach ($groups as $uk => $v) {
                $v = intval($v);

                // Определяем разряд и род
                if ($uk < $n - 1) { // не последняя группа
                    $unitIndex = $n - 2 - $uk;
                    if ($unitIndex < sizeof($unit)) {
                        $currentUnit = $unit[$unitIndex];
                        $gender = $currentUnit[3];
                    } else {
                        $currentUnit = null; // для чисел больше миллиардов
                        $gender = 0;
                    }
                } else { // последняя группа — единицы
                    $currentUnit = null;
                    $gender = 0; // мужской род для единиц
                }

                // Разбиваем группу на сотни, десятки, единицы
                $i1 = floor($v / 100);
                $i2 = floor(($v % 100) / 10);
                $i3 = $v % 10;
                $group = array();

                if ($i1 > 0) {
                    $group[] = $hundred[$i1];
                }
                if ($i2 == 1) {
                    $group[] = $a20[$i3];
                } else {
                    if ($i2 > 1) {
                        $group[] = $tens[$i2];
                    }
                    if ($i3 > 0) {
                        $group[] = $ten[$gender][$i3];
                    }
                }

                // Добавляем группу, если она не пустая
                if (!empty($group)) {
                    $out[] = implode(' ', $group);
                }

                // Добавляем разряд, если группа ненулевая и разряд определен
                if ($v > 0 && $currentUnit !== null) {
                    $out[] = $this->morph($v, $currentUnit[0], $currentUnit[1], $currentUnit[2]);
                }
            }
        } else {
            $out[] = $nul;
        }

        return trim(preg_replace('/ {2,}/', ' ', join(' ', $out)));
    }

    private function morph($n, $f1, $f2, $f5) {
        $n = abs(intval($n)) % 100;
        if ($n > 10 && $n < 20) return $f5;
        $n = $n % 10;
        if ($n > 1 && $n < 5) return $f2;
        if ($n == 1) return $f1;
        return $f5;
    }



    public function actionProductReturn()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        if(Yii::$app->request->isPost) {
            $model = new ProductReturnForm();
            $model->load(Yii::$app->request->post(), 'ProductReturnForm');

            if ($model->validate()) {
                if($this->productReturnService->processReturnMultiple($model->client_id, $model->sale_id, $model->products)) {
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'product_return_success')
                    ];
                } else {
                    return [
                        'status' => 'error',
                        'message' => $model->getErrors()
                    ];
                }
            } else {
                return [
                    'status' => 'error',
                    'message' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $clientList = $this->productReturnService->getClientList();
            return [
                'status' => 'success',
                'content' => $this->renderPartial('product-return', ['clients' => $clientList])
            ];
        }
    }


    public function actionGetClientInvoices() {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $clientId = Yii::$app->request->post('client_id');

        $invoices = $this->productReturnService->getClientRecentInvoices($clientId);

        return [
            'status' => 'success',
            'options' => $invoices
        ];
    }

    public function actionGetInvoiceProducts() {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $saleId = Yii::$app->request->post('sale_id');

        $products = $this->productReturnService->getInvoiceProducts($saleId);
        if (!$products) {
            return [
                'status' => 'error',
                'message' => 'Products not found'
            ];
        }

        return [
            'status' => 'success',
            'options' => $products
        ];
    }

    /**
     * Получение списка продуктов счета с их количествами и размерами блоков
     * Оптимизированный метод для ускорения загрузки данных
     */
    public function actionGetInvoiceProductsWithQuantities() {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $saleId = Yii::$app->request->post('sale_id');

        if (!$saleId) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'sale_id_required')
            ];
        }

        // Получаем детали продажи с продуктами
        $details = SalesDetail::find()
            ->with('product')
            ->where(['sale_id' => $saleId])
            ->all();

        // Получаем уже возвращенные продукты
        $returns = SalesReturn::find()
            ->select(['product_id', 'SUM(quantity) as returned_quantity'])
            ->where(['sale_id' => $saleId])
            ->andWhere(['status' => SalesReturn::STATUS_APPROVED])
            ->groupBy('product_id')
            ->indexBy('product_id')
            ->asArray()
            ->all();

        $products = [];
        foreach ($details as $detail) {
            if (!$detail->product) {
                continue;
            }

            // Проверяем, был ли продукт уже возвращен
            $returnedQuantity = isset($returns[$detail->product_id]) ? (float)$returns[$detail->product_id]['returned_quantity'] : 0;
            $availableQuantity = $detail->quantity - $returnedQuantity;

            // Если есть доступное количество для возврата
            if ($availableQuantity > 0) {
                $products[] = [
                    'value' => $detail->product_id,
                    'label' => $detail->product->name,
                    'quantity' => $availableQuantity,
                    'size' => $detail->product->size ?? 1
                ];
            }
        }

        return [
            'status' => 'success',
            'products' => $products
        ];
    }

    public function actionGetAvailableQuantity() {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $saleId = Yii::$app->request->post('sale_id');
        $productId = Yii::$app->request->post('product_id');

        $productReturnService = new ProductReturnService();

        $invoiceDetail = $productReturnService->getInvoiceProductDetail($saleId, $productId);

        if ($invoiceDetail) {
            return [
                'status' => 'success',
                'quantity' => $invoiceDetail->quantity
            ];
        } else {
            return [
                'status' => 'error',
                'message' => 'Product not found in invoice'
            ];
        }

    }



    public function actionSales() {

        $pageSize = 100;
        $salesPage = Yii::$app->request->get('salesPage', 1);
        $offset = ($salesPage - 1) * $pageSize;
        
        // Получаем параметры фильтрации по дате и времени
        $dateFrom = Yii::$app->request->get('date_from');
        $dateTo = Yii::$app->request->get('date_to');

        // Если не указаны date_from/date_to, используем старые параметры для совместимости
        if (!$dateFrom && !$dateTo) {
            $startDate = Yii::$app->request->get('start_date');
            $startTime = Yii::$app->request->get('start_time', '00:00');
            $endDate = Yii::$app->request->get('end_date');
            $endTime = Yii::$app->request->get('end_time', '23:59');

            if ($startDate && $endDate) {
                $dateFrom = $startDate . 'T' . $startTime;
                $dateTo = $endDate . 'T' . $endTime;
            }
        }
        
        // Базовый SQL запрос
        $sql = "SELECT
            s.id,
            s.created_at,
            c.full_name as client_name,
            s.total_sum,
            s.print_number
        FROM sales s
        LEFT JOIN client c ON s.client_id = c.id
        WHERE s.deleted_at IS NULL";
        
        // Добавляем условия фильтрации по дате и времени, если они указаны
        $params = [];
        if ($dateFrom && $dateTo) {
            $sql .= " AND s.created_at BETWEEN :start_datetime AND :end_datetime";
            // Конвертируем ISO формат в MySQL datetime
            $params[':start_datetime'] = str_replace('T', ' ', $dateFrom) . ':00';
            $params[':end_datetime'] = str_replace('T', ' ', $dateTo) . ':00';
        }
        
        // Добавляем сортировку и лимиты
        $sql .= " ORDER BY s.created_at DESC LIMIT :limit OFFSET :offset";
        
        // Подготавливаем и выполняем запрос
        $command = Yii::$app->db->createCommand($sql);
        foreach ($params as $param => $value) {
            $command->bindValue($param, $value);
        }
        $command->bindValue(':limit', $pageSize, \PDO::PARAM_INT);
        $command->bindValue(':offset', $offset, \PDO::PARAM_INT);
        $sales = $command->queryAll();
        
        // Запрос для подсчета общего количества записей с учетом фильтра
        $countSql = "SELECT COUNT(*) FROM sales s WHERE s.deleted_at IS NULL";
        if ($dateFrom && $dateTo) {
            $countSql .= " AND s.created_at BETWEEN :start_datetime AND :end_datetime";
        }
        
        $countCommand = Yii::$app->db->createCommand($countSql);
        if ($dateFrom && $dateTo) {
            $countCommand->bindValue(':start_datetime', $params[':start_datetime']);
            $countCommand->bindValue(':end_datetime', $params[':end_datetime']);
        }
        $totalSales = $countCommand->queryScalar();

        $products = Product::find()
            ->where(['deleted_at' => null])
            ->orderBy(['name' => SORT_ASC])
            ->asArray()
            ->all();

        foreach ($sales as &$sale) {
            $detailsSql = "SELECT
                sd.product_id,
                sd.quantity,
                sd.total_price,
                p.name as product_name
            FROM sales_detail sd
            LEFT JOIN product p ON sd.product_id = p.id
            WHERE sd.sale_id = :sale_id AND sd.deleted_at IS NULL";

            $detailsCommand = Yii::$app->db->createCommand($detailsSql);
            $detailsCommand->bindValue(':sale_id', $sale['id'], \PDO::PARAM_INT);
            $sale['products'] = $detailsCommand->queryAll();
            
            // Получаем бонусы для данной продажи
            $bonusSql = "SELECT
                sb.product_id,
                sb.quantity,
                p.name as product_name
            FROM sales_bonus sb
            LEFT JOIN product p ON sb.product_id = p.id
            WHERE sb.sale_id = :sale_id AND sb.deleted_at IS NULL";
            
            $bonusCommand = Yii::$app->db->createCommand($bonusSql);
            $bonusCommand->bindValue(':sale_id', $sale['id'], \PDO::PARAM_INT);
            $sale['bonuses'] = $bonusCommand->queryAll();
        }
        
        // Проверяем, является ли запрос AJAX-запросом
        if (Yii::$app->request->isAjax) {
            return $this->asJson([
                'status' => 'success',
                'content' => $this->renderPartial('sales', [
                    'sales' => $sales,
                    'products' => $products,
                    'totalSales' => $totalSales,
                    'pageSize' => $pageSize,
                    'salesPage' => $salesPage
                ])
            ]);
        }

        return $this->render('sales', [
                'sales' => $sales,
                'products' => $products,
                'totalSales' => $totalSales,
                'pageSize' => $pageSize,
                'salesPage' => $salesPage
            ]);
    }

}