<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%free_products}}`.
 */
class m250217_083236_create_free_products_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%free_products}}', [
            'id' => $this->primaryKey(),
            'product_id' => $this->integer()->null(),
            'client' => $this->string(250)->null(),
            'car_number' => $this->string(250)->null(),
            'quantity' => $this->integer()->null(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        $this->addForeignKey(
            'fk-free_products-product_id',
            '{{%free_products}}',
            'product_id',
            '{{%product}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%free_products}}');
    }
}
