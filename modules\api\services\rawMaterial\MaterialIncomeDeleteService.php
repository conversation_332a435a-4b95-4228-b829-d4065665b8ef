<?php

namespace app\modules\api\services\rawMaterial;

use Yii;
use app\common\models\Invoice;
use app\common\models\InvoiceDetail;
use app\common\models\MaterialStorage;
use app\common\models\MaterialStorageHistory;
use app\common\models\Tracking;
use app\common\models\ActionLogger;
use yii\base\Component;

/**
 * Сервис для удаления накладных с корректным списанием со склада
 */
class MaterialIncomeDeleteService extends Component
{
    /**
     * Удалить накладную с корректным списанием материалов со склада
     * 
     * @param int $invoiceId ID накладной
     * @return array Результат операции
     */
    public function deleteMaterialIncome($invoiceId)
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            // Проверяем существование накладной
            $invoice = Invoice::find()
                ->where(['id' => $invoiceId])
                ->andWhere(['accepted_at' => null])
                ->andWhere(['accept_user_id' => null])
                ->andWhere(['deleted_at' => null])
                ->one();

            if (!$invoice) {
                return [
                    'success' => false,
                    'message' => 'Накладная не найдена или уже принята'
                ];
            }

            // Получаем все детали накладной
            $invoiceDetails = InvoiceDetail::find()
                ->where(['invoice_id' => $invoiceId])
                ->andWhere(['deleted_at' => null])
                ->all();

            if (empty($invoiceDetails)) {
                return [
                    'success' => false,
                    'message' => 'Детали накладной не найдены'
                ];
            }

            // Списываем материалы со склада
            foreach ($invoiceDetails as $detail) {
                $this->deductMaterialFromStorage($detail);
                
                // Помечаем деталь как удаленную
                $detail->deleted_at = date('Y-m-d H:i:s');
                if (!$detail->save()) {
                    throw new \Exception('Ошибка при удалении деталей накладной');
                }
            }

            // Удаляем трекинг
            $this->deleteTracking($invoiceId);

            // Помечаем накладную как удаленную
            $invoice->deleted_at = date('Y-m-d H:i:s');
            if (!$invoice->save()) {
                throw new \Exception('Ошибка при удалении накладной');
            }

            // Логируем действие
            $this->logAction($invoice, $invoiceDetails);

            $transaction->commit();

            return [
                'success' => true,
                'message' => 'Накладная успешно удалена'
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => 'Ошибка при удалении накладной',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Списать материал со склада
     * 
     * @param InvoiceDetail $detail Деталь накладной
     * @throws \Exception
     */
    private function deductMaterialFromStorage($detail)
    {
        // Получаем все записи склада для данного материала, отсортированные по дате создания (от старых к новым)
        $materialStorages = MaterialStorage::find()
            ->where(['material_id' => $detail->material_id])
            ->andWhere(['>', 'quantity', 0])
            ->andWhere(['deleted_at' => null])
            ->orderBy(['created_at' => SORT_ASC])
            ->all();

        if (empty($materialStorages)) {
            throw new \Exception("Материал с ID {$detail->material_id} не найден на складе");
        }

        // Проверяем общее количество материала на складе
        $totalAvailable = array_sum(array_map(function($storage) {
            return (float)$storage->quantity;
        }, $materialStorages));

        if ($totalAvailable < $detail->quantity) {
            throw new \Exception("Недостаточно материала на складе для удаления накладной. Доступно: {$totalAvailable}, Требуется: {$detail->quantity}");
        }

        $remainingQuantity = (float)$detail->quantity;

        // Уменьшаем количество материала начиная с самых старых записей
        foreach ($materialStorages as $storage) {
            if ($remainingQuantity <= 0) {
                break; // Все необходимое количество уже удалено
            }

            $quantityToRemove = min((float)$storage->quantity, $remainingQuantity);
            $storage->quantity = (float)$storage->quantity - $quantityToRemove;
            $storage->updated_at = date('Y-m-d H:i:s');

            if (!$storage->save()) {
                throw new \Exception('Ошибка сохранения склада: ' . json_encode($storage->errors));
            }

            // Создаем запись в истории склада
            $this->createStorageHistory($storage->id, $detail->material_id, $quantityToRemove, $detail->id);

            $remainingQuantity -= $quantityToRemove;
        }

        // Проверяем, что все количество было успешно удалено
        if ($remainingQuantity > 0) {
            throw new \Exception('Не удалось удалить все необходимое количество материала со склада');
        }
    }

    /**
     * Создать запись в истории склада
     * 
     * @param int $storageId ID записи склада
     * @param int $materialId ID материала
     * @param float $quantity Количество
     * @param int $invoiceDetailId ID детали накладной
     * @throws \Exception
     */
    private function createStorageHistory($storageId, $materialId, $quantity, $invoiceDetailId)
    {
        $history = new MaterialStorageHistory();
        $history->material_storage_id = $storageId;
        $history->material_id = $materialId;
        $history->quantity = $quantity;
        $history->created_at = date('Y-m-d H:i:s');
        $history->add_user_id = Yii::$app->user->id;
        $history->type = MaterialStorageHistory::TYPE_OUTCOME;
        $history->invoice_detail_id = $invoiceDetailId;

        if (!$history->save()) {
            throw new \Exception('Ошибка сохранения истории: ' . json_encode($history->errors));
        }
    }

    /**
     * Удалить трекинг накладной
     * 
     * @param int $invoiceId ID накладной
     * @throws \Exception
     */
    private function deleteTracking($invoiceId)
    {
        $tracking = Tracking::find()
            ->where(['process_id' => $invoiceId])
            ->andWhere(['progress_type' => Tracking::TYPE_MATERIAL_INCOME])
            ->andWhere(['deleted_at' => null])
            ->andWhere(['accepted_at' => null])
            ->one();

        if (!$tracking) {
            throw new \Exception('Tracking не найден');
        }

        $tracking->deleted_at = date('Y-m-d H:i:s');
        if (!$tracking->save()) {
            throw new \Exception('Ошибка при обновлении tracking');
        }
    }

    /**
     * Логировать действие удаления накладной
     * 
     * @param Invoice $invoice Накладная
     * @param array $invoiceDetails Детали накладной
     */
    private function logAction($invoice, $invoiceDetails)
    {
        ActionLogger::actionLog(
            'delete_raw_material',
            'invoice',
            $invoice->id,
            [
                'invoice_number' => $invoice->invoice_number,
                'supplier_id' => $invoice->supplier_id,
                'total_amount' => $invoice->total_amount,
                'materials' => array_map(function($detail) {
                    return [
                        'material_id' => $detail->material_id,
                        'quantity' => $detail->quantity,
                        'price' => $detail->price
                    ];
                }, $invoiceDetails)
            ]
        );
    }

    /**
     * Проверить возможность удаления накладной
     * 
     * @param int $invoiceId ID накладной
     * @return array Результат проверки
     */
    public function canDeleteInvoice($invoiceId)
    {
        try {
            $invoice = Invoice::find()
                ->where(['id' => $invoiceId])
                ->andWhere(['accepted_at' => null])
                ->andWhere(['accept_user_id' => null])
                ->andWhere(['deleted_at' => null])
                ->one();

            if (!$invoice) {
                return [
                    'can_delete' => false,
                    'message' => 'Накладная не найдена или уже принята'
                ];
            }

            $invoiceDetails = InvoiceDetail::find()
                ->where(['invoice_id' => $invoiceId])
                ->andWhere(['deleted_at' => null])
                ->all();

            $issues = [];

            foreach ($invoiceDetails as $detail) {
                $totalAvailable = MaterialStorage::find()
                    ->where(['material_id' => $detail->material_id])
                    ->andWhere(['>', 'quantity', 0])
                    ->andWhere(['deleted_at' => null])
                    ->sum('quantity');

                if ($totalAvailable < $detail->quantity) {
                    $issues[] = "Материал ID {$detail->material_id}: доступно {$totalAvailable}, требуется {$detail->quantity}";
                }
            }

            return [
                'can_delete' => empty($issues),
                'message' => empty($issues) ? 'Накладную можно удалить' : 'Недостаточно материалов на складе',
                'issues' => $issues
            ];

        } catch (\Exception $e) {
            return [
                'can_delete' => false,
                'message' => 'Ошибка проверки возможности удаления',
                'error' => $e->getMessage()
            ];
        }
    }
}
