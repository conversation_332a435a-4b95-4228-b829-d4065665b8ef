<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use app\assets\DataTablesAsset;

/* @var $this yii\web\View */

DataTablesAsset::register($this);

$this->title = Yii::t('app', 'defect_reports');
$this->params['breadcrumbs'][] = $this->title;

?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
    </div>

    <!-- Навигационные табы -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-tabs nav-tabs-bordered" id="defectTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link active" id="product-defect-warehouse-tab" data-toggle="tab" href="#product-defect-warehouse" role="tab" aria-controls="product-defect-warehouse" aria-selected="true">
                        <i class="fas fa-box-open mr-2"></i>
                        <?= Yii::t('app', 'product_defect_from_warehouse') ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="product-repackaging-tab" data-toggle="tab" href="#product-repackaging" role="tab" aria-controls="product-repackaging" aria-selected="false">
                        <i class="fas fa-recycle mr-2"></i>
                        <?= Yii::t('app', 'product_repackaging') ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="material-defect-production-tab" data-toggle="tab" href="#material-defect-production" role="tab" aria-controls="material-defect-production" aria-selected="false">
                        <i class="fas fa-industry mr-2"></i>
                        <?= Yii::t('app', 'material_defect_from_production') ?>
                    </a>
                </li>

                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="material-return-tab" data-toggle="tab" href="#material-return" role="tab" aria-controls="material-return" aria-selected="false">
                        <i class="fas fa-undo mr-2"></i>
                        <?= Yii::t('app', 'material_returns') ?>
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Содержимое табов -->
    <div class="tab-content" id="defectTabsContent">
        <!-- Брак продукции со склада -->
        <div class="tab-pane fade show active" id="product-defect-warehouse" role="tabpanel" aria-labelledby="product-defect-warehouse-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>

        <!-- Переупаковка продукции -->
        <div class="tab-pane fade" id="product-repackaging" role="tabpanel" aria-labelledby="product-repackaging-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>

        <!-- Брак сырья из производства -->
        <div class="tab-pane fade" id="material-defect-production" role="tabpanel" aria-labelledby="material-defect-production-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>



        <!-- Возврат материалов -->
        <div class="tab-pane fade" id="material-return" role="tabpanel" aria-labelledby="material-return-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>
    </div>
</div>

<style>
.nav-tabs-bordered .nav-link {
    border: 1px solid transparent;
    border-radius: 0.25rem 0.25rem 0 0;
    color: #6c757d;
    transition: all 0.3s ease;
}

.nav-tabs-bordered .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #495057;
    background-color: #f8f9fa;
}

.nav-tabs-bordered .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    border-bottom-color: transparent;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

.tab-content {
    min-height: 400px;
}

.report-content {
    width: 100%;
}

.report-content .card {
    border: none;
    box-shadow: none;
}

.report-content .card-body {
    padding: 0;
}

.alert {
    margin-bottom: 0;
}

.mr-2 {
    margin-right: 0.5rem !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.text-right {
    text-align: right !important;
}

.text-muted {
    color: #6c757d !important;
}

.d-flex {
    display: flex !important;
}

.justify-content-center {
    justify-content: center !important;
}

.align-items-center {
    align-items: center !important;
}

.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Скрываем возможные дублированные навигационные элементы в загруженном контенте */
.report-content .nav-tabs,
.report-content nav {
    display: none !important;
}

/* Гарантируем правильное отображение спиннера */
.d-flex.justify-content-center.align-items-center {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

@media (max-width: 768px) {
    .nav-tabs {
        flex-direction: column;
    }
    
    .nav-tabs .nav-item {
        margin-bottom: 0.25rem;
    }
    
    .text-right {
        text-align: center !important;
        margin-top: 1rem;
    }
}
</style>

<?php
$js = <<<JS
(function($) {
    var reportUrls = {
        'product-defect-warehouse': '/backend/material-defect/product-defect-from-warehouse',
        'product-repackaging': '/backend/material-defect/product-repackaging',
        'material-defect-production': '/backend/material-defect/material-defect-from-production',
        'material-return': '/backend/material-defect/material-return'
    };
    
    var loadedReports = {};

    /**
     * Перехватываем отправку формы внутри загруженного отчёта и перезагружаем
     * содержимое вкладки через AJAX, не покидая страницу index.
     */
    function attachReportFormHandler(contentDiv, tabId) {
        contentDiv.find('form').off('submit.report').on('submit.report', function(e) {
            e.preventDefault();
            var formElement = $(this);
            var url = reportUrls[tabId] + (urlHasQuery(reportUrls[tabId]) ? '&' : '?') + formElement.serialize();

            // Показываем спиннер
            var tabPane = $('#' + tabId);
            var spinner = tabPane.find('.d-flex.justify-content-center.align-items-center');
            spinner.css('display', 'flex');

            $.get(url, function(response) {
                var cleanResponse = response;
                if (typeof response === 'string') {
                    cleanResponse = response.replace(/<ul[^>]*nav-tabs[^>]*>[\s\S]*?<\/ul>/gi, '');
                    cleanResponse = cleanResponse.replace(/<nav[^>]*>[\s\S]*?<\/nav>/gi, '');
                }
                contentDiv.html(cleanResponse);
                spinner.css('display', 'none');

                // Повторно инициализируем компоненты и обработчики
                setTimeout(function () {
                    // DataTables уже инициализированы в загруженном контенте
                    attachReportFormHandler(contentDiv, tabId);
                }, 50);
            });
        });
    }

    function urlHasQuery(u) {
        return u.indexOf('?') !== -1;
    }

    function loadReport(tabId) {
        console.log('Loading defect report for tab:', tabId);
        
        if (loadedReports[tabId]) {
            // Отчет уже загружен, просто скрываем спиннер и показываем контент
            var tabPane = $('#' + tabId);
            var spinner = tabPane.find('.d-flex.justify-content-center.align-items-center');
            var contentDiv = tabPane.find('.report-content');
            
            console.log('Report already loaded, hiding spinner and showing content');
            
            // Полностью удаляем спиннер
            spinner.remove();
            contentDiv.css('display', 'block');
            return;
        }

        var tabPane = $('#' + tabId);
        var spinner = tabPane.find('.d-flex.justify-content-center.align-items-center');
        var contentDiv = tabPane.find('.report-content');

        console.log('Starting new defect report load');

        // Показываем спиннер, скрываем контент
        spinner.css('display', 'flex');
        contentDiv.css('display', 'none');

        $.ajax({
            url: reportUrls[tabId],
            method: 'GET',
            timeout: 30000,
            success: function(response) {
                // Очищаем возможные дублированные элементы навигации из ответа
                var cleanResponse = response;
                if (typeof response === 'string') {
                    // Удаляем любые nav-tabs из ответа чтобы избежать дублирования
                    cleanResponse = response.replace(/<ul[^>]*nav-tabs[^>]*>[\s\S]*?<\/ul>/gi, '');
                    cleanResponse = cleanResponse.replace(/<nav[^>]*>[\s\S]*?<\/nav>/gi, '');
                }
                
                contentDiv.html(cleanResponse);
                attachReportFormHandler(contentDiv, tabId);
                loadedReports[tabId] = true;
                
                console.log('Defect report loaded successfully, removing spinner');
                // Полностью удаляем спиннер и показываем контент
                spinner.remove();
                contentDiv.css('display', 'block');
                
                // Инициализируем компоненты
                setTimeout(function() {
                    // DataTables уже инициализированы в загруженном контенте, не нужно дублировать
                    
                    contentDiv.find('.select2').each(function() {
                        if ($.fn.select2) {
                            $(this).select2({
                                width: '100%'
                            });
                        }
                    });
                }, 100);
            },
            error: function(xhr, status, error) {
                var errorMessage = 'Ошибка загрузки отчета';
                if (xhr.status === 404) {
                    errorMessage = 'Отчет не найден';
                } else if (xhr.status === 500) {
                    errorMessage = 'Внутренняя ошибка сервера';
                }
                
                contentDiv.html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle mr-2"></i>' + errorMessage + '</div>');
                loadedReports[tabId] = true; // Помечаем как загруженный даже при ошибке
                
                console.log('Defect report loading failed, removing spinner');
                // Полностью удаляем спиннер и показываем контент
                spinner.remove();
                contentDiv.css('display', 'block');
                console.error('Error loading defect report:', error);
            }
        });
    }

    $('#defectTabs a').on('click', function(e) {
        e.preventDefault();
        $(this).tab('show');
    });

    $('#defectTabs a').on('shown.bs.tab', function(e) {
        var tabId = $(e.target).attr('href').substring(1);
        setTimeout(function() {
            loadReport(tabId);
        }, 50);
    });

    $(document).ready(function() {
        setTimeout(function() {
            loadReport('product-defect-warehouse');
        }, 100);
    });
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
