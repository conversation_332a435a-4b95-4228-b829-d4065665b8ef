<?php

namespace app\modules\api\services\rawMaterial;

use Yii;
use app\common\models\ActionLogger;
use app\common\models\MaterialStatusGroup;
use app\common\models\MaterialStatus;
use app\common\models\MaterialStorage;
use app\common\models\MaterialStorageHistory;
use app\common\models\MaterialProduction;
use yii\base\Component;

/**
 * Сервис для подтверждения возврата материалов из производства на склад
 */
class MaterialProductionReturnAcceptService extends Component
{
    /**
     * Подтверждение возврата материалов из производства на склад
     *
     * @param int $groupId ID группы материалов для возврата
     * @return array Результат операции
     */
    public function acceptMaterialReturn($groupId)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Находим группу материалов для возврата из производства
            $materialStatusGroup = MaterialStatusGroup::findOne([
                'id' => $groupId,
                'status' => MaterialStatusGroup::STATUS_RETURNED_FROM_PRODUCTION,
                'deleted_at' => null,
                'accepted_at' => null,
                'accepted_user_id' => null
            ]);

            if (!$materialStatusGroup) {
                throw new \Exception('Группа возврата материалов из производства не найдена');
            }


            // Получаем все материалы из группы
            $materials = MaterialStatus::find()
                ->where([
                    'status_group_id' => $groupId,
                    'deleted_at' => null
                ])
                ->all();

            if (empty($materials)) {
                throw new \Exception('Материалы в группе не найдены');
            }

            // Обрабатываем каждый материал
            foreach ($materials as $materialStatus) {
                // Проверяем наличие материала в производстве
                 $this->validateMaterialInProduction($materialStatus);

                // Уменьшаем количество в производстве
                 $this->decreaseMaterialProduction($materialStatus);

                // Увеличиваем количество на складе
                $this->increaseMaterialStorage($materialStatus);
            }

            // Подтверждаем группу возврата
            $materialStatusGroup->accepted_user_id = Yii::$app->user->id;
            $materialStatusGroup->accepted_at = date('Y-m-d H:i:s');

            if (!$materialStatusGroup->save()) {
                throw new \Exception('Ошибка подтверждения группы возврата: ' . json_encode($materialStatusGroup->getErrors()));
            }

            // Логируем действие
            ActionLogger::actionLog(
                'accept_material_production_return',
                'material_status_group',
                $groupId,
                [
                    'materials_count' => count($materials),
                    'total_quantity' => array_sum(array_column($materials, 'quantity'))
                ]
            );

            $transaction->commit();

            return [
                'success' => true,
                'message' => 'Возврат материалов из производства успешно подтвержден'
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Проверяет наличие достаточного количества материала в производстве
     *
     * @param MaterialStatus $materialStatus
     * @throws \Exception
     */
    private function validateMaterialInProduction($materialStatus)
    {
        $totalInProduction = MaterialProduction::find()
            ->where([
                'material_id' => $materialStatus->material_id,
                'deleted_at' => null
            ])
            ->sum('quantity') ?? 0; 
    
        if ($totalInProduction < $materialStatus->quantity) {
            throw new \Exception("Недостаточно материала в производстве для возврата. Доступно: {$totalInProduction}, требуется: {$materialStatus->quantity}");
        }
    }

    /**
     * Уменьшает количество материала в производстве
     *
     * @param MaterialStatus $materialStatus
     * @throws \Exception
     */
    private function decreaseMaterialProduction($materialStatus)
    {
        if ($materialStatus->quantity <= 0) {
            throw new \Exception("Количество для списания должно быть положительным: {$materialStatus->quantity}");
        }
    
        $remainingQuantity = $materialStatus->quantity;
    
        $materialProductions = MaterialProduction::find()
            ->where([
                'material_id' => $materialStatus->material_id,
                'deleted_at' => null
            ])
            ->andWhere(['>', 'quantity', 0])
            ->orderBy(['created_at' => SORT_ASC])
            ->all();
    
        $transaction = Yii::$app->db->beginTransaction();
        try {
            foreach ($materialProductions as $production) {
                if ($remainingQuantity <= 0) {
                    break;
                }
    
                $quantityToDecrease = min($production->quantity, $remainingQuantity);
                $production->quantity -= $quantityToDecrease;
    
                if (!$production->save()) {
                    throw new \Exception("Ошибка обновления производства для material_id {$materialStatus->material_id}: " . json_encode($production->getErrors()));
                }
    
                $remainingQuantity -= $quantityToDecrease;
            }
    
            if ($remainingQuantity > 0) {
                throw new \Exception("Не удалось списать все необходимое количество из производства для material_id {$materialStatus->material_id}");
            }
    
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * Увеличивает количество материала на складе
     *
     * @param MaterialStatus $materialStatus
     * @throws \Exception
     */
    private function increaseMaterialStorage($materialStatus)
    {
        $today = date('Y-m-d'); 

        $storage = MaterialStorage::findOne([
            'material_id' => $materialStatus->material_id,
            'deleted_at' => null,
            'created_at' => $today
        ]);

        if (!$storage) {
            $storage = new MaterialStorage();
            $storage->material_id = $materialStatus->material_id;
            $storage->quantity = 0;
            $storage->created_at = $today;
        }

        $storage->quantity += $materialStatus->quantity;

        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$storage->save()) {
                throw new \Exception("Ошибка обновления склада для material_id {$materialStatus->material_id}: " . json_encode($storage->getErrors()));
            }

            $history = new MaterialStorageHistory();
            $history->material_storage_id = $storage->id;
            $history->material_id = $materialStatus->material_id;
            $history->quantity = $materialStatus->quantity;
            $history->add_user_id = Yii::$app->user->id;
            $history->type = MaterialStorageHistory::TYPE_INCOME;
            $history->created_at = date('Y-m-d H:i:s'); 
            if (!$history->save()) {
                throw new \Exception("Ошибка сохранения истории склада для material_id {$materialStatus->material_id}: " . json_encode($history->getErrors()));
            }

            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }
}
