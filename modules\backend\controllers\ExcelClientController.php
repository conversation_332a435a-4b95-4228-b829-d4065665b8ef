<?php

namespace app\modules\backend\controllers;

use app\common\models\Client;
use app\common\models\ClientBalance;
use app\common\models\ClientBalanceHistory;
use yii\web\UploadedFile;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Yii;

class ExcelClientController extends BaseController
{
    const DEFAULT_REGION_ID = 1;
    const SIMILARITY_THRESHOLD = 90;
    
    public function actionIndex()
    {
        $model = new \yii\base\DynamicModel(['file']);
        $model->addRule(['file'], 'file', ['extensions' => 'xlsx, xls']);

        if (Yii::$app->request->isPost) {
            $model->file = UploadedFile::getInstance($model, 'file');
            if ($model->file && $model->validate()) {
                $filePath = $model->file->tempName;

                try {
                    // Загружаем Excel файл
                    $spreadsheet = IOFactory::load($filePath);
                    $sheet = $spreadsheet->getActiveSheet();
                    $data = $sheet->toArray();
                    $clients = [];
                    $processedCount = 0;

                    // Структура Excel: данные в колонках B и C
                    $columnIndexes = [
                        'full_name' => 1,     // Mijozlar (колонка B, индекс 1)
                        'amount' => 2,        // Narx (колонка C, индекс 2)
                    ];

                    // Проверяем, что в данных есть хотя бы одна строка
                    if (count($data) < 2) {
                        throw new \Exception('Файл не содержит данных для импорта');
                    }

                    // Обрабатываем данные из файла
                    foreach ($data as $index => $row) {
                        // Пропускаем заголовок
                        if ($index === 0) {
                            continue;
                        }

                        // Получаем имя клиента
                        $fullName = trim($row[$columnIndexes['full_name']] ?? '');
                        if (empty($fullName)) {
                            continue; // Пропускаем строки с пустым именем
                        }

                        // Получаем сумму
                        $amount = $row[$columnIndexes['amount']] ?? 0;
                        $parsedAmount = $this->parseAmount($amount);

                        // Пропускаем если нет данных
                        if (empty($fullName) && $parsedAmount == 0) {
                            continue;
                        }

                        // Собираем данные клиента
                        $clientData = [
                            'full_name' => $fullName,
                            'amount' => $parsedAmount,
                        ];

                        $clients[] = $clientData;
                    }

                    // Начинаем транзакцию для сохранения всех изменений
                    $transaction = Yii::$app->db->beginTransaction();

                    try {
                        // Сохраняем клиентов и обновляем их балансы
                        foreach ($clients as $clientData) {
                            // Ищем клиента по имени с учетом схожести
                            $client = $this->findSimilarClient($clientData['full_name']);

                            // Логируем результат поиска
                            \Yii::info("Поиск клиента '{$clientData['full_name']}': " . ($client ? "найден ID {$client->id}" : "не найден"), 'excel-client');

                            // Если клиент не найден, создаем нового
                            if (!$client) {
                                $client = new Client();
                                $client->scenario = Client::SCENARIO_CREATE;
                                $client->full_name = $clientData['full_name'];
                                $client->region_id = self::DEFAULT_REGION_ID;

                                // Сохраняем нового клиента
                                if (!$client->save(false)) {
                                    throw new \Exception('Ошибка сохранения клиента: ' . json_encode($client->errors));
                                }

                                \Yii::info("Создан новый клиент '{$clientData['full_name']}' с ID {$client->id}", 'excel-client');
                            }

                            // Обновляем баланс клиента
                            $this->updateClientBalance($client, $clientData['amount']);
                            $processedCount++;
                        }

                        // Если все прошло успешно, фиксируем транзакцию
                        $transaction->commit();
                        Yii::$app->session->setFlash('success', 'Клиенты успешно импортированы: ' . $processedCount);
                    } catch (\Exception $e) {
                        // В случае ошибки откатываем транзакцию
                        $transaction->rollBack();
                        Yii::$app->session->setFlash('error', 'Ошибка при импорте клиентов: ' . $e->getMessage());
                    }
                } catch (\Exception $e) {
                    Yii::$app->session->setFlash('error', 'Ошибка обработки файла: ' . $e->getMessage());
                }

                return $this->refresh();
            }
        }

        return $this->render('index', ['model' => $model]);
    }

    /**
     * Парсинг суммы из Excel
     * Суммы в скобках = предоплата (отрицательный баланс)
     * Суммы без скобок = долг (положительный баланс)
     *
     * @param mixed $value Значение из Excel
     * @return float Обработанная сумма
     */
    private function parseAmount($value)
    {
        if (is_numeric($value)) {
            return (float)$value;
        }

        if (!is_string($value)) {
            return 0;
        }

        $value = trim($value);
        
        // Проверяем, есть ли значение в скобках (предоплата клиента)
        $isInBrackets = false;
        if (preg_match('/\((.*?)\)/', $value, $matches)) {
            $value = $matches[1];
            $isInBrackets = true;
        }

        // Удаляем пробелы и заменяем запятые на точки
        $value = str_replace([' ', ','], ['', '.'], $value);

        // Если строка пустая или содержит только нули, устанавливаем 0
        if (empty($value) || in_array($value, ['0', '0.00', '0.000'])) {
            return 0;
        }

        $amount = (float)$value;
        
        // Если сумма была в скобках - это предоплата клиента (отрицательный баланс в БД)
        if ($isInBrackets) {
            $amount = -$amount;
        }

        return $amount;
    }

    /**
     * Обновление баланса клиента
     *
     * @param Client $client Клиент
     * @param float $newAmount Новая сумма баланса
     * @throws \Exception
     */
    private function updateClientBalance($client, $newAmount)
    {
        $clientBalance = ClientBalance::findOne(['client_id' => $client->id]);
        if (!$clientBalance) {
            $clientBalance = new ClientBalance();
            $clientBalance->client_id = $client->id;
            $clientBalance->amount = 0;
        }

        $oldAmount = $clientBalance->amount;

        // Проверяем, изменился ли баланс
        if ($oldAmount != $newAmount) {
            $clientBalance->amount = $newAmount;

            if (!$clientBalance->save()) {
                throw new \Exception('Ошибка сохранения баланса клиента: ' . json_encode($clientBalance->errors));
            }

            // Записываем историю изменения баланса
            $balanceHistory = new ClientBalanceHistory();
            $balanceHistory->client_id = $client->id;
            $balanceHistory->amount = $clientBalance->amount;
            $balanceHistory->old_amount = $oldAmount;

            // Определяем тип операции (увеличение или уменьшение)
            $balanceHistory->type = ($clientBalance->amount > $oldAmount)
                ? ClientBalanceHistory::TYPE_INCREASE
                : ClientBalanceHistory::TYPE_DECREASE;

            if (!$balanceHistory->save()) {
                throw new \Exception('Ошибка сохранения истории баланса: ' . json_encode($balanceHistory->errors));
            }
        }
    }

    /**
     * Поиск клиента по похожему имени (улучшенный fuzzy match)
     * Учитывает похожие имена типа "АНГРЕН БАХТИЕР" и "Ангренлик Бахтиёр"
     *
     * @param string $fullName Имя клиента для поиска
     * @return Client|null Найденный клиент или null
     */
    private function findSimilarClient($fullName)
    {
        $normalizedSearchName = $this->normalizeClientName($fullName);

        Yii::info("Поиск клиента: '$fullName' -> нормализованное: '$normalizedSearchName'", 'excel-client');

        // 1. Поиск с нормализованными именами - загружаем всех клиентов и сравниваем
        $clients = Client::find()
            ->select(['id', 'full_name'])
            ->where(['deleted_at' => null])
            ->all();

        Yii::info("Загружено клиентов для сравнения: " . count($clients), 'excel-client');

        foreach ($clients as $client) {
            $normalizedDbName = $this->normalizeClientName($client->full_name);

            // Точное совпадение нормализованных имен
            if ($normalizedSearchName === $normalizedDbName) {
                Yii::info("Найдено точное совпадение: '{$client->full_name}' (ID: {$client->id})", 'excel-client');
                return $client;
            }
        }
        
        // 2. Поиск по ключевым словам нормализованных имен
        $words = preg_split('/\s+/', $normalizedSearchName, -1, PREG_SPLIT_NO_EMPTY);
        if (count($words) >= 2) {
            foreach ($clients as $client) {
                $normalizedDbName = $this->normalizeClientName($client->full_name);
                $dbWords = preg_split('/\s+/', $normalizedDbName, -1, PREG_SPLIT_NO_EMPTY);
                
                $matchedWords = 0;
                foreach ($words as $word) {
                    if (mb_strlen($word) >= 3 && in_array($word, $dbWords)) {
                        $matchedWords++;
                    }
                }
                
                // Если совпали все значимые слова
                if ($matchedWords >= 2 && $matchedWords === count($words)) {
                    return $client;
                }
            }
        }

        // 3. Затем улучшенный fuzzy search (используем уже загруженных клиентов)
        $bestMatch = null;
        $bestScore = 0;
            
        foreach ($clients as $client) {
            $normalizedDbName = $this->normalizeClientName($client->full_name);
            
            // Проверяем схожесть нормализованных имен
            similar_text($normalizedSearchName, $normalizedDbName, $percent);
            
            // Также проверяем схожесть частей имен
            $wordsScore = $this->compareNameWords($normalizedSearchName, $normalizedDbName);
            
            // Берем максимальный балл из двух методов
            $finalScore = max($percent, $wordsScore);
            
            if ($finalScore > 75 && $finalScore > $bestScore) { // Снижаем порог до 75%
                $bestMatch = $client;
                $bestScore = $finalScore;
            }
        }
        
        return $bestMatch;
    }

    /**
     * Нормализация имени клиента для лучшего сравнения
     * Учитывает невидимые символы из Excel и латинские буквы
     *
     * @param string $name Исходное имя
     * @return string Нормализованное имя
     */
    private function normalizeClientName($name)
    {
        // 1. Убираем невидимые символы и контрольные символы из Excel
        $name = preg_replace('/[\x{00A0}\x{200B}-\x{200D}\x{2060}\x{FEFF}\x{00AD}]/u', ' ', $name);
        
        // 2. Заменяем латинские "похожие" буквы на кириллицу (частая проблема в Excel)
        $latinToCyrillic = [
            'A' => 'А', 'B' => 'В', 'C' => 'С', 'E' => 'Е', 'H' => 'Н',
            'K' => 'К', 'M' => 'М', 'O' => 'О', 'P' => 'Р', 'T' => 'Т',
            'X' => 'Х', 'Y' => 'У', 'a' => 'а', 'e' => 'е', 'o' => 'о',
            'p' => 'р', 'c' => 'с', 'x' => 'х', 'y' => 'у',
        ];
        
        $name = strtr($name, $latinToCyrillic);
        
        // 3. Приводим к нижнему регистру и убираем лишние пробелы
        $name = mb_strtolower(trim($name));
        $name = preg_replace('/\s+/u', ' ', $name);
        
        // 4. Нормализуем узбекские/русские буквы
        $letterReplacements = [
            // Узбекские буквы в русские
            'ҳ' => 'х', 'х' => 'х',
            'ғ' => 'г', 'г' => 'г',
            'қ' => 'к', 'к' => 'к',
            'ў' => 'у', 'у' => 'у',
            'ҷ' => 'ж', 'ж' => 'ж',
            'ё' => 'е', 'е' => 'е',
            'ъ' => '', 'ь' => '',
            // Убираем агрессивную нормализацию 'ий' => 'и' - она портит имена
        ];
        
        foreach ($letterReplacements as $search => $replace) {
            $name = str_replace($search, $replace, $name);
        }
        
        // 5. Убираем географические суффиксы в конце слов
        $words = explode(' ', $name);
        $normalizedWords = [];
        
        foreach ($words as $word) {
            // Убираем суффиксы типа "лик", "нинг", "дан" и т.д.
            $suffixPatterns = [
                '/лик$/',     // Асакалик -> Асака
                '/нинг$/',    // Самаркандинг -> Самарканд  
                '/дан$/',     // Ташкентдан -> Ташкент
                '/га$/',      // Бухорога -> Бухоро
                '/да$/',      // Наводада -> Навои
            ];
            
            foreach ($suffixPatterns as $pattern) {
                $word = preg_replace($pattern, '', $word);
            }
            
            if (!empty($word)) {
                $normalizedWords[] = $word;
            }
        }
        
        return implode(' ', $normalizedWords);
    }

    /**
     * Сравнение слов в именах
     *
     * @param string $name1 Первое имя
     * @param string $name2 Второе имя
     * @return float Процент схожести (0-100)
     */
    private function compareNameWords($name1, $name2)
    {
        $words1 = explode(' ', $name1);
        $words2 = explode(' ', $name2);
        
        $matchedWords = 0;
        $totalWords = max(count($words1), count($words2));
        
        foreach ($words1 as $word1) {
            if (mb_strlen($word1) < 3) continue; // Пропускаем короткие слова
            
            foreach ($words2 as $word2) {
                if (mb_strlen($word2) < 3) continue;
                
                // Проверяем точное совпадение
                if ($word1 === $word2) {
                    $matchedWords++;
                    break;
                }
                
                // Проверяем схожесть слов
                similar_text($word1, $word2, $wordPercent);
                if ($wordPercent > 80) {
                    $matchedWords += 0.8; // Частичное совпадение
                    break;
                }
                
                // Проверяем, начинаются ли слова одинаково (для случаев типа "ангрен"/"ангренлик")
                if (mb_strlen($word1) >= 4 && mb_strlen($word2) >= 4) {
                    $prefix1 = mb_substr($word1, 0, 4);
                    $prefix2 = mb_substr($word2, 0, 4);
                    if ($prefix1 === $prefix2) {
                        $matchedWords += 0.6;
                        break;
                    }
                }
            }
        }
        
        return $totalWords > 0 ? ($matchedWords / $totalWords) * 100 : 0;
    }
}
