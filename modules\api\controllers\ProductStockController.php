<?php

namespace app\modules\api\controllers;

use app\common\models\ApiResponse;
use app\common\models\ClientBalance;
use app\common\models\ClientBalanceHistory;
use app\common\models\ProductStorage;
use app\common\models\Sales;
use app\common\models\SalesBonus;
use app\common\models\Tracking;
use app\common\models\ActionLogger;
use app\common\models\ProductDefect;
use app\common\models\ProductStorageHistory;
use app\modules\api\models\SendToProductDefectForm;
use app\modules\api\models\SendToProductRepackagingForm;
use Yii;
use yii\web\ForbiddenHttpException;
use app\services\InvoiceService;
use app\modules\api\models\InvoiceCreateForm;
use app\modules\api\models\InvoiceUpdateForm;
use app\services\ClientDriverService;

class ProductStockController extends BaseController
{

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        try {
            if (!Yii::$app->user->can('product_keeper')) {
                throw new ForbiddenHttpException(Yii::t('app', "You don\'t have permission to access this resource"));
            }
        } catch (ForbiddenHttpException $e) {
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            Yii::$app->response->data = ApiResponse::response(
                $e->getMessage(),
                null,
                403
            );
            Yii::$app->response->statusCode = 403;
            return false;
        }

        return true;
    }


    public function actionIndex()
    {
        if (Yii::$app->request->isGet) {
            $sql = "
                WITH ProductTotals AS (
                    SELECT
                        ps.product_id,
                        SUM(CASE
                            WHEN ps.accepted_at IS NOT NULL
                            AND ps.accepted_user_id IS NOT NULL
                            AND ps.deleted_at IS NULL
                            THEN ps.quantity
                            ELSE 0
                        END) as total_quantity
                    FROM product_storage ps
                    GROUP BY ps.product_id
                ),
                LastOperations AS (
                    SELECT
                        ps.product_id,
                        json_agg(
                            json_build_object(
                                'quantity', ps.quantity,
                                'enter_date', ps.enter_date,
                                'accepted_at', ps.accepted_at,
                                'added_by', u1.full_name,
                                'accepted_by', u2.full_name
                            )
                            ORDER BY ps.enter_date DESC
                        ) FILTER (WHERE ps.enter_date IS NOT NULL and ps.quantity > 0)
                        as operations
                    FROM (
                        SELECT *
                        FROM product_storage ps2
                        WHERE ps2.deleted_at IS NULL
                        AND ps2.accepted_at IS NOT NULL
                        AND ps2.accepted_user_id IS NOT NULL
                        ORDER BY ps2.enter_date DESC
                        LIMIT 5
                    ) ps
                    LEFT JOIN users u1 ON u1.id = ps.add_user_id
                    LEFT JOIN users u2 ON u2.id = ps.accepted_user_id
                    GROUP BY ps.product_id
                )
                SELECT
                    p.id as product_id,
                    p.name as product_name,
                    p.size,
                    COALESCE(pt.total_quantity, 0) as storage_quantity,
                    COALESCE(lo.operations, '[]'::json) as last_operations
                FROM product p
                LEFT JOIN ProductTotals pt ON pt.product_id = p.id
                LEFT JOIN LastOperations lo ON lo.product_id = p.id
                WHERE p.deleted_at IS NULL
                ORDER BY p.name ASC
            ";

            $products = Yii::$app->db->createCommand($sql)->queryAll();

            foreach ($products as &$product) {
                $product['last_operations'] = json_decode($product['last_operations'], true);
            }

            return ApiResponse::response('Отчет по складу', [
                'products' => $products
            ]);
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }

    public function actionAccept()
    {
        if (Yii::$app->request->isGet) {
            $date = Yii::$app->request->get('date') ?: date('Y-m-d');            $sql = "
                SELECT
                    ps.id as product_storage_id,
                    p.name as product_name,
                    ps.quantity,
                    ps.enter_date,
                    u.full_name as accepted_by,
                    ps.accepted_at,
                    CASE
                        WHEN ps.accepted_at IS NOT NULL AND ps.accepted_user_id IS NOT NULL THEN true
                        ELSE false
                    END as is_accepted,
                    CASE
                        WHEN latest_tracking.accepted_at IS NOT NULL
                        AND latest_tracking.status = :status_accepted
                        THEN true
                        ELSE false
                    END as status,
                    u1.full_name as out_person
                FROM product_storage ps
                JOIN product p ON p.id = ps.product_id
                LEFT JOIN users u ON u.id = ps.accepted_user_id
                LEFT JOIN users u1 ON u1.id = ps.add_user_id
                LEFT JOIN (
                    SELECT DISTINCT ON (process_id) 
                        process_id, 
                        accepted_at, 
                        status
                    FROM tracking 
                    WHERE progress_type = :progress_type 
                        AND deleted_at IS NULL
                    ORDER BY process_id, created_at DESC
                ) latest_tracking ON latest_tracking.process_id = ps.id
                WHERE ps.deleted_at IS NULL
                AND DATE(ps.enter_date) = :date
                ORDER BY ps.enter_date DESC
            ";

            $pendingProducts = Yii::$app->db->createCommand($sql)
                ->bindValue(':date', $date)
                ->bindValue(':progress_type', Tracking::TYPE_PRODUCT_RELEASE)
                ->bindValue(':status_accepted', Tracking::STATUS_ACCEPTED)
                ->queryAll();

            return ApiResponse::response('Продукты для приема', [
                'pending_products' => $pendingProducts
            ]);
        }

        if (Yii::$app->request->isPost) {
            $product_storage_id = Yii::$app->request->post('product_storage_id');

            if (!$product_storage_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {


                $productStorage = ProductStorage::findOne([
                    'id' => $product_storage_id,
                    'accepted_at' => null,
                    'accepted_user_id' => null,
                    'deleted_at' => null
                ]);

                if (!$productStorage) {
                    throw new \Exception('Продукт не найден');
                }

                $productStorage->accepted_user_id = Yii::$app->user->getId();
                $productStorage->accepted_at = date('Y-m-d H:i:s');

                if (!$productStorage->save()) {
                    throw new \Exception('Ошибка при сохранении продукта' . json_encode($productStorage->getErrors()));
                }                // Проверяем, существует ли уже запись tracking для данного продукта
                $tracking = Tracking::findOne([
                    'progress_type' => Tracking::TYPE_PRODUCT_RELEASE,
                    'process_id' => $productStorage->id,
                    'deleted_at' => null
                ]);

                if (!$tracking) {
                    // Создаем новую запись только если ее еще нет
                    $tracking = new Tracking();
                    $tracking->progress_type = Tracking::TYPE_PRODUCT_RELEASE;
                    $tracking->process_id = $productStorage->id;
                    $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                    $tracking->created_at = date('Y-m-d H:i:s');
                    $tracking->accepted_at = null;

                    if (!$tracking->save()) {
                        throw new \Exception('Ошибка при сохранении tracking');
                    }
                }

                ActionLogger::actionLog(
                    'accept_product',
                    'product_storage',
                    $productStorage->id,
                    [
                        'product_id' => $productStorage->product_id,
                        'quantity' => $productStorage->quantity,
                        'tracking_id' => $tracking->id
                    ]
                );

                // Отправляем уведомление через Firebase
                try {
                    $product = $productStorage->product;
                    $productName = $product ? $product->name : "Продукт #{$productStorage->product_id}";
                    $title = 'Продукт принят на склад';
                    $body = "Продукт {$productName} в количестве {$productStorage->quantity} принят на склад";
                    $data = [
                        'product_storage_id' => $productStorage->id,
                        'type' => 'product_accepted',
                        'product_id' => $productStorage->product_id,
                        'quantity' => $productStorage->quantity
                    ];

                    // Создаем уведомление для пользователей с ролью product_keeper
                    Yii::$app->firebase->notifyRole('product_keeper', $title, $body, $data);
                } catch (\Exception $e) {
                    // Логируем ошибку, но не прерываем выполнение
                    Yii::error('Ошибка создания уведомления: ' . $e->getMessage(), 'firebase');
                }

                $transaction->commit();
                return ApiResponse::response('Продукт успешно принят');
            } catch (\Exception $e) {
                $transaction->rollBack();
                return ApiResponse::response(
                    'Ошибка при приеме продукта',
                    ['error' => $e->getMessage()],
                    ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                );
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }



    public function actionSendToProductDefect()
    {
        if (Yii::$app->request->isGet) {
            $date = Yii::$app->request->get('date') ?: date('Y-m-d');

            $query = ProductDefect::find()
                ->select([
                    'product_defect.id',
                    'product_defect.created_at',
                    'u.username as user_name',
                    'p.name as product_name',
                    'product_defect.quantity',
                    'product_defect.description',
                    'product_defect.is_repackaging',
                    'product_defect.repackaging_reason',
                    'au.username as accepted_by',
                    'p.id as product_id',
                    new \yii\db\Expression('CASE
                        WHEN product_defect.accepted_user_id IS NOT NULL
                        THEN true
                        ELSE false
                    END as is_accepted'),
                    new \yii\db\Expression('CASE
                        WHEN t.accepted_at IS NOT NULL
                        AND t.status = ' . Tracking::STATUS_ACCEPTED . '
                        AND t.deleted_at IS NULL
                        THEN true
                        ELSE false
                    END as status')
                ])
                ->leftJoin('product p', 'p.id = product_defect.product_id')
                ->leftJoin('users u', 'u.id = product_defect.add_user_id')
                ->leftJoin('users au', 'au.id = product_defect.accepted_user_id')
                ->leftJoin('tracking t', 't.process_id = product_defect.id AND t.progress_type = ' . Tracking::TYPE_PRODUCT_DEFECT)
                ->where(['product_defect.deleted_at' => null])
                ->andWhere(['DATE(product_defect.created_at)' => $date])
                ->orderBy(['product_defect.created_at' => SORT_DESC]);

            $defectProducts = $query->asArray()->all();

            return ApiResponse::response('Продукция для брака', [
                'defect_products' => $defectProducts
            ]);
        }

        if (Yii::$app->request->isPost) {
            $model = new SendToProductDefectForm();
            $model->load(Yii::$app->request->post(), '');

            if ($model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $trackingIds = [];
                    foreach ($model->products as $product) {
                        $storages = ProductStorage::find()
                            ->where([
                                'product_id' => $product['product_id'],
                                'deleted_at' => null,
                            ])
                            ->andWhere(['IS NOT', 'accepted_at', null])
                            ->andWhere(['IS NOT', 'accepted_user_id', null])
                            ->andWhere(['>=', 'quantity', 0])
                            ->orderBy(['enter_date' => SORT_ASC])
                            ->all();

                        $totalAvailable = array_sum(array_column($storages, 'quantity'));
                        if ($totalAvailable < $product['quantity']) {
                            throw new \Exception("Недостаточно продукта #{$product['product_id']} на складе. Доступно: $totalAvailable, требуется: {$product['quantity']}");
                        }

                        $remainingQuantity = $product['quantity'];
                        foreach ($storages as $storage) {
                            if ($remainingQuantity <= 0) break;

                            $quantityToDeduct = min($remainingQuantity, $storage->quantity);
                            $storage->quantity -= $quantityToDeduct;
                            $remainingQuantity -= $quantityToDeduct;

                            if (!$storage->save()) {
                                throw new \Exception('Ошибка обновления склада');
                            }

                            $storageHistory = new ProductStorageHistory();
                            $storageHistory->product_storage_id = $storage->id;
                            $storageHistory->product_id = $product['product_id'];
                            $storageHistory->quantity = -$quantityToDeduct;
                            $storageHistory->type = ProductStorageHistory::TYPE_OUTCOME;
                            $storageHistory->created_at = date('Y-m-d H:i:s');
                            $storageHistory->add_user_id = Yii::$app->user->getId();

                            if (!$storageHistory->save()) {
                                throw new \Exception('Ошибка сохранения истории склада');
                            }
                        }

                        $productDefect = new ProductDefect();
                        $productDefect->product_id = $product['product_id'];
                        $productDefect->quantity = $product['quantity'];
                        $productDefect->add_user_id = Yii::$app->user->getId();
                        $productDefect->description = $model->description;
                        $productDefect->created_at = date('Y-m-d H:i:s');
                        $productDefect->accepted_user_id = Yii::$app->user->getId();
                        $productDefect->accepted_at = date('Y-m-d H:i:s');

                        if (!$productDefect->save()) {
                            throw new \Exception('Ошибка при добавлении брака');
                        }

                        $tracking = new Tracking();
                        $tracking->progress_type = Tracking::TYPE_PRODUCT_DEFECT;
                        $tracking->process_id = $productDefect->id;
                        $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                        $tracking->created_at = date('Y-m-d H:i:s');
                        $tracking->accepted_at = null;

                        if (!$tracking->save()) {
                            throw new \Exception('Ошибка сохранения отслеживания');
                        }

                        $trackingIds[] = $tracking->id;
                    }

                    ActionLogger::actionLog(
                        'send_to_product_defect',
                        'product_defect',
                        null,
                        [
                            'tracking_ids' => $trackingIds,
                            'products' => array_map(function ($product) {
                                return [
                                    'product_id' => $product['product_id'],
                                    'quantity' => $product['quantity']
                                ];
                            }, $model->products),
                            'description' => $model->description
                        ]
                    );

                    $transaction->commit();
                    return ApiResponse::response('Продукция успешно отправлена в брак', []);
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return ApiResponse::response(
                        $e->getMessage(),
                        null,
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                }
            }

            return ApiResponse::response(
                'Ошибка валидации',
                $model->getFirstErrors(),
                ApiResponse::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }

    /**
     * Проверяет, может ли пользователь редактировать/удалять запись о браке
     * Разрешено только в течение 24 часов после создания и только создателю записи
     * 
     * @param ProductDefect $productDefect
     * @return array ['canEdit' => bool, 'message' => string]
     */
    private function canEditDefectRecord($productDefect)
    {
        // Проверяем, что текущий пользователь является создателем записи
        if ($productDefect->add_user_id != Yii::$app->user->getId()) {
            return [
                'canEdit' => false,
                'message' => 'Вы можете редактировать только свои записи'
            ];
        }

        // Проверяем, что прошло не более 24 часов с момента создания
        $createdTime = strtotime($productDefect->created_at);
        $currentTime = time();
        $timeDifference = $currentTime - $createdTime;

        if ($timeDifference > 86400) { // 86400 секунд = 24 часа
            return [
                'canEdit' => false,
                'message' => 'Редактирование и удаление записей разрешено только в течение 24 часов после создания'
            ];
        }

        return [
            'canEdit' => true,
            'message' => ''
        ];
    }

    public function actionUpdateProductDefect()
    {
        if (Yii::$app->request->isGet) {
            $defect_id = Yii::$app->request->get('defect_id');

            if (!$defect_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }            // Проверяем, что это не запись о переупаковке
            $productDefect = ProductDefect::findOne($defect_id);
            if ($productDefect && $productDefect->is_repackaging) {
                return ApiResponse::response(
                    'Для работы с переупаковкой используйте соответствующий API',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            // Проверяем возможность редактирования (24 часа и создатель записи)
            if ($productDefect) {
                $editCheck = $this->canEditDefectRecord($productDefect);
                if (!$editCheck['canEdit']) {
                    return ApiResponse::response(
                        $editCheck['message'],
                        null,
                        ApiResponse::HTTP_NOT_FOUND
                    );
                }
            }

            $sql = "
                SELECT
                    pd.id,
                    pd.product_id,
                    p.name as product_name,
                    pd.quantity,
                    pd.description,
                    pd.created_at,
                    u.username as added_by,
                    au.username as accepted_by,
                    CASE
                        WHEN pd.accepted_user_id IS NOT NULL THEN true
                        ELSE false
                    END as is_accepted,
                    CASE
                        WHEN t.accepted_at IS NOT NULL
                        AND t.status = :status_accepted
                        AND t.deleted_at IS NULL
                        THEN true
                        ELSE false
                    END as status
                FROM product_defect pd
                LEFT JOIN product p ON p.id = pd.product_id
                LEFT JOIN users u ON u.id = pd.add_user_id
                LEFT JOIN users au ON au.id = pd.accepted_user_id
                LEFT JOIN tracking t ON t.process_id = pd.id
                    AND t.progress_type = :progress_type
                    AND t.deleted_at IS NULL
                WHERE pd.id = :defect_id
                AND pd.deleted_at IS NULL
            ";

            $defect = Yii::$app->db->createCommand($sql)
                ->bindValue(':defect_id', $defect_id)
                ->bindValue(':progress_type', Tracking::TYPE_PRODUCT_DEFECT)
                ->bindValue(':status_accepted', Tracking::STATUS_ACCEPTED)
                ->queryOne();

            if (!$defect) {
                return ApiResponse::response(
                    'Запись не найдена или уже удалена',
                    null,
                    ApiResponse::HTTP_NOT_FOUND
                );
            }

            return ApiResponse::response('', [
                'defect' => $defect
            ]);
        }

        if (Yii::$app->request->isPost) {
            $model = new SendToProductDefectForm();
            $model->load(Yii::$app->request->post(), '');

            $defect_id = Yii::$app->request->post('defect_id');
            if (!$defect_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            if (!$model->validate()) {
                return ApiResponse::response(
                    'Ошибка валидации',
                    $model->getFirstErrors(),
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $tracking = Tracking::findOne([
                    'process_id' => $defect_id,
                    'progress_type' => Tracking::TYPE_PRODUCT_DEFECT,
                    'deleted_at' => null,
                    'accepted_at' => null
                ]);

                if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
                    throw new \Exception('Запись уже подтверждена или не найдена');
                }

                $productDefect = ProductDefect::findOne($defect_id);
                if (!$productDefect) {
                    throw new \Exception('Запись не найдена');
                }

                // Возвращаем старый продукт на склад
                $today = date('Y-m-d');
                $oldStorage = ProductStorage::find()
                    ->where(['deleted_at' => null])
                    ->andWhere(['product_id' => $productDefect->product_id])
                    ->andWhere(['IS NOT', 'accepted_at', null])
                    ->andWhere(['IS NOT', 'accepted_user_id', null])
                    ->andWhere(['DATE(enter_date)' => $today])
                    ->orderBy(['enter_date' => SORT_DESC])
                    ->one();

                if (!$oldStorage) {
                    $oldStorage = new ProductStorage();
                    $oldStorage->product_id = $productDefect->product_id;
                    $oldStorage->quantity = 0;
                    $oldStorage->enter_date = date('Y-m-d H:i:s');
                    $oldStorage->add_user_id = Yii::$app->user->getId();
                    $oldStorage->accepted_at = date('Y-m-d H:i:s');
                    $oldStorage->accepted_user_id = Yii::$app->user->getId();
                }

                $oldStorage->updated_date = date('Y-m-d H:i:s');
                $oldStorage->quantity += $productDefect->quantity;
                if (!$oldStorage->save()) {
                    throw new \Exception('Ошибка возврата старого продукта на склад');
                }

                $oldStorageHistory = new ProductStorageHistory();
                $oldStorageHistory->product_storage_id = $oldStorage->id;
                $oldStorageHistory->product_id = $productDefect->product_id;
                $oldStorageHistory->quantity = $productDefect->quantity;
                $oldStorageHistory->type = ProductStorageHistory::TYPE_INCOME;
                $oldStorageHistory->created_at = date('Y-m-d H:i:s');
                $oldStorageHistory->add_user_id = Yii::$app->user->getId();

                if (!$oldStorageHistory->save()) {
                    throw new \Exception('Ошибка сохранения истории склада (возврат старого)');
                }

                // Проверяем, что приходит только один продукт
                if (count($model->products) !== 1) {
                    throw new \Exception('Для обновления брака можно указать только один продукт');
                }

                $product = $model->products[0];

                // Проверяем существование продукта
                $productModel = \app\common\models\Product::findOne($product['product_id']);
                if (!$productModel) {
                    throw new \Exception("Продукт с ID {$product['product_id']} не найден");
                }

                $storages = ProductStorage::find()
                    ->where([
                        'product_id' => $product['product_id'],
                        'deleted_at' => null,
                    ])
                    ->andWhere(['IS NOT', 'accepted_at', null])
                    ->andWhere(['IS NOT', 'accepted_user_id', null])
                    ->andWhere(['>=', 'quantity', 0])
                    ->orderBy(['enter_date' => SORT_ASC])
                    ->all();

                $totalAvailable = array_sum(array_column($storages, 'quantity'));
                if ($totalAvailable < $product['quantity']) {
                    throw new \Exception("Недостаточно продукта #{$product['product_id']} на складе. Доступно: $totalAvailable, требуется: {$product['quantity']}");
                }

                $remainingQuantity = $product['quantity'];
                foreach ($storages as $storage) {
                    if ($remainingQuantity <= 0) break;

                    $quantityToDeduct = min($remainingQuantity, $storage->quantity);
                    $storage->quantity -= $quantityToDeduct;
                    $remainingQuantity -= $quantityToDeduct;

                    if (!$storage->save()) {
                        throw new \Exception('Ошибка обновления склада');
                    }

                    $storageHistory = new ProductStorageHistory();
                    $storageHistory->product_storage_id = $storage->id;
                    $storageHistory->product_id = $product['product_id'];
                    $storageHistory->quantity = -$quantityToDeduct;
                    $storageHistory->type = ProductStorageHistory::TYPE_OUTCOME;
                    $storageHistory->created_at = date('Y-m-d H:i:s');
                    $storageHistory->add_user_id = Yii::$app->user->getId();

                    if (!$storageHistory->save()) {
                        throw new \Exception('Ошибка сохранения истории склада (списание)');
                    }
                }

                // Сохраняем старые значения для логирования
                $oldProductId = $productDefect->product_id;
                $oldQuantity = $productDefect->quantity;

                $productDefect->product_id = $product['product_id'];
                $productDefect->quantity = $product['quantity'];
                $productDefect->description = $model->description;

                if (!$productDefect->save()) {
                    throw new \Exception('Ошибка при обновлении брака');
                }

                ActionLogger::actionLog(
                    'update_product_defect',
                    'product_defect',
                    $productDefect->id,
                    [
                        'old_product_id' => $oldProductId,
                        'old_quantity' => $oldQuantity,
                        'new_product_id' => $product['product_id'],
                        'new_quantity' => $product['quantity'],
                        'description' => $model->description,
                        'tracking_id' => $tracking->id
                    ]
                );

                $transaction->commit();
                return ApiResponse::response('Брак успешно обновлен');
            } catch (\Exception $e) {
                $transaction->rollBack();
                return ApiResponse::response(
                    $e->getMessage(),
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }

    public function actionDeleteProductDefect()
    {
        if (Yii::$app->request->isPost) {
            $defect_id = Yii::$app->request->post('defect_id');

            if (!$defect_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $tracking = Tracking::findOne([
                    'process_id' => $defect_id,
                    'progress_type' => Tracking::TYPE_PRODUCT_DEFECT,
                    'deleted_at' => null,
                    'accepted_at' => null
                ]);

                if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
                    throw new \Exception('Запись уже подтверждена или не найдена');
                }

                $productDefect = ProductDefect::findOne($defect_id);
                if (!$productDefect) {
                    throw new \Exception('Запись не найдена');
                }

                // Проверяем возможность редактирования (24 часа и создатель записи)
                if ($productDefect) {
                    $editCheck = $this->canEditDefectRecord($productDefect);
                    if (!$editCheck['canEdit']) {
                        return ApiResponse::response(
                            $editCheck['message'],
                            null,
                            ApiResponse::HTTP_NOT_FOUND
                        );
                    }
                }

                $today = date('Y-m-d');
                $storage = ProductStorage::find()
                    ->where(['deleted_at' => null])
                    ->andWhere(['product_id' => $productDefect->product_id])
                    ->andWhere(['IS NOT', 'accepted_at', null])
                    ->andWhere(['IS NOT', 'accepted_user_id', null])
                    ->andWhere(['DATE(enter_date)' => $today])
                    ->orderBy(['enter_date' => SORT_DESC])
                    ->one();

                if (!$storage) {
                    $storage = new ProductStorage();
                    $storage->product_id = $productDefect->product_id;
                    $storage->quantity = 0;
                    $storage->enter_date = date('Y-m-d H:i:s');
                    $storage->add_user_id = Yii::$app->user->getId();
                    $storage->accepted_at = date('Y-m-d H:i:s');
                    $storage->accepted_user_id = Yii::$app->user->getId();
                }

                $storage->updated_date = date('Y-m-d H:i:s');
                $storage->quantity += $productDefect->quantity;
                if (!$storage->save()) {
                    throw new \Exception('Ошибка возврата на склад');
                }

                $storageHistory = new ProductStorageHistory();
                $storageHistory->product_storage_id = $storage->id;
                $storageHistory->product_id = $productDefect->product_id;
                $storageHistory->quantity = $productDefect->quantity;
                $storageHistory->type = ProductStorageHistory::TYPE_INCOME;
                $storageHistory->created_at = date('Y-m-d H:i:s');
                $storageHistory->add_user_id = Yii::$app->user->getId();

                if (!$storageHistory->save()) {
                    throw new \Exception('Ошибка сохранения истории склада');
                }

                $tracking->deleted_at = date('Y-m-d H:i:s');
                if (!$tracking->save()) {
                    throw new \Exception('Ошибка при обновлении tracking');
                }

                $productDefect->deleted_at = date('Y-m-d H:i:s');
                if (!$productDefect->save()) {
                    throw new \Exception('Ошибка при удалении брака');
                }

                ActionLogger::actionLog(
                    'delete_product_defect',
                    'product_defect',
                    $productDefect->id,
                    [
                        'product_id' => $productDefect->product_id,
                        'quantity' => $productDefect->quantity
                    ]
                );

                $transaction->commit();
                return ApiResponse::response('Брак успешно удален');
            } catch (\Exception $e) {
                $transaction->rollBack();
                return ApiResponse::response(
                    $e->getMessage(),
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }


    public function actionInvoice()
    {
        $params = Yii::$app->request->get();

        $query = (new \yii\db\Query())
            ->select([
                's.id',
                'c.full_name as client_name',
                's.car_number',
                's.created_at',
                's.status',
            ])
            ->from(['s' => 'sales'])
            ->leftJoin(['c' => 'client'], 'c.id = s.client_id')
            ->where(['s.deleted_at' => null])
            ->orderBy(['s.created_at' => SORT_DESC]);

        if (!empty($params['date'])) {
            $date = date('Y-m-d', strtotime($params['date']));
            $query->andWhere(['>=', 's.created_at', $date . ' 00:00:00'])
                ->andWhere(['<=', 's.created_at', $date . ' 23:59:59']);
        }

        $sales = $query->all();

        return ApiResponse::response("Sales list", [
            'sales' => $sales
        ]);
    }


    public function actionSalesStart()
    {
        if (Yii::$app->request->isGet) {
            $sale_id = Yii::$app->request->get('sales_id');

            if (!$sale_id) {
                return ApiResponse::response("Sale ID is required", null, 400);
            }

            $sales = Sales::find()
                ->where(['id' => $sale_id])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['status' => Sales::STATUS_NEW])
                ->one();

            if (!$sales) {
                return ApiResponse::response("Sales not found or invalid status", null, 404);
            }

            $bonusProducts = (new \yii\db\Query())
                ->select([
                    'p.name as product_name',
                    'p.size',
                    'sb.quantity'
                ])
                ->from(['sb' => 'sales_bonus'])
                ->leftJoin(['s' => 'sales'], 's.id = sb.sale_id')
                ->leftJoin(['p' => 'product'], 'p.id = sb.product_id')
                ->where([
                    'sb.sale_id' => $sale_id,
                    'sb.deleted_at' => null
                ])
                ->all();

            // Рассчитываем количество блоков для бонусных продуктов
            foreach ($bonusProducts as &$bonus) {
                $bonus['box_count'] = $bonus['quantity'] / $bonus['size'];
            }

            $totals = (new \yii\db\Query())
                ->select([
                    'SUM(quantity) as total_quantity',
                    'SUM(total_price) as total_sum'
                ])
                ->from('sales_detail')
                ->where([
                    'sale_id' => $sale_id,
                    'deleted_at' => null
                ])
                ->one();

            $query = (new \yii\db\Query())
                ->select([
                    'p.name as product_name',
                    'p.size',
                    'sd.quantity',
                    'sd.total_price'
                ])
                ->from(['sd' => 'sales_detail'])
                ->leftJoin(['p' => 'product'], 'p.id = sd.product_id')
                ->where([
                    'sd.sale_id' => $sale_id,
                    'sd.deleted_at' => null
                ]);

            $details = $query->all();

            if (empty($details)) {
                return ApiResponse::response("Sales details not found", null, 404);
            }

            // Рассчитываем количество блоков для каждого продукта
            foreach ($details as &$detail) {
                $detail['total_price'] = number_format((float)$detail['total_price'], 0, ',', '.');
                $detail['box_count'] = $detail['quantity'] / $detail['size'];
            }
            unset($detail);

            return ApiResponse::response("Sales details", [
                'details' => $details,
                'bonus_products' => $bonusProducts,
                'total_quantity' => (int)$totals['total_quantity'],
                'total_sum' => number_format((float)$totals['total_sum'], 0, ',', '.')
            ]);
        } else if (Yii::$app->request->isPost) {
            $sales_id = Yii::$app->request->post('sales_id');

            $sales = Sales::find()
                ->where(['id' => $sales_id])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['status' => Sales::STATUS_NEW])->one();
            if (!$sales) {
                return ApiResponse::response("Sales not found", null, 404);
            }

            $sales->status = Sales::STATUS_IN_PROGRESS;
            $sales->started_at = date('Y-m-d H:i:s');

            if (!$sales->save()) {
                return ApiResponse::response("Error", null, 500);
            }

            ActionLogger::actionLog(
                'start_sales',
                'sales',
                null,
                [
                    'sales_id' => $sales_id,
                    'old_status' => Sales::STATUS_NEW,
                    'new_status' => Sales::STATUS_IN_PROGRESS,
                    'details' => array_map(function ($detail) {
                        return [
                            'product_id' => $detail->product_id,
                            'quantity' => $detail->quantity,
                            'total_price' => $detail->total_price
                        ];
                    }, $sales->salesDetails)
                ]
            );

            return ApiResponse::response("Sales started", [
                'sales_id' => $sales_id
            ]);
        }
    }


    public function actionSalesFinish()
    {
        if (Yii::$app->request->isGet) {
            $sales_id = Yii::$app->request->get('sales_id');

            if (!$sales_id) {
                return ApiResponse::response("Sale ID is required", null, 400);
            }

            $sales = Sales::find()
                ->where(['id' => $sales_id])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['status' => Sales::STATUS_IN_PROGRESS])
                ->one();

            if (!$sales) {
                return ApiResponse::response("Sales not found or invalid status", null, 404);
            }

            $query = (new \yii\db\Query())
                ->select([
                    'p.name as product_name',
                    'p.size',
                    'sd.quantity',
                    'sd.total_price'
                ])
                ->from(['sd' => 'sales_detail'])
                ->leftJoin(['p' => 'product'], 'p.id = sd.product_id')
                ->where([
                    'sd.sale_id' => $sales_id,
                    'sd.deleted_at' => null
                ]);

            $details = $query->all();

            if (empty($details)) {
                return ApiResponse::response("Sales details not found", null, 404);
            }

            // Рассчитываем количество блоков для каждого продукта
            foreach ($details as &$detail) {
                $detail['total_price'] = number_format((float)$detail['total_price'], 0, ',', '.');
                $detail['box_count'] = $detail['quantity'] / $detail['size'];
            }
            unset($detail);

            $totals = (new \yii\db\Query())
                ->select([
                    'SUM(quantity) as total_quantity',
                    'SUM(total_price) as total_sum'
                ])
                ->from('sales_detail')
                ->where([
                    'sale_id' => $sales_id,
                    'deleted_at' => null
                ])
                ->one();

            $bonusProducts = (new \yii\db\Query())
                ->select([
                    'p.name as product_name',
                    'p.size',
                    'sb.quantity'
                ])
                ->from(['sb' => 'sales_bonus'])
                ->leftJoin(['s' => 'sales'], 's.id = sb.sale_id')
                ->leftJoin(['p' => 'product'], 'p.id = sb.product_id')
                ->where([
                    'sb.sale_id' => $sales_id,
                    'sb.deleted_at' => null
                ])
                ->all();

            // Рассчитываем количество блоков для бонусных продуктов
            foreach ($bonusProducts as &$bonus) {
                $bonus['box_count'] = $bonus['quantity'] / $bonus['size'];
            }

            $formattedTotalSum = number_format((float)$totals['total_sum'], 0, ',', '.');

            return ApiResponse::response("Sales details", [
                'details' => $details,
                'bonus_products' => $bonusProducts,
                'total_quantity' => (int)$totals['total_quantity'],
                'total_sum' => $formattedTotalSum
            ]);
        } else if (Yii::$app->request->isPost) {
            $sales_id = Yii::$app->request->post('sales_id');

            $sales = Sales::find()
                ->where(['id' => $sales_id])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['status' => Sales::STATUS_IN_PROGRESS])
                ->one();
            if (!$sales) {
                return ApiResponse::response("Sales not found", null, 404);
            }

            // Начинаем транзакцию
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $sales->confirm_user_id = Yii::$app->user->id;
                $sales->status = Sales::STATUS_CONFIRMED;
                $sales->completed_at = date('Y-m-d H:i:s');
                if (!$sales->save()) {
                    throw new \Exception("Ошибка при сохранении продажи");
                }

                // Обрабатываем каждый товар в продаже
                foreach ($sales->salesDetails as $detail) {
                    $remainingQuantity = $detail->quantity;

                    // Получаем все записи склада для данного продукта
                    $productStorages = ProductStorage::find()
                        ->where(['product_id' => $detail->product_id, 'deleted_at' => null])
                        ->orderBy(['enter_date' => SORT_ASC]) // Берем сначала старые записи
                        ->all();

                    $totalAvailable = array_sum(array_column($productStorages, 'quantity'));
                    if ($totalAvailable < $remainingQuantity) {
                        throw new \Exception("Недостаточно товара {$detail->product->name} на складе");
                    }

                    // Уменьшаем количество последовательно из каждой записи склада
                    foreach ($productStorages as $storage) {
                        if ($remainingQuantity <= 0) {
                            break;
                        }

                        $deductFromThisStorage = min($remainingQuantity, $storage->quantity);
                        $storage->quantity -= $deductFromThisStorage;
                        $remainingQuantity -= $deductFromThisStorage;

                        if (!$storage->save()) {
                            throw new \Exception("Ошибка при обновлении количества на складе");
                        }

                        // Записываем в историю склада
                        $history = new ProductStorageHistory();
                        $history->product_storage_id = $storage->id;
                        $history->product_id = $detail->product_id;
                        $history->quantity = $deductFromThisStorage;
                        $history->type = ProductStorageHistory::TYPE_OUTCOME;
                        $history->created_at = date('Y-m-d H:i:s');
                        if (!$history->save()) {
                            throw new \Exception("Ошибка при сохранении истории склада");
                        }
                    }
                }

                // Обрабатываем бонусные товары
                $salesBonuses = SalesBonus::findAll(['sale_id' => $sales->id, 'deleted_at' => null]);
                foreach ($salesBonuses as $bonus) {
                    $remainingQuantity = $bonus->quantity;

                    // Получаем все записи склада для бонусного продукта
                    $productStorages = ProductStorage::find()
                        ->where(['product_id' => $bonus->product_id, 'deleted_at' => null])
                        ->orderBy(['enter_date' => SORT_ASC])
                        ->all();

                    $totalAvailable = array_sum(array_column($productStorages, 'quantity'));
                    if ($totalAvailable < $remainingQuantity) {
                        throw new \Exception("Недостаточно бонусного товара {$bonus->product->name} на складе");
                    }

                    // Уменьшаем количество последовательно из каждой записи склада
                    foreach ($productStorages as $storage) {
                        if ($remainingQuantity <= 0) {
                            break;
                        }

                        $deductFromThisStorage = min($remainingQuantity, $storage->quantity);
                        $storage->quantity -= $deductFromThisStorage;
                        $remainingQuantity -= $deductFromThisStorage;

                        if (!$storage->save()) {
                            throw new \Exception("Ошибка при обновлении количества бонусного товара на складе");
                        }

                        // Записываем в историю склада
                        $history = new ProductStorageHistory();
                        $history->product_storage_id = $storage->id;
                        $history->product_id = $bonus->product_id;
                        $history->quantity = $deductFromThisStorage;
                        $history->type = ProductStorageHistory::TYPE_OUTCOME;
                        $history->created_at = date('Y-m-d H:i:s');
                        if (!$history->save()) {
                            throw new \Exception("Ошибка при сохранении истории склада для бонуса");
                        }
                    }
                }

                $clientBalance = ClientBalance::findOne(['client_id' => $sales->client_id]);
                if (!$clientBalance) {
                    $clientBalance = new ClientBalance();
                    $clientBalance->client_id = $sales->client_id;
                    $clientBalance->amount = 0;
                }
                $clientBalance->amount -= $sales->total_special_prices_sum;
                if (!$clientBalance->save()) {
                    throw new \Exception("Ошибка при сохранении баланса клиента");
                }

                $clientBalanceHistory = new ClientBalanceHistory();
                $clientBalanceHistory->client_id = $sales->client_id;
                $clientBalanceHistory->amount = $sales->total_special_prices_sum;
                $clientBalanceHistory->old_amount = $clientBalance->amount + $sales->total_special_prices_sum;
                $clientBalanceHistory->type = ClientBalanceHistory::TYPE_DECREASE;
                $clientBalanceHistory->created_at = date('Y-m-d H:i:s');
                if (!$clientBalanceHistory->save()) {
                    throw new \Exception("Ошибка при сохранении истории баланса");
                }

                ActionLogger::actionLog(
                    'finish_sales',
                    'sales',
                    null,
                    [
                        'sales_id' => $sales_id,
                        'old_status' => Sales::STATUS_IN_PROGRESS,
                        'new_status' => Sales::STATUS_CONFIRMED,
                        'confirm_user_id' => Yii::$app->user->id,
                        'details' => array_map(function ($detail) {
                            return [
                                'product_id' => $detail->product_id,
                                'quantity' => $detail->quantity,
                                'total_price' => $detail->total_price
                            ];
                        }, $sales->salesDetails)
                    ]
                );

                $transaction->commit();

                return ApiResponse::response("Sales finished", [
                    'sales_id' => $sales_id
                ]);
            } catch (\Exception $e) {
                $transaction->rollBack();
                return ApiResponse::response("Error: " . $e->getMessage(), null, 500);
            }
        }
    }



    public function actionSalesDetail()
    {
        if (Yii::$app->request->isGet) {
            $sales_id = Yii::$app->request->get('sales_id');

            if (!$sales_id) {
                return ApiResponse::response("Sales ID is required", null, 400);
            }

            $sales = Sales::find()
                ->where(['id' => $sales_id])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['status' => Sales::STATUS_CONFIRMED])
                ->one();

            if (!$sales) {
                return ApiResponse::response("Sales not found or invalid status", null, 404);
            }

            $bonusProducts = (new \yii\db\Query())
                ->select([
                    'p.name as product_name',
                    'p.size',
                    'sb.quantity'
                ])
                ->from(['sb' => 'sales_bonus'])
                ->leftJoin(['s' => 'sales'], 's.id = sb.sale_id')
                ->leftJoin(['p' => 'product'], 'p.id = sb.product_id')
                ->where([
                    'sb.sale_id' => $sales_id,
                    'sb.deleted_at' => null
                ])
                ->all();

            foreach ($bonusProducts as &$bonus) {
                $bonus['box_count'] = $bonus['quantity'] / $bonus['size'];
            }
            unset($bonus);

            $totals = (new \yii\db\Query())
                ->select([
                    'SUM(quantity) as total_quantity',
                    'SUM(total_price) as total_sum'
                ])
                ->from('sales_detail')
                ->where([
                    'sale_id' => $sales_id,
                    'deleted_at' => null
                ])
                ->one();

            $query = (new \yii\db\Query())
                ->select([
                    'sd.id',
                    'sd.quantity',
                    'sd.total_price',
                    'p.name as product_name',
                    'p.size'
                ])
                ->from(['sd' => 'sales_detail'])
                ->leftJoin(['p' => 'product'], 'p.id = sd.product_id')
                ->where([
                    'sd.sale_id' => $sales_id,
                    'sd.deleted_at' => null
                ]);

            $details = $query->all();

            if (empty($details)) {
                return ApiResponse::response("Sales details not found", null, 404);
            }

            foreach ($details as &$detail) {
                $detail['total_price'] = number_format((float)$detail['total_price'], 0, ',', '.');
                $detail['box_count'] = $detail['quantity'] / $detail['size'];
            }
            unset($detail);

            $formattedTotalSum = number_format((float)$totals['total_sum'], 0, ',', '.');

            return ApiResponse::response("Sales details", [
                'details' => $details,
                'bonus_products' => $bonusProducts,
                'total_quantity' => (int)$totals['total_quantity'],
                'total_sum' => $formattedTotalSum
            ]);
        }
    }

    /**
     * Получение списка продуктов для переупаковки
     *
     * @return array
     */
    public function actionGetProductsForRepackaging()
    {
        if (Yii::$app->request->isGet) {
            $sql = "SELECT
                    ps.product_id,
                    p.name as product_name,
                    SUM(ps.quantity) as quantity,
                    MIN(ps.enter_date) as enter_date
                FROM product_storage ps
                LEFT JOIN product p ON p.id = ps.product_id
                WHERE ps.deleted_at IS NULL and ps.accepted_at IS NOT NULL and ps.accepted_user_id IS NOT NULL
                AND ps.quantity > 0
                GROUP BY ps.product_id, p.name
                ORDER BY MIN(ps.enter_date) ASC";

            $products = Yii::$app->db->createCommand($sql)->queryAll();

            return ApiResponse::response('Продукты для переупаковки', [
                'products' => $products
            ]);
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }


    public function actionSendToProductRepackaging()
    {
        if (Yii::$app->request->isPost) {
            $model = new SendToProductRepackagingForm();
            $model->load(Yii::$app->request->post(), '');

            if (!$model->validate()) {
                return ApiResponse::response(
                    'Ошибка валидации',
                    $model->getFirstErrors(),
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $productDefects = [];
                $trackings = [];

                // Ищем все записи продукта на складе, не ограничиваясь текущим днем
                $productStorages = ProductStorage::find()
                    ->where(['product_id' => $model->product_id, 'deleted_at' => null])
                    ->andWhere(['>=', 'quantity', 0])
                    ->andWhere(['IS NOT', 'accepted_at', null])
                    ->andWhere(['IS NOT', 'accepted_user_id', null])
                    ->orderBy(['enter_date' => SORT_ASC]) // Сначала берем самые старые записи
                    ->all();

                if (empty($productStorages)) {
                    throw new \Exception('Записи на складе не найдены для продукта ID: ' . $model->product_id);
                }

                $remainingQuantity = $model->quantity;
                foreach ($productStorages as $productStorage) {
                    if ($remainingQuantity <= 0) break;

                    $quantityToDeduct = min($remainingQuantity, $productStorage->quantity);
                    $productStorage->quantity -= $quantityToDeduct;
                    $remainingQuantity -= $quantityToDeduct;

                    if ($productStorage->quantity < 0) {
                        throw new \Exception('Недостаточно продукта на складе для ID: ' . $model->product_id);
                    }

                    if (!$productStorage->save()) {
                        throw new \Exception('Ошибка при обновлении записи на складе' . json_encode($productStorage->getErrors()));
                    }

                    // Создаем запись в истории склада
                    $history = new ProductStorageHistory();
                    $history->product_storage_id = $productStorage->id;
                    $history->product_id = $productStorage->product_id;
                    $history->quantity = $quantityToDeduct;
                    $history->type = ProductStorageHistory::TYPE_REPACKAGING;
                    $history->created_at = date('Y-m-d H:i:s');
                    $history->add_user_id = Yii::$app->user->getId();

                    if (!$history->save()) {
                        throw new \Exception('Ошибка при создании записи в истории склада' . json_encode($history->getErrors()));
                    }

                    if ($remainingQuantity <= 0) {
                        break;
                    }
                }

                if ($remainingQuantity > 0) {
                    throw new \Exception('Недостаточно продукта на складе для ID: ' . $model->product_id);
                }

                $productDefect = new ProductDefect();
                $productDefect->product_id = $model->product_id;
                $productDefect->quantity = $model->quantity;
                $productDefect->add_user_id = Yii::$app->user->getId();
                $productDefect->description = 'Переупаковка продукта';
                $productDefect->is_repackaging = true;
                $productDefect->created_at = date('Y-m-d H:i:s');

                if (!$productDefect->save()) {
                    throw new \Exception('Ошибка при создании записи о переупаковке' . json_encode($productDefect->getErrors()));
                }
                $productDefects[] = $productDefect;

                // Создаем запись в tracking
                $tracking = new Tracking();
                $tracking->progress_type = Tracking::TYPE_PRODUCT_REPACKAGING;
                $tracking->process_id = $productDefect->id;
                $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                $tracking->created_at = date('Y-m-d H:i:s');
                $tracking->accepted_at = null;

                if (!$tracking->save()) {
                    throw new \Exception('Ошибка сохранения отслеживания' . json_encode($tracking->getErrors()));
                }
                $trackings[] = $tracking;

                // Логируем действие
                ActionLogger::actionLog(
                    'create_product_repackaging',
                    'product_defect',
                    $productDefect->id,
                    [
                        'product_id' => $model->product_id,
                        'quantity' => $model->quantity,
                        'tracking_id' => $tracking->id
                    ]
                );
            } catch (\Exception $e) {
                $transaction->rollBack();
                return ApiResponse::response(
                    $e->getMessage(),
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $transaction->commit();
            return ApiResponse::response('Продукция успешно отправлена на переупаковку', [
                'product_defects' => array_map(function ($pd) {
                    return $pd->id;
                }, $productDefects)
            ]);
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }


    /**
     * Удаление запроса на переупаковку продукта
     *
     * @return array
     */
    public function actionSendToProductRepackagingDelete()
    {
        if (Yii::$app->request->isPost) {
            $repackaging_id = Yii::$app->request->post('product_defect_id');

            if (!$repackaging_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $tracking = Tracking::findOne([
                    'process_id' => $repackaging_id,
                    'progress_type' => Tracking::TYPE_PRODUCT_REPACKAGING,
                    'deleted_at' => null,
                    'accepted_at' => null
                ]);

                if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
                    throw new \Exception('Запись уже подтверждена или не найдена');
                }

                $productDefect = ProductDefect::findOne([
                    'id' => $repackaging_id,
                    'deleted_at' => null,
                    'is_repackaging' => true,
                    'accepted_at' => null,
                    'accepted_user_id' => null
                ]);
                if (!$productDefect) {
                    throw new \Exception('Запись не найдена');
                }

                // Возвращаем продукт на склад
                // Сначала проверяем запись за текущий день
                $today = date('Y-m-d');
                $storage = ProductStorage::find()
                    ->where(['deleted_at' => null])
                    ->andWhere(['product_id' => $productDefect->product_id])
                    ->andWhere(['IS NOT', 'accepted_at', null])
                    ->andWhere(['IS NOT', 'accepted_user_id', null])
                    ->andWhere(['DATE(enter_date)' => $today])
                    ->orderBy(['enter_date' => SORT_DESC])
                    ->one();

                if (!$storage) {
                    $storage = new ProductStorage();
                    $storage->product_id = $productDefect->product_id;
                    $storage->quantity = 0;
                    $storage->enter_date = date('Y-m-d H:i:s');
                    $storage->add_user_id = Yii::$app->user->getId();
                    $storage->accepted_at = date('Y-m-d H:i:s');
                    $storage->accepted_user_id = Yii::$app->user->getId();
                }

                $storage->updated_date = date('Y-m-d H:i:s');
                $storage->quantity += $productDefect->quantity;
                if (!$storage->save()) {
                    throw new \Exception('Ошибка возврата на склад');
                }

                $storageHistory = new ProductStorageHistory();
                $storageHistory->product_storage_id = $storage->id;
                $storageHistory->product_id = $productDefect->product_id;
                $storageHistory->quantity = $productDefect->quantity;
                $storageHistory->type = ProductStorageHistory::TYPE_INCOME;
                $storageHistory->created_at = date('Y-m-d H:i:s');
                $storageHistory->add_user_id = Yii::$app->user->getId();

                if (!$storageHistory->save()) {
                    throw new \Exception('Ошибка сохранения истории склада');
                }

                $tracking->deleted_at = date('Y-m-d H:i:s');
                if (!$tracking->save()) {
                    throw new \Exception('Ошибка при обновлении tracking');
                }

                $productDefect->deleted_at = date('Y-m-d H:i:s');
                if (!$productDefect->save()) {
                    throw new \Exception('Ошибка при удалении записи о переупаковке');
                }

                ActionLogger::actionLog(
                    'delete_product_repackaging',
                    'product_defect',
                    $productDefect->id,
                    [
                        'product_id' => $productDefect->product_id,
                        'quantity' => $productDefect->quantity,
                        'repackaging_reason' => $productDefect->repackaging_reason
                    ]
                );

                $transaction->commit();
                return ApiResponse::response('Запись о переупаковке успешно удалена');
            } catch (\Exception $e) {
                $transaction->rollBack();
                return ApiResponse::response(
                    $e->getMessage(),
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }

    public function actionUpdateProductRepackaging()
    {
        if (Yii::$app->request->isGet) {
            $repackaging_id = Yii::$app->request->get('repackaging_id');

            if (!$repackaging_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            // Проверяем, что это запись о переупаковке
            $productDefect = ProductDefect::findOne($repackaging_id);
            if ($productDefect && !$productDefect->is_repackaging) {
                return ApiResponse::response(
                    'Для работы с браком используйте соответствующий API',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $sql = "
                SELECT
                    pd.id,
                    pd.product_id,
                    p.name as product_name,
                    pd.quantity,
                    pd.description,
                    pd.created_at,
                    u.username as added_by,
                    au.username as accepted_by,
                    CASE
                        WHEN pd.accepted_user_id IS NOT NULL THEN true
                        ELSE false
                    END as is_accepted,
                    CASE
                        WHEN t.accepted_at IS NOT NULL
                        AND t.status = :status_accepted
                        AND t.deleted_at IS NULL
                        THEN true
                        ELSE false
                    END as status
                FROM product_defect pd
                LEFT JOIN product p ON p.id = pd.product_id
                LEFT JOIN users u ON u.id = pd.add_user_id
                LEFT JOIN users au ON au.id = pd.accepted_user_id
                LEFT JOIN tracking t ON t.process_id = pd.id
                    AND t.progress_type = :progress_type
                    AND t.deleted_at IS NULL
                WHERE pd.id = :repackaging_id
                AND pd.deleted_at IS NULL
                AND pd.is_repackaging = true
            ";

            $repackaging = Yii::$app->db->createCommand($sql)
                ->bindValue(':repackaging_id', $repackaging_id)
                ->bindValue(':progress_type', Tracking::TYPE_PRODUCT_REPACKAGING)
                ->bindValue(':status_accepted', Tracking::STATUS_ACCEPTED)
                ->queryOne();

            if (!$repackaging) {
                return ApiResponse::response(
                    'Запись не найдена или уже удалена',
                    null,
                    ApiResponse::HTTP_NOT_FOUND
                );
            }

            return ApiResponse::response('', [
                'repackaging' => $repackaging
            ]);
        }

        if (Yii::$app->request->isPost) {
            $model = new SendToProductRepackagingForm();
            $model->load(Yii::$app->request->post(), '');

            $product_defect_id = Yii::$app->request->post('product_defect_id');
            if (!$product_defect_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            if (!$model->validate()) {
                return ApiResponse::response(
                    'Ошибка валидации',
                    $model->getFirstErrors(),
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $tracking = Tracking::findOne([
                    'process_id' => $product_defect_id,
                    'progress_type' => Tracking::TYPE_PRODUCT_REPACKAGING,
                    'deleted_at' => null,
                    'accepted_at' => null
                ]);

                if (!$tracking || $tracking->status === Tracking::STATUS_ACCEPTED) {
                    throw new \Exception('Запись уже подтверждена или не найдена');
                }

                $productDefect = ProductDefect::findOne([
                    'id' => $product_defect_id,
                    'is_repackaging' => true,
                    'deleted_at' => null,
                    'accepted_at' => null,
                    'accepted_user_id' => null
                ]);

                if (!$productDefect) {
                    throw new \Exception('Запись не найдена');
                }

                // Возвращаем старый продукт на склад
                $today = date('Y-m-d');
                $oldStorage = ProductStorage::find()
                    ->where(['deleted_at' => null])
                    ->andWhere(['product_id' => $productDefect->product_id])
                    ->andWhere(['IS NOT', 'accepted_at', null])
                    ->andWhere(['IS NOT', 'accepted_user_id', null])
                    ->andWhere(['DATE(enter_date)' => $today])
                    ->orderBy(['enter_date' => SORT_DESC])
                    ->one();

                if (!$oldStorage) {
                    $oldStorage = new ProductStorage();
                    $oldStorage->product_id = $productDefect->product_id;
                    $oldStorage->quantity = 0;
                    $oldStorage->enter_date = date('Y-m-d H:i:s');
                    $oldStorage->add_user_id = Yii::$app->user->getId();
                    $oldStorage->accepted_at = date('Y-m-d H:i:s');
                    $oldStorage->accepted_user_id = Yii::$app->user->getId();
                }

                $oldStorage->updated_date = date('Y-m-d H:i:s');
                $oldStorage->quantity += $productDefect->quantity;
                if (!$oldStorage->save()) {
                    throw new \Exception('Ошибка возврата старого продукта на склад');
                }

                $oldStorageHistory = new ProductStorageHistory();
                $oldStorageHistory->product_storage_id = $oldStorage->id;
                $oldStorageHistory->product_id = $productDefect->product_id;
                $oldStorageHistory->quantity = $productDefect->quantity;
                $oldStorageHistory->type = ProductStorageHistory::TYPE_INCOME;
                $oldStorageHistory->created_at = date('Y-m-d H:i:s');
                $oldStorageHistory->add_user_id = Yii::$app->user->getId();

                if (!$oldStorageHistory->save()) {
                    throw new \Exception('Ошибка сохранения истории склада (возврат старого)');
                }

                // Ищем все записи продукта на складе, не ограничиваясь текущим днем
                $productStorages = ProductStorage::find()
                    ->where(['product_id' => $model->product_id, 'deleted_at' => null])
                    ->andWhere(['>=', 'quantity', 0])
                    ->andWhere(['IS NOT', 'accepted_at', null])
                    ->andWhere(['IS NOT', 'accepted_user_id', null])
                    ->orderBy(['enter_date' => SORT_ASC]) // Сначала берем самые старые записи
                    ->all();

                if (empty($productStorages)) {
                    throw new \Exception('Записи на складе не найдены для продукта ID: ' . $model->product_id);
                }

                $remainingQuantity = $model->quantity;
                foreach ($productStorages as $productStorage) {
                    if ($remainingQuantity <= 0) break;

                    $quantityToDeduct = min($remainingQuantity, $productStorage->quantity);
                    $productStorage->quantity -= $quantityToDeduct;
                    $remainingQuantity -= $quantityToDeduct;

                    if ($productStorage->quantity < 0) {
                        throw new \Exception('Недостаточно продукта на складе для ID: ' . $model->product_id);
                    }

                    if (!$productStorage->save()) {
                        throw new \Exception('Ошибка при обновлении записи на складе' . json_encode($productStorage->getErrors()));
                    }

                    // Создаем запись в истории склада
                    $history = new ProductStorageHistory();
                    $history->product_storage_id = $productStorage->id;
                    $history->product_id = $productStorage->product_id;
                    $history->quantity = -$quantityToDeduct;
                    $history->type = ProductStorageHistory::TYPE_REPACKAGING;
                    $history->created_at = date('Y-m-d H:i:s');
                    $history->add_user_id = Yii::$app->user->getId();

                    if (!$history->save()) {
                        throw new \Exception('Ошибка при создании записи в истории склада' . json_encode($history->getErrors()));
                    }
                }

                if ($remainingQuantity > 0) {
                    throw new \Exception('Недостаточно продукта на складе для ID: ' . $model->product_id);
                }

                // Обновляем данные о переупаковке
                $productDefect->product_id = $model->product_id;
                $productDefect->quantity = $model->quantity;
                $productDefect->description = 'Переупаковка продукта';
                $productDefect->is_repackaging = true;

                if (!$productDefect->save()) {
                    throw new \Exception('Ошибка при обновлении записи о переупаковке' . json_encode($productDefect->getErrors()));
                }

                // Логируем действие
                ActionLogger::actionLog(
                    'update_product_repackaging',
                    'product_defect',
                    $productDefect->id,
                    [
                        'old_product_id' => $productDefect->getOldAttribute('product_id'),
                        'old_quantity' => $productDefect->getOldAttribute('quantity'),
                        'new_product_id' => $model->product_id,
                        'new_quantity' => $model->quantity,
                        'tracking_id' => $tracking->id
                    ]
                );
            } catch (\Exception $e) {
                $transaction->rollBack();
                return ApiResponse::response(
                    $e->getMessage(),
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $transaction->commit();
            return ApiResponse::response('Переупаковка успешно обновлена');
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }
























    public function actionGetClients()
    {
        $clientGenerator = function () {
            $query = (new \yii\db\Query())
                ->select([
                    'c.id',
                    'c.full_name',
                    'c.phone_number',
                    'r.name as region_name'
                ])
                ->from(['c' => 'client'])
                ->leftJoin(['r' => 'region'], 'r.id = c.region_id')
                ->where(['c.deleted_at' => null])
                ->orderBy(['c.full_name' => SORT_ASC]);

            foreach ($query->each() as $client) {
                yield $client;
            }
        };

        $clients = $clientGenerator();
        $firstClient = $clients->current();
        if (!$firstClient) {
            return ApiResponse::response("Клиенты не найдены", [], 404);
        }

        $productGenerator = function () {
            $query = (new \yii\db\Query())
                ->select(['p.id', 'p.name'])
                ->from(['p' => 'product'])
                ->where(['p.deleted_at' => null])
                ->orderBy(['p.id' => SORT_ASC]);

            foreach ($query->each() as $product) {
                yield $product;
            }
        };

        $clientList = iterator_to_array($clientGenerator());
        $productList = iterator_to_array($productGenerator());

        return ApiResponse::response("Список клиентов", [
            'clients' => $clientList,
            'products' => $productList
        ]);
    }

    /**
     * Получение списка водителей клиента
     *
     * @OA\Get(
     *     path="/api/sales/get-drivers",
     *     summary="Получение списка водителей клиента",
     *     description="Возвращает список водителей для указанного клиента",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="client_id",
     *         in="query",
     *         description="ID клиента",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Список водителей клиента",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Список водителей клиента"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="drivers",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="Иванов Иван"),
     *                         @OA\Property(property="car_number", type="string", example="A123BC")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="ID клиента обязателен"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     )
     * )
     */
    public function actionGetDrivers()
    {
        $client_id = Yii::$app->request->get('client_id');

        if (empty($client_id)) {
            return ApiResponse::response("ID клиента обязателен", null, 400);
        }

        $service = new ClientDriverService();
        $drivers = $service->getDriversByClientId($client_id);

        return ApiResponse::response("Список водителей клиента", [
            'drivers' => array_values($drivers)
        ]);
    }

    /**
     * Получение деталей водителя
     *
     * @OA\Get(
     *     path="/api/sales/get-driver-details",
     *     summary="Получение деталей водителя",
     *     description="Возвращает информацию о водителе по его ID",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="driver_id",
     *         in="query",
     *         description="ID водителя",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Информация о водителе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Информация о водителе"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="car_number", type="string", example="A123BC")
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     )
     * )
     */
    public function actionGetDriverDetails()
    {
        $driver_id = Yii::$app->request->get('driver_id');

        $service = new InvoiceService();
        $details = $service->getDriverDetails($driver_id);

        return ApiResponse::response("Информация о водителе", $details);
    }

    /**
     * Создание водителя
     *
     * @OA\Post(
     *     path="/api/sales/create-driver",
     *     summary="Создание нового водителя",
     *     description="Создает нового водителя для клиента",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"client_id", "driver_name"},
     *             @OA\Property(property="client_id", type="integer", example=1),
     *             @OA\Property(property="driver_name", type="string", example="Иванов Иван"),
     *             @OA\Property(property="car_number", type="string", example="A123BC")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Водитель успешно создан",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Водитель успешно добавлен"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="success", type="boolean", example=true)
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Не указаны обязательные параметры"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     )
     * )
     */
    public function actionCreateDriver()
    {
        $client_id = Yii::$app->request->post('client_id');
        $driver_name = Yii::$app->request->post('driver_name');
        $car_number = Yii::$app->request->post('car_number');

        $service = new ClientDriverService();
        $result = $service->createDriver($client_id, $driver_name, $car_number);

        if ($result['success']) {
            return ApiResponse::response($result['message'], [
                'id' => $result['id'],
                'name' => $driver_name,
                'car_number' => $car_number
            ]);
        } else {
            return ApiResponse::response($result['message'], null, 400);
        }
    }

    /**
     * Обновление водителя
     *
     * @OA\Put(
     *     path="/api/sales/update-driver",
     *     summary="Обновление данных водителя",
     *     description="Обновляет данные существующего водителя",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"id", "driver_name"},
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="driver_name", type="string", example="Иванов Иван"),
     *             @OA\Property(property="car_number", type="string", example="A123BC")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Водитель успешно обновлен",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Водитель успешно обновлен"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="success", type="boolean", example=true)
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Не указаны обязательные параметры"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Водитель не найден",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Водитель не найден"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=404)
     *         )
     *     )
     * )
     */
    public function actionUpdateDriver()
    {
        $id = Yii::$app->request->post('id');
        $driver_name = Yii::$app->request->post('driver_name');
        $car_number = Yii::$app->request->post('car_number');

        $service = new ClientDriverService();
        $result = $service->updateDriver($id, $driver_name, $car_number);

        if ($result['success']) {
            return ApiResponse::response($result['message'], [
                'id' => $result['id'],
                'name' => $driver_name,
                'car_number' => $car_number
            ]);
        } else {
            $code = strpos($result['message'], 'не найден') !== false ? 404 : 400;
            return ApiResponse::response($result['message'], null, $code);
        }
    }

    /**
     * Удаление водителя
     *
     * @OA\Delete(
     *     path="/api/sales/delete-driver",
     *     summary="Удаление водителя",
     *     description="Удаляет водителя (мягкое удаление)",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"id"},
     *             @OA\Property(property="id", type="integer", example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Водитель успешно удален",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Водитель успешно удален"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="success", type="boolean", example=true)
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Неверный ID водителя"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Водитель не найден",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Водитель не найден"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=404)
     *         )
     *     )
     * )
     */
    public function actionDeleteDriver()
    {
        $id = Yii::$app->request->post('id');

        $service = new ClientDriverService();
        $result = $service->deleteDriver($id);

        if ($result['success']) {
            return ApiResponse::response($result['message'], [
                'success' => true
            ]);
        } else {
            $code = strpos($result['message'], 'не найден') !== false ? 404 : 400;
            return ApiResponse::response($result['message'], null, $code);
        }
    }

    /**
     * Создание инвойса
     *
     * @OA\Post(
     *     path="/api/sales/create-invoice",
     *     summary="Создание нового инвойса",
     *     description="Создает новый инвойс (продажу)",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"client_id", "products"},
     *             @OA\Property(property="client_id", type="integer", example=1),
     *             @OA\Property(property="driver_id", type="integer", example=1),
     *             @OA\Property(property="car_number", type="string", example="A123BC"),
     *             @OA\Property(property="total_price", type="number", format="float", example=1000, description="Необязательное поле, будет рассчитано автоматически на сервере"),
     *             @OA\Property(
     *                 property="products",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="product_id", type="integer", example=1),
     *                     @OA\Property(property="quantity", type="integer", example=10, description="Количество в штуках (можно указать либо quantity, либо quantity_block)"),
     *                     @OA\Property(property="quantity_block", type="number", format="float", example=0.83, description="Количество в блоках (будет автоматически конвертировано в штуки с учетом размера блока)"),
     *                     @OA\Property(property="special_price", type="number", format="float", example=100, description="Необязательное поле, будет установлено автоматически на сервере"),
     *                     @OA\Property(property="sell_price", type="number", format="float", example=120, description="Необязательное поле, будет установлено автоматически на сервере")
     *                 )
     *             ),
     *             @OA\Property(property="has_bonus", type="boolean", example=false, description="Если true, то требуется указать бонусные продукты в массиве bonus. Если false, то бонусные продукты будут проигнорированы, даже если они указаны."),
     *             @OA\Property(
     *                 property="bonus",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="product_id", type="integer", example=2),
     *                     @OA\Property(property="quantity", type="integer", example=5, description="Количество в штуках (можно указать либо quantity, либо quantity_block)"),
     *                     @OA\Property(property="quantity_block", type="number", format="float", example=0.42, description="Количество в блоках (будет автоматически конвертировано в штуки с учетом размера блока)")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Инвойс успешно создан",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Запись успешно создана"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="status", type="string", example="success"),
     *                 @OA\Property(
     *                     property="data",
     *                     type="object",
     *                     @OA\Property(property="sales_id", type="integer", example=1)
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Ошибка валидации"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="status",
     *                     type="string",
     *                     example="error"
     *                 ),
     *                 @OA\Property(
     *                     property="message",
     *                     type="object",
     *                     @OA\Property(
     *                         property="driver",
     *                         type="array",
     *                         @OA\Items(type="string", example="Водитель обязателен")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     )
     * )
     */
    public function actionCreateInvoice()
    {
        $model = new InvoiceCreateForm();
        $model->load(Yii::$app->request->post(), '');

        if (!$model->validate()) {
            return ApiResponse::response("Ошибка валидации", [
                'status' => 'error',
                'message' => $model->getErrors()
            ], 200);
        }

        $service = new InvoiceService();
        $result = $service->createInvoice([
            'client_id' => $model->client_id,
            'driver_id' => $model->driver_id,
            'car_number' => $model->car_number,
            'products' => $model->products,
            'has_bonus' => $model->has_bonus,
            'bonus' => $model->bonus
        ]);

        if ($result['status'] === 'success') {
            return ApiResponse::response($result['message'], [
                'status' => 'success',
                'data' => $result['data']
            ]);
        } else {
            return ApiResponse::response("Ошибка при создании инвойса", [
                'status' => 'error',
                'message' => $result['message']
            ], 200);
        }
    }

    /**
     * Получение списка всех продуктов с информацией о количестве в блоках
     *
     * @OA\Get(
     *     path="/api/sales/get-products",
     *     summary="Получение списка всех продуктов",
     *     description="Возвращает список всех продуктов с информацией о количестве в блоках",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Список продуктов",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Список продуктов"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="products",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="Продукт 1"),
     *                         @OA\Property(property="size", type="integer", example=12),
     *                         @OA\Property(property="price", type="number", format="float", example=100.50),
     *                         @OA\Property(property="quantity", type="integer", example=120),
     *                         @OA\Property(property="quantity_block", type="number", format="float", example=10),
     *                         @OA\Property(property="type", type="integer", example=0)
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     )
     * )
     */
    public function actionGetProducts()
    {
        // Создаем SQL-запрос для получения продуктов с информацией о количестве на складе и ценах
        $sql = "
            SELECT
                p.id,
                p.name,
                p.size,
                p.type,
                COALESCE(pp.price, 0) as price,
                COALESCE(SUM(ps.quantity), 0) as quantity
            FROM
                product p
            LEFT JOIN
                product_price pp ON p.id = pp.product_id AND pp.deleted_at IS NULL
                AND pp.end_date = '9999-12-31'
            LEFT JOIN
                product_storage ps ON p.id = ps.product_id AND ps.deleted_at IS NULL
                AND ps.accepted_at IS NOT NULL
            WHERE
                p.deleted_at IS NULL
            GROUP BY
                p.id, p.name, p.size, p.type, pp.price
            ORDER BY
                p.priority ASC, p.name ASC
        ";

        $products = Yii::$app->db->createCommand($sql)->queryAll();

        // Добавляем количество в блоках для каждого продукта
        foreach ($products as &$product) {
            // Если size равен 0, устанавливаем его в 1, чтобы избежать деления на ноль
            $size = $product['size'] > 0 ? $product['size'] : 1;

            // Вычисляем количество в блоках
            $product['quantity_block'] = round($product['quantity'] / $size, 2);
        }

        return ApiResponse::response("Список продуктов", [
            'products' => $products
        ]);
    }

    /**
     * Получение списка инвойсов
     *
     * @OA\Get(
     *     path="/api/sales/get-invoices",
     *     summary="Получение списка инвойсов",
     *     description="Возвращает список инвойсов с возможностью фильтрации по дате и клиенту",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="client_id",
     *         in="query",
     *         description="ID клиента (необязательно)",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="date_from",
     *         in="query",
     *         description="Дата начала периода в формате YYYY-MM-DD (необязательно)",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2023-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="date_to",
     *         in="query",
     *         description="Дата окончания периода в формате YYYY-MM-DD (необязательно)",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2023-12-31")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Статус инвойса (необязательно): 1 - новый, 2 - в процессе, 3 - подтвержден",
     *         required=false,
     *         @OA\Schema(type="integer", enum={1, 2, 3}, example=3)
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Номер страницы (по умолчанию 1)",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Количество записей на странице (по умолчанию 20, максимум 100)",
     *         required=false,
     *         @OA\Schema(type="integer", example=20)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Список инвойсов",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Список инвойсов"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="invoices",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01 12:00:00"),
     *                         @OA\Property(property="client_name", type="string", example="ООО Компания"),
     *                         @OA\Property(property="driver", type="string", example="Иванов И.И."),
     *                         @OA\Property(property="car_number", type="string", example="A123BC"),
     *                         @OA\Property(property="total_sum", type="string", example="10 000"),
     *                         @OA\Property(property="status", type="integer", example=3),
     *                         @OA\Property(property="status_name", type="string", example="Подтвержден")
     *                     )
     *                 ),
     *                 @OA\Property(property="total_count", type="integer", example=100),
     *                 @OA\Property(property="page", type="integer", example=1),
     *                 @OA\Property(property="per_page", type="integer", example=20),
     *                 @OA\Property(property="page_count", type="integer", example=5)
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     )
     * )
     */
    public function actionGetInvoices()
    {
        // Получаем параметры запроса
        $params = [
            'client_id' => Yii::$app->request->get('client_id'),
            'date_from' => Yii::$app->request->get('date_from'),
            'date_to' => Yii::$app->request->get('date_to'),
            'status' => Yii::$app->request->get('status'),
            'page' => (int)Yii::$app->request->get('page', 1),
            'per_page' => (int)Yii::$app->request->get('per_page', 20)
        ];

        // Используем сервис для получения списка инвойсов
        $service = new InvoiceService();
        $result = $service->getInvoices($params);

        return ApiResponse::response("Список инвойсов", $result);
    }

    /**
     * Получение деталей инвойса
     *
     * @OA\Get(
     *     path="/api/sales/get-invoice-details",
     *     summary="Получение деталей инвойса",
     *     description="Возвращает детальную информацию об инвойсе по его ID",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="invoice_id",
     *         in="query",
     *         description="ID инвойса",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Детали инвойса",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Детали инвойса"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="invoice",
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2023-01-01 12:00:00"),
     *                     @OA\Property(property="client_name", type="string", example="ООО Компания"),
     *                     @OA\Property(property="driver", type="string", example="Иванов И.И."),
     *                     @OA\Property(property="car_number", type="string", example="A123BC"),
     *                     @OA\Property(property="total_sum", type="string", example="10 000"),
     *                     @OA\Property(property="status", type="integer", example=3),
     *                     @OA\Property(property="status_name", type="string", example="Подтвержден")
     *                 ),
     *                 @OA\Property(
     *                     property="products",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="product_id", type="integer", example=1),
     *                         @OA\Property(property="product_name", type="string", example="Продукт 1"),
     *                         @OA\Property(property="quantity", type="integer", example=10),
     *                         @OA\Property(property="quantity_block", type="number", format="float", example=0.83),
     *                         @OA\Property(property="special_price", type="number", format="float", example=100),
     *                         @OA\Property(property="sell_price", type="number", format="float", example=120),
     *                         @OA\Property(property="total_price", type="string", example="1 200")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="bonus_products",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="product_id", type="integer", example=2),
     *                         @OA\Property(property="product_name", type="string", example="Бонусный продукт"),
     *                         @OA\Property(property="quantity", type="integer", example=5),
     *                         @OA\Property(property="quantity_block", type="number", format="float", example=0.42)
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="ID инвойса обязателен"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Инвойс не найден",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Инвойс не найден"),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="code", type="integer", example=404)
     *         )
     *     )
     * )
     */
    public function actionGetInvoiceDetails()
    {
        // Получаем ID инвойса из запроса
        $invoice_id = Yii::$app->request->get('invoice_id');

        // Проверяем, что ID инвойса указан
        if (empty($invoice_id)) {
            return ApiResponse::response("ID инвойса обязателен", null, 400);
        }

        // Используем сервис для получения деталей инвойса
        $service = new InvoiceService();
        $result = $service->getInvoiceDetails($invoice_id);

        // Проверяем, что инвойс найден
        if (!$result) {
            return ApiResponse::response("Инвойс не найден", null, 404);
        }

        return ApiResponse::response("Детали инвойса", $result);
    }




    /**
     * Обновление инвойса
     *
     * @OA\Post(
     *     path="/api/sales/update-invoice",
     *     summary="Обновление инвойса",
     *     description="Обновляет существующий инвойс",
     *     tags={"Sales"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"invoice_id", "client_id", "products"},
     *             @OA\Property(property="invoice_id", type="integer", example=1, description="ID инвойса для обновления"),
     *             @OA\Property(property="client_id", type="integer", example=1),
     *             @OA\Property(property="driver_id", type="integer", example=1),
     *             @OA\Property(property="car_number", type="string", example="A123BC"),
     *             @OA\Property(
     *                 property="products",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="product_id", type="integer", example=1),
     *                     @OA\Property(property="quantity", type="integer", example=10, description="Количество в штуках (можно указать либо quantity, либо quantity_block)"),
     *                     @OA\Property(property="quantity_block", type="number", format="float", example=0.83, description="Количество в блоках (будет автоматически конвертировано в штуки с учетом размера блока)"),
     *                     @OA\Property(property="special_price", type="number", format="float", example=100, description="Необязательное поле, будет установлено автоматически на сервере"),
     *                     @OA\Property(property="sell_price", type="number", format="float", example=120, description="Необязательное поле, будет установлено автоматически на сервере")
     *                 )
     *             ),
     *             @OA\Property(property="has_bonus", type="boolean", example=false, description="Если true, то требуется указать бонусные продукты в массиве bonus. Если false, то бонусные продукты будут проигнорированы, даже если они указаны."),
     *             @OA\Property(
     *                 property="bonus",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="product_id", type="integer", example=2),
     *                     @OA\Property(property="quantity", type="integer", example=5, description="Количество в штуках (можно указать либо quantity, либо quantity_block)"),
     *                     @OA\Property(property="quantity_block", type="number", format="float", example=0.42, description="Количество в блоках (будет автоматически конвертировано в штуки с учетом размера блока)")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Результат обновления инвойса",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Инвойс успешно обновлен"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="status",
     *                     type="string",
     *                     example="success"
     *                 ),
     *                 @OA\Property(
     *                     property="message",
     *                     type="string",
     *                     example="Запись успешно обновлена"
     *                 ),
     *                 @OA\Property(
     *                     property="data",
     *                     type="object",
     *                     @OA\Property(property="sales_id", type="integer", example=1)
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=200)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка валидации",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Ошибка валидации"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="status",
     *                     type="string",
     *                     example="error"
     *                 ),
     *                 @OA\Property(
     *                     property="message",
     *                     type="object",
     *                     @OA\Property(
     *                         property="invoice_id",
     *                         type="array",
     *                         @OA\Items(type="string", example="ID инвойса обязателен")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(property="code", type="integer", example=400)
     *         )
     *     )
     * )
     */
    public function actionUpdateInvoice()
    {
        $model = new InvoiceUpdateForm();
        $model->load(Yii::$app->request->post(), '');

        if (!$model->validate()) {
            return ApiResponse::response("Ошибка валидации", [
                'status' => 'error',
                'message' => $model->getErrors()
            ], 400);
        }

        $service = new InvoiceService();
        $result = $service->updateInvoice([
            'invoice_id' => $model->invoice_id,
            'client_id' => $model->client_id,
            'driver_id' => $model->driver_id,
            'car_number' => $model->car_number,
            'products' => $model->products,
            'has_bonus' => $model->has_bonus,
            'bonus' => $model->bonus
        ]);

        if ($result['status'] === 'error') {
            return ApiResponse::response("Ошибка обновления инвойса", $result, 400);
        }

        return ApiResponse::response("Инвойс успешно обновлен", $result);
    }
}
