<?php

namespace app\modules\backend\controllers;

use Yii;
use app\common\models\ProductIngredients;
use app\common\models\Product;
use app\common\models\Material;
use app\common\models\MaterialCategory;
use yii\web\Response;
use yii\helpers\ArrayHelper;

class ProductIngredientsController extends BaseController
{
    public function actionIndex()
    {
        $query = ProductIngredients::find()
            ->select([
                'product_ingredients.*',
                'product.name as product_name',
                'material.name as material_name'
            ])
            ->leftJoin('product', 'product.id = product_ingredients.product_id')
            ->leftJoin('material', 'material.id = product_ingredients.material_id')
            ->where(['product_ingredients.end_date' => '9999-12-31'])
            ->asArray()
            ->all();

        $groupedData = [];
        foreach ($query as $row) {
            if (!isset($groupedData[$row['product_id']])) {
                $groupedData[$row['product_id']] = [
                    'id' => $row['id'],
                    'product_id' => $row['product_id'],
                    'product_name' => $row['product_name'],
                    'start_date' => $row['start_date'],
                    'end_date' => $row['end_date'],
                    'materials' => [],
                    'alternative_materials' => []
                ];
            }

            if ($row['is_alternative']) {
                $groupedData[$row['product_id']]['alternative_materials'][] = [
                    'id' => $row['material_id'],
                    'name' => $row['material_name']
                ];
            } else {
                $groupedData[$row['product_id']]['materials'][] = [
                    'id' => $row['material_id'],
                    'name' => $row['material_name']
                ];
            }
        }

        return $this->render('index', [
            'result' => array_values($groupedData),
        ]);
    }

    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new ProductIngredients();

            if ($model->load(Yii::$app->request->post())) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $material_ids = $model->material_ids;
                    $success = true;
                    $errors = [];

                    if (empty($material_ids) && empty($model->product_id)) {
                        $model->addError('material_ids', Yii::t('app', 'Materials and product are required'));
                        $success = false;
                    }

                    if ($success) {
                        // Получаем данные об альтернативных ингредиентах
                        $alternative_material_ids = Yii::$app->request->post('alternative_material_ids', []);

                        // Сохраняем основные ингредиенты
                        foreach ($material_ids as $material_id) {
                            $ingredient = new ProductIngredients();
                            $ingredient->product_id = $model->product_id;
                            $ingredient->material_id = $material_id;
                            $ingredient->is_alternative = false;
                            $ingredient->start_date = date('Y-m-d H:i:s');
                            $ingredient->end_date = '9999-12-31';

                            if (!$ingredient->save()) {
                                $success = false;
                                $errors = array_merge($errors, $ingredient->getErrors());
                                break;
                            }
                        }

                        // Сохраняем альтернативные ингредиенты, если есть
                        if ($success && !empty($alternative_material_ids)) {
                            foreach ($alternative_material_ids as $material_id) {
                                $ingredient = new ProductIngredients();
                                $ingredient->product_id = $model->product_id;
                                $ingredient->material_id = $material_id;
                                $ingredient->is_alternative = true;
                                $ingredient->start_date = date('Y-m-d H:i:s');
                                $ingredient->end_date = '9999-12-31';

                                if (!$ingredient->save()) {
                                    $success = false;
                                    $errors = array_merge($errors, $ingredient->getErrors());
                                    break;
                                }
                            }
                        }
                    }

                    if ($success) {
                        $transaction->commit();
                        return [
                            'status' => 'success',
                            'message' => Yii::t('app', 'Ingredients added successfully'),
                        ];
                    } else {
                        $transaction->rollBack();
                        return [
                            'status' => 'error',
                            'errors' => $errors ?: $model->getErrors(),
                        ];
                    }
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage(),
                    ];
                }
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }
        }

        // Получаем список материалов с категориями
        $materials = Material::find()
            ->select(['id', 'name', 'category_id'])
            ->where(['deleted_at' => NULL])
            ->asArray()
            ->all();

        $materialsList = ArrayHelper::map($materials, 'id', 'name');

        return [
            'status' => 'success',
            'content' => $this->renderPartial('create', [
                'model' => new ProductIngredients(),
                'materialsList' => $materialsList,
                'materials' => $materials,
            ])
        ];
    }

    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $post = Yii::$app->request->post();

            if (empty($post['ProductIngredients']['id'])) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Invalid request')
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $currentModel = ProductIngredients::findOne($post['ProductIngredients']['id']);
                if (!$currentModel) {
                    throw new \Exception(Yii::t('app', 'Product ingredient not found.'));
                }

                $old_product_id = $currentModel->product_id;
                $product_id = $post['ProductIngredients']['product_id'];
                $material_ids = $post['ProductIngredients']['material_ids'];
                $alternative_material_ids = $post['alternative_material_ids'] ?? [];

                if (empty($product_id) || empty($material_ids)) {
                    throw new \Exception(Yii::t('app', 'Materials and product are required'));
                }

                if ($old_product_id != $product_id) {
                    // Если изменился продукт, деактивируем все ингредиенты
                    ProductIngredients::updateAll(
                        ['end_date' => date('Y-m-d H:i:s')],
                        [
                            'product_id' => $old_product_id,
                            'end_date' => '9999-12-31'
                        ]
                    );
                }

                // Получаем текущие основные ингредиенты
                $currentMaterials = ProductIngredients::find()
                    ->select('material_id')
                    ->where([
                        'product_id' => $product_id,
                        'end_date' => '9999-12-31',
                        'is_alternative' => false
                    ])
                    ->column();

                // Получаем текущие альтернативные ингредиенты
                $currentAlternativeMaterials = ProductIngredients::find()
                    ->select('material_id')
                    ->where([
                        'product_id' => $product_id,
                        'end_date' => '9999-12-31',
                        'is_alternative' => true
                    ])
                    ->column();

                // Деактивируем удаленные основные ингредиенты
                $materialsToDeactivate = array_diff($currentMaterials, $material_ids);
                if (!empty($materialsToDeactivate)) {
                    ProductIngredients::updateAll(
                        ['end_date' => date('Y-m-d H:i:s')],
                        [
                            'product_id' => $product_id,
                            'material_id' => $materialsToDeactivate,
                            'is_alternative' => false,
                            'end_date' => '9999-12-31'
                        ]
                    );
                }

                // Деактивируем удаленные альтернативные ингредиенты
                $alternativeMaterialsToDeactivate = array_diff($currentAlternativeMaterials, $alternative_material_ids);
                if (!empty($alternativeMaterialsToDeactivate)) {
                    ProductIngredients::updateAll(
                        ['end_date' => date('Y-m-d H:i:s')],
                        [
                            'product_id' => $product_id,
                            'material_id' => $alternativeMaterialsToDeactivate,
                            'is_alternative' => true,
                            'end_date' => '9999-12-31'
                        ]
                    );
                }

                // Добавляем новые основные ингредиенты
                $materialsToAdd = array_diff($material_ids, $currentMaterials);
                foreach ($materialsToAdd as $material_id) {
                    $model = new ProductIngredients();
                    $model->product_id = $product_id;
                    $model->material_id = $material_id;
                    $model->is_alternative = false;
                    $model->start_date = date('Y-m-d H:i:s');
                    $model->end_date = '9999-12-31';

                    if (!$model->save()) {
                        throw new \Exception(Yii::t('app', 'Error saving ingredient'));
                    }
                }

                // Добавляем новые альтернативные ингредиенты
                $alternativeMaterialsToAdd = array_diff($alternative_material_ids, $currentAlternativeMaterials);
                foreach ($alternativeMaterialsToAdd as $material_id) {
                    $model = new ProductIngredients();
                    $model->product_id = $product_id;
                    $model->material_id = $material_id;
                    $model->is_alternative = true;
                    $model->start_date = date('Y-m-d H:i:s');
                    $model->end_date = '9999-12-31';

                    if (!$model->save()) {
                        throw new \Exception(Yii::t('app', 'Error saving alternative ingredient'));
                    }
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Product ingredients updated successfully'),
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        }

        $id = Yii::$app->request->get('id');
        $model = ProductIngredients::findOne($id);

        if (!$model) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Product ingredient not found.')
            ];
        }

        // Получаем список материалов с категориями
        $materials = Material::find()
            ->select(['id', 'name', 'category_id'])
            ->where(['deleted_at' => NULL])
            ->asArray()
            ->all();

        $materialsList = ArrayHelper::map($materials, 'id', 'name');

        return [
            'status' => 'success',
            'content' => $this->renderPartial('update', [
                'model' => $model,
                'materialsList' => $materialsList,
                'materials' => $materials,
            ])
        ];
    }

    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $post = Yii::$app->request->post();

            if (empty($post['ProductIngredients']['id'])) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Invalid request')
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $model = ProductIngredients::findOne($post['ProductIngredients']['id']);
                if (!$model) {
                    throw new \Exception(Yii::t('app', 'Product ingredient not found.'));
                }

                ProductIngredients::updateAll(
                    ['end_date' => date('Y-m-d H:i:s')],
                    [
                        'product_id' => $model->product_id,
                        'end_date' => '9999-12-31'
                    ]
                );

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Product ingredients deleted successfully'),
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                ];
            }
        }

        $id = Yii::$app->request->get('id');
        $model = ProductIngredients::findOne($id);

        if (!$model) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Product ingredient not found.')
            ];
        }

        return [
            'status' => 'success',
            'content' => $this->renderPartial('delete', [
                'model' => $model
            ])
        ];
    }
}