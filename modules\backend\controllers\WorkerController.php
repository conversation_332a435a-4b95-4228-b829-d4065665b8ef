<?php

namespace app\modules\backend\controllers;

use Yii;
use app\modules\backend\models\Worker;
use app\modules\backend\models\WorkerFinances;
use app\modules\backend\models\WorkerSalary;
use yii\web\Response;

/**
 * WorkerController implements the CRUD actions for Worker model.
 */
class WorkerController extends BaseController
{


    public function actionIndex()
    {
        $currentMonth = date('Y-m');
        
        $sql = "SELECT 
                    w.id,
                    w.full_name,
                    w.phone_number,
                    w.phone_number_2,       
                    w.address,
                    ws.amount as salary,
                    w.position_id,
                    p.name as position_name,
                    w.created_at,
                    w.deleted_at,
                    COALESCE(wf.paid_amount, 0) as paid_amount,
                   COALESCE(
                        CASE 
                            WHEN ws.amount - COALESCE(wf.paid_amount, 0) < 0 THEN NULL
                            ELSE ws.amount - COALESCE(wf.paid_amount, 0)
                        END, 0) as debt
                FROM worker w
                LEFT JOIN position p ON p.id = w.position_id
                LEFT JOIN worker_salary ws ON w.id = ws.worker_id 
                    AND ws.deleted_at IS NULL 
                    AND ws.end_date = '9999-12-31'
                LEFT JOIN (
                    SELECT worker_id, SUM(amount) as paid_amount 
                    FROM worker_finances 
                    WHERE month = :month 
                    AND deleted_at IS NULL
                    GROUP BY worker_id
                ) wf ON w.id = wf.worker_id
                WHERE w.deleted_at IS NULL
                ORDER BY w.created_at DESC";
                
        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':month', $currentMonth);
        $result = $command->queryAll();

        return $this->render('index', [
            'result' => $result
        ]);
    }



    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new Worker();
            $workerSalary = new WorkerSalary();
            
            $model->load(Yii::$app->request->post());
            $workerSalary->load(Yii::$app->request->post());
            
            if ($model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    if (!empty($model->phone_number)) {
                        $model->phone_number = str_replace(' ', '', $model->phone_number);
                        if (!str_starts_with($model->phone_number, '+998')) {
                            $model->phone_number = '+998' . $model->phone_number;
                        }
                    }
                    
                    if (!empty($model->phone_number_2)) {
                        $model->phone_number_2 = str_replace(' ', '', $model->phone_number_2);
                        if (!str_starts_with($model->phone_number_2, '+998')) {
                            $model->phone_number_2 = '+998' . $model->phone_number_2;
                        }
                    }

                    $model->save(false);
                    $workerSalary->end_date = date('9999-12-31');
                    $workerSalary->created_at = date('Y-m-d');
                    $workerSalary->worker_id = $model->id;
                    $workerSalary->save();
                    
                    $transaction->commit();
                    
                    return [
                        'status' => 'success',
                        'message' => 'Worker created successfully.',
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'fail',
                        'message' => $e->getMessage(),
                    ];
                }
            } else {
                return [
                    'status' => 'fail',
                    'errors' => array_merge($model->getErrors(), $workerSalary->getErrors()),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $model = new Worker();
            $workerSalary = new WorkerSalary();
            $workerSalary->start_date = date('Y-m-d'); 
            
            return [
                "status" => true,
                "content" => $this->renderPartial('create', [
                    'model' => $model,
                    'workerSalary' => $workerSalary
                ])
            ];
        }
    }



    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $model = Worker::find()->where(['id' => $postData['Worker']['id']])->one();
            $oldData = $model->attributes;
            if (!$model) {
                return ['status' => 'error', 'message' => 'Worker not found.'];
            }

            $model->load($postData);

            if(!$model->validate())
            {
                return [
                    'status' => 'fail',
                    'errors' => $model->getErrors(),
                ];
            }

            if (!empty($model->phone_number)) {
                $model->phone_number = str_replace(' ', '', $model->phone_number);
                if (!str_starts_with($model->phone_number, '+998')) {
                    $model->phone_number = '+998' . $model->phone_number;
                }
            }
            
            if (!empty($model->phone_number_2)) {
                $model->phone_number_2 = str_replace(' ', '', $model->phone_number_2);
                if (!str_starts_with($model->phone_number_2, '+998')) {
                    $model->phone_number_2 = '+998' . $model->phone_number_2;
                }
            }

            

            if ($model->save(false)) {
                return [
                    'status' => 'success',
                    'message' => 'Worker updated successfully.',
                ];
            } else {
                return [
                    'status' => 'fail',
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Worker::find()->where(['id' => $id])->one();
            
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => 'Worker not found.',
                ];
            } else {
                $model->phone_number = substr($model->phone_number, 4);
                if (!empty($model->phone_number_2)) {
                    $model->phone_number_2 = substr($model->phone_number_2, 4);
                }
                return [
                    "status" => true,
                    "content" => $this->renderPartial('update', ['model' => $model]),
                ];
            }
        }
    }



    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();
            $model = Worker::findOne($postData['Worker']['id']);
            if (!$model) {
                return ['status' => 'error', 'message' => 'Worker not found.'];
            }

            $model->deleted_at = date('Y-m-d H:i:s');
            if ($model->save(false)) {
                return [
                    'status' => 'success',
                    'message' => 'Worker deleted successfully.',
                ];
            } else {
                return [
                    'status' => 'fail',
                    'errors' => $model->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $worker_id = Yii::$app->request->get('id');
            $model = Worker::findOne($worker_id);
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => 'Worker not found.',
                ];
            }

            return [
                "status" => 'success',
                "content" => $this->renderPartial('delete', ['model' => $model]),
            ];
        }

        return ['status' => 'error', 'message' => 'Invalid request method.'];
    }


    public function actionSalaryChange()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $workerId = Yii::$app->request->post('worker_id');
            $model = Worker::findOne($workerId);
            
            if (!$model) {
                return [
                    'status' => 'fail',
                    'message' => Yii::t('app', 'worker_not_found'),
                ];
            }

            $currentSalary = WorkerSalary::find()
                ->where(['worker_id' => $workerId])
                ->andWhere(['end_date' => '9999-12-31'])
                ->one();

            $workerSalary = new WorkerSalary();
            $workerSalary->load(Yii::$app->request->post());
            $workerSalary->worker_id = $workerId;
            
            if ($currentSalary && strtotime($workerSalary->start_date) <= strtotime($currentSalary->start_date)) {
                return [
                    'status' => 'fail',
                    'errors' => [
                        'start_date' => [Yii::t('app', 'new_salary_date_must_be_greater')]
                    ]
                ];
            }
            
            if ($workerSalary->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    if ($currentSalary) {
                        $currentSalary->end_date = date('Y-m-d', strtotime('-1 day', strtotime($workerSalary->start_date)));
                        $currentSalary->deleted_at = date('Y-m-d H:i:s');
                        $currentSalary->save(false);
                    }

                    $workerSalary->end_date = '9999-12-31';
                    $workerSalary->created_at = date('Y-m-d H:i:s');
                    $workerSalary->save(false);
                    
                    $transaction->commit();
                    
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'salary_changed_successfully'),
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'fail',
                        'message' => $e->getMessage(),
                    ];
                }
            } else {
                return [
                    'status' => 'fail',
                    'errors' => $workerSalary->getErrors(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Worker::findOne($id);
            
            if (!$model) {
                return [
                    'status' => 'fail',
                    'message' => Yii::t('app', 'worker_not_found'),
                ];
            }

            $currentSalary = WorkerSalary::find()
                ->where(['worker_id' => $id])
                ->andWhere(['end_date' => '9999-12-31'])
                ->one();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('change-salary', [
                    'model' => $model,
                    'currentSalary' => $currentSalary
                ])
            ];
        }
    }


    public function actionView()
    {
        $id = Yii::$app->request->get('id');
        $model = Worker::findOne($id);
        if (!$model) {
            return [
                'status' => 'fail',
                'message' => Yii::t('app', 'worker_not_found'),
            ];
        }

        $currentSalary = WorkerSalary::find()
            ->where(['worker_id' => $id])
            ->andWhere(['end_date' => '9999-12-31'])
            ->andWhere(['deleted_at' => null])
            ->one();

        $salaryHistory = WorkerSalary::find()
            ->where(['worker_id' => $id])
            ->orderBy(['start_date' => SORT_DESC])
            ->all();

             // Получаем историю выплат
             $salaryPayments = WorkerFinances::find()
             ->where(['worker_id' => $id])
             ->andWhere(['deleted_at' => null])
             ->andWhere(['or',
                 ['type' => WorkerFinances::TYPE_SALARY],
                 ['type' => WorkerFinances::TYPE_ADVANCE]
             ])
             ->orderBy(['created_at' => SORT_DESC])
             ->all();

             
        return $this->render('view', [
            'model' => $model,
            'currentSalary' => $currentSalary,
            'salaryHistory' => $salaryHistory,
            'salaryPayments' => $salaryPayments
        ]);
    }


}
