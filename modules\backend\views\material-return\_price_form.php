<?php

use yii\helpers\Html;

?>

<div class="alert alert-info">
    <i class="fas fa-info-circle"></i>
    Укажите цену за единицу для каждого материала. Общая сумма будет списана с баланса поставщика.
</div>

<form id="material-price-form">
    <div id="materials-price-list">
        <?php foreach ($materials as $material): ?>
            <div class="row mb-3 material-price-row">
                <div class="col-md-4">
                    <label class="form-label">
                        <strong><?= Html::encode($material['material_name']) ?></strong><br>
                        <small class="text-muted">
                            Количество: <?= number_format($material['quantity'], 2) ?> <?= Html::encode($material['unit_type_name']) ?>
                        </small>
                    </label>
                </div>
                <div class="col-md-4">
                    <input type="number" 
                           class="form-control price-input" 
                           data-material-id="<?= $material['material_id'] ?>"
                           data-quantity="<?= $material['quantity'] ?>"
                           placeholder="Цена за <?= Html::encode($material['unit_type_name']) ?>"
                           value="<?= $material['suggested_price'] > 0 ? number_format($material['suggested_price'], 2, '.', '') : '' ?>"
                           step="0.01" 
                           min="0" 
                           required>
                    <small class="text-muted">
                        <?php if ($material['suggested_price'] > 0): ?>
                            Последняя цена: <?= number_format($material['suggested_price'], 2) ?> сум
                        <?php endif; ?>
                    </small>
                </div>
                <div class="col-md-4">
                    <div class="form-control-plaintext">
                        <strong class="material-total">
                            <?php 
                            $total = $material['suggested_price'] * $material['quantity'];
                            echo number_format($total, 2) . ' сум';
                            ?>
                        </strong>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <div class="row mt-3 pt-3 border-top">
        <div class="col-md-6">
            <strong>Общая сумма возврата:</strong>
        </div>
        <div class="col-md-6 text-right">
            <strong class="text-primary" id="total-amount">
                <?php 
                $totalSum = 0;
                foreach ($materials as $material) {
                    $totalSum += $material['suggested_price'] * $material['quantity'];
                }
                echo number_format($totalSum, 2);
                ?> сум
            </strong>
        </div>
    </div>
</form>
