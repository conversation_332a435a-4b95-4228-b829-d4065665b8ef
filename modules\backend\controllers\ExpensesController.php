<?php

namespace app\modules\backend\controllers;

use app\common\models\CashboxDetail;
use Yii;
use yii\web\Response;
use app\modules\backend\models\Expenses;
use app\modules\backend\models\ExpensesType;
use app\common\models\Tracking;
use app\common\models\Cashbox;
use app\common\models\PaymentType;

class ExpensesController extends BaseController
{
    public function actionIndex()
    {
        $today = date('Y-m-d');

        $query = Expenses::find()
            ->select([
                'expenses.*',
                'expenses_type.name as expense_type_name',
                'users.full_name as added_by_user'
            ])
            ->leftJoin('expenses_type', 'expenses.expense_type_id = expenses_type.id')
            ->leftJoin('users', 'expenses.add_user_id = users.id')
            ->where(['expenses.deleted_at' => null])
            ->andWhere(['>=', 'expenses.created_at', $today . ' 00:00:00'])
            ->andWhere(['<=', 'expenses.created_at', $today . ' 23:59:59'])
            ->orderBy(['expenses.id' => SORT_DESC]);

        $result = $query->asArray()->all();

        $expenseTypes = ExpensesType::find()
            ->where(['deleted_at' => null])
            ->all();

        return $this->render('index', [
            'result' => $result,
            'expenseTypes' => $expenseTypes
        ]);
    }

    public function actionSearch()
    {
        $startDate = Yii::$app->request->post('start_date');
        $endDate = Yii::$app->request->post('end_date');
        $expenseTypeId = Yii::$app->request->post('expense_type_id');

        $query = Expenses::find()
            ->select([
                'expenses.*',
                'expenses_type.name as expense_type_name',
                'users.full_name as added_by_user'
            ])
            ->leftJoin('expenses_type', 'expenses.expense_type_id = expenses_type.id')
            ->leftJoin('users', 'expenses.add_user_id = users.id');

        if ($startDate) {
            $query->andWhere(['>=', 'DATE(expenses.created_at)', $startDate]);
        }
        if ($endDate) {
            $query->andWhere(['<=', 'DATE(expenses.created_at)', $endDate]);
        }
        if ($expenseTypeId) {
            $query->andWhere(['expenses.expense_type_id' => $expenseTypeId]);
        }

        $query->andWhere(['expenses.deleted_at' => null]);
        $query->orderBy(['expenses.created_at' => SORT_DESC]);
        $result = $query->asArray()->all();

        if (Yii::$app->request->isAjax) {
            return $this->renderAjax('_index', [
                'result' => $result
            ]);
        }

        return $this->asJson([
            'status' => 'success',
            'data' => $result
        ]);
    }


    
    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new Expenses();
            $model->scenario = Expenses::SCENARIO_CREATE;
            if ($model->load(Yii::$app->request->post()) && $model->validate()) {

                $model->add_user_id = Yii::$app->user->id;
                $model->created_at = date('Y-m-d H:i:s');
                
                // Логика определения статуса расхода
                if ($model->payment_type == PaymentType::CASH) {
                    // Для наличных платежей проверяем роль пользователя
                    if (Yii::$app->user->can('product_keeper')) {
                        // Кладовщик продукции - наличные автоматически подтверждаются
                        $model->status = Expenses::TYPE_ACCEPTED;
                    } else {
                        // Остальные роли - наличные требуют подтверждения
                        $model->status = Expenses::TYPE_NOT_ACCEPTED;
                    }
                } else {
                    // Безналичные всегда автоматически подтверждаются
                    $model->status = Expenses::TYPE_ACCEPTED;
                }
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    if(!$model->save(false)) {
                        throw new \Exception('Не удалось сохранить расход');
                    }

                    if($model->status !== Expenses::TYPE_NOT_ACCEPTED) {
                        $tracking = new Tracking();
                        $tracking->progress_type = Tracking::TYPE_EXPENSES;
                        $tracking->process_id = $model->id;
                        $tracking->created_at = date('Y-m-d H:i:s');
                        $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                        $tracking->accepted_at = null;
                        if (!$tracking->save(false)) {
                            throw new \Exception('Не удалось сохранить tracking');
                        }
                    }

                    // Получаем выбранную кассу
                    $cashbox = Cashbox::findOne($model->cashbox_id);
                    if (!$cashbox) {
                        throw new \Exception('Касса не найдена');
                    }

                    if($model->status == Expenses::TYPE_ACCEPTED) {
                        // Проверяем достаточность средств в кассе
                        if($cashbox->balance < $model->summa) {
                            throw new \Exception(Yii::t('app', 'Not enough funds on the source cashbox.'));
                        }

                        $cashbox_detail = new CashboxDetail();
                        $cashbox_detail->cashbox_id = $cashbox->id;
                        $cashbox_detail->amount = $model->summa;
                        $cashbox_detail->add_user_id = Yii::$app->user->id;
                        $cashbox_detail->created_at = date('Y-m-d H:i:s');
                        $cashbox_detail->type = $model->payment_type; // Используем тип платежа из модели
                        if (!$cashbox_detail->save(false)) {
                            throw new \Exception('Не удалось сохранить cashbox detail');
                        }

                        $cashbox->balance -= $model->summa;
                        if (!$cashbox->save(false)) {
                            throw new \Exception('Не удалось обновить кассу');
                        }
                    }

                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'record_successfully_created')
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ];
                }
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $model = new Expenses();
            $expenseTypes = ExpensesType::find()
            ->where(['deleted_at' => null])
            ->andWhere(['not in', 'name', [
                'Taminotchi uchun',
                'Avans',
                'Oylik',
                'Bonus',
                'Debt',
                'One time payment',
                'Vacation pay'
            ]])
            ->all();
            return [
                'status' => 'success',
                'content' => $this->renderPartial('create', [
                    'model' => $model,
                    'expenseTypes' => $expenseTypes
                ])
            ];
        }
    }

    /**
     * Получает доступные типы платежей для выбранной кассы
     */
    public function actionGetPaymentTypesByCashbox()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $cashboxId = Yii::$app->request->get('cashbox_id');

        if (!$cashboxId) {
            return [
                'status' => 'error',
                'message' => 'Не указан ID кассы'
            ];
        }

        $cashbox = Cashbox::findOne($cashboxId);
        if (!$cashbox) {
            return [
                'status' => 'error',
                'message' => 'Касса не найдена'
            ];
        }

        // Получаем доступные типы платежей для данной кассы
        $paymentTypes = PaymentType::find()
            ->joinWith('cashboxPaymentTypes cpt')
            ->where([
                'cpt.cashbox_id' => $cashboxId,
                'payment_type.deleted_at' => null,
                'cpt.deleted_at' => null
            ])
            ->all();

        $result = [];
        foreach ($paymentTypes as $paymentType) {
            $result[] = [
                'id' => $paymentType->id,
                'type' => $paymentType->type,
                'label' => PaymentType::getTypeDescription($paymentType->type)
            ];
        }

        return [
            'status' => 'success',
            'payment_types' => $result
        ];
    }


    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $id = $data['Expenses']['id'];

            // Проверяем, что расход существует
            $model = Expenses::findOne($id);
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Record not found')
                ];
            }

            // Проверяем, что расход можно редактировать
            if (!Expenses::canBeEdited($id)) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Cannot update confirmed expense')
                ];
            }

            // Сохраняем старые значения для отката изменений
            $oldSumma = $model->summa;
            $oldCashboxId = $model->cashbox_id;
            $oldPaymentType = $model->payment_type;

            // Загружаем новые данные
            $model->load($data);

            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // 1. Обрабатываем старую кассу
                $oldCashbox = Cashbox::findOne($oldCashboxId);
                if (!$oldCashbox) {
                    throw new \Exception(Yii::t('app', 'Old cashbox not found'));
                }

                // Возвращаем деньги в старую кассу
                $oldCashbox->balance += $oldSumma;
                if (!$oldCashbox->save(false)) {
                    throw new \Exception(Yii::t('app', 'Error updating old cashbox balance'));
                }

                // Создаем запись о входе средств в старую кассу
                $oldCashboxDetail = new CashboxDetail();
                $oldCashboxDetail->cashbox_id = $oldCashbox->id;
                $oldCashboxDetail->add_user_id = Yii::$app->user->id;
                $oldCashboxDetail->type = $oldPaymentType; // Используем старый тип платежа
                $oldCashboxDetail->amount = $oldSumma;
                $oldCashboxDetail->created_at = date('Y-m-d H:i:s');

                if (!$oldCashboxDetail->save(false)) {
                    throw new \Exception(Yii::t('app', 'Error saving old cashbox detail'));
                }

                // 2. Обрабатываем новую кассу
                $newCashbox = Cashbox::findOne($model->cashbox_id);
                if (!$newCashbox) {
                    throw new \Exception(Yii::t('app', 'New cashbox not found'));
                }

                // Проверяем, достаточно ли средств в новой кассе
                if ($newCashbox->balance < $model->summa) {
                    throw new \Exception(Yii::t('app', 'Not enough funds in the cashbox'));
                }

                // Вычитаем новую сумму из новой кассы
                $newCashbox->balance -= $model->summa;
                if (!$newCashbox->save(false)) {
                    throw new \Exception(Yii::t('app', 'Error updating new cashbox balance'));
                }

                // Создаем запись о выходе средств из новой кассы
                $newCashboxDetail = new CashboxDetail();
                $newCashboxDetail->cashbox_id = $newCashbox->id;
                $newCashboxDetail->add_user_id = Yii::$app->user->id;
                $newCashboxDetail->type = $model->payment_type; // Используем новый тип платежа
                $newCashboxDetail->amount = $model->summa;
                $newCashboxDetail->created_at = date('Y-m-d H:i:s');

                if (!$newCashboxDetail->save(false)) {
                    throw new \Exception(Yii::t('app', 'Error saving new cashbox detail'));
                }

                // 3. Обновляем расход
                $model->created_at = date('Y-m-d H:i:s');
                if (!$model->save(false)) {
                    throw new \Exception(Yii::t('app', 'Error updating expense'));
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'expense_updated_successfully')
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                Yii::error($e->getMessage());
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Expenses::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Record not found')
                ];
            }

            // Проверяем, что расход можно редактировать
            if (!Expenses::canBeEdited($id)) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Cannot update confirmed expense')
                ];
            }

            $expenseTypes = ExpensesType::find()
                ->where(['deleted_at' => null])
                ->all();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model,
                    'expenseTypes' => $expenseTypes
                ])
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Invalid request'
        ];
    }

    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $id = $data['Expenses']['id'];

            $model = Expenses::findOne($id);
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Record not found')
                ];
            }

            // Проверяем, что расход можно удалить
            if (!Expenses::canBeEdited($id)) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Cannot delete confirmed expense')
                ];
            }

            // Получаем tracking для последующего удаления
            $tracking = Tracking::find()
                ->where(['process_id' => $model->id])
                ->andWhere(['progress_type' => Tracking::TYPE_EXPENSES])
                ->andWhere(['is', 'accepted_at', null])
                ->andWhere(['is', 'deleted_at', null])
                ->one();

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Получаем кассу из которой был произведен расход
                $cashbox = Cashbox::findOne($model->cashbox_id);
                if (!$cashbox) {
                    throw new \Exception(Yii::t('app', 'Cashbox not found'));
                }

                // Возвращаем сумму в кассу
                $cashbox->balance += $model->summa;
                if (!$cashbox->save(false)) {
                    throw new \Exception(Yii::t('app', 'Failed to update cashbox balance'));
                }

                $cashboxDetail = new CashboxDetail();
                $cashboxDetail->cashbox_id = $cashbox->id;
                $cashboxDetail->amount = $model->summa;
                $cashboxDetail->add_user_id = Yii::$app->user->id;
                $cashboxDetail->created_at = date('Y-m-d H:i:s');
                $cashboxDetail->type = $model->payment_type; // Используем тип платежа из модели
                if (!$cashboxDetail->save(false)) {
                    throw new \Exception(Yii::t('app', 'Failed to save cashbox detail'));
                }

                // Проверяем существование объекта $tracking перед обновлением
                if ($tracking !== null) {
                    $tracking->deleted_at = date('Y-m-d H:i:s');
                    if (!$tracking->save(false)) {
                        throw new \Exception(Yii::t('app', 'Failed to update tracking'));
                    }
                }

                $model->deleted_at = date('Y-m-d H:i:s');
                if (!$model->save(false)) {
                    throw new \Exception(Yii::t('app', 'Failed to delete expense'));
                }

                $transaction->commit();

                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'Record successfully deleted')
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Expenses::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Record not found')
                ];
            }

            // Проверяем, что расход можно удалить
            if (!Expenses::canBeEdited($id)) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Cannot delete confirmed expense')
                ];
            }

            return [
                'status' => 'success',
                'content' => $this->renderPartial('delete', [
                    'model' => $model
                ])
            ];
        }

        return [
            'status' => 'error',
            'message' => Yii::t('app', 'Invalid request')
        ];
    }



    public function actionExpensesExcel()
    {
            $startDate = Yii::$app->request->post('start_date');
            $endDate = Yii::$app->request->post('end_date');
            $expenseTypeId = Yii::$app->request->post('expense_type_id');

            $query = Expenses::find()
                ->select([
                    'expenses.*',
                    'expenses_type.name as expense_type_name',
                    'users.full_name as added_by_user'
                ])
                ->leftJoin('expenses_type', 'expenses.expense_type_id = expenses_type.id')
                ->leftJoin('users', 'expenses.add_user_id = users.id');

            if ($startDate) {
                $query->andWhere(['>=', 'DATE(expenses.created_at)', date('Y-m-d', strtotime($startDate))]);
            }
            if ($endDate) {
                $query->andWhere(['<=', 'DATE(expenses.created_at)', date('Y-m-d', strtotime($endDate))]);
            }
            if ($expenseTypeId) {
                $query->andWhere(['expenses.expense_type_id' => $expenseTypeId]);
            }

            $query->andWhere(['expenses.deleted_at' => null]);
            $query->orderBy(['expenses.created_at' => SORT_DESC]);
            $result = $query->asArray()->all();

            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            $sheet->setCellValue('A1', Yii::t('app', 'expense_type'));
            $sheet->setCellValue('B1', Yii::t('app', 'added_by'));
            $sheet->setCellValue('C1', Yii::t('app', 'payment_type'));
            $sheet->setCellValue('D1', Yii::t('app', 'amount'));
            $sheet->setCellValue('E1', Yii::t('app', 'expenses_created_at'));
            $sheet->setCellValue('F1', Yii::t('app', 'description'));

            $headerStyle = [
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'rgb' => 'E0E0E0',
                    ],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
            ];
            $sheet->getStyle('A1:F1')->applyFromArray($headerStyle);

            $row = 2;
            foreach ($result as $item) {
                $expenseType = $item['expense_type_name'] ?? null;
                $translatedExpenseType = Yii::t('app', match ($expenseType) {
                    'Oylik' => 'salary',
                    'Avans' => 'advance',
                    'Bonus' => 'bonus',
                    'Debt' => 'debt',
                    'One time payment' => 'one_time_payment',
                    default => $expenseType,
                });

                $sheet->setCellValue('A' . $row, $translatedExpenseType);
                $sheet->setCellValue('B' . $row, $item['added_by_user']);
                $sheet->setCellValue('C' . $row, Expenses::getTypePayment($item['payment_type']));
                $sheet->setCellValueExplicit('D' . $row, $item['summa'], \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_NUMERIC);
                $sheet->getStyle('D' . $row)->getNumberFormat()->setFormatCode('#,##0');
                $sheet->setCellValue('E' . $row, date("d.m.Y H:i", strtotime($item['created_at'])));
                $sheet->setCellValue('F' . $row, $item['description']);

                $sheet->getStyle('A' . $row . ':F' . $row)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);

                $row++;
            }

            foreach (range('A', 'F') as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }

            $filename = 'expenses';
            if ($startDate && $endDate) {
                $filename .= '_' . date('Y-m-d', strtotime($startDate)) . '_to_' . date('Y-m-d', strtotime($endDate));
            } elseif ($startDate) {
                $filename .= '_from_' . date('Y-m-d', strtotime($startDate));
            } elseif ($endDate) {
                $filename .= '_to_' . date('Y-m-d', strtotime($endDate));
            } else {
                $filename .= '_all_time_' . date('Y-m-d');
            }
            $filename .= '.xlsx';

            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

            $stream = fopen('php://temp', 'w+');
            $writer->save($stream);
            rewind($stream); // Rewind the stream pointer

            return Yii::$app->response->sendStreamAsFile($stream, $filename, [
                'mimeType' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ]);
        }
}
