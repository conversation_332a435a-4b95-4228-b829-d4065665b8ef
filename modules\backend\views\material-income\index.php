<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "income_materials");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
?>

<style>
    .modal-dialog {
        max-width: 800px;
    }
    </style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin')): ?>
                <a href="#" class="btn btn-primary material-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "income_material") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'material-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="material-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th><?= Yii::t("app", "Name") ?></th>
                    <th><?= Yii::t("app", "Description") ?></th>
                    <th><?= Yii::t("app", "Total Quantity") ?></th>
                    <th><?= Yii::t("app", "In Production") ?></th>
                    <th><?= Yii::t("app", "Defect Quantity") ?></th>
                    <th><?= Yii::t("app", "Price") ?></th>
                    <th><?= Yii::t("app", "Total Sum") ?></th>
                    <th><?= Yii::t("app", "material_income_created_at") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                        <td><?= Html::encode($model['name']) ?></td>
                        <td><?= Html::encode($model['description']) ?></td>
                        <td><?= Html::encode($model['total_quantity']) ?></td>
                        <td><?= Html::encode($model['in_production']) ?></td>
                        <td><?= Html::encode($model['defect_quantity']) ?></td>
                        <td><?= Yii::$app->formatter->asDecimal($model['price'], 2) . ($model['is_dollar'] ? ' $' : '') ?></td>
                        <td><?= Yii::$app->formatter->asDecimal($model['total_sum'], 2) . ($model['is_dollar'] ? ' $' : '') ?></td>
                        <td data-order="<?= !empty($model['created_at']) ? strtotime($model['created_at']) : '' ?>">
                            <?= !empty($model['created_at']) ? Html::encode(date('d.m.Y H:i', strtotime($model['created_at']))) : 'N/A' ?>
                        </td>
                        <td>
                        <a href="#" class="badge badge-info material-detail" data-toggle="modal" data-target="#ideal-large-modal-without-save" data-material-id="<?= Html::encode($model['id']) ?>">
                                <span class="red-text"><?= Yii::t("app", "view") ?></span>
                        </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "Create Material") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "Update Material") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "material_income_history_view") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    // Определяем конфигурацию DataTable
    var dataTableConfig = {
        "deferRender": true,
        "processing": true,
        "language": {
            "search": searchLabel,
            "lengthMenu": lengthMenuLabel,
            "zeroRecords": zeroRecordsLabel,
            "info": infoLabel,
            "infoEmpty": infoEmptyLabel,
            "infoFiltered": infoFilteredLabel
        },
        "pageLength": 50,
        "order": [[7, 'desc']],
        "columnDefs": [
            {
                "targets": [1],
                "orderable": false
            }
        ]
    };

    // Инициализация DataTable с оптимизацией
    function initializeDataTable() {
        var table = $('#material-grid-view');
        
        if ($.fn.DataTable.isDataTable(table)) {
            table.DataTable().destroy();
        }

        // Создаем экземпляр DataTable асинхронно
        setTimeout(function() {
            table.DataTable(dataTableConfig);
        }, 0);
    }

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }
    
    function initializeMaterialCreate() {
        $(document).off('click.material-create').on('click.material-create', '.material-create', function() {
            $.ajax({
                url: '/backend/material-income/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("material-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.material-create-button').on('click.material-create-button', '.material-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#material-create-form').serialize();
                $.ajax({
                    url: '/backend/material-income/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#material-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                        toastr.error('An error occurred while processing your request.');
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeViewDetail() {
        $(document).off('click.material-detail').on('click.material-detail', '.material-detail', function() {
            var materialId = $(this).data('material-id');
            $.ajax({
                url: '/backend/material-income/detail',
                dataType: 'json',
                type: 'GET',
                data: {id: materialId},
                success: function(response) {
                    if (response && response.status === 'success') {
                        $('#ideal-large-modal-without-save .modal-title').html(three);
                        $('#ideal-large-modal-without-save .modal-body').html(response.content);
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });
    }

    function initializeComponents() {
        initializeViewDetail();
        initializeDropdown();
        initializeSelect2();
        initializeMaterialCreate();
    }

    // Инициализация при первой загрузке
    initializeDataTable();
    initializeComponents();

    // Переинициализация после PJAX
    $(document).on('pjax:success', function() {
        // Используем requestAnimationFrame для оптимизации производительности
        requestAnimationFrame(function() {
            initializeDataTable();
            initializeComponents();
        });
    });
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
