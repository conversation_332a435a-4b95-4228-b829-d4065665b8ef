<?php

namespace app\modules\backend\controllers;

use app\common\models\Cashbox;
use app\common\models\CashboxDetail;
use Yii;
use yii\data\ActiveDataProvider;
use yii\web\Response;
use app\modules\backend\models\Expenses;
use app\common\models\Tracking;
use PDO;

class CashierController extends BaseController
{
    public function actionIndex()
    {
        $sql = "SELECT 
                    ex.id,
                    et.name AS expense_name,
                    u.full_name AS added_by,
                    ex.description,
                    ex.summa,
                    ex.created_at,
                    ex.deleted_at
                FROM expenses AS ex
                LEFT JOIN expenses_type AS et ON ex.expense_type_id = et.id
                LEFT JOIN users AS u ON ex.add_user_id = u.id
                WHERE ex.status = :status
                AND ex.deleted_at IS NULL
                ORDER BY ex.created_at DESC";
        
        $expenses = Yii::$app->db->createCommand($sql)
            ->bindValue(':status', Expenses::TYPE_NOT_ACCEPTED, PDO::PARAM_INT)
            ->queryAll();
    
        return $this->render('index', [
            'expenses' => $expenses
        ]);
    }

    public function actionAccept()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $model = Expenses::findOne($data['Expenses']['id']);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => 'Расход не найден'
                ];
            }

            if ($model->status !== Expenses::TYPE_NOT_ACCEPTED) {
                return [
                    'status' => 'error',
                    'message' => 'Расход уже подтвержден'
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $model->status = Expenses::TYPE_ACCEPTED;
                if (!$model->save(false)) {
                    throw new \Exception('Не удалось обновить статус расхода');
                }


                // Получаем кассу из расхода
                $cashbox = Cashbox::findOne($model->cashbox_id);
                if (!$cashbox) {
                    throw new \Exception('Касса не найдена');
                }

                // Расходы создаются в сомах (ID=2), но касса может быть в другой валюте
                $expenseCurrencyId = 2; // Сомы
                $cashboxCurrencyId = $cashbox->currency_id;
                
                // Сумма для списания с кассы (в валюте кассы)
                $deductionAmount = $model->summa;
                
                // Если валюты разные, делаем конвертацию
                if ($expenseCurrencyId != $cashboxCurrencyId) {
                    $deductionAmount = \app\common\models\CurrencyCourse::convertCurrency(
                        $model->summa, 
                        $expenseCurrencyId, 
                        $cashboxCurrencyId
                    );
                    
                    if ($deductionAmount === null) {
                        throw new \Exception('Не удалось выполнить конвертацию валют');
                    }
                }

                if ($cashbox->balance < $deductionAmount) {
                    throw new \Exception(Yii::t('app', 'Not enough funds on the source cashbox.'));
                }

                $cashbox_detail = new CashboxDetail();
                $cashbox_detail->cashbox_id = $cashbox->id;
                $cashbox_detail->amount = $deductionAmount;
                $cashbox_detail->add_user_id = Yii::$app->user->id;
                $cashbox_detail->created_at = date('Y-m-d H:i:s');
                $cashbox_detail->type = CashboxDetail::TYPE_OUT;
                if (!$cashbox_detail->save(false)) {
                    throw new \Exception('Не удалось сохранить cashbox detail');
                }

                $cashbox->balance -= $deductionAmount;
                if (!$cashbox->save(false)) {
                    throw new \Exception('Не удалось обновить кассу');
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'record_successfully_accepted')
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        } else if(Yii::$app->request->isGet)
        {
            $id = Yii::$app->request->get('id');
            $model = Expenses::findOne($id);
            if (!$model) {
                throw new \Exception('Рекорд не найден');
            }
            return [
                'status' => 'success',
                'content' => $this->renderPartial('accept', [
                    'model' => $model
                ])
            ];
        }
    }
}