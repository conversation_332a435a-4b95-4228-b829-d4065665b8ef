<?php 

namespace app\modules\backend\models;

use Yii;
use yii\base\Model;

class SupplierPayForm extends Model
{
    public $supplier_id;
    public $amount;
    public $course;
    public $type;
    public $cashbox_id;

    public function rules()
    {
        return [
            ['supplier_id', 'required'],
            ['amount', 'required'],
            ['type', 'required'],
            ['cashbox_id', 'required'],
            ['amount', 'number'],
            ['course', 'number'],
            ['cashbox_id', 'integer'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'supplier_id' => Yii::t('app', 'supplier'),
            'amount' => Yii::t('app', 'amount'),
            'course' => Yii::t('app', 'course'),
            'type' => Yii::t('app', 'payment_type'),
            'cashbox_id' => Yii::t('app', 'cashbox'),
        ];
    }

    public function beforeValidate()
    {
        $attributes = ['amount', 'course'];

        foreach ($attributes as $attribute) {
            if (is_string($this->$attribute) && !empty($this->$attribute)) {
                $originalValue = $this->$attribute;
                
                $cleanValue = preg_replace('/[\s\x{A0}]+/u', '', $originalValue);
                $cleanValue = str_replace(',', '.', $cleanValue);
                
                if (is_numeric($cleanValue)) {
                    $this->$attribute = floatval($cleanValue);
                }
            }
        }
        
        return parent::beforeValidate();
    }
    
}