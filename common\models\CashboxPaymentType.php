<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "cashbox_payment_type".
 *
 * @property int $cashbox_id
 * @property int $payment_type_id
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Cashbox $cashbox
 * @property PaymentType $paymentType
 */
class CashboxPaymentType extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'cashbox_payment_type';
    }

    /**
     * {@inheritdoc}
     */
    public static function primaryKey()
    {
        return ['cashbox_id', 'payment_type_id'];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['cashbox_id', 'payment_type_id'], 'required'],
            [['cashbox_id', 'payment_type_id'], 'default', 'value' => null],
            [['cashbox_id', 'payment_type_id'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            [['cashbox_id'], 'exist', 'skipOnError' => true, 'targetClass' => Cashbox::class, 'targetAttribute' => ['cashbox_id' => 'id']],
            [['payment_type_id'], 'exist', 'skipOnError' => true, 'targetClass' => PaymentType::class, 'targetAttribute' => ['payment_type_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'cashbox_id' => 'Cashbox ID',
            'payment_type_id' => 'Payment Type ID',
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[Cashbox]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCashbox()
    {
        return $this->hasOne(Cashbox::class, ['id' => 'cashbox_id']);
    }

    /**
     * Gets query for [[PaymentType]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPaymentType()
    {
        return $this->hasOne(PaymentType::class, ['id' => 'payment_type_id']);
    }
}
