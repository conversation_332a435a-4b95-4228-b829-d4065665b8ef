<?php

use yii\helpers\Html;
use app\modules\backend\models\Expenses;
use app\common\models\Tracking;

?>

<?php if($result): ?>
    <div>
        <table id="payment-grid-view" class="table table-bordered table-striped compact">
            <thead>
                <tr>
                    <th><?= Yii::t("app", "client_name") ?></th>
                    <th><?= Yii::t("app", "payment_created_at") ?></th>
                    <th><?= Yii::t("app", "who_entered") ?></th>
                    <th><?= Yii::t("app", "payment_type") ?></th>
                    <th><?= Yii::t("app", "amount") ?></th>
                    <th><?= Yii::t("app", "status") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($result as $row): ?>
                    <tr>
                        <td><?= Html::encode($row['client_name']) ?></td>
                        <td><?= Html::encode(date("d.m.Y h:i", strtotime($row['created_at']))) ?></td>
                        <td><?= Html::encode($row['user_name']) ?></td>
                        <td><?= Html::encode(Expenses::getTypePayment($row['type'])) ?></td>
                        <td><?= Html::encode(number_format($row['summa'], 0, '.', ' ')) ?></td>
                        <td>
                            <span class="<?= $row['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?php
                                $statusText = $row['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                echo $statusText;
                                ?>
                            </span>
                        </td>
                        <td>
                            <?php
                            $tracking = Tracking::find()
                                ->where(['process_id' => $row['id']])
                                ->andWhere(['progress_type' => Tracking::PAY_FOR_CLIENT])
                                ->andWhere(['is', 'accepted_at', null])
                                ->andWhere(['is', 'deleted_at', null])
                                ->one();

                            $canDelete = $tracking !== null;
                            ?>
                            <?php if ($canDelete): ?>
                                <div class="dropdown d-inline">
                                    <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                        <?php echo Yii::t("app", "detail"); ?>
                                    </a>
                                    <div class="dropdown-menu">
                                        <a href="#" class="dropdown-item payment-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($row['id']) ?>">
                                            <span class="blue-text"><?= Yii::t("app", "update") ?></span>
                                        </a>
                                        <a href="#" class="dropdown-item payment-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($row['id']) ?>">
                                            <span class="red-text"><?= Yii::t("app", "delete") ?></span>
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else: ?>
    <p><?= Yii::t('app', 'no_data_available') ?></p>
<?php endif; ?>
