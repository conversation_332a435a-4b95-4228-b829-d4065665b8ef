<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;
use app\assets\DataTablesAsset;

/* @var $this yii\web\View */

DataTablesAsset::register($this);

$this->title = Yii::t('app', 'reports');
$this->params['breadcrumbs'][] = $this->title;

?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
    </div>

    <!-- Навигационные табы -->
    <div class="row mb-4">
        <div class="col-12">
            <?php if (!Yii::$app->user->can('seller')): ?>
            <ul class="nav nav-tabs nav-tabs-bordered" id="reportTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link active" id="sales-tab" data-toggle="tab" href="#sales" role="tab" aria-controls="sales" aria-selected="true">
                        <i class="fas fa-chart-line mr-2"></i>
                        <?= Yii::t('app', 'sales_report') ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="finance-tab" data-toggle="tab" href="#finance" role="tab" aria-controls="finance" aria-selected="false">
                        <i class="fas fa-dollar-sign mr-2"></i>
                        <?= Yii::t('app', 'finance_report') ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="product-stock-tab" data-toggle="tab" href="#product-stock" role="tab" aria-controls="product-stock" aria-selected="false">
                        <i class="fas fa-boxes mr-2"></i>
                        <?= Yii::t('app', 'product_stock') ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="material-stock-tab" data-toggle="tab" href="#material-stock" role="tab" aria-controls="material-stock" aria-selected="false">
                        <i class="fas fa-warehouse mr-2"></i>
                        <?= Yii::t('app', 'material_stock') ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="production-tab" data-toggle="tab" href="#production" role="tab" aria-controls="production" aria-selected="false">
                        <i class="fas fa-industry mr-2"></i>
                        <?= Yii::t('app', 'production_report_today') ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="free-products-tab" data-toggle="tab" href="#free-products" role="tab" aria-controls="free-products" aria-selected="false">
                        <i class="fas fa-gift mr-2"></i>
                        <?= Yii::t('app', 'free_products') ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="debt-tab" data-toggle="tab" href="#debt" role="tab" aria-controls="debt" aria-selected="false">
                        <i class="fas fa-hand-holding-usd mr-2"></i>
                        <?= Yii::t('app', 'client_debts') ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="supplier-balance-tab" data-toggle="tab" href="#supplier-balance" role="tab" aria-controls="supplier-balance" aria-selected="false">
                        <i class="fas fa-handshake mr-2"></i>
                        <?= Yii::t('app', 'supplier_balance') ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="cashbox-tab" data-toggle="tab" href="#cashbox" role="tab" aria-controls="cashbox" aria-selected="false">
                        <i class="fas fa-cash-register mr-2"></i>
                        <?= Yii::t('app', 'cashbox_report') ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="security-tab" data-toggle="tab" href="#security" role="tab" aria-controls="security" aria-selected="false">
                        <i class="fas fa-shield-alt mr-2"></i>
                        <?= Yii::t('app', 'security_report') ?>
                    </a>
                </li>
                <?php endif; ?>

                <li class="nav-item" role="presentation">
                    <a class="nav-link" id="product-sales-tab" data-toggle="tab" href="#product-sales" role="tab" aria-controls="product-sales" aria-selected="false">
                        <i class="fas fa-chart-bar mr-2"></i>
                        <?= Yii::t('app', 'product_sales_report') ?>
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Содержимое табов -->
    <div class="tab-content" id="reportTabsContent">
        <!-- Отчет по продажам -->
        <div class="tab-pane fade show active" id="sales" role="tabpanel" aria-labelledby="sales-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>

        <!-- Отчет по финансам -->
        <div class="tab-pane fade" id="finance" role="tabpanel" aria-labelledby="finance-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>

        <!-- Отчет по остаткам продукции -->
        <div class="tab-pane fade" id="product-stock" role="tabpanel" aria-labelledby="product-stock-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>

        <!-- Отчет по остаткам сырья -->
        <div class="tab-pane fade" id="material-stock" role="tabpanel" aria-labelledby="material-stock-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>


         <div class="tab-pane fade" id="production" role="tabpanel" aria-labelledby="production-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>

        <!-- Отчет по бесплатным продуктам -->
        <div class="tab-pane fade" id="free-products" role="tabpanel" aria-labelledby="free-products-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>

        <!-- Отчет по задолженностям клиентов -->
        <div class="tab-pane fade" id="debt" role="tabpanel" aria-labelledby="debt-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>

        <!-- Отчет по балансу поставщиков -->
        <div class="tab-pane fade" id="supplier-balance" role="tabpanel" aria-labelledby="supplier-balance-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>

        <!-- Отчет по кассе -->
        <div class="tab-pane fade" id="cashbox" role="tabpanel" aria-labelledby="cashbox-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>

        <!-- Отчет охранника -->
        <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>
        
        <!-- Отчет по продажам продуктов -->
        <div class="tab-pane fade" id="product-sales" role="tabpanel" aria-labelledby="product-sales-tab">
            <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"><?= Yii::t('app', 'loading') ?>...</span>
                </div>
            </div>
            <div class="report-content" style="display: none;"></div>
        </div>
      
    </div>
</div>

<style>
.nav-tabs-bordered .nav-link {
    border: 1px solid transparent;
    border-radius: 0.25rem 0.25rem 0 0;
    color: #6c757d;
    transition: all 0.3s ease;
}

.nav-tabs-bordered .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #495057;
    background-color: #f8f9fa;
}

.nav-tabs-bordered .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    border-bottom-color: transparent;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

.tab-content {
    min-height: 400px;
}

.report-content {
    width: 100%;
}

.report-content .card {
    border: none;
    box-shadow: none;
}

.report-content .card-body {
    padding: 0;
}

.alert {
    margin-bottom: 0;
}

.mr-2 {
    margin-right: 0.5rem !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.text-right {
    text-align: right !important;
}

.text-muted {
    color: #6c757d !important;
}

.d-flex {
    display: flex !important;
}

.justify-content-center {
    justify-content: center !important;
}

.align-items-center {
    align-items: center !important;
}

.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Скрываем возможные дублированные навигационные элементы в загруженном контенте */
.report-content .nav-tabs,
.report-content nav.nav-tabs,
.report-content nav.navbar {
    display: none !important;
}

/* Гарантируем правильное отображение спиннера */
.d-flex.justify-content-center.align-items-center {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

@media (max-width: 768px) {
    .nav-tabs {
        flex-direction: column;
    }
    
    .nav-tabs .nav-item {
        margin-bottom: 0.25rem;
    }
    
    .text-right {
        text-align: center !important;
        margin-top: 1rem;
    }
}
</style>

<?php
$js = <<<JS
(function($) {
    var reportUrls = {
        'sales': '/backend/report/sales',
        'finance': '/backend/report/finance',
        'product-stock': '/backend/report/product-stock',
        'material-stock': '/backend/report/material-stock',
        'production': '/backend/report/production',
        'free-products': '/backend/report/free-products',
        'debt': '/backend/report/debt',
        'supplier-balance': '/backend/report/supplier-balance',
        'cashbox': '/backend/report/cashbox',
        'security': '/backend/report/security',
        'product-sales': '/backend/report/product-sales'
    };
    
    var loadedReports = {};

/**
 * Перехватываем отправку формы внутри загруженного отчёта и перезагружаем
 * содержимое вкладки через AJAX, не покидая страницу index.
 */
function attachReportFormHandler(contentDiv, tabId) {
    contentDiv.find('form').off('submit.report').on('submit.report', function(e) {
        e.preventDefault();
        var formElement = $(this);
        var url = reportUrls[tabId] + (urlHasQuery(reportUrls[tabId]) ? '&' : '?') + formElement.serialize();

        // Показываем спиннер
        var tabPane = $('#' + tabId);
        var spinner = tabPane.find('.d-flex.justify-content-center.align-items-center');
        spinner.css('display', 'flex');

        $.get(url, function(response) {
            var cleanResponse = response;
            if (typeof response === 'string') {
                cleanResponse = response.replace(/<ul[^>]*nav-tabs[^>]*>[\s\S]*?<\/ul>/gi, '');
                cleanResponse = cleanResponse.replace(/<nav[^>]*class="[^\"]*(?:nav-tabs|navbar)[^\"]*"[\s\S]*?<\/nav>/gi, '');
            }
            contentDiv.html(cleanResponse);
            spinner.css('display', 'none');

            // Повторно инициализируем компоненты и обработчики
            setTimeout(function () {
                attachReportFormHandler(contentDiv, tabId);
                attachReportPaginationHandler(contentDiv, tabId);
                contentDiv.find('table').each(function() {
                    if ($.fn.DataTable && !$.fn.DataTable.isDataTable(this)) {
                        try {
                            // Пропускаем таблицы с классом no-datatables
                            if ($(this).hasClass('no-datatables')) {
                                return;
                            }
                            
                            $(this).DataTable({
                                pageLength: 25,
                                responsive: true,
                                language: {
                                    search: 'Поиск:',
                                    zeroRecords: 'Ничего не найдено',
                                    infoEmpty: 'Нет данных',
                                    infoFiltered: '(отфильтровано из _MAX_ записей)'
                                }
                            });
                        } catch (e) {
                            console.warn('DataTables initialization failed for table:', e);
                        }
                    }
                });
            }, 50);
        });
    });
}

function urlHasQuery(u) {
    return u.indexOf('?') !== -1;
}

function attachReportPaginationHandler(contentDiv, tabId) {
    contentDiv.off('click.pagination').on('click.pagination', '.pagination a.page-link', function(e) {
        var href = $(this).attr('href');
        if (!href) { return; }
        e.preventDefault();

        var tabPane = $('#' + tabId);
        var spinner = tabPane.find('.d-flex.justify-content-center.align-items-center');
        spinner.css('display', 'flex');

        $.get(href, function(response) {
            var cleanResponse = response;
            if (typeof response === 'string') {
                cleanResponse = response.replace(/<ul[^>]*nav-tabs[^>]*>[\s\S]*?<\/ul>/gi, '');
                cleanResponse = cleanResponse.replace(/<nav[^>]*class="[^\"]*(?:nav-tabs|navbar)[^\"]*"[\s\S]*?<\/nav>/gi, '');
            }
            contentDiv.html(cleanResponse);
            spinner.css('display', 'none');

            // Повторно инициализируем компоненты и обработчики
            setTimeout(function () {
                attachReportFormHandler(contentDiv, tabId);
                attachReportPaginationHandler(contentDiv, tabId);
                contentDiv.find('table').each(function() {
                    if ($.fn.DataTable && !$.fn.DataTable.isDataTable(this)) {
                        try {
                            // Пропускаем таблицы с классом no-datatables
                            if ($(this).hasClass('no-datatables')) {
                                return;
                            }
                            
                            $(this).DataTable({
                                pageLength: 25,
                                responsive: true,
                                language: {
                                    search: 'Поиск:',
                                    zeroRecords: 'Ничего не найдено',
                                    infoEmpty: 'Нет данных',
                                    infoFiltered: '(отфильтровано из _MAX_ записей)'
                                }
                            });
                        } catch (e) {
                            console.warn('DataTables initialization failed for table:', e);
                        }
                    }
                });
                
                // Переинициализируем календари product-sales если они есть
                if (typeof window.initProductSalesDatePickers === 'function') {
                    window.initProductSalesDatePickers();
                }
                
                // Переинициализируем DataTable для product-sales если они есть
                if (typeof window.initProductSalesDataTable === 'function') {
                    window.initProductSalesDataTable();
                }
            }, 50);
        });
    });
}

    function loadReport(tabId) {
        console.log('Loading report for tab:', tabId);
        
        if (loadedReports[tabId]) {
            // Отчет уже загружен, просто скрываем спиннер и показываем контент
            var tabPane = $('#' + tabId);
            var spinner = tabPane.find('.d-flex.justify-content-center.align-items-center');
            var contentDiv = tabPane.find('.report-content');
            
            console.log('Report already loaded, hiding spinner and showing content');
            console.log('Spinner elements found:', spinner.length);
            console.log('Content elements found:', contentDiv.length);
            
            // Полностью удаляем спиннер
            spinner.remove();
            contentDiv.css('display', 'block');
            return;
        }

        var tabPane = $('#' + tabId);
        var spinner = tabPane.find('.d-flex.justify-content-center.align-items-center');
        var contentDiv = tabPane.find('.report-content');

        console.log('Starting new report load');
        console.log('Spinner elements found:', spinner.length);
        console.log('Content elements found:', contentDiv.length);

        // Показываем спиннер, скрываем контент
        spinner.css('display', 'flex');
        contentDiv.css('display', 'none');

        $.ajax({
            url: reportUrls[tabId],
            method: 'GET',
            timeout: 30000,
            success: function(response) {
                // Очищаем возможные дублированные элементы навигации из ответа
                var cleanResponse = response;
                if (typeof response === 'string') {
                    // Удаляем ТОЛЬКО навигационные элементы с вкладками внутри ответа, но сохраняем пагинацию
                    cleanResponse = response.replace(/<nav[^>]*class="[^\"]*(?:nav-tabs|navbar)[^\"]*"[\s\S]*?<\/nav>/gi, '');
                }
                
                contentDiv.html(cleanResponse);
                attachReportFormHandler(contentDiv, tabId);
                attachReportPaginationHandler(contentDiv, tabId);
                loadedReports[tabId] = true;
                
                console.log('Report loaded successfully, removing spinner');
                // Полностью удаляем спиннер и показываем контент
                spinner.remove();
                contentDiv.css('display', 'block');
                
                // Инициализируем компоненты
                setTimeout(function() {
                    contentDiv.find('table').each(function() {
                        if ($.fn.DataTable && !$.fn.DataTable.isDataTable(this)) {
                            try {
                                // Пропускаем таблицы с классом no-datatables
                                if ($(this).hasClass('no-datatables')) {
                                    return;
                                }
                                
                                $(this).DataTable({
                                    "pageLength": 25,
                                    "responsive": true,
                                    "language": {
                                        "search": "Поиск:",
                                        "lengthMenu": "Показать _MENU_ записей",
                                        "zeroRecords": "Ничего не найдено",
                                        "info": "Показано _START_ до _END_ из _TOTAL_ записей",
                                        "infoEmpty": "Нет данных",
                                        "infoFiltered": "(отфильтровано из _MAX_ записей)",
                                        "paginate": {
                                            "first": "Первая",
                                            "last": "Последняя",
                                            "next": "Следующая",
                                            "previous": "Предыдущая"
                                        }
                                    }
                                });
                            } catch (e) {
                                console.warn('DataTables initialization failed for table:', e);
                            }
                        }
                    });
                    
                    contentDiv.find('.select2').each(function() {
                        if ($.fn.select2) {
                            $(this).select2({
                                width: '100%'
                            });
                        }
                    });
                    
                    // Переинициализируем календари product-sales если они есть
                    if (typeof window.initProductSalesDatePickers === 'function') {
                        window.initProductSalesDatePickers();
                    }
                    
                    // Переинициализируем DataTable для product-sales если они есть
                    if (typeof window.initProductSalesDataTable === 'function') {
                        window.initProductSalesDataTable(contentDiv[0]);
                    }
                }, 100);
            },
            error: function(xhr, status, error) {
                var errorMessage = 'Ошибка загрузки отчета';
                if (xhr.status === 404) {
                    errorMessage = 'Отчет не найден';
                } else if (xhr.status === 500) {
                    errorMessage = 'Внутренняя ошибка сервера';
                }
                
                contentDiv.html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle mr-2"></i>' + errorMessage + '</div>');
                loadedReports[tabId] = true; // Помечаем как загруженный даже при ошибке
                
                console.log('Report loading failed, removing spinner');
                // Полностью удаляем спиннер и показываем контент
                spinner.remove();
                contentDiv.css('display', 'block');
                console.error('Error loading report:', error);
            }
        });
    }

    $('#reportTabs a').on('click', function(e) {
        e.preventDefault();
        $(this).tab('show');
    });

    $('#reportTabs a').on('shown.bs.tab', function(e) {
        var tabId = $(e.target).attr('href').substring(1);
        setTimeout(function() {
            loadReport(tabId);
        }, 50);
    });

    $(document).ready(function() {
        setTimeout(function() {
            loadReport('sales');
        }, 100);
    });
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>