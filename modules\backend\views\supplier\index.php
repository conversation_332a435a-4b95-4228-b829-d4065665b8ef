<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t('app', 'suppliers_section');
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");
$viewLabel = Yii::t('app', 'view');

?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin')): ?>
                <a href="#" class="btn btn-primary supplier-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "add_supplier") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'supplier-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="supplier-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "suplier_name") ?></th>
                        <th><?= Yii::t("app", "currency") ?></th>
                        <th><?= Yii::t("app", "phone_number") ?></th>
                        <th><?= Yii::t("app", "phone_number_2") ?></th>
                        <th><?= Yii::t("app", "address") ?></th>
                        <th><?= Yii::t("app", "balance") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                        <td><?= Html::encode($model['full_name']) ?></td>
                        <td><?= Html::encode($model['currency_name']) ?></td>
                        <td><?= Html::encode($model['phone_number']) ?></td>
                        <td><?= Html::encode($model['phone_number_2']) ?></td>
                        <td><?= Html::encode($model['address']) ?></td>
                        <td><?= number_format($model['balance'], 0, '.', ',') ?></td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <a href="<?= Url::to(['/backend/supplier/view', 'id' => $model['id']]) ?>" class="dropdown-item">
                                        <?= Yii::t("app", "view") ?>
                                    </a>

                                    <?php if(empty($model['deleted_at'])): ?>
                                        <a href="#" class="dropdown-item supplier-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>

                                        <a href="#" class="dropdown-item supplier-pay" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "pay") ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "add_supplier") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "edit_supplier") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "supplier_delete") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "supplier_pay") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');
    var four = $('#four').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";
    var all = "{$all}";
    var viewLabel = "{$viewLabel}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#supplier-grid-view')) {
            $('#supplier-grid-view').DataTable().destroy();
        }
        
        $('#supplier-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[0, 'desc']], 
            "columnDefs": [
                {
                    "targets": [1, 2, 5], 
                    "orderable": false
                }
            ]
        });
    }

    $(document).off('pjax:end').on('pjax:end', function() {
        initializeDataTable();
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            var href = $(this).attr('href');
            if (href && !$(this).hasClass('supplier-update') && !$(this).hasClass('supplier-pay')) {
                window.location.href = href;
            }
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }

    function initializeSupplierCreate() {
        $(document).off('click.supplier-create').on('click.supplier-create', '.supplier-create', function() {
            $.ajax({
                url: '/backend/supplier/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("supplier-create-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.supplier-create-button').on('click.supplier-create-button', '.supplier-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#supplier-create-form').serialize();
                $.ajax({
                    url: '/backend/supplier/create',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#supplier-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeSupplierUpdate() {
        $(document).off('click.supplier-update').on('click.supplier-update', '.supplier-update', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/supplier/update',
                data: {id: id},
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(two);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("supplier-update-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.supplier-update-button').on('click.supplier-update-button', '.supplier-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#supplier-update-form').serialize();
                $.ajax({
                    url: '/backend/supplier/update',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#supplier-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.errors) {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeSupplierPay() {
        $(document).off('click.supplier-pay').on('click.supplier-pay', '.supplier-pay', function(e) {
            e.preventDefault();
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/supplier/pay',
                data: {id: id},
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    if (response && response.status === 'success') {
                        $('#ideal-mini-modal .modal-title').html(four);
                        $('#ideal-mini-modal .modal-body').html(response.content || 'Нет данных');
                        $('#ideal-mini-modal .mini-button').addClass("supplier-pay-button");
                        initializeSelect2(); 
                        $('#ideal-mini-modal').modal('show');
                    } else {
                        $('#ideal-mini-modal').modal('hide');
                        iziToast.error({
                            message: response && response.message ? response.message : 'Неизвестная ошибка',
                            position: 'topRight'
                        });
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    $('#ideal-mini-modal').modal('hide');
                    iziToast.error({
                        title: 'Ошибка',
                        message: 'Не удалось выполнить запрос: ' + (xhr.statusText || errorThrown),
                        position: 'topRight'
                    });
                }
            });
        });

        $(document).off('click.supplier-pay-button').on('click.supplier-pay-button', '.supplier-pay-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#supplier-pay-form').serialize();
                $.ajax({
                    url: '/backend/supplier/pay',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#supplier-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response && response.status === 'error') {
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').css('color', 'red').text(errors.join(', '));
                            });
                            button.prop('disabled', false);

                            if (response.message) {
                                $('#form-error-message').text(response.message).css('color', 'red').show();
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    
    initializeDataTable();
    initializeDropdown();
    initializeSelect2();
    initializeSupplierCreate();
    initializeSupplierUpdate();
    initializeSupplierPay();

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
