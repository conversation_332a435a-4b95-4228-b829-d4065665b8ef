<?php

use yii\db\Migration;

class m250422_050747_make_phone_number_nullable_in_client extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn('client', 'phone_number', $this->string(255)->null());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->alterColumn('client', 'phone_number', $this->string(255)->notNull());
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250422_050747_make_phone_number_nullable_in_client cannot be reverted.\n";

        return false;
    }
    */
}
