<?php

use yii\db\Migration;

/**
 * Class m250601_000001_add_is_alternative_to_product_ingredients
 */
class m250601_000001_add_is_alternative_to_product_ingredients extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Добавляем колонку is_alternative в таблицу product_ingredients
        $this->addColumn('product_ingredients', 'is_alternative', $this->boolean()->defaultValue(false)->notNull()->after('material_id'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем колонку is_alternative из таблицы product_ingredients
        $this->dropColumn('product_ingredients', 'is_alternative');
    }
}
