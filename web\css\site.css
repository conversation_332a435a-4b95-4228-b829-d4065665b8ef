main>.container {
    padding: 70px 15px 20px;
}

.footer {
    background-color: #f5f5f5;
    font-size: .9em;
    height: 60px;
}

.footer>.container {
    padding-right: 15px;
    padding-left: 15px;
}

.not-set {
    color: #c55;
    font-style: italic;
}

/* add sorting icons to gridview sort links */
a.asc:after,
a.desc:after {
    content: '';
    left: 3px;
    display: inline-block;
    width: 0;
    height: 0;
    border: solid 5px transparent;
    margin: 4px 4px 2px 4px;
    background: transparent;
}

a.asc:after {
    border-bottom: solid 7px #212529;
    border-top-width: 0;
}

a.desc:after {
    border-top: solid 7px #212529;
    border-bottom-width: 0;
}

.grid-view th {
    white-space: nowrap;
}

.hint-block {
    display: block;
    margin-top: 5px;
    color: #999;
}

.error-summary {
    color: #a94442;
    background: #fdf7f7;
    border-left: 3px solid #eed3d7;
    padding: 10px 20px;
    margin: 0 0 15px 0;
}

/* align the logout "link" (button in form) of the navbar */
.nav li>form>button.logout {
    padding-top: 7px;
    color: rgba(255, 255, 255, 0.5);
}

.places .row:nth-child(even) .col-2 {
    background: #F1F1F1;
}

.places .row:nth-child(even) .col-10 {
    background: #F1F1F1;
}

@media(max-width:767px) {
    .nav li>form>button.logout {
        display: block;
        text-align: left;
        width: 100%;
        padding: 10px 0;
    }
}

.nav>li>form>button.logout:focus,
.nav>li>form>button.logout:hover {
    text-decoration: none;
    color: rgba(255, 255, 255, 0.75);
}

.nav>li>form>button.logout:focus {
    outline: none;
}

.card {
    padding: 10px;
    margin-top: 10px;
}

.btn {
    line-height: 30px !important;
}

/* Стили для выпадающего меню */
.nav-collapse .navbar-nav li.active>a.has-dropdown {
    color: #6777ef !important;
    font-weight: 600;
}

.nav-collapse .navbar-nav .dropdown-menu {
    position: absolute;
    background: white;
    border-radius: 3px;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.03);
    border: 1px solid #f9f9f9;
    min-width: 200px;
    z-index: 1000;
    display: none;
}

.nav-collapse .navbar-nav .dropdown-menu li a {
    padding: 10px 20px;
    display: block;
    color: #6c757d;
    white-space: nowrap;
    transition: all 0.3s;
}

.nav-collapse .navbar-nav .dropdown-menu li a:hover {
    background: #f8f9fa;
    color: #6777ef;
    padding-left: 25px;
}

.nav-collapse .navbar-nav li.active .dropdown-menu {
    display: block;
}

.bonus-row {
    background-color: #f8f9fa;
    color: #666;
}