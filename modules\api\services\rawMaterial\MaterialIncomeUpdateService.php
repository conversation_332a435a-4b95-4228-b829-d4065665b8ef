<?php

namespace app\modules\api\services\rawMaterial;

use Yii;
use app\common\models\Invoice;
use app\common\models\InvoiceDetail;
use app\common\models\MaterialStorage;
use app\common\models\MaterialStorageHistory;
use app\common\models\Material;
use app\common\models\ActionLogger;
use app\modules\api\models\IncomeRawMaterialForm;
use yii\base\Component;

/**
 * Сервис для обновления накладных с синхронизацией склада
 */
class MaterialIncomeUpdateService extends Component
{
    /**
     * Получить данные накладной для редактирования
     * 
     * @param int $invoiceId ID накладной
     * @return array Результат операции
     */
    public function getInvoiceForUpdate($invoiceId)
    {
        try {
            $invoice = Invoice::find()
                ->select([
                    'invoice.*',
                    'supplier.full_name as supplier_name'
                ])
                ->leftJoin('supplier', 'supplier.id = invoice.supplier_id')
                ->where(['invoice.id' => $invoiceId])
                ->andWhere(['invoice.accepted_at' => null])
                ->andWhere(['invoice.accept_user_id' => null])
                ->andWhere(['invoice.deleted_at' => null])
                ->asArray()
                ->one();

            if (!$invoice) {
                return [
                    'success' => false,
                    'message' => 'Накладная не найдена или уже принята',
                    'data' => null
                ];
            }

            $materials = InvoiceDetail::find()
                ->select([
                    'invoice_detail.*',
                    'material.name as material_name',
                    'material.unit_type'
                ])
                ->leftJoin('material', 'material.id = invoice_detail.material_id')
                ->where(['invoice_id' => $invoiceId])
                ->andWhere(['IS', 'invoice_detail.deleted_at', null])
                ->asArray()
                ->all();

            // Добавляем название единицы измерения для каждого материала
            foreach ($materials as &$material) {
                $material['unit_type_name'] = Material::getUnitTypeName($material['unit_type']);
            }

            return [
                'success' => true,
                'message' => 'Данные накладной получены',
                'data' => [
                    'invoice' => $invoice,
                    'materials' => $materials
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Ошибка получения данных накладной',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Обновить накладную с синхронизацией склада
     * 
     * @param int $invoiceId ID накладной
     * @param IncomeRawMaterialForm $form Валидированная форма с новыми данными
     * @return array Результат операции
     */
    public function updateMaterialIncome($invoiceId, IncomeRawMaterialForm $form)
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            // Проверяем существование накладной
            $invoice = Invoice::find()
                ->where(['id' => $invoiceId])
                ->andWhere(['accepted_at' => null])
                ->andWhere(['deleted_at' => null])
                ->andWhere(['accept_user_id' => null])
                ->one();

            if (!$invoice) {
                throw new \Exception('Накладная не найдена или уже принята');
            }

            // Получаем старые детали накладной
            $oldDetails = InvoiceDetail::find()
                ->where(['invoice_id' => $invoiceId])
                ->andWhere(['deleted_at' => null])
                ->indexBy('material_id')
                ->all();

            // Откатываем старые изменения склада
            $this->revertOldStorageChanges($oldDetails);

            // Обновляем данные накладной
            $invoice->supplier_id = (int)$form->supplier_id;
            $invoice->description = $form->description;

            if (!$invoice->save()) {
                throw new \Exception('Ошибка обновления накладной: ' . json_encode($invoice->getErrors()));
            }

            // Обрабатываем новые материалы
            $updatedMaterialIds = [];
            foreach ($form->materials as $material) {
                $materialId = $material['material_id'];
                $updatedMaterialIds[] = $materialId;

                if (isset($oldDetails[$materialId])) {
                    // Обновляем существующую деталь
                    $invoiceDetail = $oldDetails[$materialId];
                    $invoiceDetail->price = $material['price'] ?? null;
                    $invoiceDetail->quantity = $material['quantity'];
                    $invoiceDetail->remainder_quantity = $material['quantity'];
                } else {
                    // Создаем новую деталь
                    $invoiceDetail = new InvoiceDetail();
                    $invoiceDetail->invoice_id = $invoice->id;
                    $invoiceDetail->material_id = $materialId;
                    $invoiceDetail->price = $material['price'] ?? null;
                    $invoiceDetail->quantity = $material['quantity'];
                    $invoiceDetail->remainder_quantity = $material['quantity'];
                    $invoiceDetail->created_at = date('Y-m-d H:i:s');
                }

                if (!$invoiceDetail->save()) {
                    throw new \Exception('Ошибка сохранения деталей накладной');
                }

                // Обновляем склад для нового количества
                $this->updateMaterialStorage($material, $invoiceDetail->id);
            }

            // Удаляем детали, которых нет в новых данных
            foreach ($oldDetails as $materialId => $detail) {
                if (!in_array($materialId, $updatedMaterialIds)) {
                    $detail->delete();
                }
            }

            // Логируем действие
            $this->logAction($invoice, $form);

            $transaction->commit();

            return [
                'success' => true,
                'message' => 'Накладная успешно обновлена'
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => 'Ошибка при обновлении накладной',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Откатить старые изменения склада
     *
     * @param array $oldDetails Старые детали накладной
     * @throws \Exception
     */
    private function revertOldStorageChanges($oldDetails)
    {
        foreach ($oldDetails as $detail) {
            // Находим записи истории для этой детали
            $historyRecords = MaterialStorageHistory::find()
                ->where(['invoice_detail_id' => $detail->id])
                ->andWhere(['type' => MaterialStorageHistory::TYPE_INCOME])
                ->all();

            foreach ($historyRecords as $history) {
                // Уменьшаем количество на складе (откатываем поступление)
                $storage = MaterialStorage::findOne($history->material_storage_id);
                if ($storage) {
                    $storage->quantity -= $history->quantity;
                    $storage->updated_at = date('Y-m-d H:i:s');

                    if (!$storage->save()) {
                        throw new \Exception('Ошибка отката склада: ' . json_encode($storage->errors));
                    }
                }

                // Удаляем старую запись истории
                if (!$history->delete()) {
                    throw new \Exception('Ошибка удаления старой записи истории: ' . json_encode($history->errors));
                }
            }
        }
    }

    /**
     * Обновить склад материалов
     * 
     * @param array $material
     * @param int $invoiceDetailId
     * @throws \Exception
     */
    private function updateMaterialStorage($material, $invoiceDetailId)
    {
        // Ищем существующую запись склада за сегодня
        $storage = MaterialStorage::find()
            ->where(['material_id' => $material['material_id']])
            ->andWhere(['>=', 'updated_at', date('Y-m-d 00:00:00')])
            ->andWhere(['<=', 'updated_at', date('Y-m-d 23:59:59')])
            ->one();

        if (!$storage) {
            $storage = new MaterialStorage();
            $storage->material_id = $material['material_id'];
            $storage->created_at = date('Y-m-d H:i:s');
            $storage->quantity = 0;
        }

        $storage->quantity += $material['quantity'];
        $storage->updated_at = date('Y-m-d H:i:s');

        if (!$storage->save()) {
            throw new \Exception('Ошибка сохранения склада: ' . json_encode($storage->errors));
        }

        // Создаем запись истории
        $history = new MaterialStorageHistory();
        $history->material_storage_id = $storage->id;
        $history->material_id = $material['material_id'];
        $history->quantity = $material['quantity'];
        $history->created_at = date('Y-m-d H:i:s');
        $history->add_user_id = Yii::$app->user->id;
        $history->type = MaterialStorageHistory::TYPE_INCOME;
        $history->invoice_detail_id = $invoiceDetailId;

        if (!$history->save()) {
            throw new \Exception('Ошибка сохранения истории: ' . json_encode($history->errors));
        }
    }

    /**
     * Логировать действие
     * 
     * @param Invoice $invoice
     * @param IncomeRawMaterialForm $form
     */
    private function logAction($invoice, $form)
    {
        ActionLogger::actionLog(
            'update_raw_material',
            'invoice',
            $invoice->id,
            [
                'invoice_number' => $invoice->invoice_number,
                'supplier_id' => $invoice->supplier_id,
                'total_amount' => $invoice->total_amount,
                'updated_materials' => array_map(function($material) {
                    return [
                        'material_id' => $material['material_id'],
                        'quantity' => $material['quantity']
                    ];
                }, $form->materials)
            ]
        );
    }
}
