<?php

use yii\db\Migration;

class m250613_130000_create_equipment_photos_table extends Migration
{
    public function safeUp()
    {
        $this->createTable('equipment_photos', [
            'id' => $this->primaryKey(),
            'equipment_id' => $this->integer()->notNull(),
            'photo_path' => $this->string(255)->notNull(),
            'is_main' => $this->boolean()->defaultValue(false),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);

        $this->addForeignKey(
            'fk-equipment_photos-equipment_id',
            'equipment_photos',
            'equipment_id',
            'equipment',
            'id',
            'CASCADE'
        );

        $this->createIndex(
            'idx-equipment_photos-equipment_id',
            'equipment_photos',
            'equipment_id'
        );
    }

    public function safeDown()
    {
        $this->dropForeignKey('fk-equipment_photos-equipment_id', 'equipment_photos');
        $this->dropIndex('idx-equipment_photos-equipment_id', 'equipment_photos');
        $this->dropTable('equipment_photos');
    }
}
