<?php

namespace app\modules\api\services\manufacter;

use Yii;
use app\common\models\ActionLogger;
use app\common\models\Material;
use app\common\models\MaterialProduction;
use app\common\models\Product;
use app\common\models\ProductDefect;
use app\common\models\ProductIngredients;
use app\common\models\ProductStorage;
use app\common\models\ProductStorageHistory;
use app\common\models\ProductStorageHistoryMaterials;
use yii\base\Component;

/**
 * Сервис для обновления выпущенной продукции
 */
class UpdateReleaseFinishedProductService extends Component
{
    /**
     * Обновление выпущенной продукции
     *
     * @param int $productStorageId ID записи о выпуске продукции
     * @param int $productId ID продукта
     * @param int|float $quantity Количество продукта
     * @return array Результат операции
     * @throws \Exception
     */
    public function updateReleaseFinishedProduct($productStorageId, $productId, $quantity)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $productStorage = ProductStorage::findOne($productStorageId);
            if (!$productStorage) {
                throw new \Exception('record_not_found');
            }

            // Проверяем, не подтверждена ли запись
            if ($productStorage->accepted_at !== null) {
                throw new \Exception('dont_update_accepted_record');
            }

            // Проверяем, не удалена ли запись
            if ($productStorage->deleted_at !== null) {
                throw new \Exception('dont_update_deleted_record');
            }

            // Проверяем, изменились ли параметры продукта
            $parametersChanged = false;
            $oldProductId = $productStorage->product_id;
            $oldQuantity = $productStorage->quantity;

            // Проверяем изменение типа продукта
            if ($oldProductId != $productId) {
                $parametersChanged = true;
            }

            // Проверяем изменение количества продукта
            // Используем epsilon для сравнения с плавающей точкой
            $epsilon = 0.0001;
            if (abs($oldQuantity - $quantity) > $epsilon) {
                $parametersChanged = true;
            }



            // Получаем продукты для переупаковки
            $repackagingProducts = $this->getRepackagingProducts($productId);
            $repackagingQuantity = (float)$this->calculateRepackagingQuantity($repackagingProducts);

            // Если есть продукты на переупаковке, уменьшаем количество для производства
            $originalQuantity = (float)$quantity;
            $newProductionQuantity = (float)$quantity;

            if ($repackagingQuantity > 0) {
                // Если количество на переупаковке больше или равно требуемому, не нужно производить новые
                // Иначе производим только разницу
                $newProductionQuantity = ($repackagingQuantity >= $originalQuantity)
                    ? 0 // Явно устанавливаем в 0, а не вычитаем
                    : $originalQuantity - $repackagingQuantity;
            }

            // Возвращаем материалы для старого продукта
            // ИСПРАВЛЕНИЕ: Возвращаем материалы только при изменении продукта или уменьшении количества
            if ($oldProductId != $productId || $oldQuantity > $quantity) {
                $this->returnMaterialsForOldProduct($productStorage);
            }

            // ИСПРАВЛЕНИЕ: Инициализируем переменную для материалов
            $usedMaterials = [];

            // Расходуем материалы только если нужно произвести новые продукты
            if ($newProductionQuantity > $epsilon) {
                $usedMaterials = $this->prepareUsedMaterials($productId, $newProductionQuantity);

                // Списываем материалы (основные или альтернативные)
                $this->deductMaterials($usedMaterials);
            }

            // Создаем запись в истории склада для изменения
            $this->createStorageHistoryForUpdate($productStorage, $productId, $quantity, $usedMaterials);

            // Обновляем существующую запись вместо создания новой
            $productStorage->product_id = $productId;
            $productStorage->quantity = $quantity;
            $productStorage->updated_date = date('Y-m-d H:i:s');

            if (!$productStorage->save()) {
                throw new \Exception('Ошибка при обновлении записи: ' . json_encode($productStorage->getErrors()));
            }


            // Обрабатываем переупакованные продукты
            if ($repackagingQuantity > 0) {
                $this->processRepackagingProducts($productStorage, $repackagingProducts);
            }

            // Логируем обновление выпуска продукции
            $this->logUpdateReleaseFinishedProduct($productStorage,  $repackagingQuantity, $originalQuantity, $newProductionQuantity, $repackagingProducts);


            $transaction->commit();
            return [
                'success' => true,
                'message' => 'Запись успешно обновлена',
                'product_storage_id' => $productStorage->id
            ];
        } catch (\Exception $e) {
            $transaction->rollBack();

            return [
                'success' => false,
                'message' => 'Ошибка при обновлении записи',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Получение продуктов для переупаковки
     *
     * @param int $productId ID продукта
     * @return array Продукты для переупаковки
     */
    protected function getRepackagingProducts($productId)
    {
        return ProductDefect::find()
            ->where([
                'product_id' => $productId,
                'is_repackaging' => true,
                'deleted_at' => null
            ])
            ->andWhere(['IS NOT', 'accepted_at', null])
            ->andWhere(['IS NOT', 'accepted_user_id', null])
            ->all();
    }

    /**
     * Расчет количества продуктов для переупаковки
     *
     * @param array $repackagingProducts Продукты для переупаковки
     * @return float Количество продуктов для переупаковки
     */
    protected function calculateRepackagingQuantity($repackagingProducts)
    {
        $repackagingQuantity = 0;
        foreach ($repackagingProducts as $repackagingProduct) {
            $repackagingQuantity += $repackagingProduct->quantity;
        }
        return $repackagingQuantity;
    }

    /**
     * Возвращает материалы для старого продукта
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     * @throws \Exception
     */
    protected function returnMaterialsForOldProduct($productStorage)
    {
        // Если записи нет или количество <= 0, нечего возвращать
        if (!$productStorage || $productStorage->quantity <= 0) {
            return;
        }

        // ИСПРАВЛЕНИЕ: Получаем количество переупакованных продуктов
        $repackagingProducts = $this->getRepackagingProducts($productStorage->product_id);
        $repackagingQuantity = $this->calculateRepackagingQuantity($repackagingProducts);

        // Рассчитываем количество материалов для возврата (только за новые продукты)
        $quantityForMaterialReturn = max(0, $productStorage->quantity - $repackagingQuantity);

        // Получаем все записи истории склада для этого product_storage
        $storageHistories = ProductStorageHistory::find()
            ->where(['product_storage_id' => $productStorage->id])
            ->all();

        if (empty($storageHistories)) {
            // Если история не найдена, возвращаем материалы на основе ингредиентов продукта
            // ИСПРАВЛЕНИЕ: Передаем количество для возврата (исключая переупакованные)
            $this->returnMaterialsBasedOnIngredients($productStorage, $quantityForMaterialReturn);
            return;
        }

        // Для каждой записи истории получаем все использованные материалы
        foreach ($storageHistories as $history) {
            // Получаем материалы из новой таблицы ProductStorageHistoryMaterials
            $historyMaterials = ProductStorageHistoryMaterials::find()
                ->where(['product_storage_history_id' => $history->id])
                ->all();

            // Если есть данные о списанных материалах, возвращаем их
            if (!empty($historyMaterials)) {
                foreach ($historyMaterials as $material) {
                    $this->returnMaterialToProduction($material->material_id, (float)$material->quantity);
                }
            } else {
                // Если данных в новой таблице нет (старые записи), проверяем data в ProductStorageHistory
                if (!empty($history->data)) {
                    $historyData = json_decode($history->data, true);

                    // Возвращаем основные материалы
                    if (isset($historyData['materials']) && is_array($historyData['materials'])) {
                        foreach ($historyData['materials'] as $materialId => $quantity) {
                            $this->returnMaterialToProduction($materialId, (float)$quantity);
                        }
                    }

                    // Возвращаем альтернативные материалы
                    if (isset($historyData['alternative_materials']) && is_array($historyData['alternative_materials'])) {
                        foreach ($historyData['alternative_materials'] as $materialId => $quantity) {
                            $this->returnMaterialToProduction($materialId, (float)$quantity);
                        }
                    }
                }
            }
        }
    }

    /**
     * Возвращает материалы на основе ингредиентов продукта
     * Используется как запасной вариант, если нет истории использования материалов
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     * @param float|null $quantityToReturn Количество для возврата (если null, используется quantity из productStorage)
     * @throws \Exception
     */
    protected function returnMaterialsBasedOnIngredients($productStorage, $quantityToReturn = null)
    {
        // ИСПРАВЛЕНИЕ: Получаем информацию о продукте для учета размера блока
        $product = Product::findOne([
            'id' => $productStorage->product_id,
            'deleted_at' => null
        ]);

        if (!$product) {
            return; // Если продукт не найден, нечего возвращать
        }

        // Получаем основные ингредиенты продукта
        $ingredients = ProductIngredients::find()
            ->where([
                'product_id' => $productStorage->product_id,
                'end_date' => '9999-12-31'
            ])
            ->all();

        if (empty($ingredients)) {
            return; // Если ингредиентов нет, нечего возвращать
        }

        foreach ($ingredients as $ingredient) {
            $materialId = $ingredient->material_id;
            // ИСПРАВЛЕНИЕ: Штучный выпуск продукции - соотношение 1:1
            // Возвращаем ровно столько единиц материала, сколько было штук продукции
            // Учитываем переданное количество для возврата (исключая переупакованные)
            $materialQuantity = $quantityToReturn !== null ? $quantityToReturn : $productStorage->quantity;

            // Возвращаем материал в производство
            $this->returnMaterialToProduction($materialId, $materialQuantity);
        }
    }

    /**
     * Возвращает материал в производство
     *
     * @param int $materialId ID материала
     * @param float $quantity Количество для возврата
     * @throws \Exception
     */
    protected function returnMaterialToProduction($materialId, $quantity)
    {
        $materialProduction = MaterialProduction::find()
            ->where([
                'material_id' => $materialId,
                'deleted_at' => null,
                'DATE(created_at)' => date('Y-m-d')
            ])
            ->one();

        if (!$materialProduction) {
            $materialProduction = new MaterialProduction();
            $materialProduction->material_id = $materialId;
            $materialProduction->quantity = 0;
            $materialProduction->created_at = date('Y-m-d H:i:s');
        }

        $materialProduction->quantity += $quantity;
        if (!$materialProduction->save()) {
            throw new \Exception('Ошибка при возврате материала в производство');
        }
    }

    /**
     * Подготавливает массив используемых материалов
     *
     * @param int $productId ID продукта
     * @param float $newProductionQuantity Количество для производства
     * @return array Массив используемых материалов
     * @throws \Exception
     */
    protected function prepareUsedMaterials($productId, $newProductionQuantity)
    {
        // ИСПРАВЛЕНИЕ: Получаем информацию о продукте для учета размера блока
        $product = Product::findOne([
            'id' => $productId,
            'deleted_at' => null
        ]);

        if (!$product) {
            throw new \Exception('Продукт не найден');
        }

        // Получаем основные ингредиенты продукта вместе с информацией о материалах
        $productIngredients = ProductIngredients::find()
            ->select(['pi.*', 'm.name as material_name', 'm.unit_type', 'm.category_id'])
            ->from(['pi' => ProductIngredients::tableName()])
            ->leftJoin(['m' => Material::tableName()], 'm.id = pi.material_id')
            ->where([
                'pi.product_id' => $productId,
                'pi.end_date' => '9999-12-31',
                'pi.is_alternative' => false,
                'm.deleted_at' => null
            ])
            ->all();

        if (empty($productIngredients)) {
            throw new \Exception(Yii::t('app', 'for_this_product_no_main_ingredients'));
        }

        // Получаем все альтернативные ингредиенты для продукта
        $alternativeIngredients = ProductIngredients::find()
            ->select(['pi.*', 'm.name as material_name', 'm.unit_type', 'm.category_id'])
            ->from(['pi' => ProductIngredients::tableName()])
            ->leftJoin(['m' => Material::tableName()], 'm.id = pi.material_id')
            ->where([
                'pi.product_id' => $productId,
                'pi.end_date' => '9999-12-31',
                'pi.is_alternative' => true,
                'm.deleted_at' => null
            ])
            ->all();

        // Индексируем альтернативные материалы по категории и типу единиц измерения для быстрого поиска
        $alternativesByCategory = [];
        foreach ($alternativeIngredients as $altIngredient) {
            $categoryId = $altIngredient->getAttribute('category_id') ?? 0;
            $unitType = $altIngredient->getAttribute('unit_type') ?? 0;
            $key = "{$categoryId}_{$unitType}";

            if (!isset($alternativesByCategory[$key])) {
                $alternativesByCategory[$key] = [];
            }

            $alternativesByCategory[$key][] = $altIngredient;
        }

        // Проверяем наличие материалов и используем альтернативные при необходимости
        $usedMaterials = [];
        $usedAlternatives = [];

        // Получаем информацию о доступных материалах в производстве за один запрос
        $materialIds = array_column($productIngredients, 'material_id');
        $materialProductionsQuery = MaterialProduction::find()
            ->where(['material_id' => $materialIds])
            ->andWhere(['deleted_at' => null])
            ->andWhere(['>', 'quantity', 0])
            ->orderBy(['material_id' => SORT_ASC, 'created_at' => SORT_ASC])
            ->all();

        // Группируем материалы по ID для быстрого доступа
        $materialProductionsByMaterialId = [];
        foreach ($materialProductionsQuery as $production) {
            $mid = $production->material_id;
            if (!isset($materialProductionsByMaterialId[$mid])) {
                $materialProductionsByMaterialId[$mid] = [];
            }
            $materialProductionsByMaterialId[$mid][] = $production;
        }

        foreach ($productIngredients as $ingredient) {
            $materialId = $ingredient->material_id;
            // ИСПРАВЛЕНИЕ: Штучный выпуск продукции - соотношение 1:1
            // Для выпуска N штук продукции нужно ровно N единиц материала (без учета размера блока)
            $requiredQuantity = $newProductionQuantity;

            // Проверяем наличие основного материала
            $materialProductions = $materialProductionsByMaterialId[$materialId] ?? [];
            $totalAvailable = array_sum(array_column($materialProductions, 'quantity'));

            // Если основного материала достаточно, используем только его
            if ($totalAvailable >= $requiredQuantity) {
                // Используем только основной материал
                $usedMaterials[$materialId] = [
                    'material_id' => $materialId,
                    'quantity' => $requiredQuantity,
                    'is_alternative' => false
                ];
            } else {
                // Если основного материала недостаточно, используем сколько есть,
                // а остальное берем из альтернативного материала
                $remainingRequired = $requiredQuantity - $totalAvailable;

                // Ищем альтернативный материал для оставшегося количества
                // Используем оптимизированный поиск альтернативного материала
                $alternativeMaterialId = null;
                $altTotalAvailable = 0;
                $altMaterialProductions = [];

                // Проверяем альтернативные материалы на совпадение категорий
                foreach ($alternativeIngredients as $altIngredient) {
                    // Проверяем совпадение категорий материалов
                    if ($altIngredient->getAttribute('category_id') != $ingredient->getAttribute('category_id')) {
                        continue; // Пропускаем материалы с несовпадающей категорией
                    }

                    $altMaterialId = $altIngredient->material_id;

                    // Проверяем наличие альтернативного материала в производстве
                    $altProductions = MaterialProduction::find()
                        ->where([
                            'material_id' => $altMaterialId,
                            'deleted_at' => null
                        ])
                        ->andWhere(['>', 'quantity', 0])
                        ->all();

                    $available = array_sum(array_column($altProductions, 'quantity'));

                    // Если альтернативного материала достаточно, используем его
                    if ($available >= $remainingRequired) {
                        $alternativeMaterialId = $altMaterialId;
                        $altTotalAvailable = $available;
                        $altMaterialProductions = $altProductions;
                        break;
                    }
                }

                if ($alternativeMaterialId) {
                    // Проверяем, достаточно ли альтернативного материала
                    if ($altTotalAvailable < $remainingRequired) {
                        // Если альтернативного материала недостаточно, выбрасываем исключение
                        $materialName = $ingredient->getAttribute('material_name') ?? "Материал #{$materialId}";
                        $altMaterialName = $alternativeIngredients[array_search($alternativeMaterialId, array_column($alternativeIngredients, 'material_id'))]->getAttribute('material_name') ?? "Материал #{$alternativeMaterialId}";

                        throw new \Exception(Yii::t('app', 'Not enough alternative material "{alt_name}" for "{original_name}". Available: {available}, Required: {required}', [
                            'alt_name' => $altMaterialName,
                            'original_name' => $materialName,
                            'available' => $altTotalAvailable,
                            'required' => $remainingRequired,
                        ]));
                    }
                    // Используем основной материал (сколько есть)
                    if ($totalAvailable > 0) {
                        $usedMaterials[$materialId] = [
                            'material_id' => $materialId,
                            'quantity' => $totalAvailable,
                            'is_alternative' => false
                        ];
                    }

                    // Используем альтернативный материал для оставшегося количества
                    $altMaterialKey = "alt_{$materialId}_{$alternativeMaterialId}";
                    $usedMaterials[$altMaterialKey] = [
                        'material_id' => $alternativeMaterialId,
                        'quantity' => $remainingRequired,
                        'is_alternative' => true,
                        'original_material_id' => $materialId,
                        'original_name' => $ingredient->getAttribute('material_name') ?? "Материал #{$materialId}",
                        'alternative_name' => $alternativeIngredients[array_search($alternativeMaterialId, array_column($alternativeIngredients, 'material_id'))]->getAttribute('material_name') ?? "Материал #{$alternativeMaterialId}"
                    ];

                    // Сохраняем информацию об использовании альтернативы для логирования
                    $usedAlternatives[$materialId] = [
                        'alt_id' => $alternativeMaterialId,
                        'original_name' => $ingredient->getAttribute('material_name') ?? "Материал #{$materialId}",
                        'alternative_name' => $alternativeIngredients[array_search($alternativeMaterialId, array_column($alternativeIngredients, 'material_id'))]->getAttribute('material_name') ?? "Материал #{$alternativeMaterialId}",
                        'original_quantity' => $totalAvailable,
                        'alternative_quantity' => $remainingRequired
                    ];
                } else {
                    // Если альтернативы нет, выбрасываем исключение
                    $materialName = $ingredient->getAttribute('material_name') ?? "Материал #{$materialId}";
                    throw new \Exception(Yii::t('app', 'Not enough material "{material_name}" in production and no alternative available. Available: {available}, Required: {required}', [
                        'material_name' => $materialName,
                        'available' => $totalAvailable,
                        'required' => $requiredQuantity,
                    ]));
                }
            }
        }

        // Логируем использование альтернативных материалов
        if (!empty($usedAlternatives)) {
            ActionLogger::actionLog(
                'use_alternative_materials_update',
                'product_storage',
                null,
                [
                    'product_id' => $productId,
                    'alternatives' => $usedAlternatives
                ]
            );
        }

        return $usedMaterials;
    }

    /**
     * Списание материалов
     *
     * @param array $usedMaterials Использованные материалы
     * @throws \Exception
     */
    protected function deductMaterials($usedMaterials)
    {
        foreach ($usedMaterials as $originalMaterialId => $materialData) {
            $materialId = $materialData['material_id'];
            $requiredQuantity = $materialData['quantity'];

            $materialProductions = MaterialProduction::find()
                ->where([
                    'material_id' => $materialId,
                    'deleted_at' => null
                ])
                ->andWhere(['>', 'quantity', 0])
                ->orderBy(['created_at' => SORT_ASC])
                ->all();

            $remainingQuantity = $requiredQuantity;
            foreach ($materialProductions as $production) {
                if ($remainingQuantity <= 0) break;

                $quantityToDeduct = min($production->quantity, $remainingQuantity);
                $production->quantity -= $quantityToDeduct;
                $remainingQuantity -= $quantityToDeduct;

                if (!$production->save()) {
                    throw new \Exception('Ошибка при обновлении количества в производстве');
                }
            }
        }
    }


    /**
     * Создает записи в истории склада при обновлении продукта
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     * @param int $newProductId Новый ID продукта
     * @param float $newQuantity Новое количество
     * @param array $usedMaterials Использованные материалы
     * @throws \Exception
     */
    protected function createStorageHistoryForUpdate($productStorage, $newProductId, $newQuantity, $usedMaterials = [])
    {
        // Если изменился тип продукта или количество, создаем записи в истории
        $oldProductId = $productStorage->product_id;
        $oldQuantity = $productStorage->quantity;
        $epsilon = 0.0001;

        // Если изменился тип продукта или количество
        if ($oldProductId != $newProductId || abs($oldQuantity - $newQuantity) > $epsilon) {
            // Удаляем старые записи истории для этого product_storage
            $oldHistories = ProductStorageHistory::find()
                ->where(['product_storage_id' => $productStorage->id])
                ->all();

            foreach ($oldHistories as $oldHistory) {
                // Полностью удаляем запись, а не просто помечаем как удаленную
                $oldHistory->delete();
            }

            // Запись в историю нового количества (приход)
            $storageHistoryIn = new ProductStorageHistory();
            $storageHistoryIn->product_storage_id = $productStorage->id;
            $storageHistoryIn->product_id = $newProductId;
            $storageHistoryIn->quantity = $newQuantity;
            $storageHistoryIn->type = ProductStorageHistory::TYPE_INCOME;
            $storageHistoryIn->created_at = date('Y-m-d H:i:s');
            $storageHistoryIn->add_user_id = Yii::$app->user->getId();

            if (!$storageHistoryIn->save()) {
                throw new \Exception('Ошибка сохранения истории склада (приход при обновлении)');
            }

            // Сохраняем информацию об использованных материалах
            if (!empty($usedMaterials)) {
                $this->saveUsedMaterialsHistory($storageHistoryIn->id, $usedMaterials);
            }
        }
    }

    /**
     * Помечает старую запись о выпуске продукции как удаленную
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     * @throws \Exception
     */
    protected function markOldProductStorageAsDeleted($productStorage)
    {
        $oldProductStorage = $productStorage;
        $oldProductStorage->deleted_at = date('Y-m-d H:i:s');

        if (!$oldProductStorage->save()) {
            throw new \Exception('Ошибка при удалении старой записи: ' . json_encode($oldProductStorage->getErrors()));
        }
    }

    /**
     * Создает новую запись о выпуске продукции
     *
     * @param int $productId ID продукта
     * @param float $quantity Количество
     * @return ProductStorage Новая запись о выпуске продукции
     * @throws \Exception
     */
    protected function createNewProductStorage($productId, $quantity)
    {
        $productStorage = new ProductStorage();
        $productStorage->product_id = $productId;
        $productStorage->add_user_id = Yii::$app->user->getId();
        $productStorage->quantity = $quantity;
        $productStorage->enter_date = date('Y-m-d H:i:s');

        if (!$productStorage->save()) {
            throw new \Exception('Ошибка при создании новой записи: ' . json_encode($productStorage->getErrors()));
        }

        return $productStorage;
    }

    /**
     * Создает записи в истории склада
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     * @param int $productId ID продукта
     * @param float $quantity Количество
     * @throws \Exception
     */
    protected function createStorageHistories($productStorage, $productId, $quantity)
    {

        // Запись в историю нового количества (приход)
        $storageHistoryIn = new ProductStorageHistory();
        $storageHistoryIn->product_storage_id = $productStorage->id;
        $storageHistoryIn->product_id = $productId;
        $storageHistoryIn->quantity = $quantity;
        $storageHistoryIn->type = ProductStorageHistory::TYPE_INCOME;
        $storageHistoryIn->created_at = date('Y-m-d H:i:s');
        $storageHistoryIn->add_user_id = Yii::$app->user->getId();

        if (!$storageHistoryIn->save()) {
            throw new \Exception('Ошибка сохранения истории склада (приход)');
        }
    }

    /**
     * Обрабатывает переупакованные продукты
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     * @param array $repackagingProducts Продукты для переупаковки
     * @throws \Exception
     */
    protected function processRepackagingProducts($productStorage, $repackagingProducts)
    {
        // Проверяем, есть ли уже использованные продукты на переупаковке для этой записи
        // Ищем записи, которые были помечены как удаленные (использованные ранее)
        $usedRepackagingProducts = ProductDefect::find()
            ->where([
                'product_id' => $productStorage->product_id,
                'is_repackaging' => true,
            ])
            ->andWhere(['IS NOT', 'deleted_at', null])  // Ищем удаленные (использованные)
            ->andWhere(['IS NOT', 'accepted_at', null])  // Которые были подтверждены
            ->andWhere(['IS NOT', 'accepted_user_id', null])  // И имеют пользователя
            ->all();

        // Восстанавливаем ранее использованные продукты
        foreach ($usedRepackagingProducts as $usedProduct) {
            $usedProduct->deleted_at = null;
            if (!$usedProduct->save()) {
                throw new \Exception('Ошибка при восстановлении записи о переупаковке');
            }
        }

        // Теперь используем новые продукты на переупаковке
        foreach ($repackagingProducts as $repackagingProduct) {
            $repackagingProduct->deleted_at = date('Y-m-d H:i:s');
            if (!$repackagingProduct->save()) {
                throw new \Exception('Ошибка при обновлении записи о переупаковке');
            }
        }
    }

    /**
     * Логирует обновление выпуска продукции
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     * @param float $repackagingQuantity Количество продуктов для переупаковки
     * @param float $originalQuantity Исходное количество
     * @param float $newProductionQuantity Количество для производства
     * @param array $repackagingProducts Продукты для переупаковки
     */
    /**
     * Сохраняет информацию об использованных материалах в истории склада
     *
     * @param int $storageHistoryId ID записи истории склада
     * @param array $usedMaterials Информация о списанных материалах
     * @throws \Exception
     */
    protected function saveUsedMaterialsHistory($storageHistoryId, $usedMaterials)
    {
        foreach ($usedMaterials as $materialKey => $materialData) {
            $material = new ProductStorageHistoryMaterials();
            $material->product_storage_history_id = $storageHistoryId;
            $material->material_id = $materialData['material_id'];
            $material->quantity = $materialData['quantity'];
            $material->created_at = date('Y-m-d H:i:s');

            if (isset($materialData['is_alternative']) && $materialData['is_alternative']) {
                $material->is_alternative = true;
                if (isset($materialData['original_material_id'])) {
                    $material->original_material_id = $materialData['original_material_id'];
                }
            } else {
                $material->is_alternative = false;
            }

            // Проверяем соответствие модели перед сохранением
            if (!$material->validate()) {
                throw new \Exception('Ошибка валидации при сохранении истории материалов: ' . json_encode($material->getErrors()));
            }

            if (!$material->save()) {
                throw new \Exception('Ошибка при сохранении истории материалов');
            }
        }
    }

    protected function logUpdateReleaseFinishedProduct($productStorage, $repackagingQuantity, $originalQuantity, $newProductionQuantity, $repackagingProducts)
    {
        $logData = [
            'product_id' => $productStorage->product_id,
            'quantity' => $productStorage->quantity,
        ];

        if (isset($repackagingQuantity) && $repackagingQuantity > 0) {
            $logData['repackaging_quantity'] = $repackagingQuantity;
            $logData['original_quantity'] = $originalQuantity;
            $logData['new_production_quantity'] = $newProductionQuantity;
            $logData['repackaging_product_ids'] = array_map(fn($product) => $product->id, $repackagingProducts);
        }

        ActionLogger::actionLog(
            'update_release_finished_product',
            'product_storage',
            $productStorage->id,
            $logData
        );
    }
}
