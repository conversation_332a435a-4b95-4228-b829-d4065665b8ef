<?php

use yii\bootstrap5\Html;
use app\common\models\Sales;
use yii\web\JqueryAsset;

// Определяем переменные для пагинации в начале файла
$pageSize = isset($pageSize) ? $pageSize : 10;
$salesPage = isset($salesPage) ? $salesPage : 1;
$totalPages = isset($totalSales) ? ceil($totalSales / $pageSize) : 1;
$this->title = Yii::t('app', 'sell_history');

JqueryAsset::register($this);
?>

<style>
    .pagination {
        margin: 20px 0;
        display: flex;
        justify-content: center;
        gap: 5px;
    }

    .pagination .page-item {
        list-style: none;
    }

    .pagination .page-link {
        padding: 8px 12px;
        border: 1px solid #ddd;
        color: #333;
        text-decoration: none;
        border-radius: 4px;
    }

    .pagination .page-item.active .page-link {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .pagination .page-link:hover {
        background-color: #f8f9fa;
    }

    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        pointer-events: none;
        background-color: #fff;
        border-color: #ddd;
    }

    /* Дополнительные стили для улучшения отступов */
    .sales-header {
        padding: 0.5rem 0;
        margin-bottom: 1.5rem;
    }

    .sales-header .form-group {
        margin-bottom: 0;
    }

    .sales-header .btn {
        white-space: nowrap;
        min-width: 100px;
    }

    .sales-filters {
        display: flex;
        align-items: end;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .sales-filters .form-group {
        flex: 0 0 auto;
    }

    .sales-filters .form-control {
        min-width: 180px;
    }

    .sales-buttons {
        display: flex;
        gap: 0.5rem;
        flex: 0 0 auto;
    }

    .table-responsive {
        margin-top: 1rem;
    }

    /* Улучшение отступов для мобильных устройств */
    @media (max-width: 768px) {
        .sales-filters {
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;
        }

        .sales-filters .form-control {
            min-width: 100%;
        }

        .sales-buttons {
            justify-content: stretch;
        }

        .sales-buttons .btn {
            flex: 1;
        }
    }

    /* Стили для кастомного datetime picker */
    .datetime-container {
        position: relative;
    }

    .custom-datetime-picker {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        display: none;
        padding: 15px;
        min-width: 300px;
    }

    .custom-datetime-picker.show {
        display: block;
    }

    .datetime-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .datetime-nav-btn {
        background: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 3px;
        padding: 5px 10px;
        cursor: pointer;
        font-size: 14px;
    }

    .datetime-nav-btn:hover {
        background: #e9ecef;
    }

    .datetime-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 2px;
        margin-bottom: 15px;
    }

    .datetime-cell {
        padding: 8px;
        text-align: center;
        cursor: pointer;
        border-radius: 3px;
        font-size: 14px;
    }

    .datetime-cell:hover {
        background: #e9ecef;
    }

    .datetime-cell.selected {
        background: #007bff;
        color: white;
    }

    .datetime-cell.today {
        background: #ffc107;
        color: black;
    }

    .datetime-cell.other-month {
        color: #6c757d;
    }

    .time-inputs {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-bottom: 15px;
    }

    .time-inputs input {
        width: 60px;
        padding: 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
        text-align: center;
    }

    .datetime-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .datetime-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }

    .datetime-btn.primary {
        background: #007bff;
        color: white;
    }

    .datetime-btn.primary:hover {
        background: #0056b3;
    }

    .datetime-btn.secondary {
        background: #6c757d;
        color: white;
    }

    .datetime-btn.secondary:hover {
        background: #545b62;
    }
</style>

<div class="card-body">


    <div class="sales-header">
        <div class="row align-items-center">
            <div class="col-md-4">
                <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
            </div>
            <div class="col-md-8">
                <div class="sales-filters justify-content-end">
                    <div class="form-group datetime-container">
                        <input type="text" id="date-from" class="form-control custom-datetime-input" readonly
                            placeholder="Выберите дату и время"
                            value="<?= isset($_GET['date_from']) ? date('d.m.Y H:i', strtotime($_GET['date_from'])) : date('d.m.Y') . ' 00:00' ?>">
                        <div class="custom-datetime-picker" id="picker-date-from">
                            <div class="datetime-header">
                                <button type="button" class="datetime-nav-btn" data-action="prev-month">&lt;</button>
                                <span class="current-month-year"></span>
                                <button type="button" class="datetime-nav-btn" data-action="next-month">&gt;</button>
                            </div>
                            <div class="datetime-grid"></div>
                            <div class="time-inputs">
                                <label>Время:</label>
                                <input type="number" class="hour-input" min="0" max="23" value="00">
                                <span>:</span>
                                <input type="number" class="minute-input" min="0" max="59" value="00">
                            </div>
                            <div class="datetime-actions">
                                <button type="button" class="datetime-btn secondary" data-action="cancel">Отмена</button>
                                <button type="button" class="datetime-btn primary" data-action="ok">ОК</button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group datetime-container">
                        <input type="text" id="date-to" class="form-control custom-datetime-input" readonly
                            placeholder="Выберите дату и время"
                            value="<?= isset($_GET['date_to']) ? date('d.m.Y H:i', strtotime($_GET['date_to'])) : date('d.m.Y') . ' 23:59' ?>">
                        <div class="custom-datetime-picker" id="picker-date-to">
                            <div class="datetime-header">
                                <button type="button" class="datetime-nav-btn" data-action="prev-month">&lt;</button>
                                <span class="current-month-year"></span>
                                <button type="button" class="datetime-nav-btn" data-action="next-month">&gt;</button>
                            </div>
                            <div class="datetime-grid"></div>
                            <div class="time-inputs">
                                <label>Время:</label>
                                <input type="number" class="hour-input" min="0" max="23" value="23">
                                <span>:</span>
                                <input type="number" class="minute-input" min="0" max="59" value="59">
                            </div>
                            <div class="datetime-actions">
                                <button type="button" class="datetime-btn secondary" data-action="cancel">Отмена</button>
                                <button type="button" class="datetime-btn primary" data-action="ok">ОК</button>
                            </div>
                        </div>
                    </div>
                    <div class="sales-buttons">
                        <button class="btn btn-outline-secondary" type="button" id="apply-filter"><?php echo Yii::t('app', 'search') ?></button>
                        <a href="/backend/sales-invoice/index" class="btn btn-primary">
                            <?= Yii::t("app", "back_to_invoice") ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th><?= Yii::t('app', 'date') ?></th>
                    <th><?= Yii::t('app', 'client') ?></th>
                    <th><?= Yii::t('app', 'print_number') ?></th>
                    <?php foreach ($products as $product): ?>
                        <th class="text-center"><?= Html::encode($product['name']) ?></th>
                    <?php endforeach; ?>
                    <th class="text-center">Бонус</th>
                    <th><?= Yii::t('app', 'total_quantity') ?></th>
                    <th><?= Yii::t('app', 'total_sum') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($sales as $sale): ?>
                    <tr>
                        <td><?= date('d.m.Y H:i', strtotime($sale['created_at'])) ?></td>
                        <td><?= Html::encode($sale['client_name']) ?></td>
                        <td><?= Html::encode($sale['print_number'] ?? '-') ?></td>
                        <?php
                        $totalQuantity = 0;
                        $productQuantities = array_fill(0, count($products), 0);

                        // Создаем ассоциативный массив для сопоставления product_id с индексом
                        $productIndexMap = [];
                        foreach ($products as $index => $product) {
                            $productIndexMap[$product['id']] = $index;
                        }

                        foreach ($sale['products'] as $product) {
                            if (isset($productIndexMap[$product['product_id']])) {
                                $productQuantities[$productIndexMap[$product['product_id']]] = $product['quantity'];
                                $totalQuantity += $product['quantity'];
                            }
                        }

                        // Добавляем количество бонусов к общему количеству
                        if (isset($sale['bonuses']) && !empty($sale['bonuses'])) {
                            foreach ($sale['bonuses'] as $bonus) {
                                $totalQuantity += $bonus['quantity'];
                            }
                        }

                        foreach ($productQuantities as $quantity): ?>
                            <td class="text-center"><?= $quantity ?></td>
                        <?php endforeach; ?>

                        <?php
                        // Получаем бонусы для данной продажи
                        $bonusText = '';
                        if (isset($sale['bonuses']) && !empty($sale['bonuses'])) {
                            $bonusItems = [];
                            foreach ($sale['bonuses'] as $bonus) {
                                $bonusItems[] = Html::encode($bonus['product_name']) . ': ' . $bonus['quantity'];
                            }
                            $bonusText = implode('<br>', $bonusItems);
                        }
                        ?>
                        <td class="text-center" style="font-size: 12px;"><?= $bonusText ?: '-' ?></td>

                        <td class="text-center"><strong><?= $totalQuantity ?></strong></td>
                        <td class="text-center"><strong><?= number_format($sale['total_sum'], 0, '.', ' ') ?></strong></td>
                    </tr>
                <?php endforeach; ?>
                <tr>
                    <td colspan="3"><strong>Сумма</strong></td>
                    <?php
                    $grandTotal = 0;
                    $columnTotals = array_fill(0, count($products), 0);
                    $totalBonusQuantity = 0;

                    // Используем тот же маппинг для подсчета итогов
                    foreach ($sales as $sale) {
                        foreach ($sale['products'] as $product) {
                            if (isset($productIndexMap[$product['product_id']])) {
                                $columnTotals[$productIndexMap[$product['product_id']]] += $product['quantity'];
                            }
                        }

                        // Подсчитываем общее количество бонусов
                        if (isset($sale['bonuses']) && !empty($sale['bonuses'])) {
                            foreach ($sale['bonuses'] as $bonus) {
                                $totalBonusQuantity += $bonus['quantity'];
                            }
                        }

                        $grandTotal += $sale['total_sum'];
                    }

                    foreach ($columnTotals as $total): ?>
                        <td class="text-center"><strong><?= $total ?></strong></td>
                    <?php endforeach; ?>

                    <td class="text-center"><strong><?= $totalBonusQuantity ?></strong></td>

                    <td class="text-center"><strong><?= array_sum($columnTotals) + $totalBonusQuantity ?></strong></td>
                    <td class="text-center"><strong><?= number_format($grandTotal, 0, '.', ' ') ?></strong></td>
                </tr>
            </tbody>
        </table>

        <!-- Пагинация для продаж -->
        <nav aria-label="Page navigation" class="mt-3">
            <ul class="pagination justify-content-center">
                <?php if ($salesPage > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?= Html::encode(Yii::$app->urlManager->createUrl([
                                                        'backend/sales-invoice/sales',
                                                        'salesPage' => $salesPage - 1,
                                                        'date_from' => Yii::$app->request->get('date_from'),
                                                        'date_to' => Yii::$app->request->get('date_to')
                                                    ])) ?>">
                            &laquo;
                        </a>
                    </li>
                <?php endif; ?>

                <?php
                $startPage = max(1, $salesPage - 2);
                $endPage = min($totalPages, $startPage + 4);
                if ($endPage - $startPage < 4) {
                    $startPage = max(1, $endPage - 4);
                }

                if ($startPage > 1) {
                    echo '<li class="page-item"><a class="page-link" href="' . Html::encode(Yii::$app->urlManager->createUrl([
                        'backend/sales-invoice/sales',
                        'salesPage' => 1,
                        'date_from' => Yii::$app->request->get('date_from'),
                        'date_to' => Yii::$app->request->get('date_to')
                    ])) . '">1</a></li>';
                    if ($startPage > 2) {
                        echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                    }
                }

                for ($i = $startPage; $i <= $endPage; $i++): ?>
                    <li class="page-item <?= $i == $salesPage ? 'active' : '' ?>">
                        <a class="page-link" href="<?= Html::encode(Yii::$app->urlManager->createUrl([
                                                        'backend/sales-invoice/sales',
                                                        'salesPage' => $i,
                                                        'date_from' => Yii::$app->request->get('date_from'),
                                                        'date_to' => Yii::$app->request->get('date_to')
                                                    ])) ?>">
                            <?= $i ?>
                        </a>
                    </li>
                <?php endfor;

                if ($endPage < $totalPages) {
                    if ($endPage < $totalPages - 1) {
                        echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                    }
                    echo '<li class="page-item"><a class="page-link" href="' . Html::encode(Yii::$app->urlManager->createUrl([
                        'backend/sales-invoice/sales',
                        'salesPage' => $totalPages,
                        'date_from' => Yii::$app->request->get('date_from'),
                        'date_to' => Yii::$app->request->get('date_to')
                    ])) . '">' . $totalPages . '</a></li>';
                }
                ?>

                <?php if ($salesPage < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?= Html::encode(Yii::$app->urlManager->createUrl([
                                                        'backend/sales-invoice/sales',
                                                        'salesPage' => $salesPage + 1,
                                                        'date_from' => Yii::$app->request->get('date_from'),
                                                        'date_to' => Yii::$app->request->get('date_to')
                                                    ])) ?>">
                            &raquo;
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
</div>

<script>
    // Функция для обновления страницы с учетом параметров фильтра
    function updatePage(page, pageNumber) {
        // Используем нативный JavaScript вместо jQuery для AJAX запроса
        const dateFrom = document.getElementById('date-from') ? document.getElementById('date-from').value : '';
        const dateTo = document.getElementById('date-to') ? document.getElementById('date-to').value : '';

        const xhr = new XMLHttpRequest();
        xhr.open('GET', '/backend/sales-invoice/sales?salesPage=' + pageNumber +
            '&date_from=' + encodeURIComponent(dateFrom) +
            '&date_to=' + encodeURIComponent(dateTo), true);

        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.status === 'success') {
                        // Обновляем всю карточку целиком
                        document.querySelector('.card-body').innerHTML = response.content;
                    }
                } catch (e) {
                    console.error('Ошибка при разборе ответа:', e);
                }
            } else {
                console.error('AJAX Error:', xhr.statusText);
            }
        };

        xhr.onerror = function() {
            console.error('AJAX Error: Network error');
        };

        xhr.send();
    }
</script>

<script>
    // Кастомный DateTime Picker
    class CustomDateTimePicker {
        constructor(inputId, pickerId) {
            this.input = document.getElementById(inputId);
            this.picker = document.getElementById(pickerId);
            this.currentDate = new Date();
            this.selectedDate = new Date();

            this.init();
        }

        init() {
            // Парсим текущее значение поля
            if (this.input.value) {
                const parts = this.input.value.split(' ');
                if (parts.length === 2) {
                    const dateParts = parts[0].split('.');
                    const timeParts = parts[1].split(':');
                    if (dateParts.length === 3 && timeParts.length === 2) {
                        this.selectedDate = new Date(dateParts[2], dateParts[1] - 1, dateParts[0], timeParts[0], timeParts[1]);
                        this.currentDate = new Date(this.selectedDate);
                    }
                }
            }

            this.render();
            this.bindEvents();
        }

        bindEvents() {
            // Открытие picker при клике на input
            this.input.addEventListener('click', (e) => {
                e.stopPropagation();
                this.show();
            });

            // Закрытие при клике вне picker
            document.addEventListener('click', (e) => {
                if (!this.picker.contains(e.target) && e.target !== this.input) {
                    this.hide();
                }
            });

            // Предотвращаем закрытие при клике внутри picker
            this.picker.addEventListener('click', (e) => {
                e.stopPropagation();
            });

            // Навигация по месяцам
            this.picker.querySelector('[data-action="prev-month"]').addEventListener('click', (e) => {
                e.stopPropagation();
                this.currentDate.setMonth(this.currentDate.getMonth() - 1);
                this.render();
            });

            this.picker.querySelector('[data-action="next-month"]').addEventListener('click', (e) => {
                e.stopPropagation();
                this.currentDate.setMonth(this.currentDate.getMonth() + 1);
                this.render();
            });

            // Кнопки действий
            this.picker.querySelector('[data-action="ok"]').addEventListener('click', (e) => {
                e.stopPropagation();
                this.applySelection();
            });

            this.picker.querySelector('[data-action="cancel"]').addEventListener('click', (e) => {
                e.stopPropagation();
                this.hide();
            });

            // Изменение времени с валидацией
            this.picker.querySelector('.hour-input').addEventListener('change', (e) => {
                e.stopPropagation();
                let value = parseInt(e.target.value) || 0;
                if (value > 23) value = 23;
                if (value < 0) value = 0;
                e.target.value = value.toString().padStart(2, '0');
                this.selectedDate.setHours(value);
            });

            this.picker.querySelector('.minute-input').addEventListener('change', (e) => {
                e.stopPropagation();
                let value = parseInt(e.target.value) || 0;
                if (value > 59) value = 59;
                if (value < 0) value = 0;
                e.target.value = value.toString().padStart(2, '0');
                this.selectedDate.setMinutes(value);
            });

            // Валидация при вводе (ограничиваем длину)
            this.picker.querySelector('.hour-input').addEventListener('input', (e) => {
                e.stopPropagation();
                let value = e.target.value;
                if (value.length > 2) {
                    e.target.value = value.slice(0, 2);
                }
                let numValue = parseInt(e.target.value);
                if (numValue > 23) {
                    e.target.value = '23';
                }
            });

            this.picker.querySelector('.minute-input').addEventListener('input', (e) => {
                e.stopPropagation();
                let value = e.target.value;
                if (value.length > 2) {
                    e.target.value = value.slice(0, 2);
                }
                let numValue = parseInt(e.target.value);
                if (numValue > 59) {
                    e.target.value = '59';
                }
            });

            // Предотвращаем закрытие при фокусе на поля времени
            this.picker.querySelector('.hour-input').addEventListener('click', (e) => {
                e.stopPropagation();
            });

            this.picker.querySelector('.minute-input').addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        render() {
            this.renderHeader();
            this.renderCalendar();
            this.renderTime();
        }

        renderHeader() {
            const monthNames = ['Январь', 'Февраль', 'Март', 'Апрель', 'Май', 'Июнь',
                'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь'
            ];
            const header = this.picker.querySelector('.current-month-year');
            header.textContent = `${monthNames[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;
        }

        renderCalendar() {
            const grid = this.picker.querySelector('.datetime-grid');
            grid.innerHTML = '';

            // Заголовки дней недели
            const dayNames = ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс'];
            dayNames.forEach(day => {
                const cell = document.createElement('div');
                cell.className = 'datetime-cell';
                cell.style.fontWeight = 'bold';
                cell.style.backgroundColor = '#f8f9fa';
                cell.textContent = day;
                grid.appendChild(cell);
            });

            // Дни месяца
            const firstDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
            const lastDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - (firstDay.getDay() === 0 ? 6 : firstDay.getDay() - 1));

            for (let i = 0; i < 42; i++) {
                const cellDate = new Date(startDate);
                cellDate.setDate(startDate.getDate() + i);

                const cell = document.createElement('div');
                cell.className = 'datetime-cell';
                cell.textContent = cellDate.getDate();

                if (cellDate.getMonth() !== this.currentDate.getMonth()) {
                    cell.classList.add('other-month');
                }

                if (this.isSameDay(cellDate, new Date())) {
                    cell.classList.add('today');
                }

                if (this.isSameDay(cellDate, this.selectedDate)) {
                    cell.classList.add('selected');
                }

                cell.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.selectedDate.setFullYear(cellDate.getFullYear());
                    this.selectedDate.setMonth(cellDate.getMonth());
                    this.selectedDate.setDate(cellDate.getDate());
                    this.render();
                });

                grid.appendChild(cell);
            }
        }

        renderTime() {
            this.picker.querySelector('.hour-input').value = this.selectedDate.getHours().toString().padStart(2, '0');
            this.picker.querySelector('.minute-input').value = this.selectedDate.getMinutes().toString().padStart(2, '0');
        }

        isSameDay(date1, date2) {
            return date1.getDate() === date2.getDate() &&
                date1.getMonth() === date2.getMonth() &&
                date1.getFullYear() === date2.getFullYear();
        }

        show() {
            this.picker.classList.add('show');
        }

        hide() {
            this.picker.classList.remove('show');
        }

        applySelection() {
            const day = this.selectedDate.getDate().toString().padStart(2, '0');
            const month = (this.selectedDate.getMonth() + 1).toString().padStart(2, '0');
            const year = this.selectedDate.getFullYear();
            const hours = this.selectedDate.getHours().toString().padStart(2, '0');
            const minutes = this.selectedDate.getMinutes().toString().padStart(2, '0');

            this.input.value = `${day}.${month}.${year} ${hours}:${minutes}`;
            this.hide();
        }

        getISOString() {
            return this.selectedDate.toISOString().slice(0, 16);
        }
    }

    // Инициализация обработчиков для фильтрации
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализация кастомных datetime picker
        const dateFromPicker = new CustomDateTimePicker('date-from', 'picker-date-from');
        const dateToPicker = new CustomDateTimePicker('date-to', 'picker-date-to');

        // Функция для конвертации формата даты
        function convertToISOFormat(dateStr) {
            if (!dateStr) return '';
            const parts = dateStr.split(' ');
            if (parts.length !== 2) return '';

            const dateParts = parts[0].split('.');
            const timeParts = parts[1].split(':');

            if (dateParts.length !== 3 || timeParts.length !== 2) return '';

            const year = dateParts[2];
            const month = dateParts[1].padStart(2, '0');
            const day = dateParts[0].padStart(2, '0');
            const hour = timeParts[0].padStart(2, '0');
            const minute = timeParts[1].padStart(2, '0');

            return `${year}-${month}-${day}T${hour}:${minute}`;
        }

        // Обработчик для кнопки применения фильтра
        document.getElementById('apply-filter').addEventListener('click', function() {
            const dateFromValue = document.getElementById('date-from').value;
            const dateToValue = document.getElementById('date-to').value;

            if (!dateFromValue || !dateToValue) {
                alert('Пожалуйста, выберите обе даты');
                return;
            }

            // Конвертируем в ISO формат для сравнения
            const dateFromISO = convertToISOFormat(dateFromValue);
            const dateToISO = convertToISOFormat(dateToValue);

            if (dateFromISO > dateToISO) {
                alert('Дата "от" не может быть больше даты "до"');
                return;
            }

            // Создаем URL с параметрами фильтра
            const url = new URL(window.location.href.split('?')[0], window.location.origin);
            url.searchParams.set('date_from', dateFromISO);
            url.searchParams.set('date_to', dateToISO);
            url.searchParams.delete('salesPage'); // сбрасываем пагинацию при новом фильтре

            window.location.href = url.toString();
        });

        // Обработчик для кнопки очистки фильтра (если она существует)
        const clearFilterBtn = document.getElementById('clear-filter');
        if (clearFilterBtn) {
            clearFilterBtn.addEventListener('click', function() {
                // Устанавливаем значения по умолчанию (текущий день)
                const today = new Date();
                const todayStr = today.toISOString().split('T')[0];

                document.getElementById('date-from').value = todayStr + 'T00:00';
                document.getElementById('date-to').value = todayStr + 'T23:59';

                // Удаляем параметры фильтра из URL
                const url = new URL(window.location.href);
                url.searchParams.delete('date_from');
                url.searchParams.delete('date_to');
                url.searchParams.delete('salesPage');

                window.location.href = url.toString();
            });
        }
    });
</script>