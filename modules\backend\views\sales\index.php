<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "sales_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");

$detailText = Yii::t("app", "detail");
$editText = Yii::t("app", "edit");
$activeText = Yii::t("app", "active");
$inactiveText = Yii::t("app", "inactive");

?>

<style>
    /* Стили для индикатора загрузки */
    #sales-grid-pjax.loading {
        position: relative;
        min-height: 200px;
    }
    
    #sales-grid-pjax.loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1000;
    }
    
    #sales-grid-pjax.loading::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 50px;
        height: 50px;
        margin: -25px 0 0 -25px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 1001;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Стили для Select2 */
    #client_filter.select2 {
        min-width: 150px !important;
        width: 100% !important;
    }

    #client_filter + .select2-container {
        width: 150px !important;
    }

    /* Стили для полей ввода даты */
    input[type="date"].form-control {
        width: 150px;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6">
            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('product_keeper')): ?>
                <div class="d-flex justify-content-end align-items-center gap-2">
                    <!-- Date inputs -->
                    <div class="d-flex gap-2" style="min-width: 200px;">
                        <input type="date" id="date_from" class="form-control" placeholder="<?= Yii::t('app', 'From Date') ?>">
                        <input type="date" id="date_to" class="form-control" placeholder="<?= Yii::t('app', 'To Date') ?>">
                    </div>

                    <!-- Worker filter dropdown -->
                    <select id="client_filter" class="form-control select2">
                        <option value=""><?= Yii::t('app', 'clients') ?></option>
                        <?php foreach($clients as $client): ?>
                            <option value="<?= $client->id ?>"><?= $client->full_name ?></option>
                        <?php endforeach; ?>
                    </select>

                    <!-- Filter button -->
                    <button type="button" class="btn btn-primary" id="search-button">
                        <?= Yii::t('app', 'search') ?>
                    </button>
                   
                </div>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'sales-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="sales-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "client_name") ?></th>
                        <th><?= Yii::t("app", "sell_user") ?></th>
                        <th><?= Yii::t("app", "total_quantity") ?></th>
                        <th><?= Yii::t("app", "total_sum") ?></th>
                        <th><?= Yii::t("app", "sales_created_at") ?></th>
                        <th><?= Yii::t("app", "status") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($result as $row): ?>
                        <tr>
                            <td><?= Html::encode($row['client_name']) ?></td>
                            <td><?= Html::encode($row['sell_user_name']) ?></td>
                            <td><?= Html::encode($row['total_quantity']) ?></td>
                            <td><?= Html::encode($row['total_sum']) ?></td>
                            <td><?= Html::encode(date('d.m.Y',strtotime($row['created_at']))) ?></td>
                            <td>
                                <span class="<?= $row['deleted_at'] == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                    <?php
                                    $statusText = $row['deleted_at'] == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive");
                                    echo $statusText;
                                    ?>
                                </span>
                            </td>
                            <td>
                            <a href="#" class="badge badge-info sales-view" data-toggle="modal" data-target="#ideal-large-modal-without-save" data-id="<?= Html::encode($row['id']) ?>">
                                <span><?= Yii::t("app", "detail") ?></span>
                            </a>
                        </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "sales_view") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";
    var all = "{$all}";

    var detailText = "{$detailText}";
    var editText = "{$editText}";
    var activeText = "{$activeText}";
    var inactiveText = "{$inactiveText}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#sales-grid-view')) {
            $('#sales-grid-view').DataTable().destroy();
        }
        
        $('#sales-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[4, 'desc']], 
            "columnDefs": [
                {
                    "targets": [6], 
                    "orderable": false
                },
                {
                    "targets": 4,
                    "type": "date-eu",
                    "render": function(data, type, row) {
                        if (type === 'sort') {
                            var parts = data.split(' ');
                            var dateParts = parts[0].split('.');
                            var timePart = parts.length > 1 ? parts[1].replace(':', '') : '0000';
                            return dateParts[2] + dateParts[1] + dateParts[0] + timePart;
                        }
                        return data;
                    }
                }
            ]
        });
    }

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSalesView() {
        $(document).off('click.sales-view').on('click.sales-view', '.sales-view', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/sales/detail',
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function(response) {
                    $('#ideal-large-modal-without-save .modal-body').html(response.content);
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });
    }

    function initializeSearch() {
        $('#search-button').on('click', function() {
            var startDate = $('#date_from').val();
            var endDate = $('#date_to').val();
            var clientId = $('#client_filter').val();

            $.ajax({
                url: '/backend/sales/search',
                type: 'POST',
                data: {
                    start_date: startDate,
                    end_date: endDate,
                    client_id: clientId
                },
                success: function(response) {
                    $('#sales-grid-pjax').html(response);
                    initializeDataTable();
                },
                error: function(xhr, status, error) {
                    console.error('Search error:', error);
                }
            });
        });
    }

    function initializeAll() {
        initializeDropdown();
        initializeSalesView();
        initializeSearch();
        initializeDataTable();
    }

    initializeAll();

    $(document).on('pjax:success', function() {
        initializeAll();
    });

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
