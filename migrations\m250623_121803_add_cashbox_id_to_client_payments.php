<?php

use yii\db\Migration;

/**
 * Class m250623_121803_add_cashbox_id_to_client_payments
 */
class m250623_121803_add_cashbox_id_to_client_payments extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Добавляем колонку cashbox_id
        $this->addColumn('client_payments', 'cashbox_id', $this->bigInteger()->after('client_id'));
        
        // Добавляем внешний ключ
        $this->addForeignKey(
            'fk-client_payments-cashbox_id',
            'client_payments',
            'cashbox_id',
            'cashbox',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем внешний ключ
        $this->dropForeignKey('fk-client_payments-cashbox_id', 'client_payments');
        
        // Удаляем колонку
        $this->dropColumn('client_payments', 'cashbox_id');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250623_121803_add_cashbox_id_to_client_payments cannot be reverted.\n";

        return false;
    }
    */
}
