<?php

namespace app\common\models;
use app\modules\backend\models\Users;

use Yii;

/**
 * This is the model class for table "invoice".
 *
 * @property int $id
 * @property string $invoice_number
 * @property int $supplier_id
 * @property string|null $created_at
 * @property float $total_amount
 * @property string|null $description
 * @property string|null $deleted_at
 * @property int|null $accept_user_id
 * @property string|null $accepted_at
 * @property int|null $source
 *
 * @property Users $acceptUser
 * @property InvoiceDetail[] $invoiceDetails
 * @property Supplier $supplier
 */
class Invoice extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'invoice';
    }

    const SOURCE_WEB = 1;
    const SOURCE_MOBILE = 2;

    public static function getSourceList($source)
    {
        return match ($source) {
            self::SOURCE_WEB => Yii::t('app', 'web'),
            self::SOURCE_MOBILE => Yii::t('app', 'mobile'),
            default => null,
        };
    }
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['invoice_number', 'supplier_id', 'total_amount'], 'required'],
            [['supplier_id', 'accept_user_id', 'deleted_at', 'source'], 'default', 'value' => null],
            [['supplier_id', 'accept_user_id', 'source'], 'integer'],
            [['created_at', 'deleted_at', 'accepted_at'], 'safe'],
            [['total_amount'], 'number'],
            [['description'], 'string'],
            [['invoice_number'], 'string', 'max' => 50],
            [['supplier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Supplier::class, 'targetAttribute' => ['supplier_id' => 'id']],
            [['accept_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['accept_user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'invoice_number' => 'Invoice Number',
            'supplier_id' => 'Supplier ID',
            'created_at' => 'Created At',
            'total_amount' => 'Total Amount',
            'description' => 'Description',
            'accept_user_id' => 'Accept User ID',
        ];
    }

    /**
     * Gets query for [[AcceptUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAcceptUser()
    {
        return $this->hasOne(Users::class, ['id' => 'accept_user_id']);
    }

    /**
     * Gets query for [[InvoiceDetails]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getInvoiceDetails()
    {
        return $this->hasMany(InvoiceDetail::class, ['invoice_id' => 'id']);
    }

    /**
     * Gets query for [[Supplier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSupplier()
    {
        return $this->hasOne(Supplier::class, ['id' => 'supplier_id']);
    }
}
