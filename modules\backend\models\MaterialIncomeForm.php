<?php

namespace app\modules\backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;


class MaterialIncomeForm extends Model
{
    public $material_id;
    public $quantity;
    public $price;
    public $description;
    public $supplier_id;

    public function rules()
    {
        return [
            [['material_id', 'quantity', 'supplier_id'], 'required'],
            [['material_id', 'quantity', 'price', 'supplier_id'], 'integer'],
            [['description'], 'string', 'max' => 255],
        ];
    }

    public function attributeLabels()
    {   
        return [
            'material_id' => Yii::t('app', 'material'),
            'quantity' => Yii::t('app', 'quantity'),
            'price' => Yii::t('app', 'price'),
            'description' => Yii::t('app', 'description'),    
            'supplier_id' => Yii::t('app', 'supplier'),
        ];
    }

}