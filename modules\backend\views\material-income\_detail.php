<style>
    .card {
        background-color: white !important;
    }
    .table th {
        background-color: #f8f9fa; /* Светлый фон для заголовков таблицы */
    }
    .badge-success {
        background-color: #28a745; /* Зеленый цвет для активного статуса */
    }
    .badge-danger {
        background-color: #dc3545; /* Красный цвет для неактивного статуса */
    }
    .table-responsive {
        max-height: 300px;
        overflow-y: auto;
    }
    .table-responsive thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 1;
    }
    .table tbody tr {
        height: 48px;
    }
</style>

<?php if (!empty($materialIncomeHistory)): ?>
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>#</th>
                    <th><?= Yii::t('app', 'material_income') ?></th>
                    <th><?= Yii::t('app', 'type') ?></th>
                    <th><?= Yii::t('app', 'quantity') ?></th>
                    <th><?= Yii::t('app', 'price') ?></th>
                    <th><?= Yii::t('app', 'total_amount') ?></th>
                    <th><?= Yii::t('app', 'material_added_by') ?></th>
                    <th><?= Yii::t('app', 'material_added_date') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($materialIncomeHistory as $index => $history): ?>
                    <tr>
                        <td><?= $index + 1 ?></td>
                        <td><?= $history->material->name ?></td>
                        <td>
                            <?php 
                                switch($history->type) {
                                    case \app\common\models\MaterialStorageHistory::TYPE_INCOME:
                                        echo Yii::t('app', 'material_income_to_storage');
                                        break;
                                    case \app\common\models\MaterialStorageHistory::TYPE_OUTCOME:
                                        echo Yii::t('app', 'material_outcome');
                                        break;
                                    case \app\common\models\MaterialStorageHistory::TYPE_INCOME_TO_PRDUCTION:
                                        echo Yii::t('app', 'Income to Production');
                                        break;
                                    case \app\common\models\MaterialStorageHistory::TYPE_OUTCOME_FROM_PRDUCTION:
                                        echo Yii::t('app', 'Outcome from Production');
                                        break;
                                    default:
                                        echo '-';
                                }
                            ?>
                        </td>
                        <td><?= $history->quantity ?></td>
                        <td><?= $history->invoiceDetail->price ?? 0 ?></td>
                        <td><?= ($history->quantity ?? 0) * ($history->invoiceDetail->price ?? 0) ?></td>
                        <td><?= $history->addUser ? $history->addUser->full_name : '-' ?></td>
                        <td><?= date('d.m.Y', strtotime($history->created_at)) ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else: ?>
    <p class="text-center"><?= Yii::t('app', 'Nothing found') ?></p>
<?php endif; ?>

<?php
$js = <<<JS
document.addEventListener('DOMContentLoaded', function() {
    const tableContainer = document.querySelector('.table-responsive');
    const table = tableContainer.querySelector('table');
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');
    const maxRows = 5;
    const rowHeight = 48; // Фиксированная высота строки

    if (rows.length > maxRows) {
        tableContainer.style.maxHeight = `\${(maxRows * rowHeight) + 48}px`; // +48px для заголовка
        tableContainer.style.overflowY = 'auto';
        tableContainer.style.overflowX = 'hidden'; // Скрываем горизонтальную прокрутку
    } else {
        tableContainer.style.maxHeight = 'none';
        tableContainer.style.overflowY = 'visible';
    }
});
JS;
$this->registerJs($js);
?>
