<?php

use yii\bootstrap5\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<div class="position-form">
    <form id="position-update-form">
        <input type="hidden" name="Position[id]" value="<?= Html::encode($model->id) ?>">
        <div class="form-group">
            <label for="name"><?= Yii::t('app', 'position_name') ?></label>
            <input type="text" id="name" name="Position[name]" maxlength="255" class="form-control" value="<?= Html::encode($model->name) ?>">
            <div class="error-container" id="name-error"></div>
        </div>

        <div class="form-group">
            <label for="status"><?= Yii::t('app', 'status') ?></label>
            <select id="status" name="Position[status]" class="form-control select2">
                <option value="1" <?= is_null($model->deleted_at) ? 'selected' : '' ?>><?= Yii::t('app', 'active') ?></option>
                <option value="0" <?= !is_null($model->deleted_at) ? 'selected' : '' ?>><?= Yii::t('app', 'inactive') ?></option>
            </select>
            <div class="error-container" id="status-error"></div>
        </div>
    </form>
</div>

<?php
$this->registerCss("
    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #ced4da;
    }
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 36px;
    }
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }
    .select2-container {
        z-index: 9999;
    }
    .select2-dropdown {
        z-index: 9999;
    }
");
?>
