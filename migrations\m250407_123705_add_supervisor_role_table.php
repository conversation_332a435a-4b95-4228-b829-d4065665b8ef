<?php

use yii\db\Migration;

class m250407_123705_add_supervisor_role_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {

        $auth = Yii::$app->authManager;

        $adminRole = $auth->createRole('supervisor');
        $adminRole->description = 'Supervisor';
        $auth->add($adminRole);

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $auth = Yii::$app->authManager;
        $auth->remove($auth->getRole('supervisor'));
    }

   
}
