<?php

namespace app\modules\backend\controllers;

use app\common\models\MaterialStorageHistory;
use Yii;
use app\common\models\MaterialStorage;
use app\common\models\Invoice;
use app\common\models\InvoiceDetail;
use app\common\models\Tracking;
use yii\web\Response;
use app\modules\backend\models\MaterialIncomeForm;

/**
 * MaterialController implements the CRUD actions for Material model.
 */
class MaterialIncomeController extends BaseController
{
    /**
     * Lists all Material models.
     * @return mixed
     */
    public function actionIndex()   
    {
        $sql = "SELECT 
                    m.id,
                    m.name,
                    m.description,
                    m.created_at,
                    COALESCE(SUM(ms.quantity), 0) as total_quantity,
                    COALESCE(md.defect_quantity, 0) as defect_quantity,
                    COALESCE(latest_mc.price, 0) as price,
                    COALESCE(latest_mc.price * COALESCE(SUM(ms.quantity), 0), 0) as total_sum,
                    COALESCE(mp.quantity, 0) as in_production,
                    CASE WHEN latest_mc.currency_id = 1 THEN true ELSE false END as is_dollar
                FROM material m
                LEFT JOIN material_storage ms ON m.id = ms.material_id AND ms.deleted_at IS NULL
                LEFT JOIN (
                    SELECT material_id, SUM(quantity) as defect_quantity
                    FROM material_defect
                    WHERE deleted_at IS NULL
                    GROUP BY material_id
                ) md ON m.id = md.material_id
                LEFT JOIN (
                    SELECT material_id, price, currency_id
                    FROM material_currency mc
                    WHERE mc.created_at = (
                        SELECT MAX(created_at)
                        FROM material_currency mc2
                        WHERE mc2.material_id = mc.material_id
                        AND mc2.deleted_at IS NULL
                    )
                    AND mc.deleted_at IS NULL
                ) latest_mc ON m.id = latest_mc.material_id
                LEFT JOIN currency c ON latest_mc.currency_id = c.id
                LEFT JOIN (
                    SELECT material_id, SUM(quantity) as quantity
                    FROM material_production
                    GROUP BY material_id
                ) mp ON m.id = mp.material_id
                WHERE m.deleted_at IS NULL
                GROUP BY m.id, m.name, m.description, m.created_at, md.defect_quantity, latest_mc.price, latest_mc.currency_id, mp.quantity
                ORDER BY m.id";
    
        $result = Yii::$app->db->createCommand($sql)->queryAll();
    
        if (Yii::$app->request->isAjax) {
            return $this->renderAjax('index', [
                'result' => $result,
            ]);
        }
    
        return $this->render('index', [
            'result' => $result,
        ]);
    }

    /**
     * Creates a new Material model.
     * @return mixed
     */
    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $model = new MaterialIncomeForm();
            return [
                'content' => $this->renderPartial('create', [
                    'model' => $model,
                ]),
            ];
        }

        if (Yii::$app->request->isPost) {
            $model = new MaterialIncomeForm();
            $model->load(Yii::$app->request->post());

            if ($model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $invoice = new Invoice();
                    $invoice->invoice_number = (int)substr(date('YmdHis'), 0, 10) . str_pad((string)Invoice::find()->count() + 1, 4, '0', STR_PAD_LEFT);
                    $invoice->supplier_id = $model->supplier_id ?? 0;
                    $invoice->total_amount = (float)($model->price ?? 0) * (float)$model->quantity;
                    $invoice->description = $model->description;
                    $invoice->source = Invoice::SOURCE_WEB;
                    $invoice->created_at = date('Y-m-d H:i:s');

                    if(!$invoice->save()){
                        throw new \Exception('Invoice save error');
                    }

                    $invoiceDetail = new InvoiceDetail();
                    $invoiceDetail->invoice_id = $invoice->id;
                    $invoiceDetail->material_id = $model->material_id;
                    $invoiceDetail->price = $model->price ?? 0;
                    $invoiceDetail->quantity = $model->quantity;
                    $invoiceDetail->remainder_quantity = $model->quantity;
                    $invoiceDetail->created_at = date('Y-m-d H:i:s');

                    if(!$invoiceDetail->save()){
                        throw new \Exception('Invoice detail save error');
                    }

                    $materialStorage = MaterialStorage::find()
                        ->where(['material_id' => $model->material_id])
                        ->andWhere(['>=', 'updated_at', date('Y-m-d 00:00:00')])
                        ->andWhere(['<=', 'updated_at', date('Y-m-d 23:59:59')])
                        ->one();

                    if(!$materialStorage){
                        $materialStorage = new MaterialStorage();
                        $materialStorage->material_id = $model->material_id;
                        $materialStorage->quantity = 0;
                        $materialStorage->created_at = date('Y-m-d H:i:s');
                    }

                    $materialStorage->quantity += $model->quantity;
                    $materialStorage->updated_at = date('Y-m-d H:i:s');

                    if(!$materialStorage->save()){
                        throw new \Exception( json_encode($materialStorage->getErrors()));
                    }

                    $materialStorageHistory = new MaterialStorageHistory(); 
                    $materialStorageHistory->material_storage_id = $materialStorage->id;
                    $materialStorageHistory->material_id = $model->material_id;
                    $materialStorageHistory->quantity = $model->quantity;
                    $materialStorageHistory->created_at = date('Y-m-d H:i:s');
                    $materialStorageHistory->add_user_id = Yii::$app->user->id;
                    if(!$materialStorageHistory->save()){
                        throw new \Exception( json_encode($materialStorageHistory->getErrors()));
                    }
                    
                        
                        


                    $tracking = new Tracking();
                    $tracking->progress_type = Tracking::TYPE_MATERIAL_INCOME;
                    $tracking->process_id = $invoice->id;
                    $tracking->created_at = date('Y-m-d H:i:s');
                    $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                    $tracking->accepted_at = null;
                    
                    if (!$tracking->save()) {
                        throw new \Exception('Tracking save error');
                    }

                    $transaction->commit();

                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'Material received successfully.')
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'errors' => ['error' => $e->getMessage()],
                    ];
                }
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors(),
                ];
            }
        }
    }


    public function actionDetail()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
    
        $id = Yii::$app->request->get('id');
        $materialIncomeHistory = MaterialStorageHistory::find()
            ->where(['material_id' => $id, 'type' => MaterialStorageHistory::TYPE_INCOME])
            ->with('invoiceDetail')
            ->orderBy(['created_at' => SORT_DESC])
            ->all();

        if (Yii::$app->request->isGet) {
            return [
                'status' => 'success',
                'content' => $this->renderPartial('_detail', [
                    'materialIncomeHistory' => $materialIncomeHistory,
                ]),
            ];
        }
    
        return [
            'status' => 'error',
            'message' => Yii::t('app', 'Invalid request.'),
        ];
    }
 
}
