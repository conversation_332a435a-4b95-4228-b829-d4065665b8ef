<?php

namespace app\common\jobs;

use Yii;
use yii\base\BaseObject;
use yii\queue\JobInterface;
use app\common\models\Notification;
use app\common\models\NotificationReadStatus;

/**
 * Задача для отправки уведомлений через Firebase
 */
class FirebaseNotificationJob extends BaseObject implements JobInterface
{
    /**
     * ID уведомления
     * @var int
     */
    public $notification_id;

    /**
     * Выполнение задачи
     * @param \yii\queue\Queue $queue
     * @return void
     */
    public function execute($queue)
    {
        // Получаем уведомление из БД
        $notification = Notification::findOne($this->notification_id);
        if (!$notification) {
            Yii::error("Уведомление с ID {$this->notification_id} не найдено", 'firebase');
            return;
        }

        // Если уведомление уже отправлено, пропускаем
        if ($notification->is_sent) {
            return;
        }

        try {
            $data = $notification->data ? json_decode($notification->data, true) : [];

            // Если уведомление для роли
            if ($notification->role) {
                // Сначала пробуем отправить через топик (для всех устройств с подпиской на роль)
                $topicResult = Yii::$app->firebase->sendToTopic("role_{$notification->role}", $notification->title, $notification->body, $data);

                // Затем отправляем индивидуально каждому устройству пользователей с этой ролью
                $roleTokens = $this->getTokensByRole($notification->role);
                $allRoleSuccess = true;

                if (!empty($roleTokens)) {
                    foreach ($roleTokens as $token) {
                        $result = Yii::$app->firebase->sendToDevice($token, $notification->title, $notification->body, $data);

                        if (!$result['success']) {
                            $allRoleSuccess = false;
                            Yii::error("Ошибка отправки уведомления роли {$notification->role} на токен {$token}: " . json_encode($result), 'firebase');
                        }
                    }
                }

                // Если хотя бы один из способов отправки сработал, помечаем уведомление как отправленное
                if ($topicResult['success'] || $allRoleSuccess) {
                    $notification->markAsSent();
                } else {
                    Yii::error("Ошибка отправки уведомления роли {$notification->role}: " . json_encode($topicResult), 'firebase');
                }
            }
            // Если уведомление для конкретных пользователей (без роли)
            else {
                // Получаем всех пользователей, связанных с этим уведомлением
                $readStatuses = NotificationReadStatus::find()
                    ->where(['notification_id' => $notification->id])
                    ->all();

                $allSuccess = true;

                foreach ($readStatuses as $status) {
                    $userTokens = $this->getUserToken($status->user_id);

                    if (!empty($userTokens)) {
                        foreach ($userTokens as $token) {
                            $result = Yii::$app->firebase->sendToDevice($token, $notification->title, $notification->body, $data);

                            if (!$result['success']) {
                                $allSuccess = false;
                                Yii::error("Ошибка отправки уведомления пользователю {$status->user_id} на токен {$token}: " . json_encode($result), 'firebase');
                            }
                        }
                    }
                }

                if ($allSuccess || empty($readStatuses)) {
                    $notification->markAsSent();
                }
            }
        } catch (\Exception $e) {
            Yii::error("Исключение при отправке уведомления {$notification->id}: " . $e->getMessage(), 'firebase');
        }
    }

    /**
     * Получить токены устройств пользователя
     *
     * @param int $user_id ID пользователя
     * @return array Массив токенов устройств
     */
    private function getUserToken($user_id)
    {
        $tokens = \app\common\models\UserDeviceToken::getActiveTokensByUserId($user_id);

        if (empty($tokens)) {
            return [];
        }

        // Возвращаем массив токенов устройств
        return array_map(function($token) {
            return $token->device_token;
        }, $tokens);
    }

    /**
     * Получить токены устройств пользователей с определенной ролью
     *
     * @param string $role Название роли
     * @return array Массив токенов устройств
     */
    private function getTokensByRole($role)
    {
        $tokens = \app\common\models\UserDeviceToken::getActiveTokensByRole($role);

        if (empty($tokens)) {
            return [];
        }

        // Возвращаем массив токенов устройств
        return array_map(function($token) {
            return $token->device_token;
        }, $tokens);
    }
}
