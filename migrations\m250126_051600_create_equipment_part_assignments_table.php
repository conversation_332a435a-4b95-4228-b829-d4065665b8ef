<?php

use yii\db\Migration;

/**
 * Создание таблицы equipment_part_assignments для связи запчастей с оборудованием
 * Это решает проблему дублирования записей в equipment_parts
 */
class m250126_051600_create_equipment_part_assignments_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Создаем таблицу связей запчастей с оборудованием
        $this->createTable('{{%equipment_part_assignments}}', [
            'id' => $this->primaryKey(),
            'equipment_id' => $this->integer()->notNull()->comment('ID оборудования'),
            'equipment_part_id' => $this->integer()->notNull()->comment('ID запчасти'),
            'quantity' => $this->integer()->notNull()->defaultValue(1)->comment('Количество установленных единиц'),
            'installation_date' => $this->date()->comment('Дата установки'),
            'status' => $this->integer()->notNull()->defaultValue(1)->comment('Статус назначения: 1-активно, 0-снято, 2-в ремонте'),
            'comment' => $this->text()->comment('Комментарий к назначению'),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP')->comment('Дата создания'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP')->comment('Дата обновления'),
            'created_by' => $this->integer()->comment('Кто создал запись'),
        ]);

        // Добавляем индексы
        $this->createIndex(
            'idx-equipment_part_assignments-equipment_id',
            '{{%equipment_part_assignments}}',
            'equipment_id'
        );

        $this->createIndex(
            'idx-equipment_part_assignments-equipment_part_id',
            '{{%equipment_part_assignments}}',
            'equipment_part_id'
        );

        $this->createIndex(
            'idx-equipment_part_assignments-status',
            '{{%equipment_part_assignments}}',
            'status'
        );

        // Уникальный индекс для предотвращения дублирования активных назначений
        $this->createIndex(
            'idx-equipment_part_assignments-unique-active',
            '{{%equipment_part_assignments}}',
            ['equipment_id', 'equipment_part_id', 'status'],
            false
        );

        // Добавляем внешние ключи
        $this->addForeignKey(
            'fk-equipment_part_assignments-equipment_id',
            '{{%equipment_part_assignments}}',
            'equipment_id',
            '{{%equipment}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-equipment_part_assignments-equipment_part_id',
            '{{%equipment_part_assignments}}',
            'equipment_part_id',
            '{{%equipment_parts}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-equipment_part_assignments-created_by',
            '{{%equipment_part_assignments}}',
            'created_by',
            '{{%users}}',
            'id',
            'SET NULL'
        );

        // Добавляем комментарий к таблице
        $this->addCommentOnTable('{{%equipment_part_assignments}}', 'Таблица связей запчастей с оборудованием');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем внешние ключи
        $this->dropForeignKey('fk-equipment_part_assignments-created_by', '{{%equipment_part_assignments}}');
        $this->dropForeignKey('fk-equipment_part_assignments-equipment_part_id', '{{%equipment_part_assignments}}');
        $this->dropForeignKey('fk-equipment_part_assignments-equipment_id', '{{%equipment_part_assignments}}');

        // Удаляем таблицу
        $this->dropTable('{{%equipment_part_assignments}}');
    }
}
